import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CodeExecution } from './code-execution.model';

@Injectable()
export class CodeExecutionService {
  constructor(
    @InjectModel(CodeExecution) private codeExecutionModel: typeof CodeExecution,
  ) {}

  createCodeExecution(dto) {
    try {
      const data={...dto}
      return this.codeExecutionModel.create(data);
    } catch (error) {
      Logger.log("Error creating code execution",error)
    }
  }
 }
