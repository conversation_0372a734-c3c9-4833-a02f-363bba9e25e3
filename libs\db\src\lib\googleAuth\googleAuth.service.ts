import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { GoogleAuth } from './googleAuth.model';

@Injectable()
export class GoogleAuthModelService {
  constructor(
    @InjectModel(GoogleAuth)
    private googleAuthModel: typeof GoogleAuth
  ) {}

  find(dto: string): Promise<GoogleAuth> {
    return this.googleAuthModel.findOne({
      where: {
        tokenId: dto,
      },
    });
  }

  create(dto): Promise<GoogleAuth> {
    return this.googleAuthModel.create(dto);
  }

  update(id: number, dto) {
    return this.googleAuthModel.update({ ...dto }, { where: { id } });
  }

  delete(id: number) {
    return this.googleAuthModel.destroy({ where: { id } });
  }
}
