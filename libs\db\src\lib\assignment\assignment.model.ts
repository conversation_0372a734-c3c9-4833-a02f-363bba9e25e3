import { ApiProperty } from '@nestjs/swagger';
import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import {
  DomainAssessment
} from '../domain-questions/domain-assessment.model';
import { LiveCoding } from '../live-coding/live-coding.model';
import { TakeHomeTask } from '../take-home/take-home-task.model';

@Table({
  tableName: 'assignment-new',
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class Assignment extends Model<Assignment> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({ example: '1', description: 'Job Id' })
  @Column({type: DataType.INTEGER, allowNull: false})
    jobId: number;

  @ApiProperty({ example: '1', description: 'company Id' })
  @Column({type: DataType.INTEGER, allowNull: false})
  companyId: number;

  @ApiProperty({
    example: '12',
    description: 'workflow id',
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
    workflowId: number;

  @ApiProperty({ example: 'Angular developer', description: 'Job Title' })
  @Column({ type: DataType.STRING, allowNull: false })
    title: string;

  @ForeignKey(() => LiveCoding)
  @Column({ type: DataType.INTEGER, allowNull: true })
    liveCodingId: number;

    @BelongsTo(()=>LiveCoding)
    liveCoding:LiveCoding

  @ForeignKey(() => TakeHomeTask)
  @Column({ type: DataType.INTEGER, allowNull: true })
    takeHomeTaskId: number;

    @BelongsTo(()=>TakeHomeTask)
    takeHomeTask:TakeHomeTask

  @ForeignKey(() => DomainAssessment)
  @Column({ type: DataType.INTEGER, allowNull: true })
    domainId: number;

    @BelongsTo(()=>DomainAssessment)
    domainAssessment:DomainAssessment

  @ApiProperty({ description: 'Array of the candidate dd' })
  @Column({ type: DataType.ARRAY(DataType.INTEGER), allowNull: true })
  candidate?: any[];

  @ApiProperty({ description: "Array of the candidate's workflow status" })
  @Column({ type: DataType.ARRAY(DataType.JSONB), allowNull: true })
  candidateStatus?: { id: number, status: string }[];

  @ApiProperty({
    example: '12 Hrs',
    description: 'Deadline for fullfill the assignment',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    deadline: string;

  @ApiProperty({ description: 'Array of the userIds' })
  @Column({ type: DataType.ARRAY(DataType.INTEGER), allowNull: true })
    reviewers: number[];

    @ApiProperty({
      example: 'true/false',
      description: 'Is active',
    })
    @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue:true })
      isActive: boolean;
}


