import { Injectable } from '@nestjs/common';
import { roomUser } from './roomUser.interface';

@Injectable()
export class RoomUserService {
  private users: roomUser[] = [];

  addUser = (user: roomUser) => {
    if (user.name) {
      user.name = user.name.trim();
      this.users.push(user);

      return { user };
    } else {
      return { error: 'Enter a valid name' };
    }
  };

  removeUser = (id) => {
    const index = this.users.findIndex((user) => user.id === id);
    if (index !== -1) {
      return this.users.splice(index, 1)[0];
    }

    return null;
  };

  findUser = (id) => this.users.find((user) => user.id === id);

  usersInRoom = (room) => this.users.filter((user) => user.room === room);
}
