import {
  Controller,
  Post,
  Body,
  Get,
  Patch,
  Delete,
  UseGuards,
  Param,
  Query,
  Headers,
} from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  DeleteJobDto,
  FilterJobDto,
  FilterPublicCompanyJobsDto,
  JobCreationChatbotService,
  JobCreationSuggestionDto,
  JobDto,
  Jobs,
  JobService,
  SingleJobFilterDto,
  UpdateJobDto,
} from "@microservices/recruitment-db";
// import { LangchainService } from "@microservices/integrations";
// import { QdrantClient } from "@qdrant/qdrant-js"
//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],
// const langchainService = new LangchainService();
@ApiTags("Job")
@ApiBearerAuth("access-token")
@Controller("job")
export class JobController {
  constructor(private readonly jobService: JobService,
    private readonly jobChatbotService: JobCreationChatbotService,
  ) {}

  @ApiOperation({ summary: "Create job" })
  @ApiResponse({ status: 200, type: Jobs })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post()
  @Permissions("job-post:add")
  create(@Headers('authorization') authHeader:any,@Body() dto: JobDto, @AuthUser() user: any) {
    return this.jobService.createJob(
      dto,
      user["https://urecruits.com/companyId"],
      user["https://urecruits.com/userId"],
      authHeader
    );
  }
  // @ApiOperation({ summary: "Create job" })
  // @ApiResponse({ status: 200, type: Jobs })
  // @Get('/open-search/job/:id')
  // addJobObOpenSearch(@Param("id") id: number) {
  //   return this.jobService.addDataonOpensearch(id);
  // }

  @ApiOperation({ summary: "Create Assignment for Assessment Package" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/assignment")
  @Permissions("assessment:view")
  createJobForAssessment(@Headers('authorization') authHeader: any, @Body() dto, @AuthUser() user: any) {
    return this.jobService.createAssignmentForAssessment(
      dto,
      user["https://urecruits.com/userId"],
      user["https://urecruits.com/companyId"],
      authHeader
    );
  }

  @ApiOperation({ summary: "Get all jobs" })
  @ApiResponse({ status: 200, type: Jobs })
  // @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  getAllJobs(@Query() query: FilterJobDto) {
    return this.jobService.getAllJobs(query);
  }

  @ApiOperation({ summary: "Get job by id (only publish)" })
  @ApiResponse({ status: 200, type: Jobs })
  @Get("/public-job/:id")
  getJob(@Param("id") id: number, @Query() query: SingleJobFilterDto) {
    return this.jobService.getJob(id, query,'publish');
  }

  @ApiOperation({ summary: "Get job by id" })
  @ApiResponse({ status: 200, type: Jobs })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/company/:id")
  @Permissions("OR","candidate","recruiter")
  getPrivateJob(@Param("id") id: number, @AuthUser() user: any) {
    return this.jobService.getCompanyJob(
      id,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get job by id" })
  @ApiResponse({ status: 200, type: Jobs })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/:id")
  async getJobById(@Param("id") id: number) {
    const result=await this.jobService.getJobData(id)
    return result;
  }
  @ApiOperation({ summary: "Edit job" })
  @ApiResponse({ status: 200, type: Jobs })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch()
  @Permissions("job-post:edit",'recruiter')
  editJob(@Headers('authorization') authHeader:any,@Body() dto: UpdateJobDto, @AuthUser() user: any) {
    return this.jobService.editJob(
      dto,
      user["https://urecruits.com/companyId"],
      authHeader
    );
  }

  @ApiOperation({ summary: "Delete job" })
  @ApiResponse({ status: 201, type: "" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete()
  @Permissions("job-post:delete")
  deleteJob(@Body() dto: DeleteJobDto, @AuthUser() user: any) {
    return this.jobService.deleteJob(
      dto.id,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get company jobs" })
  @ApiResponse({ status: 200, type: Jobs })
  @Get("/public-jobs/company")
  getPublicCompanyJob(@Query() query: FilterPublicCompanyJobsDto) {
    return this.jobService.getPublicCompanyJob(query);
  }

  @ApiOperation({ summary: "Get Match job for candidate" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/match/jobs")
  @Permissions("candidate")
  getMatchingJob(@Query() query: FilterJobDto, @AuthUser() user: any) {
    const userId = user["https://urecruits.com/userId"];
    return this.jobService.getMatchedJobs(query, userId);
  }

  @ApiOperation({ summary: "Get today's Matched jobs count for candidate" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/match/count")
  @Permissions("candidate")
  getTodayMatchJobs(@AuthUser() user: any) {
    const userId = user["https://urecruits.com/userId"];
    return this.jobService.countTodayMatchedJobs(userId);
  }

  @ApiOperation({ summary: "Create Dummy jobs" })
  @ApiResponse({ status: 200 })
  @Post("/dummy")
  createDummyJobs(@Body() dto) {
    return this.jobService.createDummy(dto)
  }

  @ApiOperation({ summary: "Use user prompt to get job details suggestions." })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/suggestions")
  @Permissions("recruiter")
  getJobCreationSuggestions(@AuthUser() user: any, @Body() body: JobCreationSuggestionDto) {
    const companyId = user["https://urecruits.com/companyId"];
    const userId = user["https://urecruits.com/userId"];
    return this.jobChatbotService.startConversation(body, companyId,userId);
  }

  // @ApiOperation({ summary: "Feed jobs to Qdrant" })
  // @ApiResponse({ status: 200 })
  // @Get("/qdrant/feed") 
  // async getJobCreationSuggestionsFromChatbot() {
  //   const client = new QdrantClient({ url: process.env.QDRANT_DATABASE_URL, apiKey: process.env.QDRANT_API_KEY });

  //   // Fetch jobs in batches
  //   const batchSize = 10;
  //   let offset = 0;
  //   let hasMoreJobs = true;

  //   while (hasMoreJobs) {
  //     const jobs = await this.jobService.getAllJobs({ limit: batchSize, offset });
  //     if (!jobs.rows?.length) {
  //       hasMoreJobs = false;
  //       break;
  //     }

  //     const points = [];
  //     for (let job of jobs.rows) {
  //       const data = this.jobService.getJobDataForQdrant(job);
  //       if(!data){
  //         continue;
  //       }
  //       try {
  //         const jobVector = await this.langchainService.getEmbeddings(data.str);
  //         points.push({
  //           id:data.id,
  //           vector:jobVector,
  //           payload:data.payload
  //         })
  //       } catch (error) {
  //         console.error("Error fetching embeddings:", error);
  //       }

  //       // Add a delay to respect rate limits
  //       await new Promise(resolve => setTimeout(()=>{
  //         resolve(true)
  //       }, 3000));
  //     }

  //     console.log({ points });
  //     try {
  //       const response = await client.upsert("jobs", { wait: true, points });
  //       console.log("Upsert Response:", response);
  //     } catch (error) {
  //       console.error("Upsert failed:", error);
  //     }

  //     // Move to the next batch
  //     offset += batchSize;
  //   }
  // }
}
