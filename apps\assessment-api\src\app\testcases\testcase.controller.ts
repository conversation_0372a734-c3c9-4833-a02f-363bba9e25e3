import { TestCase, TestCaseService, QuestionsService, Questions } from '@microservices/db';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  Logger,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { TestCaseGeneratorService } from './testcase-generator.service';
import { GenerateTestCaseDto } from './dto/generate-testcase.dto';

@Controller()
@ApiBearerAuth("access-token")
export class TestCaseController {
  constructor(
    private readonly testCaseService: TestCaseService,
    private readonly questionService: QuestionsService,
    private readonly testCaseGeneratorService: TestCaseGeneratorService
  ) { }

  @ApiOperation({ summary: 'Create test case' })
  @ApiResponse({ status: 201 })
  @Post('testcase')
  async create(@Body() createTestCaseDto) {
    try {
      const result = await this.testCaseService.create(createTestCaseDto);
      return result;
    } catch (error) {
      throw error;
    }
  }

  @ApiOperation({ summary: 'Generate test cases using AI' })
  @ApiResponse({
    status: 201,
    description: 'Returns generated test cases ID'
  })
  @Post('generate-test-cases')
  async generateTestCases(@Body() generateTestCaseDto: GenerateTestCaseDto) {
    try {
      // Generate test cases
      const result = await this.testCaseGeneratorService.generateTestCases(
        generateTestCaseDto.languageId,
        generateTestCaseDto.questionDescription,
        generateTestCaseDto.nameQuestion
      );


      // Return the ID for later retrieval
      return {
        id: result.id,
        message: 'Test cases generated successfully'
      };
    } catch (error) {
      return {
        error: error.message || 'Failed to generate test cases',
        status: 'error'
      };
    }
  }

  @ApiOperation({ summary: 'Retrieve generated test cases by ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns previously generated test cases'
  })
  @Get('generate-test-cases/:id')
  getGeneratedTestCases(@Param('id') id: string) {
    const testCases = this.testCaseGeneratorService.getGeneratedTestCases(id);

    if (!testCases) {
      return {
        error: 'Test cases not found',
        status: 'error'
      };
    }

    return testCases;
  }

  @ApiOperation({ summary: 'Save generated test cases to database' })
  @ApiResponse({
    status: 201,
    description: 'Saves the generated test cases to the database'
  })
  @ApiQuery({ name: 'generationId', required: true, description: 'ID of the generated test cases' })
  @Post('save-test-cases')
  async saveTestCases(
    @Query('generationId') generationId: string,
    @Body() questionData: any
  ) {
    try {

      // Get the generated test cases from cache
      const generatedData = this.testCaseGeneratorService.getGeneratedTestCases(generationId);

      if (!generatedData) {
        return {
          error: 'Generated test cases not found',
          status: 'error'
        };
      }



      // Format the testCaseInputs from the generated data
      const testCaseInputs = this.testCaseGeneratorService.formatTestCaseInputsForQuestion(
        generatedData.testCasesData
      );

      // Create a new Questions instance with safe string conversion
      const questionToCreate = new Questions();

      // Safely convert all fields to ensure no arrays/objects are passed
      questionToCreate.name = String(questionData.name || generatedData.questionName);
      questionToCreate.description = String(questionData.description || generatedData.questionDescription);
      questionToCreate.languageId = Number(questionData.languageId || generatedData.languageId);
      questionToCreate.outputDescription = String(questionData.outputDescription || 'Return the expected output');
      questionToCreate.outputType = String(questionData.outputType || 'any');
      questionToCreate.candidateInstruction = String(questionData.candidateInstruction || generatedData.questionDescription);
      questionToCreate.starterCode = String(questionData.starterCode || '');
      questionToCreate.testCaseInputs = testCaseInputs; // This is already a JSON string from formatTestCaseInputsForQuestion

      // Set optional fields safely
      questionToCreate.useAIGeneratedTestCases = true;
      questionToCreate.packageId = questionData.packageId || null;
      questionToCreate.databaseId = questionData.databaseId || null;

      // Create the question first
      const question = await this.questionService.create(questionToCreate);

      if (!question || !question.id) {
        throw new Error('Failed to create question');
      }

      // Format test cases for database
      const testCasesToCreate = this.testCaseGeneratorService.formatTestCasesForDB(
        generatedData.testCasesData,
        question.id
      );
      // Create test cases in database
      const createdTestCases = [];
      const failedTestCases = [];

      for (let i = 0; i < testCasesToCreate.length; i++) {
        try {
          const tc = testCasesToCreate[i];
          // Log the test case data before creation
          // Verify the test case data
          if (!tc.questionId) {
            failedTestCases.push({ index: i, reason: 'Missing questionId', data: tc });
            continue;
          }

          if (!tc.input) {
            failedTestCases.push({ index: i, reason: 'Missing input', data: tc });
            continue;
          }

          if (!tc.output) {
            failedTestCases.push({ index: i, reason: 'Missing output', data: tc });
            continue;
          }

          // Check if output is valid string
          if (typeof tc.output !== 'string') {
            tc.output = String(tc.output);
          }

          // Check if output is too long
          if (tc.output.length > 255) {
            tc.output = tc.output.substring(0, 250) + '...';
          }

          // Create the test case
          const testCase = await this.testCaseService.create(tc);

          // Log the created test case

          createdTestCases.push(testCase);
        } catch (error) {
          failedTestCases.push({ index: i, reason: error.message });
        }
      }

      // Remove from cache after saving to database
      this.testCaseGeneratorService.removeFromCache(generationId);

      return {
        questionId: question.id,
        testCasesCount: createdTestCases.length,
        failedTestCasesCount: failedTestCases.length,
        failedTestCases: failedTestCases,
        status: 'success',
        message: 'Test cases saved to database successfully'
      };
    } catch (error) {
      return {
        error: error.message || 'Failed to save test cases to database',
        status: 'error'
      };
    }
  }

  @ApiOperation({ summary: 'Get all test case' })
  @ApiResponse({ status: 200 })
  @Get('testcase')
  findAll() {
    return this.testCaseService.findAll();
  }

  @ApiOperation({ summary: 'Get test case by id' })
  @ApiResponse({ status: 200 })
  @Get('testcase/:id')
  findOne(@Param('id') id: string) {
    return this.testCaseService.findOneById(+id);
  }

  @ApiOperation({ summary: 'Update test case' })
  @ApiResponse({ status: 201 })
  @Patch('testcase/:id')
  update(@Param('id') id: string, @Body() updateProblemDto: TestCase) {
    return this.testCaseService.update(+id, updateProblemDto);
  }

  @ApiOperation({ summary: 'Delete test case' })
  @ApiResponse({ status: 201 })
  @Delete('testcase/:id')
  remove(@Param('id') id: string) {
    return this.testCaseService.remove(+id);
  }
}
