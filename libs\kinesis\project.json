{"root": "libs/kinesis", "sourceRoot": "libs/kinesis/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/kinesis/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/kinesis"], "options": {"jestConfig": "libs/kinesis/jest.config.js", "passWithNoTests": true}}}, "tags": []}