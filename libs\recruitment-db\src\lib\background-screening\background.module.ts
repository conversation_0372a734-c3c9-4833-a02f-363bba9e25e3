import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { BackgroundService } from './background.service';
import { Jobs } from "../job/job.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { BackgroundModel } from "./background.model";
import { User } from "../users/users.model";
import { FileModule } from 'apps/pre-recruitment-api/src/file/file.module';
import { HttpModule } from '@nestjs/axios';
import { CompaniesModule } from '../companies/companies.module';
import { EmailModule } from '@microservices/email';

@Module({
  imports: [
    SequelizeModule.forFeature([Subscribe, BackgroundModel, Jobs, User]),
    FileModule,HttpModule,CompaniesModule,EmailModule
  ],
  providers: [BackgroundService],
  exports: [BackgroundService],
})
export class BackgroundModule {}
