import { <PERSON><PERSON>sTo, Column, DataType, ForeignKey, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "../users/users.model";
import { BGVStatus } from "./background.enum";
import { Jobs } from "../job/job.model";
import { Company } from "../companies/companies.model";

@Table({ tableName: "background-screening", createdAt: true, updatedAt: true })
export class BackgroundModel extends Model<BackgroundModel> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "1", description: "User Id" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({ example: "1", description: "Job Id" })
  @ForeignKey(() => Jobs)
  @Column({ type: DataType.INTEGER, allowNull: false })
  jobId: number;

  @ApiProperty({ example: "1", description: "Company Id" })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({
    example: "Universal",
    description: "Vendor Selected for Background Screening",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  vendor: string;

  @ApiProperty({
    example: "Reviewed",
    description: "Result of Background Screening",
  })
  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: BGVStatus.NOT_INITIATED,
  })
  BGV_status: string;

  @ApiProperty({
    example: "No discrepancy is reported in the report",
    description: "Recruiter Comment",
  })
  @Column({ type: DataType.TEXT, allowNull: true })
  recruiter_comment: string;

  @BelongsTo(() => User)
  users: User;

  @BelongsTo(() => Company)
  company: Company;
  
  @BelongsTo(() => Jobs)
  job: Jobs;
}
