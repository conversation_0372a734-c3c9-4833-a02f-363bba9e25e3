import { Controller, Post, Body, Get, Patch, Delete, UseGuards, Param, Query, Put, BadRequestException } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  AddRecruiterDto,
  Address,
  AddressesService,
  CompaniesService,
  Company,
  Recruiter,
  RecruitersService,
  RegisterCompanyDto,
  UpdateAddressDto,
  UpdateCompanyDto,
  IAMUsersDto,
  UsersService,
  User,
  RolesService,
  Role,
  ChangeRolesDto,
  GetMembersDto,
  GetCompaniesForJobDto,
  EditRecruiterDto,
  SendMailDto, FilterCompanyJobsDto, Jobs, JobService, GetPublicCompaniesDto,
} from "@microservices/recruitment-db";
import { SendCompanyEmailDto } from '../../../../libs/recruitment-db/src/lib/companies/dto/send-company-email.dto';
import { EditPermissionsDto } from '../../../../libs/recruitment-db/src/lib/companies/dto/editPermissionsForRole.dto';
import { CreateCompanyRoleDto } from '../../../../libs/recruitment-db/src/lib/roles/dto/create-company-role.dto'
import { DeleteUserDto } from "./dto/delete-user.dto";
// import { LangchainService } from "@microservices/integrations";
// import { QdrantClient } from "@qdrant/qdrant-js"

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   candidateId: user["https://urecruits.com/candidateId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],
// const langchainService = new LangchainService();
@ApiTags("Company")
@Controller("company")
export class CompanyController {
  constructor(
    private readonly companiesService: CompaniesService,
    private readonly addressesService: AddressesService,
    private readonly recruitersService: RecruitersService,
    private readonly usersService: UsersService,
    private readonly rolesService: RolesService,
    private readonly jobService: JobService,
  ) { }

  @ApiOperation({ summary: "Register company" })
  @ApiResponse({ status: 201, type: Company })
  @Post()
  create(@Body() dto: RegisterCompanyDto) {
    return this.companiesService.registerCompany(dto);
  }

  @ApiOperation({ summary: "Get company" })
  @ApiResponse({ status: 200, type: Company })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("company-profile:view", "recruiter", "OR")
  getCompany(@AuthUser() user: any) {
    return this.companiesService.getCompany(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "Get company" })
  @Get("/open-search/:id")
  addOnOpensearch(@Param("id") id: number) {
    return this.companiesService.addDataonOpensearch(id);
  }

  @ApiOperation({ summary: "Check company by tenantId" })
  @ApiResponse({ status: 200, type: Company })
  @Get("/check/:tenantId")
  getByEmail(@Param("tenantId") tenantId: string) {
    return this.companiesService.isExistCompany(tenantId);
  }

  @ApiOperation({ summary: "Add new address" })
  @ApiResponse({ status: 200, type: Address })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/address")
  @Permissions("OR", "company-profile:add", "company-profile:edit")
  addAddress(@AuthUser() user: any) {
    return this.addressesService.create(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "Update address" })
  @ApiResponse({ status: 200, type: Address })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/address")
  @Permissions("company-profile:edit")
  updateAddress(@Body() dto: UpdateAddressDto, @AuthUser() user: any) {
    return this.addressesService.update({ ...dto, companyId: user["https://urecruits.com/companyId"] });
  }

  @ApiOperation({ summary: "Delete address" })
  @ApiResponse({ status: 200, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/address")
  @Permissions("company-profile:edit")
  deleteAddress(@Body() dto: UpdateAddressDto, @AuthUser() user: any) {
    return this.addressesService.delete({ ...dto, companyId: user["https://urecruits.com/companyId"] });
  }

  @ApiOperation({ summary: "Update company" })
  @ApiResponse({ status: 200, type: Company })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch()
  @Permissions("company-profile:edit")
  updateCompany(@Body() dto: UpdateCompanyDto, @AuthUser() user: any) {
    return this.companiesService.updateCompany({ ...dto, id: user["https://urecruits.com/companyId"] });
  }

  @ApiOperation({ summary: "Edit team member" })
  @ApiResponse({ status: 200, type: Recruiter })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/edit-member")
  @Permissions("team-members:edit")
  editCompanyMember(@Body() dto: EditRecruiterDto, @AuthUser() user: any) {
    return this.recruitersService.editMember(
      { ...dto, companyId: user["https://urecruits.com/companyId"] });
  }

  @ApiOperation({ summary: "Add new team member" })
  @ApiResponse({ status: 200, type: Recruiter })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiBearerAuth('access-token')
  @Post("/add-member")
  @Permissions("team-members:add")
  addCompanyMember (@Body() dto: AddRecruiterDto, @AuthUser() user: any) {
    return this.recruitersService.addRecruiter(
      { ...dto, companyId: user["https://urecruits.com/companyId"], tenantId: user["https://urecruits.com/tenantId"] });
  }

  @ApiOperation({ summary: "IAM page" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/iam")
  getIAM(@AuthUser() user: any) {
    return this.companiesService.aim(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "IAM users page" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/iam/users")
  @Permissions("team-members:view")
  getIAMUsers(@AuthUser() user: any, @Query() query: IAMUsersDto) {
    return this.usersService.AIMUsers(user["https://urecruits.com/companyId"], query);
  }

  @ApiOperation({ summary: 'Get lead users' })
  @ApiResponse({ status: 200, type: [User] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-lead-users')
  @Permissions('recruiter')
  getLeadUsers(@AuthUser() user: any, @Query() query: any) {
    return this.usersService.getLeadUsers(user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"], query);
  }

  @ApiOperation({ summary: 'Get WorkFlow Reviewers' })
  @ApiResponse({ status: 200, type: [User] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-workflow-reviewers')
  @Permissions('recruiter')
  getWorkFlowReviewers(@AuthUser() user: any) {
    return this.usersService.getWorkFlowReviewers(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get Roles' })
  @ApiResponse({ status: 200, type: [Role] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/roles')
  @Permissions('recruiter')
  getCompanyRoles(@AuthUser() user: any) {
    return this.rolesService.getAvailableCompanyRoles(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Delete company member' })
  @ApiResponse({ status: 200, type: 'String' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete('/delete-member')
  @Permissions('team-members:delete')
  deleteMember(@Body() dto: DeleteUserDto, @AuthUser() user: any) {
    return this.usersService.deleteMember(dto.userId, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Change company member roles' })
  @ApiResponse({ status: 200, type: User })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Put('/member-roles')
  @Permissions('team-members:edit')
  changeCompanyMemberRoles(@Body() dto: ChangeRolesDto, @AuthUser() user: any) {
    return this.usersService.changeCompanyMemberRoles(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "Get members" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/members")
  @Permissions("team-members:view")
  getMembers(@AuthUser() user: any, @Query() query: GetMembersDto) {
    return this.recruitersService.getMembers(query, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({ summary: "Get new member's count of a week" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/members/week-counts")
  @Permissions("team-members:view")
  getNewMembersCountOfWeek(@AuthUser() user: any) {
    return this.recruitersService.getNewMembersCountOfWeek(user["https://urecruits.com/companyId"]);
  }


  @ApiOperation({ summary: "Get members by a companyId" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/all/members")
  @Permissions("candidate")
  getCompanyMembers(@AuthUser() user: any, @Query() query: GetMembersDto) {
    return this.recruitersService.getMembersByCompanyIds(query);
  }

  @ApiOperation({ summary: "Get company recruiter profile" })
  @ApiResponse({ status: 200, type: Recruiter })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/member/:id")
  @Permissions("team-members:view")
  getMemberProfile(@Param("id") id: number, @AuthUser() user: any) {
    return this.recruitersService.getRecruiterById(id, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "Get companies for job" })
  @ApiResponse({ status: 200 })
  @Get("/for-job")
  getCompaniesJob(@Query() query: GetCompaniesForJobDto) {
    return this.companiesService.getCompaniesForJobs(query);
  }

  @ApiOperation({ summary: "Resend Activate Email" })
  @ApiResponse({ status: 201, type: "any" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/send-verification")
  @Permissions("team-members:add")
  resendActivateEmail(@Body() dto: SendMailDto, @AuthUser() user: any) {
    return this.recruitersService.resendActivateEmail(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get all company jobs' })
  @ApiResponse({ status: 200, type: Jobs })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get("/jobs")
  @Permissions('job-post:view')
  getAllJobs(@Query() query: FilterCompanyJobsDto, @AuthUser() user: any) {
    return this.jobService.getAllCompanyJobs(query, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "Get job's count of a week" })
  @ApiResponse({ status: 200, type: Jobs })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get("/jobs/week-counts")
  @Permissions('job-post:view')
  getJobsCountOfWeek(@Query() query: FilterCompanyJobsDto, @AuthUser() user: any) {
    return this.jobService.getCompanyJobsWeekCount(query, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get all workflow of company jobs' })
  @ApiResponse({ status: 200, type: Jobs })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get("/jobs/workflow")
  @Permissions('job-post:view')
  getAllCompanyJobsWithWorkflow(@Query() query: FilterCompanyJobsDto, @AuthUser() user: any) {
    return this.jobService.getAllCompanyJobsWithWorkflow(query, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "Get all companies (company list table)" })
  @ApiResponse({ status: 200 })
  @Get("/list-table")
  getCompaniesListTable(@Query() query: GetPublicCompaniesDto) {
    return this.companiesService.getPublicCompanies(query);
  }

  @ApiOperation({ summary: "get company by tenantId" })
  @ApiResponse({ status: 200, type: Company })
  @Get("/public/:tenantId")
  getPublicCompanyByTenantId(@Param("tenantId") tenantId: string) {
    return this.companiesService.getPublicCompanyByTenantId(tenantId);
  }

  @ApiOperation({ summary: 'Send Email on behalf of company' })
  @ApiResponse({ status: 200, type: 'Array' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiBearerAuth('access-token')
  @Post('/send-email')
  @Permissions('recruiter')
  sendEmail(@Body() dto: SendCompanyEmailDto, @AuthUser() user: any) {
    return this.companiesService.sendEmail(dto, user['https://urecruits.com/companyId']);
  }

  @ApiOperation({ summary: 'Get Statistic Home-Page' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/home-page')
  @Permissions('recruiter')
  getStatsHomePage(@AuthUser() user: any) {
    return this.companiesService.getStatistic(user['https://urecruits.com/companyId']);
  }

  @ApiOperation({ summary: 'Add permissions to company role by roleId' })
  @ApiResponse({ status: 200, type: 'Array' })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/role/add-permissions')
  @Permissions('OR', 'recruiter', 'company-owner', 'company-admin')
  addPermissionsToRole(@Body() dto: EditPermissionsDto, @AuthUser() user: any) {
    return this.companiesService.addPermissionsToCompanyRole(dto);
  }

  @ApiOperation({ summary: 'Remove permissions from company role by roleId' })
  @ApiResponse({ status: 200, type: 'Array' })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete('/role/remove-permissions')
  @Permissions('OR', 'recruiter', 'company-owner', 'company-admin')
  removePermissionsToRole(@Body() dto: EditPermissionsDto, @AuthUser() user: any) {
    return this.companiesService.removePermissionsToCompanyRole(dto);
  }


  @ApiOperation({ summary: 'create a role for company using tenantId' })
  @ApiResponse({ status: 200, type: 'Array' })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/role/create')
  @Permissions('recruiter')
  createCompanyRole(@Body() dto: CreateCompanyRoleDto, @AuthUser() user: any) {
    if (!dto.roleName) {
      throw new BadRequestException("Name of the default role is not provided.")
    }
    return this.companiesService.createCompanyRole(dto.roleName, user["https://urecruits.com/tenantId"], user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'get all roles by company tenant id from auth0' })
  @ApiResponse({ status: 200, type: 'Array' })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/auth0/roles')
  @Permissions('recruiter')
  gellAllRoles(@AuthUser() user: any) {
    return this.companiesService.getAllCompanyRoles(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'get all roles by company tenant id from auth0' })
  @ApiResponse({ status: 200, type: 'Array' })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/default/roles')
  @Permissions('recruiter')
  getAllDefaultRoles(@AuthUser() user: any) {
    return this.rolesService.getDefaultCompanyRoles();
  }

  // @ApiOperation({ summary: "Seed company data into Qdrant" })
  // @ApiResponse({ status: 200 })
  // @Get("/qdrant/seed")
  // async seedCompanyDataToQdrant() {
  //   const client = new QdrantClient({ url: process.env.QDRANT_DATABASE_URL, apiKey: process.env.QDRANT_API_KEY });

  //   // Fetch companies in batches
  //   const batchSize = 5; 
  //   let offset = 0;
  //   let hasMoreCompanies = true;

  //   while (hasMoreCompanies) {
  //     const companies = await this.companiesService.getAllCompanies({ limit: batchSize, offset });
  //     if (!companies.rows?.length) {
  //       hasMoreCompanies = false;
  //       break;
  //     }

  //     const points = [];
  //     for (let company of companies.rows) {
  //       const data = await this.companiesService.getCompanyDataForQdrant(company);
  //       if(!data){
  //         continue;
  //       }
  //       try {
  //         const companyVector = await this.langchainService.getEmbeddings(data.str);
  //         console.log({ companyVector });
  //         if(companyVector){
  //           points.push({
  //             id:data.id,
  //             vector:companyVector,
  //             payload:data.payload
  //           })
  //         }
  //       } catch (error) {
  //         console.error("Error fetching embeddings:", error);
  //       }

  //       // Add a delay to avoid rate limits
  //       await new Promise(resolve => setTimeout(resolve, 2000));
  //     }

  //     console.log({ points });
  //     try {
  //       const response = await client.upsert("company", { wait: true, points });
  //       console.log("Upsert Response:", response);
  //     } catch (error) {
  //       console.error("Upsert failed:", error);
  //     }

  //     offset += batchSize;
  //   }
  // }
}
