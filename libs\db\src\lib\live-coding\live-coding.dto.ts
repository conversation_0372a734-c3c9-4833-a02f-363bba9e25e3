import { <PERSON>NotE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsString } from "class-validator";

export class liveCodingDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsNotEmpty()
  starterCode: string;

  @IsString()
  @IsNotEmpty()
  instruction: string;

  @IsOptional()
  packageId: number;

  @IsNumber()
  @IsNotEmpty()
  languageId: number;

  @IsNumber()
  @IsOptional()
  databaseId: number;

  @IsString()
  @IsOptional()
  taskId?: string;

  status:string;
}
