
//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   candidateId: user["https://urecruits.com/candidateId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { EditOfferDto, OfferModel } from "@microservices/recruitment-db";
import { Body, Controller, Get, Param, Patch, Query, Res, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { CandidateOffersService } from "libs/recruitment-db/src/lib/candidateoffers/candidate-offers.service";
import { FilterCandidateOffersDTO } from '@microservices/recruitment-db'
@ApiTags("Candidate Offers")
@ApiBearerAuth("access-token")
@Controller("candidate-offers")
export class CandidateOffersController {
  constructor(private readonly candidateOffersService: CandidateOffersService) { }

  @ApiOperation({ summary: "Get offers for candidate" })
  @ApiResponse({ status: 200, type: [OfferModel] })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("")
  @Permissions("candidate")
  getCandidateOffers(
    @Query() query: FilterCandidateOffersDTO,
    @AuthUser() user: any
  ) {
    const userId = user["https://urecruits.com/userId"]
    return this.candidateOffersService.getCandidateOffers(userId, query)
  }

  @ApiOperation({ summary: "Get candidate offer by id" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/:offerId")
  @Permissions("candidate")
  getOfferById(
    @AuthUser() user: any,
    @Param('offerId') offerId: number
  ) {
    const userId = user["https://urecruits.com/userId"]
    return this.candidateOffersService.getOfferById(userId, offerId)
  }

  @ApiOperation({ summary: "Edit candidate offer" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/:companyId/:offerId")
  @Permissions("candidate")
  updateOfferById(
    @Body() dto: EditOfferDto,
    @Param('companyId') companyId: number,
    @Param('offerId') offerId: number,
    @AuthUser() user: any,
  ) {
    return this.candidateOffersService.updateOfferById(offerId, dto, companyId)
  }

  @ApiOperation({ summary: "Get Signature Request Document By SignatureRequestID for candidate" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/hellosign/signature-document/:offerId")
  @Permissions("candidate")
  async getCandidateOfferPreview(
    @Param('offerId') offerId: number,
    @Res() res: any,
    @AuthUser() user: any
  ) {
    const fileBuffer = await this.candidateOffersService.getSignatureDocumentPreview(
      offerId,
      user["https://urecruits.com/userId"]
    );
    res.set({
      "Content-Type": "application/pdf",
      "Content-Disposition": 'inline; filename="file.pdf"',
    });
    res.send(fileBuffer);
  }
}