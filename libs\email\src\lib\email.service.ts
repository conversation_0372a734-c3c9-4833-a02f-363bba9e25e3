import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import * as SendGrid from "@sendgrid/mail";
import MailComposer = require("nodemailer/lib/mail-composer");
import { EmailResolverService } from "./email.resolver.service";
// import * as nodemailer from 'nodemailer'
import axios from "axios"

const companyLogo = LogoUrl => {
  return `<table border="0" cellpadding="0" cellspacing="0"
            class="module" data-role="module-button"
            data-type="button" role="module"
            style="table-layout:fixed;" width="100%"
            data-muid="1eed8581-b8b8-44b8-a1dc-35995691a242.1">
            <tbody>
                <tr>
                    <td align="center" bgcolor="#FFFFFF;"
                        class="outer-td mobile-img"
                        style="padding:0px 0px 0px 0px; background-color:transparent; padding-bottom: 52px; padding-top: 80px">
                        <table border="0" cellpadding="0"
                            cellspacing="0" class="wrapper-mobile"
                            style="text-align:center;">
                            <tbody>
                               <img src="${LogoUrl||""}" alt="Company logo" border="0" style="width: 250px;height: 250px;">
                                <tr>
                                    <td align="center"
                                        bgcolor="#f8fafc"
                                        class="inner-td"
                                        style="border-radius:6px; font-size:14px; text-align:center; background-color:inherit;">
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>`;
}

const position = positionValue => {
  return `<table class="module" role="module" data-type="text"
            border="0" cellpadding="0" cellspacing="0" width="100%"
            style="table-layout: fixed;"
            data-muid="d75e535b-cf57-4dcb-9bef-4c85ab2878c4"
            data-mc-module-version="2019-10-22">
            <tbody>
                <tr>
                    <td style="padding: 24px 0 12px 0; line-height: 36px; text-align:inherit;"
                        class="mobile-title" height="100%"
                        valign="top" bgcolor=""
                        role="module-content">
                        <div>
                            <div
                                style="font-family: inherit; text-align: inherit">
                                <span
                                    style="font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; box-sizing: border-box; color: #2A2C33; font-size: 24px; font-weight: bold; ">
                                    ${positionValue||""}
                                </span>
                            </div>
                            <div></div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>`
}
@Injectable()
export class EmailService {
  constructor(private readonly emailResolver: EmailResolverService) {
    SendGrid.setApiKey(process.env.SENDGRID_TOKEN);
  }
  private sender = process.env.EMAIL_SENDER;

  async send(template: string, data: object, email: string): Promise<void> {
    try {
      const html = this.emailResolver.upload(template);
      const parsedHtml = this.fillTemplate(html, data);
      const mail = {
        to: email,
        subject: 'Welcome to Urecruits!',
        from: this.sender,
        html: parsedHtml,
      };
      await SendGrid.send(mail);
    } catch (e) {
      Logger.error('Something went wrong with emails');
      Logger.error(e);
    }
  }

  async sendEmailToRecruiters(msg: string) {
    return { message: msg };
  }
  async sendEmailToCandidate(msg: string) {
    return { message: msg };
  }

  async recruitmentEmail() {
    Logger.debug("Email send successfully");
  }

  private fillTemplate(html: string, fillData: object): string {
    if (typeof fillData !== 'object' || !fillData) return html;
    const replacedPlaceholders: Set<string> = new Set();

    for (const [key, value] of Object.entries(fillData)) {
      if (replacedPlaceholders.has(key)) {
        continue; // Skip already replaced keys
      }

      const placeholder = new RegExp(`<%${key}%>`, 'g');

      if (key === 'companyLogo' && value) {
        const logoReplacement = companyLogo(value);
        html = html.replace(placeholder, logoReplacement);
      } else if (key === 'position' && value) {
        html = html.replace(placeholder, position(value));
      } else {
        html = html.replace(placeholder, value ? String(value) : '');
      }
      replacedPlaceholders.add(key);
    }

    return html;
  }

  async sendRecruitmentActivityService(template: string, data: any, email: string, subject: string = "Welcome to uRecruits!", jobData: null | any = null) {
    try {
      const html = this.emailResolver.recruitment(template);
      const parsedHtml = this.fillTemplate(html, data);
      let mail;
      if (data?.attachments) {
        mail = {
          to: email,
          subject: subject,
          // from: this.sender",
          from: "<EMAIL>",
          html: parsedHtml,
          attachments: data?.attachments ? data?.attachments : null
        };
      } else {
        mail = {
          to: email,
          subject: subject,
          // from: this.sender",
          from: "<EMAIL>",
          html: parsedHtml,
        };
      }

        await SendGrid.send(mail);
      if (data?.userId) {
        const obj = {
          to: email,
          subject: subject,
          body: JSON.stringify(parsedHtml),
          userId: data.userId,
          jobData: jobData || null
        }
        const url = `https://recruitment-micro.urecruits.com/api/gmail/send-email`;

          await axios.post(url, obj);
      }
      return { data: "mail sent successfully", status: 201 }
    } catch (e) {
      Logger.error('Something went wrong with emails');
      Logger.error(e);
    }
  }

  async sendRecruitment(template: string, data: any, email: string, subject: string = "Welcome to uRecruits!", jobData: null | any = null) {
    try {
      const html = this.emailResolver.recruitment(template);
      const parsedHtml = this.fillTemplate(html, data);
      let mail;
      if (data?.attachments) {
        mail = {
          to: email,
          subject: subject,
          // from: this.sender",
          from: "<EMAIL>",
          html: parsedHtml,
          attachments: data?.attachments ? data?.attachments : null
        };
      } else {
        mail = {
          to: email,
          subject: subject,
          // from: this.sender",
          from: "<EMAIL>",
          html: parsedHtml,
        };
      }

      await SendGrid.send(mail);
      if (data?.userId) {
        const obj = {
          to: email,
          subject: subject,
          body: JSON.stringify(parsedHtml),
          userId: data.userId,
          jobData: jobData || null
        }
        const url = `https://recruitment-micro.urecruits.com/api/gmail/send-email`;
        await axios.post(url, obj);
      }
      return { data: "mail sent successfully", status: 201 }
    } catch (e) {
      Logger.error('Something went wrong with emails');
      Logger.error(e);
    }
  }

  async sendGmail(client, template, companyLogo, to, subject, position, body, attachmentsStr) {
    let html;
    try {
      html = this.emailResolver.upload(`${template}.html`);
      html = this.fillTemplate(html, {
        companyLogo,
        position,
        body,
      });
    } catch (e) {
      Logger.error(e);
      throw new HttpException('Can\'t load email template', HttpStatus.BAD_REQUEST);
    }

    const attachments = [];
    if (attachmentsStr) {
      const attachmentsArr = attachmentsStr.split(',');
      for (let i = 0; i < attachmentsArr.length; i++) {
        const url = attachmentsArr[i].trim();
        const filename = url.replace(/^.*[\\\/]/, '');
        attachments.push(
          {
            filename,
            url,
          }
        );
      }
    }

    const success = [];
    const fail = [];
    let errCount = 0;
    const toArr = to.split(',');
    for (let i = 0; i < toArr.length; i++) {
      const to = toArr[i].trim();
      try {
        const mail = new MailComposer({
          to,
          subject,
          html,
          attachments,
        });
        const message = await mail.compile().build();
        const encodedMessage = Buffer.from(message)
          .toString('base64')
          .replace(/\+/g, '-')
          .replace(/\//g, '_')
          .replace(/=+$/, '');

        await client.users.messages.send({
          userId: 'me',
          requestBody: {
            raw: encodedMessage,
          },
        });
        success.push(to);
      } catch (e) {
        fail.push(to);
        errCount++;
        Logger.error(e.response?.data?.error?.message || e);
      }
    }

    if (errCount) {
      if (errCount === toArr.length) {
        throw new HttpException('Can\'t send any email', HttpStatus.BAD_REQUEST);
      }
      Logger.error('Can\'t send some emails');
    }

    return {
      success,
      fail,
    };
  }

  async sendOutlook(client, template, companyLogo, to, subject, position, body, attachmentsStr) {
    let html;
    try {
      html = this.emailResolver.upload(`${template}.html`);
      html = this.fillTemplate(html, {
        companyLogo,
        position,
        body,
      });
    } catch (e) {
      Logger.error(e);
      throw new HttpException('Can\'t load email template', HttpStatus.BAD_REQUEST);
    }

    const attachments = [];
    if (attachmentsStr) {
      const attachmentsArr = attachmentsStr.split(',');
      for (let i = 0; i < attachmentsArr.length; i++) {
        const url = attachmentsArr[i].trim();
        const name = url.replace(/^.*[\\\/]/, '');
        const response: any = await fetch(url);
        const contentBytes = (await response.buffer()).toString('base64');
        attachments.push(
          {
            '@odata.type': '#microsoft.graph.fileAttachment',
            name,
            contentBytes,
          }
        );
      }
    }

    const mail = {
      message: {
        subject,
        body: {
          contentType: 'html',
          content: html,
        },
        toRecipients: [],
        attachments,
      },
      saveToSentItems: 'true',
    };

    const success = [];
    const fail = [];
    let errCount = 0;
    const toArr = to.split(',');
    for (let i = 0; i < toArr.length; i++) {
      const to = toArr[i].trim();
      try {
        mail.message.toRecipients = [
          {
            emailAddress: {
              address: to,
            },
          },
        ];
        await client
          .api('/me/sendMail')
          .post(mail);
        success.push(to);
      } catch (e) {
        fail.push(to);
        errCount++;
        Logger.error(e.response?.data?.error?.message || e);
      }
    }

    if (errCount) {
      if (errCount === toArr.length) {
        throw new HttpException('Can\'t send any email', HttpStatus.BAD_REQUEST);
      }
      Logger.error('Can\'t send some emails');
    }

    return {
      success,
      fail,
    };
  }
}
