import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { PlanPackages } from "./plan-packages.model";
import { Op } from "sequelize";
import * as planPackagesData from "./planpackagesdata.json";

@Injectable()
export class PlanPackageService {
  public constructor (
    @InjectModel(PlanPackages) private planPackageRepository: typeof PlanPackages,
  ) {}

  async seed() {
    await this.planPackageRepository
      .bulkCreate(planPackagesData, {
        validate: true,
      })
      .then(async () =>
        this.planPackageRepository.sequelize.query(
          `ALTER SEQUENCE "${
            this.planPackageRepository.tableName
          }_id_seq" RESTART WITH ${
            (await this.planPackageRepository.count()) + 1
          }`
        )
      );
    return true;
  }

  async createPlanPackage(dto){
    try{
      return await this.planPackageRepository.create(dto)
    }catch(error){
      throw new Error("Error while creating plan package"+error)
    }
  }

  async getPlanPackageDetails(dto){
    let where:any={}
    if(dto.name){
      where.name = {
        [Op.eq] : dto.name
      }
    }
    if(dto.id){
      where.id=dto.id
    }
    if(dto.companyId){
      where.companyId=dto.companyId
    }
    return await this.planPackageRepository.findOne({where})
  }

  async getDefaultAndCompanyPlans(dto){
    let where:any={}
    if(dto.name){
      where.name = {
        [Op.eq] : dto.name
      }
    }
    if(dto.id){
      where.id=dto.id
    }
    if(dto.companyId){
      where.companyId={
        [Op.or]: [null, dto.companyId]
      }
    }else{
      where.companyId=null
    }
    return await this.planPackageRepository.findAll({where})
  }

  async updatePlanPackage(dto){
    try{
      const plan = await this.getPlanPackageDetails({id: dto.id})
      if (plan) {
        Object.assign(plan, { ...dto })
        await plan.save()
      } else {
        await this.planPackageRepository.create(dto);
      }
    }catch(error){
      throw new Error("Error while updating plan package"+error)
    }
  }
  async deletePlanPackage(dto){
    try{
      if(!dto.productId){
        throw new BadRequestException("Product ID is not provided")
      }
      return await this.planPackageRepository.destroy({where:{id:dto.id}})
    }catch(error){
      throw new Error("Error while deleting Plan Package"+error)
    }

  }
}
