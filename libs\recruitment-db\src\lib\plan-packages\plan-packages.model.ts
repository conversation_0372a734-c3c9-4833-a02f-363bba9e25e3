import {
  Column,
  DataType,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../companies/companies.model';

interface PlanPackagesAttr {
  name:string;
  companyId: number;
  isCustom: boolean;
}

@Table({
  tableName: 'plan-packages',
  timestamps:false,
  createdAt:false,
  updatedAt:false
 })
export class PlanPackages extends Model<PlanPackages, PlanPackagesAttr> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'Full Cycle', description: 'name of the main subscription plan' })
  @Column({ type: DataType.STRING, unique: true, allowNull: false })
  name: string;

  @ApiProperty({ example: '1', description: 'companyId' })
  @ForeignKey(()=>Company)
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;

  @ApiProperty({ example: 'true', description: 'is custom package' })
  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue : false })
  isCustom: boolean;

  @BelongsTo(() => Company)
  company: Company;
}
