{"root": "libs/recruitment-db", "sourceRoot": "libs/recruitment-db/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/recruitment-db/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/recruitment-db"], "options": {"jestConfig": "libs/recruitment-db/jest.config.js", "passWithNoTests": true}}}, "tags": []}