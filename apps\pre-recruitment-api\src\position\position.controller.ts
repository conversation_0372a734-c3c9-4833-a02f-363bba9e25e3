import { Controller, Get, Param, Post, Query, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  Position, PositionsService,
} from "@microservices/recruitment-db";
import { AuthGuard } from "@nestjs/passport";
import { PermissionsGuard } from "@microservices/auth";
// import { QdrantClient } from "@qdrant/js-client-rest";
// import { LangchainService } from "@microservices/integrations";

// const langchainService = new LangchainService();

@ApiTags("Position")
@Controller("position")
export class PositionController {
  constructor (private readonly positionsService: PositionsService) {}
 
  @ApiOperation({ summary: "Get positions" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get("/filter")
  async getAllWithLimit(@Query() find: any) {        
    return await this.positionsService.findByLabelWithLimit(find);
  }
  
  @ApiOperation({ summary: "Get positions" })
  @ApiResponse({ status: 200, type: [Position] })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/:label?')
  getAll (@Param('label') label?: string,) {
    return this.positionsService.findByLabel(label);
  }

  // @ApiOperation({ summary: "Feed Positions to Qdrant" })
  // @ApiResponse({ status: 200 })
  // @Post('/feed-positions')
  // async feedPositionsToQdrant() {
  //     const client = new QdrantClient({ 
  //       url: process.env.QDRANT_DATABASE_URL, 
  //       apiKey: process.env.QDRANT_API_KEY 
  //     });
    
  //     // Process positions in batches of 100
  //     const batchSize = 50;
  //     let offset = 0;
  //     let hasMorePositions = true;
  //     console.log("Starting batch",offset);
  //     while (hasMorePositions) {
  //       // Get positions batch from your API/database
  //       const positions = await this.positionsService.allPositions({ limit: batchSize, offset });
        
  //       if (!positions?.length) {
  //         hasMorePositions = false;
  //         break;
  //       }
    
  //       const points = [];
  //       for (let position of positions) {
  //         // Format position data for embedding
  //         const positionText = `PositionName: ${position.label}`;
  //         console.log({positionText});
          
  //         try {
  //           const vector = await langchainService.getEmbeddings(positionText);
  //           points.push({
  //             id: position.id,
  //             vector: vector,
  //             payload: {
  //               id: position.id,
  //               label: position.label,
  //               value: position.value,
  //             }
  //           });
  //         } catch (error) {
  //           console.error("Error generating embeddings:", error);
  //         }
    
  //         // Rate limiting
  //         await new Promise(resolve => setTimeout(resolve, 200));
  //       }
    
  //       // Batch upsert to Qdrant
  //       try {
  //         await client.upsert("positions", { 
  //           wait: true, 
  //           points 
  //         });
  //         console.log("Positions upserted successfully");
  //       } catch (error) {
  //         console.error("Failed to upsert positions:", error);
  //       }
  //       console.log("Finished batch",offset);
    
  //       offset += batchSize;
  //     }
  //   }
}
