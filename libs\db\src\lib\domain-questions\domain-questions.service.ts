import { Injectable, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { DomainQuestions } from "./domain-questions.model";
import { AssessmentCategory, DomainAssessment } from "./domain-assessment.model";
import { CreateDomainAssessmentDto, DomainFilterDto } from "./dto/domain-filter.dto";
import { Op } from "sequelize";
import { getDummyDomainData } from "../utils/fillDummyAssessmentData";
import { putRecordOnQdrant } from "@microservices/qdrant";

@Injectable()
export class DomainQuestionsService {
  constructor(
    @InjectModel(DomainAssessment)
    private domainAssessment: typeof DomainAssessment,
    @InjectModel(DomainQuestions)
    private domainQuestion: typeof DomainQuestions,
  ) { }

  async findAllDomainAssessment() {
    return this.domainAssessment.findAll({ include: [DomainQuestions] });
  }

  findAllAssessment(companyId: number, query = null) {
    let where: any = { companyId };
    if (query?.excludedId) {
      where.id = {
        [Op.ne]: query.excludedId
      }
    }

    return this.domainAssessment.findAll({ include: [DomainQuestions], where });
  }

  findAllWithFilter(dto, companyId: number) {
    const order = [];
    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }

    const where = {
      companyId,
    };

    if (dto.score !== undefined) {
      where['passing'] = {
        [Op.gt]: dto.score,
      };
    }

    if (dto.industryId) {
      if (dto.industryId instanceof Array) {
        where['industryId'] = {
          [Op.in]: dto.industryId,
        };
      } else {
        where['industryId'] = {
          [Op.eq]: dto.industryId,
        };
      }
    }

    if (dto.departmentId) {
      if (dto.departmentId instanceof Array) {
        where['departmentId'] = {
          [Op.in]: dto.departmentId,
        };
      } else {
        where['departmentId'] = {
          [Op.eq]: dto.departmentId,
        };
      }
    }

    if (dto.time) {
      if (dto.time instanceof Array) {
        where['duration'] = {
          [Op.in]: dto.time,
        };
      } else {
        where['duration'] = {
          [Op.eq]: dto.time,
        };
      }
    }

    if (dto.search) {
      where['name'] = {
        [Op.like]: `%${dto.search}%`,
      };
    }

    return this.domainAssessment.findAndCountAll({
      include: [DomainQuestions],
      distinct: true,
      limit: dto.limit,
      offset: dto.offset,
      order,
      where,
    });
  }

  async createDummyAssessment(companyId: number) {
    const data = await getDummyDomainData(companyId);
    if (data.length) {
      for (let i = 0; i < data.length; i++) {
        try {
          const domain = await this.create(data[i], 1);
          putRecordOnQdrant([domain.recordData]);
        } catch (err) {
          console.log("Error creating dummy domain assessment", err);
          continue;
        }
      }
    }
  }



  async create(dto: CreateDomainAssessmentDto, userId: number) {
    // Clean up questions to remove any frontend-generated IDs
    const cleanedQuestions = dto.questions?.map(q => {
      const { id, ...rest } = q;
      return rest;
    }) ?? [];

    const domainAssessment = await this.domainAssessment.create(
      {
        ...dto,
        questions: cleanedQuestions,
        category: AssessmentCategory.domainAssessment
      },
      { include: [DomainQuestions] },
    );

    const data = this.getDomainAssessmentDataForQdrant(domainAssessment);
    const openSearchData = this.addDataOnOpenSearch(domainAssessment, 'create');

    return { response: domainAssessment, recordData: openSearchData };
  }

  async update(id: number, dto: DomainAssessment, companyId: number, userId: number) {
    const data = await this.domainAssessment.findOne({ where: { id, companyId }, include: [DomainQuestions] });
    if (data) {
      if (dto.questions?.length) {
        for (let i = 0; i < dto.questions.length; i++) {
          const isExist = dto.questions[i].id && typeof dto.questions[i].id !== 'string' && await this.domainQuestion.findOne({ where: { id: dto.questions[i].id } });
          if (isExist) {
            await isExist.update({ ...dto.questions[i] });
          } else {
            delete dto.questions[i].id;
            await this.domainQuestion.create({ ...dto.questions[i], domainAssessmentId: id });
          }
        }
      }
      Object.assign(data, { ...dto, companyId: data.companyId, id: data.id });
      const domain = await data.save();
      const recordData = this.getDomainAssessmentDataForQdrant(domain);
      return { response: domain, recordData };
    } else {
      return null;
    }
  }

  async updateInstruction(id: number, dto: any, companyId) {
    try {
      const data = await this.domainAssessment.findOne({ where: { id, companyId } });
      Object.assign(data, { ...dto });
      return await data.save();
    } catch (err) {
      Logger.log("Error : ", err)

    }
  }

  async remove(id: number, companyId: number, userId: number) {
    try {
      const domain = await this.domainAssessment.findOne({ where: { id, companyId }, include: [DomainQuestions] });
      if (domain) {
        const recordData = this.getDomainAssessmentDataForQdrant(domain, "delete");
        await domain.destroy();
        return { response: domain, recordData };
      } else {
        return null;
      }
    } catch (err) {
      Logger.log(`Error Removing domain assessment with Id ${id} : `, err)
    }

  }

  findOneById(id: number, companyId: number): Promise<DomainAssessment> {
    return this.domainAssessment.findOne({ where: { id, companyId }, include: [{ model: DomainQuestions, order: [["id", "ASC"]] }] });
  }

  findById(id: number): Promise<DomainAssessment> {
    return this.domainAssessment.findOne({ where: { id }, include: [{ model: DomainQuestions, order: [["id", "ASC"]] }] });
  }

  async getAssessmentDuration(id: number) {
    const data = await this.domainAssessment.findOne({ where: { id }, attributes: ["duration"] });
    return data;
  }

  getDomainAssessmentRecordData(domainAssessment: DomainAssessment, userId, status, action) {
    return {
      for: 'assessment',
      // action:'',
      data: {
        domainId: domainAssessment.id,
        assessmentType: 'domain',
        name: domainAssessment.name,
        companyId: domainAssessment.companyId,
        createdBy: userId,
        status,
        action,
        domain: domainAssessment,
        createdAt: new Date(),
      }
    }
  }

  addDataOnOpenSearch(assessment: DomainAssessment, action: string) {
    return {
      for: 'assessment',
      data: {
        domainAssessmentId: assessment.id,
        assessmentType: 'domain',
        name: assessment.name,
        companyId: assessment.companyId,
        industryId: assessment.industryId,
        departmentId: assessment.departmentId,
        status: 'ACTIVE',
        action: action.toLowerCase(),
        domainAssessment: assessment,
        createdAt: new Date()
      }
    };
  }

  getDomainAssessmentDataForQdrant(assessment: DomainAssessment, action = 'create') {
    const str = action === 'create' ? `
        DomainAssessment: ${assessment.name}
        Domain Id: ${assessment.id}
        Duration: ${assessment.duration}
        Passing Score: ${assessment.passing}
        Company Id: ${assessment.companyId}
        Category: ${assessment.category}
        Questions:
        ${assessment.questions.map(q =>
      `- ${q.name} (Type: ${q.type}, Score: ${q.score})
              Answers: ${!!q.answers ? (q.type === 'text' ? q.answers : JSON.parse(q.answers)?.map(a => `${a.name} (Correct: ${a.isCorrect}, Score: ${a.score})`).join(", ")) : 'No answers'}`
    ).join("\n")}` : '';

    return {
      for: 'domain-assessments',
      id: assessment.id,
      action,
      ...(action === 'create' && {
        str,
        payload: {
          id: assessment.id,
          name: assessment.name,
          duration: assessment.duration,
          passing: assessment.passing,
          companyId: assessment.companyId,
          industryId: assessment.industryId,
          departmentId: assessment.departmentId,
          additionalDepartment: assessment.additionalDepartment,
          questions: assessment.questions,
          category: assessment.category,
          createdAt: assessment.createdAt,
          updatedAt: assessment.updatedAt,
        }
      }),
    }
  }
}
