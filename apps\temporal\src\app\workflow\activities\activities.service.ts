import { Injectable, Logger } from "@nestjs/common";
import { EmailService } from "@microservices/email";
import { HttpService } from "@nestjs/axios";
import { TestManagerDto } from "../dto/create-test-manager.dto";
import { roundStatus, roundType } from "../enum/round.enum";

@Injectable()
export class ActivitiesService {
  constructor(
    private emailService: EmailService,
    private readonly httpService: HttpService
  ) { }
  private recruitmentUrl = `${process.env.WEB_APP_URI}`;

  async workflowStart(data): Promise<any> {
    const { user, jobData, workflowData } = data;
    if (jobData?.isAssessment) {
      return
    }

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${jobData?.title} Job Application`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname}</p>
        <p>We would like to update you that the hiring process for ${user?.firstname} ${user?.lastname} has now begun for the ${jobData?.title} at ${workflowData?.company?.name}.</p>
        <p>The candidate, ${user?.firstname} ${user?.lastname}, has been moved forward in the recruitment workflow.</p>
        <p>As the recruiting process moves forward, we will carefully review ${user?.firstname} ${user?.lastname} application, including their resume and any accompanying documents. If their profile aligns with the requirements and objectives of the role, we will proceed with the next steps, such as scheduling an interview or requesting further information.</p>
        <p>We appreciate your support in facilitating a smooth recruitment process and We look forward to further exploring ${user?.firstname} ${user?.lastname}.
        `,
        userId: workflowData?.authorId,
      },
      workflowData?.author?.email,
      "Job Application Update – Hiring Process Initiated",
      { notificationTitle: "Hiring Process Initiated", notificationMsg: `The hiring process for ${user?.firstname} ${user?.lastname} has now begun for the ${jobData?.title} at ${workflowData?.company?.name}.` }
    );
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${jobData?.title} Job Application`,
        body: `
            <p>Hello ${user?.firstname} ${user?.lastname},</p>
            <p>Thank you for applying for the ${jobData?.title} position at ${workflowData?.company?.name}. We appreciate your interest in joining our team.</p>
            <p>As part of our hiring process, this role includes multiple rounds of interviews. We will be reaching out to you shortly with details regarding the first round.</p>
          `,
        userId: user?.id,
      },
      user?.email,
      "Job Application Update: Hiring Process Initiated",
      { ...jobData, for: "candidate", notificationTitle: `Application status for ${jobData?.title} Job`, notificationMsg: `Thank you for applying for the ${jobData?.title} position at ${workflowData?.company?.name}. We will contact you soon for the interview process.` }
    );
    return { message: "workflow started well!!!" };
  }
  // Technical-Coding interview / HR / Project-manager/ Senior HR
  async roundInitiated(data, currentRound): Promise<any> {
    const { user, jobData, workflowData } = data;
    if (jobData?.isAssessment) {
      return;
    }
    let redirectUrl;
    let linkUrl;
    let buttonText;
    let emailSubject;
    switch (currentRound) {
      case roundType.CONDITIONAL_OFFFER_LETTER_GENERATION: {
        redirectUrl = `${this.recruitmentUrl}/recruitment/offers/${jobData.id}`;
        linkUrl = `/recruitment/offers/${jobData.id}`
        buttonText = "Go to Offer Letter";
        emailSubject = `Initiate Offer Letter Round for ${user?.firstname} ${user?.lastname} `
        break;
      }

      case roundType.BACKGROUND_SCREENING: {
        redirectUrl = `${this.recruitmentUrl}/recruitment/background-screening/candidates/${jobData.id}`;
        linkUrl = `/recruitment/background-screening/candidates/${jobData.id}`;
        emailSubject = `Initiate Background Screening Round for ${user?.firstname} ${user?.lastname}`
        buttonText = "Go to Background Screening";
        break;
      }

      case roundType.DRUG_SCREENING: {
        redirectUrl = `${this.recruitmentUrl}/recruitment/drug-screening/candidates/${jobData.id}`;
        linkUrl = `/recruitment/drug-screening/candidates/${jobData.id}`;
        emailSubject = `Initiate Drug Screening Round for ${user?.firstname} ${user?.lastname} `
        buttonText = "Go to Drug Screening";
        break;
      }
    }

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Action Required: Initiate ${currentRound} Round`,
        body: `
          <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname}</p>
          <p>We are pleased to inform you that the current round for ${user?.firstname} ${user?.lastname} is an ${currentRound}.</p>
          <p> To proceed, we kindly request you to go to urecruits platform by clicking below button.</p>
          <br/>
         <a href="${redirectUrl}"
        style="
          text-decoration: none;
          color: inherit;
          display: block;
          width: 100%;
          height: 100%;
        ">
        <button style="
        font-size: 14px;
        font-weight: 900;
        line-height: 100%;
        color: white;
        padding: 16px 28px;
        border: none;
        cursor: pointer;
        background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
        border-radius: 4px;
        ">${buttonText}</button>
        </a>
          `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update:${emailSubject}`,
      { link: linkUrl, 
        notificationTitle: `${currentRound} has started`, 
        notificationMsg: `${emailSubject} for ${jobData?.title} ${jobData?.isAssessment ? " assignment" : " job"}.` }
    );

    currentRound != roundType.CONDITIONAL_OFFFER_LETTER_GENERATION && await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${jobData?.title}`,
        body: `
          <h2>Hello ${user?.firstname} ${user?.lastname},</h2>
          <p>Thanks again for applying for an ${jobData.title} job.</p>
          <p>As we said earlier, this process contains several rounds.</p>
          <p>Current round is ${currentRound}.</p>
          <p>You will be notified soon when recruiter will take an action</p>
          `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: ${currentRound} started`,
      {
        ...jobData, for: "candidate",
        notificationTitle: `${currentRound} has started`,
        notificationMsg: `You will be notified soon when recruiter will take an action`
      }
    );

    return { message: `Mails sent successfully` };
  }

  async proposeTimeSlot(data, roundType): Promise<any> {
    const { user, jobData, workflowData } = data;
    const recruitmentUrl = `${process.env.WEB_APP_URI}`;
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: "Action Required: Scheduling Your Interview Time Slot",
        body: `<p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname}</p>
        <p>We are pleased to notify you that the candidate- ${user?.firstname} ${user?.lastname} for the ${jobData.title} position has progressed to the ${roundType} stage. Kindly proceed with booking an appropriate time slot for the interview.</p>
        <p>Please click the button below to choose a convenient time:</p>
        <br/>
         <a href="${recruitmentUrl}/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}"
         style="
           text-decoration: none;
           color: inherit;
           display: block;
           width: 100%;
           height: 100%;
         ">
         <button style="
         font-size: 14px;
         font-weight: 900;
         line-height: 100%;
         color: white;
         padding: 16px 28px;
         border: none;
         cursor: pointer;
         background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
         border-radius: 4px;
         ">Choose</button>
         </a>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: Schedule ${roundType} for ${user?.firstname} ${user?.lastname}`,
      {
        link: `/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}`,
        "notificationTitle": `Schedule ${roundType}`,
        "notificationMsg": `Please schedule the ${roundType} with ${user?.firstname} ${user?.lastname} for the ${jobData?.title} position.`
      }
    );

    return { message: "Message sent to recruiter successfully" };
  }

  async informTimeSlot(data, roundType, appointmentData): Promise<any> {
    const { user, jobData, workflowData } = data;
    const eventId = appointmentData?.eventId || "";
    const recruitmentUrl = `${process.env.WEB_APP_URI}`;
    let url = (appointmentData?.description==="Live Task/Coding Assessment" || appointmentData?.description==="") ?  `/live-coding/online/${appointmentData?.roomId}` : `/live-coding/interview/${appointmentData?.roomId}`
    // mail to candidate
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${roundType} : Meeting Scheduled for ${jobData?.isAssessment ? `Assignment-${jobData.title}` : `Job Position-${jobData.title}`} in ${workflowData?.company?.name}`,
        body: `
        <p>Dear ${user?.firstname} ${user?.lastname},</p>
        <p>We are pleased to inform you that your interview with ${workflowData?.company?.name} has been successfully scheduled. Kindly find the details below:</p>
        <p>
            <strong>Date:</strong>  ${appointmentData?.date}<br>
            <strong>Time:</strong> ${appointmentData?.slot} ${appointmentData.timeZone ? `(${appointmentData.timeZone})` : ''}<br>
        </p>
        <p>You can join the meeting via the <strong>Calendar</strong> section of your dashboard or directly through the <a href='${recruitmentUrl+url}' style="color: inherit;">Link</a>.</p>
        <p>If the proposed time does not suit you, we understand that you may have other commitments. In that case, kindly inform us at your earliest convenience, and we will be happy to reschedule the interview to a time that works better for you.</p>
        <p>To reschedule, please choose time slot from calender by clicking "Choose" button shown below, and we will do our best to accommodate your preferences.</p>
        <p>Thank you for your attention, and we look forward to meeting you.</p>
        <br/>

        <a href="${recruitmentUrl}/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}&reschedule=true&eventId=${eventId}"
        style="
          text-decoration: none;
          color: inherit;
          display: block;
          width: 100%;
          height: 100%;
        ">
        <button style="
        font-size: 14px;
        font-weight: 900;
        line-height: 100%;
        color: white;
        padding: 16px 28px;
        border: none;
        cursor: pointer;
        background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
        border-radius: 4px;
        ">Reschedule</button>
        </a>
    
        <p>Best regards,</p>
        `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: interview for ${roundType} is ${data?.rescheduled ? "rescheduled" : "scheduled"}.`,
      {
        ...jobData, for: "candidate",
        link: `/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}&reschedule=true&eventId=${eventId}`,
        notificationTitle: `Interview Scheduled for ${roundType}`,
        notificationMsg: `Your interview for the job position of ${jobData?.title} is scheduled with ${workflowData?.company?.name} . Reschedule if needed.`
      }
    );
    // mail to interviewers
    await appointmentData.interviewers.forEach(async (interviewer) => {
      await this.emailService.sendRecruitmentActivityService(
        "jobDetail.html",
        {
          companyLogo: `${workflowData?.company?.avatar || ""}`,
          position: `${roundType} : Meeting Scheduled`,
          body: `
        <p>Dear ${interviewer?.name},</p>
        <p>We hope this message finds you well.</p>
        <p>We have scheduled an interview with <strong>${user?.firstname} ${user?.lastname}</strong> for ${jobData?.isAssessment ? "an Assignment-" : "job position of "} <strong>${jobData.title}</strong> for the round ${roundType}. Kindly find the details below: </p>
        <p><strong>Date:</strong> ${appointmentData?.date}</p>
        <p><strong>Time:</strong> ${appointmentData?.slot} ${appointmentData.timeZone ? `(${appointmentData.timeZone})` : ''}</p>
        <p>You can join the meeting via the <strong>Calendar</strong> section of your dashboard or directly through the <a href='${recruitmentUrl+url}' style="color: #099C73;" >Link</a>.</p>
        <p>Kindly confirm your attendance for the interview. If the scheduled time is not convenient for you, please let us know, and we will be happy to reschedule.</p>
        <p>You can reschedule the interview by clicking the button below:</p>
        <br/>

        <a href="${recruitmentUrl}/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}&reschedule=true&eventId=${eventId}"
        style="
          text-decoration: none;
          color: inherit;
          display: block;
          width: 100%;
          height: 100%;
        ">
        <button style="
        font-size: 14px;
        font-weight: 900;
        line-height: 100%;
        color: white;
        padding: 16px 28px;
        border: none;
        cursor: pointer;
        background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
        border-radius: 4px;
        ">Reschedule</button>
        </a>
        <br/>

        <p>Thank you for your cooperation.</p>
        `,
          userId: interviewer?.id,
        },
        interviewer?.email,
        `${jobData.title} Update: interview for ${roundType} is ${eventId ? 'Re' : ''}scheduled`,
        {
          link: `/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}&reschedule=true&eventId=${eventId}`,
          notificationTitle: `Interview Scheduled for ${roundType}`,
          notificationMsg: `You have an interview scheduled with ${user?.firstname} ${user?.lastname} for ${jobData?.isAssessment ? "an Assignment of" : "the job position of "} ${jobData?.title}.Reschedule if needed.`
        }
      );
    });
    // mail to recruiter
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Interview Scheduled for ${roundType} – ${jobData.title}`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname},</p>
        <p>We are pleased to inform you that a meeting has been scheduled for an interview round with the candidate as follows:</p>
        <p>
            <strong>Date:</strong> ${appointmentData?.date}<br>
            <strong>Time:</strong> ${appointmentData?.slot} ${appointmentData.timeZone ? `(${appointmentData.timeZone})` : ''}<br>
        </p>
        <p>You can join the meeting via the <strong>Calendar</strong> widget of your dashboard or directly through the <a href='${recruitmentUrl+url}' style="color: #099C73;" >Link</a>.</p>
        <p>To ensure a seamless interview process, please coordinate accordingly. If the scheduled time is not convenient, you may reschedule at your earliest convenience by clicking the button below:</p>
        <br/>
        <a href="${recruitmentUrl}/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}&reschedule=true&eventId=${eventId}"
        style="
          text-decoration: none;
          color: inherit;
          display: block;
          width: 100%;
          height: 100%;
        "><button style="
        font-size: 14px;
        font-weight: 900;
        line-height: 100%;
        color: white;
        padding: 16px 28px;
        border: none;
        cursor: pointer;
        background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
        border-radius: 4px;
        ">Reschedule</button>
        </a>
        <br/>

    
        <p>Thank you for your attention, and we look forward to a successful interview round.</p>
    
        <p>Best regards,</p>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: interview for ${roundType} is scheduled`,
      {
        link: `/candidate/meeting-appointment?jobId=${jobData?.id}&candidateId=${user.id}&roundType=${roundType}&reschedule=true&eventId=${eventId}`,
        notificationTitle: `Interview Scheduled for ${roundType}`,
        notificationMsg: `You have an interview scheduled with ${user?.firstname} ${user?.lastname} for ${jobData?.isAssessment ? "the assignment-" : "the job position of "} ${jobData?.title}.Reschedule if needed.`
      }
    );

    return { message: "Message sent to participants successfully" };
  }

  async scoreUpdateReminder(data, roundType, appointmentData): Promise<any> {
    const { user, jobData, workflowData } = data;
    const eventId = appointmentData?.eventId || "";

    const recruitmentUrl = `${process.env.WEB_APP_URI}`;

    // mail to interviewers
    await appointmentData.interviewers.forEach(async (interviewer) => {
      await this.emailService.sendRecruitmentActivityService(
        "jobDetail.html",
        {
          companyLogo: `${workflowData?.company?.avatar || ""}`,
          position: `Action Required:Provide Score for Candidate – ${roundType}`,
          body: `
          <p>Dear ${interviewer?.name},</p>
          <p>We hope this message finds you well.</p>
          <p>We appreciate your time and effort in conducting the interview with ${user?.firstname} ${user?.lastname} for ${jobData?.isAssessment ? "an assignment -" : "the position of"} ${jobData.title} in the ${roundType} round.</p>
          <p>To proceed with the next steps in the hiring process, we kindly request you to provide a score for the candidate. Your evaluation is crucial in determining the candidate’s progression to the next stage.</p>
            <a href="${recruitmentUrl}/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}" style="
              text-decoration: none;
              color: inherit;
              display: block;
              width: 100%;
              height: 100%;
              "><button style="
              font-size: 14px;
              font-weight: 900;
              line-height: 100%;
              color: white;
              padding: 16px 28px;
              border: none;
              cursor: pointer;
              background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
              border-radius: 4px;
            ">Give Score </button></a>
          <br/>
          <p>Thank you for your time and contribution to the hiring process</p>`,
          userId: interviewer?.id,
        },
        interviewer?.email,
        `${jobData.title} Update: Give Score to candidate for ${roundType}`,
        {
          link: `/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}`,
          notificationTitle: `Give score to candidate for ${roundType}`,
          notificationMsg: `${user?.firstname} ${user?.lastname} has completed the ${roundType} round for ${jobData?.title} ${jobData?.isAssessment ? 'assignment' : 'position'}. Please provide a score to proceed to the next stage.`
        }
      );
    });

    // mail to recruiter
    !appointmentData.interviewers.find(interviewer => interviewer?.id === workflowData?.authorId) && await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${roundType} : Meeting Scheduled for ${jobData?.isAssessment ? "Assignment-" : "Job Position-"}${jobData.title} of ${user?.firstname} ${user?.lastname}`,
        body: `
        
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname},</p>
        <p>We hope this message finds you well.</p>
        <p>We hope you successfully conducted the interview of <strong>${user?.firstname} ${user?.lastname}</strong> for ${jobData?.isAssessment ? "the assignment of " : "the position of"} <strong>${jobData.title}</strong> in the ${roundType} round.</p>
        <p>Kindly provide a score for the candidate and proceed to the next round.</p>
        
          <a href="${recruitmentUrl}/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}" style="
            text-decoration: none;
            color: inherit;
            display: block;
            width: 100%;
            height: 100%;
          "><button style="
          font-size: 14px;
          font-weight: 900;
          line-height: 100%;
          color: white;
          padding: 16px 28px;
          border: none;
          cursor: pointer;
          background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
          border-radius: 4px;
        ">Give Score</button></a>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: Give Score to candidate for ${roundType}`,
      {
        link: `/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}`,
        notificationTitle: `Give Score to candidate for ${roundType}`,
        notificationMsg: `${user?.firstname} ${user?.lastname} has completed the ${roundType} for ${jobData?.title} ${jobData?.isAssessment ? 'assignment' : 'position'}. Please provide a score to proceed to the next stage.`
      }
    );

    return {
      message: "Score Update Reminder Message sent to Recuiters successfully",
    };
  }

  async appointmentBooked(data, appointmentData): Promise<any> {
    const { user, jobData, workflowData } = data;
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Your meeting is scheduled with ${workflowData?.company?.name}`,
        body: `
        <p>Dear ${user?.firstname} ${user?.lastname},</p>
        <p>We are excited to inform you that your interview for ${jobData?.isAssessment ? "the assignment-" : "the position of"} ${jobData.title} has been scheduled.</p>
        <p>Date: ${appointmentData?.date}</p>
        <p>Time: ${appointmentData?.slot}</p>
        <br/><p>You can join the meeting via the <strong>Calendar</strong> section of your dashboard.</p>
        <button style="
          font-size: 14px;
          font-weight: 900;
          line-height: 100%;
          color: white;
          padding: 16px 28px;
          border: none;
          cursor: pointer;
          background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
          border-radius: 4px;
        ">
          <a href="https://urecruits.com/" style="
              text-decoration: none;
              color: inherit;
              display: block;
              width: 100%;
              height: 100%;
          ">
              Click here to Login
          </a>
        </button>
        <br/>
        <p>Please ensure that you come prepared for the meeting and bring any relevant documents or materials you would like to discuss. If there is any additional information you require prior to the meeting, please feel free to let us know, and we will do our best to provide it.</p>
        <p>Thank you for considering us as a potential employer, and we look forward to meeting you on the specified date and time.</p>
      `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: Appointment booked...`,
      {
        ...jobData, for: "candidate",
        notificationTitle: `Meeting Scheduled`,
        notificationMsg: `${workflowData?.company?.name} has scheduled a meeting with you for ${jobData?.isAssessment ? "the assignment – " : ""}${jobData.title}.`
      }
    );

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Meeting Scheduled`,
        body: `
      <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname}</p>
      <p>we are writing to confirm the scheduled meeting initiated by the ${user?.firstname} ${user?.lastname}, regarding the ${jobData?.isAssessment ? "assignment-" : ""} ${jobData.title}  at ${workflowData?.company?.name}</p>
      <p>Date: ${appointmentData?.date}</p>
      <p>Time: ${appointmentData?.slot}  ${appointmentData.timeZone ? `(${appointmentData.timeZone})` : ''}</p>
      <br/><p>You can join the meeting via the <strong>Calendar</strong> section of your dashboard.</p>
      <button style="
      font-size: 14px;
      font-weight: 900;
      line-height: 100%;
      color: white;
      padding: 16px 28px;
      border: none;
      cursor: pointer;
      background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
      border-radius: 4px;
     ">
        <a href="https://urecruits.com/" style="
            text-decoration: none;
            color: inherit;
            display: block;
            width: 100%;
            height: 100%;
        ">
            Login
        </a>
      </button>
      <br/>
      <p>We kindly request you to be prepared for the meeting and review the candidate's resume and any other relevant documents provided. If there is any additional information or specific topics you would like the candidate to address during the meeting, please feel free to communicate them to us, and we will relay the message accordingly.</p>
      <p>Thank you for your attention to this matter, and we appreciate your cooperation. We are eager to connect and further evaluate the candidate's potential as a valuable addition to our team.</p>
      `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: Confirmation of the scheduled appointment for ${user?.firstname} ${user?.lastname}`,
      {
        notificationTitle: `Meeting Scheuled Successfully...`,
        notificationMsg: `You have scheduled a meeting with ${user?.firstname} ${user?.lastname} for ${jobData?.isAssessment ? "the assignment – " : "job of "} ${jobData.title}.`
      }
    );
    return { message: "Appointment booked successfully" };
  }

  async interviewReminder(appointmentData, data, roundType): Promise<any> {
    const { user, jobData, workflowData } = data;
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Reminder: ${roundType} in 10 Minutes`,
        body: `
          <p>Dear ${user?.firstname} ${user?.lastname},</p>
          <p>This is a friendly reminder that your ${roundType} for ${jobData?.isAssessment ? "the assignment - " : "the position of "}${jobData.title} is scheduled to start in 10 minutes.</p>
          <p>The interview will be conducted via online meet on uRecruits platform, and your interviewers will be ${workflowData.author?.firstname}. The estimated duration of the interview is 1 Hour.</p>
          <p>Please make sure you are in a quiet and well-lit environment with a stable internet connection. We are looking forward to your participation and wish you the best of luck!</p>
          <p>Thank you for considering our company as your potential employer.</p>
          <p>Interview Time: ${appointmentData?.date}</p>
          <p>Interview Time: ${appointmentData?.slot}</p>
        `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Reminder: ${roundType} is starting in 10 minutes...`,
      {
        ...jobData, for: "candidate",
        notificationTitle: `${roundType} is starting in 10 minutes...`,
        notificationMsg: `Your ${roundType} for the ${jobData.title} ${jobData?.isAssessment ? "assignment" : "position"} is in 10 minutes.`
      }
    );

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Reminder: ${roundType} in 10 Minutes`,
        body: `
        <p>Dear ${workflowData.author?.firstname},</p>
        <p>Your ${roundType} for the ${jobData.title} ${jobData?.isAssessment ? "assignment" : "position"} is in 10 minutes.</p>
        <p>Please join the online meeting on uRecruits. Your candidate will be  ${user?.firstname} ${user?.lastname}. The interview will last approximately 1 hour.</p>
        <p>Ensure a quiet, well-lit space and a stable internet connection. Best of luck! Thank you for considering us.</p>
        <p>Interview Date: ${appointmentData?.date}</p>
        <p>Interview Time: ${appointmentData?.slot}</p>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Reminder: ${roundType} is starting in 10 minutes for ${user?.firstname} ${user?.lastname}`,
      {
        notificationTitle: `${roundType} starts in 10 minutes`,
        notificationMsg: `Your meeting with ${user?.firstname} ${user?.lastname} for the ${roundType} of ${jobData.title} ${jobData?.isAssessment ? "assignment" : "position"} starts in 10 minutes.`
      }
    );

    return { message: `${roundType} reminder has been sent` };
  }

  async interviewStarted(data, roundType): Promise<any> {
    const { user, jobData, workflowData } = data;
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${roundType} is started`,
        body: `
          <p>Dear ${user?.firstname} ${user?.lastname},</p>
          <p>We are excited to inform you that your ${roundType} for ${jobData?.isAssessment ? "the assignment -" : "the position of"} ${jobData.title} has officially begun.</p>
          <p>The interview will be conducted via online meet on urecruits platform, and your interviewers are ${workflowData.author?.firstname}.</p>
          <p>Please ensure that you are in a quiet and well-lit environment with a stable internet connection to ensure a smooth interview experience.</p>
          <p>We wish you the best of luck with your interview! Your performance during this session will play a crucial role in our assessment, and we are looking forward to getting to know you better.</p>
          <p>Thank you for considering our company as your potential employer.</p>
        `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: Interview for ${roundType} has started..`
    );

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo:`${workflowData?.company?.avatar || ""}`,
        position: `${roundType} is Started`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname}</p>
        <p>Interview has been started with ${user?.firstname} ${user?.lastname} </p>
         <p>for the ${jobData?.isAssessment ? "assignment" : "position"} of ${jobData?.title}</p>
         <p>Please join the interview, ignore if you are already entered into the interview.</p>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: ${roundType} has started for ${user?.firstname} ${user?.lastname}..`
    );
    return { message: `${roundType} has been started` };
  }

  async interviewCompleted(data, roundType): Promise<any> {
    const { user, jobData, workflowData } = data;

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${roundType} has been Completed`,
        body: `
        <p>Dear ${user?.firstname} ${user?.lastname},</p>
        <p>Thank you for participating in the ${roundType} for ${jobData.title}.</p>
        <p>Your impressive performance is noted. Our team will review and assess your fit for the role, and we'll get back to you soon with the results.</p>
        <p>If you have questions or want to share more, feel free to reach out. Thanks again for your time, and we'll be in touch soon with the next steps.</p>
      `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: ${roundType} has completed`,
      {
        ...jobData, for: "candidate",
        notificationTitle: `${roundType} is completed successfully`,
        notificationMsg: `Your ${roundType} for ${jobData.title} has completed. You will be notified soon with the results.`
      }
    );

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${roundType} Round Completed`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname},</p>
        <p>I hope this finds you well. An update on ${jobData.title}'s ${roundType}:</p>
        <p>${user?.firstname} ${user?.lastname} performed exceptionally, demonstrating strong technical skills </p>
        <p>You can see the playback of this coding interview in review and score tab in uRecruits portal.</p>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: ${roundType} has completed for ${user?.firstname} ${user?.lastname}...`,
      {
        notificationTitle: `${roundType} has completed for candidate`,
        notificationMsg: `${user?.firstname} ${user?.lastname} has completed the ${roundType} round for ${jobData.title}.`
      }
    );
    return { message: `${roundType} has been ended` };
  }

  async roundCompleted(data, currentRound): Promise<any> {
    const { user, jobData, workflowData } = data;

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Action Required: Initiate ${currentRound} Round`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname},</p>
        <p>We hope this message finds you well.</p>
        <p>We are pleased to inform you that the current round, ${currentRound}, for ${user?.firstname} ${user?.lastname} has been successfully completed.</p>
        <p>You will receive information about the next round in the near future, if there are any remaining rounds in your workflow.</p>
        <br/>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: ${currentRound} has completed for ${user?.firstname} ${user?.lastname}...`,
      {
        notificationTitle: `${currentRound} has completed for candidate`,
        notificationMsg: `${user?.firstname} ${user?.lastname} has successfully completed the ${currentRound} round for ${jobData.title}. You will receive details on the next steps soon.`

      }
    );

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${currentRound} passed successfully`,
        body: `
        <p>Dear ${user?.firstname} ${user?.lastname},</p>
        <p>We are writing to inform you that you have successfully completed the ${currentRound} Round for the ${jobData.title} ${jobData?.isAssessment ? "assignment" : "position"} at ${workflowData?.company?.name}.</p>
        <p>Congratulations on your performance during this crucial stage of our selection process. Your dedication and technical skills have stood out, and we are excited about the potential you could bring to our team.</p>
        <p>Next steps in our hiring process may include additional interviews or assessments, and we will keep you updated on your progress. If you have any questions or require further information, please do not hesitate to reach out to our HR team.</p>
        <p>Thank you for your interest in joining, and we look forward to potentially welcoming you to our team in the near future.</p>
      `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: You have passed ${currentRound} successfully.`,
      {
        ...jobData, for: "candidate",
        notificationTitle: `${currentRound} is Successfully Completed`,
        notificationMsg: `Congratulations! You have successfully completed the ${currentRound} round for ${jobData.title} at ${workflowData?.company?.name}. We will update you on the next steps soon.`
      }
    );

    return { message: `${currentRound} passed successfully` };
  }

  async roundRejected(data, roundType): Promise<any> {
    const { user, jobData, workflowData } = data;
    if (jobData?.isAssessment) {
      return;
    }
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar}`,
        position: `Failed in ${roundType}`,
        body: `
          <p>Dear ${user?.firstname} ${user?.lastname},</p>
          <p>We hope this message finds you well. We would like to thank you for your interest in the ${jobData.title} position at ${workflowData?.company?.name} and for participating in our ${roundType} round.</p>
          <p>After careful evaluation of your performance in the ${roundType}, we regret to inform you that we have decided not to move forward with your application for this role. While you demonstrated strong skills and qualifications, we have chosen to proceed with other candidates who we believe are better aligned with our current needs.</p>
          <p>We understand that this may be disappointing news, and we want to emphasize that our decision was not a reflection of your abilities. Your technical skills and experience are impressive, and we encourage you to continue pursuing opportunities in your field.</p>
          <p>We appreciate the effort and time you invested in the interview process and your interest in ${workflowData?.company?.name}. If you would like feedback on your interview or have any questions, please do not hesitate to reach out to us. We are happy to provide constructive feedback to support your future career endeavors.</p>
          <p>Once again, thank you for considering ${workflowData?.company?.name} as a potential employer, and we wish you the very best in your job search and your future endeavors.</p>
        `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: You are failed in ${roundType}`,
      {
        ...jobData, for: "candidate",
        notificationTitle: `${roundType} Not Cleared`,
        notificationMsg: `Thank you for participating in the ${roundType} round for ${jobData.title} at ${workflowData?.company?.name}. Unfortunately, we are unable to proceed with your application at this time.`
      }
    );

    return { message: `${roundType} Interview is failed` };
  }

  // Functional/Domain Assessment
  async assessmentInitiated(data, currentRound): Promise<any> {
    const { user, jobData, workflowData } = data;
    const redirect_uri = process.env.WEB_APP_URI;
    let redirectUrl;
    let linkUrl;
    let assessment_deadline;
    if (currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT) {
      assessment_deadline = workflowData?.domainDeadline;
      redirectUrl = `${redirect_uri}/candidate/domain-assessment/${jobData?.id}/${workflowData?.domainId}`;
      linkUrl = `/candidate/domain-assessment/${jobData?.id}/${workflowData?.domainId}`;
    } else {
      assessment_deadline = workflowData?.takeHomeTaskDeadline*24;
      redirectUrl = `${redirect_uri}/live-coding/offline/${jobData.id}/${workflowData.takeHomeTaskId}`;
      linkUrl = `/live-coding/offline/${jobData.id}/${workflowData.takeHomeTaskId}`;
    }

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar}`,
        position: `${jobData?.title}`,
        body: `
          <h2>Hello ${user?.firstname} ${user?.lastname},</h2>
          ${!jobData?.isAssessment ? `<p>Thanks again for applying for an ${jobData.title} job.As mentioned earlier, the selection process consists of multiple rounds.</p>
            `: ""}
          <p> This round is for the ${currentRound} for the ${jobData?.isAssessment ? " assignment of " : "job position of "}${jobData?.title}.</p>
          <p><strong>Deadline:</strong> You must complete this round within ${assessment_deadline} hours from receiving this email. Failing to do so will result in disqualification from further stages.</p>
          <p> Please click on the button given below to start the test.</p>
          <br/>
          <button style="
          font-size: 14px;
          font-weight: 900;
          line-height: 100%;
          color: white;
          padding: 16px 28px;
          border: none;
          cursor: pointer;
          background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
          border-radius: 4px;
         ">
            <a href="${redirectUrl}" style="
                text-decoration: none;
                color: inherit;
                display: block;
                width: 100%;
                height: 100%;
            ">
                Start Test
            </a>
          </button>
          <br/>
          Once you complete the ${currentRound} round, your results will be shared via email or can be accessed in your Scoreboard on the uRecruits system.</p>
          `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: ${currentRound} has started...`,
      {
        ...jobData, for: "candidate", link: linkUrl,
        notificationTitle: `${currentRound} has begun for ${jobData.title}`,
        notificationMsg: `The ${currentRound} for ${jobData.title} has started. Please ensure you complete it before the deadline to proceed further. `
      }
    );

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar}`,
        position: `${currentRound} Round Initiated`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname},</p>
        <p>I hope this email finds you well. The <strong>${currentRound}</strong> for job position - <strong>${jobData?.isAssessment ? "Assignment - " : ""}${jobData.title}</strong> has been initiated for <strong>${user?.firstname} ${user?.lastname}</strong> and is currently in progress.</p>
        <p>You will be notified once the candidate completes this round. In the meantime, you can review previous rounds and stay updated on their progress.</p>`,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: ${currentRound} has started for ${user?.firstname} ${user?.lastname}.`,
      {
        link: `/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}`,
        notificationTitle: `${currentRound} has started`,
        notificationMsg: `The ${currentRound} of ${jobData.title} has started for ${user?.firstname} ${user?.lastname}. You can review their progress in the Scoreboard.`
      }
    );
    const currentDate = new Date();
    const deadline = assessment_deadline * 24 * 60 * 60 * 1000;
    const newDate = new Date(currentDate.getTime() + deadline);
    let assessmentDuration;
    try {
      if (currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT) {
        const url = `${process.env.ASSESSMENT_API_URI}/api/domain-questions/duration/${workflowData?.domainId}`;
        const response = await this.fetchHandler(url);
        assessmentDuration = timeToMinutes(response?.data?.duration);
      } else {
        assessmentDuration = workflowData.takeHomeTaskTime;
      }

      const obj: TestManagerDto = {
        candidateId: user?.id,
        jobId: jobData?.id,
        assessmentId:
          currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT
            ? workflowData?.domainId
            : workflowData?.takeHomeTaskId,
        deadline: newDate,
        timeDuration: assessmentDuration,
        assessmentType:
          currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT
            ? AssessmentCategory?.domainAssessment
            : AssessmentCategory?.codingAssessment,
        companyId: jobData.companyId,
      };

      const url = `https://wtt-dev.urecruits.com/api/test-manager`;
      await this.postHandler(url, obj);
    } catch (error) {
      Logger.log(error);
    }

    return { message: `${currentRound} Round Initiated` };
  }

  async assessmentSubmitted(data, currentRound, domainResultId): Promise<any> {
    const { user, jobData, workflowData } = data;
    const recruitmentUrl = `${process.env.WEB_APP_URI}`;
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${currentRound} has been Completed`,
        body: `
        <p>Dear ${user?.firstname} ${user?.lastname},</p>
        <p>Thank you for participating in the ${currentRound} for ${jobData?.isAssessment ? "the assignment of " : " the job position of "}${jobData.title}.</p>
        <p>We appreciate the effort and dedication you demonstrated during this stage. Our team will carefully review your performance to assess your suitability for the role, and we will update you with the results soon.</p>
        <p>If you have any questions or would like to share additional information, please feel free to reach out. We sincerely appreciate your time and interest, and we will be in touch shortly regarding the next steps.</p>
        `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: ${currentRound} has been submitted successfully...`,
      {
        ...jobData, for: "candidate",
        notificationTitle: `${currentRound} has been submitted`,
        notificationMsg: `You have successfully completed the ${currentRound} for ${jobData.title}. We will update you with the results soon.`
      }
    );

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${currentRound} Round Completed`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname},</p>
        <p>I hope you are doing well. I wanted to provide you with an update on the ${currentRound} of ${jobData?.isAssessment ? "the assignment for " : ""}${jobData.title}.</p>
        <p><strong>${user?.firstname} ${user?.lastname}</strong> has demonstrated exceptional performance, showcasing strong technical skills.</p>
        
        <p>You can review the assessment and provide a score and feedback by clicking the button below.</p>
          <br/>
          <a href="${recruitmentUrl}${currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT ? `/recruitment/answer-sheet/${domainResultId}/${user?.firstname}%20${user?.lastname}` : `/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}`}" style="
          text-decoration: none;
          color: inherit;
          display: block;
          width: 100%;
          height: 100%;
        "><button style="
        font-size: 14px;
        font-weight: 900;
        line-height: 100%;
        color: white;
        padding: 16px 28px;
        border: none;
        cursor: pointer;
        background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
        border-radius: 4px;
      ">Review & Score</button></a>
        `,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: ${user?.firstname} ${user?.lastname}, has submitted for ${currentRound}.`,
      {
        link: currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT ? `/recruitment/answer-sheet/${domainResultId}/${user?.firstname}%20${user?.lastname}` : `/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}`,
        notificationTitle: `Assessment submitted for ${currentRound}`,
        notificationMsg: `${user?.firstname} ${user?.lastname} has successfully completed the ${currentRound} for ${jobData.title}. You can now review the assessment and provide a score and feedback.`,
      }
    );
    return { message: `${currentRound} has been submitted` };
  }

  async ReviewAssessmentReminder(
    data,
    currentRound,
    domainResultId
  ): Promise<any> {
    const { user, jobData, workflowData } = data;
    const recruitmentUrl = `${process.env.WEB_APP_URI}`;

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Reminder to review ${currentRound} Round`,
        body: `
        <p>Dear ${workflowData?.author?.firstname} ${workflowData?.author?.lastname},</p>
        <p>We hope you are doing well.</p>
        <p>This is a gentle reminder that the review and scoring of ${user?.firstname} ${user?.lastname}'s ${currentRound} is still pending. Your prompt attention to this matter will help ensure a seamless and efficient hiring process.</p>
        <p>Kindly take a moment to review the assessment and provide your score and feedback. Your evaluation is essential for us to proceed with the next steps in the recruitment process for ${user?.firstname} ${user?.lastname}.</p>
        <p>To complete the review, please click the button below:</p>
        <br/>
        <a href="${recruitmentUrl}${currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT ? `/recruitment/answer-sheet/${domainResultId}/${user?.firstname}%20${user?.lastname}` : `/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}`}" style="
        text-decoration: none;
        color: inherit;
        display: block;
        width: 100%;
        height: 100%;
        ">
          <button style="
            font-size: 14px;
            font-weight: 900;
            line-height: 100%;
            color: white;
            padding: 16px 28px;
            border: none;
            cursor: pointer;
            background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
            border-radius: 4px;"
          >
              Review & Score
          </button>
        </a>`,
        userId: workflowData?.authorId,
      },
      workflowData.author?.email,
      `${jobData.title} Update: Review & give score for ${currentRound}, for ${user?.firstname} ${user?.lastname}.`,
      {
        link: currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT ? `/recruitment/answer-sheet/${domainResultId}/${user?.firstname}%20${user?.lastname}` : `/recruitment/job/${jobData?.id}/candidate-scoreboard/${user.id}`,
        notificationTitle: ` Review & scoring pending for ${user?.firstname} ${user?.lastname}`,
        notificationMsg: `A reminder to review and provide feedback on ${user?.firstname} ${user?.lastname}'s ${currentRound} assessment for ${jobData.title}.`,
      }
    );
    return { message: `Reminder to review ${currentRound} sent successfully` };
  }

  async domainAssessmentRejected(data): Promise<any> {
    const { user, jobData, workflowData } = data;
    if (jobData?.isAssessment) {
      return;
    }
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Failed in Functional/Domain Assessment`,
        body: `
          <p>Dear ${user?.firstname} ${user?.lastname},</p>
          <p>We hope you are doing well. We sincerely appreciate your interest in the ${jobData.title} position at ${workflowData?.company?.name} and thank you for participating in our Functional/Domain Assessment round.</p>
          <p>After a thorough evaluation of your performance in this assessment, we regret to inform you that we will not be moving forward with your application at this time. While you possess strong skills and qualifications, we have decided to proceed with candidates whose profiles align more closely with our current requirements.</p>
          <p>We understand that this news may be disappointing, but please know that this decision is not a reflection of your overall abilities. Your expertise and experience are commendable, and we encourage you to continue exploring opportunities that match your strengths and aspirations.</p>
          <p>We truly appreciate the time and effort you dedicated to this process, as well as your interest in ${workflowData?.company?.name}. If you would like to receive feedback on your assessment or have any questions, please feel free to reach out. We would be happy to provide insights that may support your future career endeavors.</p>
          <p>Once again, thank you for considering ${workflowData?.company?.name} as a potential employer. We wish you the very best in your job search and future professional journey.</p>`,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: Failed in Functional/Domain Assessment. `,
      {
        ...jobData, for: "candidate",
        notificationTitle: `Functional/Domain Assessment Results`,
        notificationMsg: `We appreciate your participation in the Functional/Domain Assessment for ${jobData.title}. Unfortunately, we are unable to move forward with your application at this time.`,
      }
    );

    return { message: "Functional/Domain Assessment Interview is failed" };
  }

  async domainAssessmentCompleted(data): Promise<any> {
    const { user, jobData, workflowData } = data;
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `Functional/Domain Assessment passed successfully`,
        body: `
        <p>Dear ${user?.firstname} ${user?.lastname},</p>
        <p>We are writing to inform you that you have successfully completed the Functional/Domain Assessment Round for the ${jobData.title} ${jobData?.isAssessment ? "assignment" : "position"} at ${workflowData?.company?.name}.</p>
        <p>Congratulations on your performance during this crucial stage of our selection process. Your dedication and technical skills have stood out, and we are excited about the potential you could bring to our team.</p>
        <p>Our technical assessment is designed to evaluate a candidate's coding and problem-solving abilities, and your performance demonstrated a strong match for the requirements of the role.</p>
        <p>Next steps in our hiring process may include additional interviews or assessments, and we will keep you updated on your progress. If you have any questions or require further information, please do not hesitate to reach out to our HR team.</p>
        <p>Thank you for your interest in joining, and we look forward to potentially welcoming you to our team in the near future.</p>
      `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: Successfully completed Functional/Domain Assessment. `,
      {
        ...jobData, for: "candidate",
        notificationTitle: `Functional/Domain Assessment Completed`,
        notificationMsg: `Congratulations! You have successfully completed the Functional/Domain Assessment Round for ${jobData.title} at ${workflowData?.company?.name}. We will update you on the next steps soon.`,
      }
    );

    return { message: "Functional/Domain Assessment passed successfully" };
  }

  async assessmentDeadline(data, currentRound): Promise<any> {
    const { user, jobData, workflowData } = data;

    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo:`${workflowData?.company?.avatar || ""}`,
        position: `Failed in Functional/Domain Assessment`,
        body: `<p>Dear ${user?.firstname} ${user?.lastname},</p>
        <p>We hope this message finds you well. We sincerely appreciate your participation in the ${currentRound} for the ${jobData.title} ${jobData?.isAssessment ? "assignment" : "position"} at ${workflowData?.company?.name}.</p>
        <p>Unfortunately, the deadline for the assessment has now passed, and we are unable to accept further submissions. Our evaluation process is currently underway.</p>
        <p>We recognize and appreciate the effort and dedication you put into this assessment. While we regret that we cannot proceed further with your application at this stage, we strongly encourage you to explore other opportunities that align with your skills and experience.</p>
        <p>If you have any questions or would like feedback regarding your assessment, please feel free to reach out. We would be happy to provide insights to support your professional growth.</p>
        <p>Once again, we sincerely appreciate your interest in ${workflowData?.company?.name}. We wish you the very best in your job search and future career endeavors.</p>
        `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: Deadline for ${currentRound} has passed. `,
      {
        ...jobData, for: "candidate",
        notificationTitle: `Deadline for ${currentRound} has passed`,
        notificationMsg: `The deadline for the ${currentRound} for ${jobData.title} has passed. We are unable to accept further submissions.`
      }
    );

    return { message: `${currentRound} deadline is over` };
  }

  async changeOfferLetterStatus(data): Promise<any> {
    const { user, jobData, workflowData, subscribeId } = data;
    await this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {
        companyLogo: `${workflowData?.company?.avatar || ""}`,
        position: `${jobData?.title}`,
        body: `
          <h2>Hello ${user?.firstname} ${user?.lastname},</h2>
          <p>Thanks again for applying for an ${jobData.title} job.</p>
          <p>As we said earlier, this work has several rounds.</p>
          <p>You Are Cleared Your All Rounds, Thanks For Your Job Applying</p>
          <br/>
          `,
        userId: user?.id,
      },
      user?.email,
      `${jobData.title} Update: All rounds for this position is completed`,
      { ...jobData, for: "candidate" }
    );

    const roundObj = {
      jobId: jobData?.id,
      userId: user?.id,
      subscribeId,
      roundName: "Conditional Offer Letter Generation",
      status: "In Progress",
    };
    const roundUrl = `https://recruitment-micro.urecruits.com/api/round/changeStatus`;
    await this.patchHandler(roundUrl, roundObj);

    return { message: "Conditional Offer Letter Generation started" };
  }

  async changeRoundStatus(
    data,
    roundType,
    status = roundStatus.IN_PROGRESS
  ): Promise<any> {
    const { jobData, user, subscribeId } = data;
    const roundObj = {
      jobId: jobData?.id,
      userId: user?.id,
      subscribeId,
      roundName: roundType,
      status,
    };
    const roundUrl = `https://recruitment-micro.urecruits.com/api/round/changeStatus`;
    await this.patchHandler(roundUrl, roundObj);

    return { message: `${roundType} is started` };
  }

  async fetchHandler(url: string) {
    return await this.httpService.get(url).toPromise();
  }

  async postHandler(url: string, obj) {
    await this.httpService.post(url, obj).toPromise();
  }

  async patchHandler(url: string, obj) {
    await this.httpService.patch(url, obj).toPromise();
  }

}

enum AssessmentCategory {
  domainAssessment = "Domain Assessment",
  codingAssessment = "Coding Assessment",
  // thirdPartyAssessment = "Third Party Assessment",
}

enum TestStatus {
  NOT_STARTED = "Not Started",
  IN_PROGRESS = "In Progress",
  COMPLETED = "Completed",
}

function timeToMinutes(input) {
  const hourRegex = /(\d+)\s*Hr/;
  const minuteRegex = /(\d+)\s*min/;

  let totalMinutes = 0;

  const hourMatch = input.match(hourRegex);
  if (hourMatch) {
    const hours = parseInt(hourMatch[1], 10);
    totalMinutes += hours * 60;
  }

  const minuteMatch = input.match(minuteRegex);
  if (minuteMatch) {
    const minutes = parseInt(minuteMatch[1], 10);
    totalMinutes += minutes;
  }

  return totalMinutes;
}

function updateTestmanager() { }
