import { Column, DataType, Has<PERSON>any, Model, Table,ForeignKey } from "sequelize-typescript";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { EventUser } from "./event-user.model";


@Table({ tableName: "event" })
export class Event extends Model<Event> {
  @ApiProperty({ example: 1, description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "2023-01-12T10:30:00Z",
    description: "Date in GMT. When an event begins.",
  })
  @Column({ type: DataType.DATE, allowNull: false })
  startGmt: Date;

  @ApiPropertyOptional({
    example: "2023-01-12T11:30:00Z",
    description: "Date in GMT. When an event ends.",
  })
  @Column({ type: DataType.DATE, allowNull: true })
  endGmt: Date;

  @ApiProperty({ example: "Event title", description: "Title of event" })
  @Column({ type: DataType.STRING, allowNull: false })
  title: string;

  @ApiPropertyOptional({
    example: "Event description",
    description: "Description of event",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  description: string;

  @ApiProperty({ example: "true/false", description: "Reminder of the event" })
  @Column({ type: DataType.BOOLEAN, allowNull: false })
  remindMe: boolean;

  @ApiPropertyOptional({
    example: "10 minutes",
    description: "Remind me before time",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  timeBefore: string;

  @ApiPropertyOptional({ example: 11, description: "Job ID" })
  // @ForeignKey(() => Jobs)
  @Column({ type: DataType.INTEGER, allowNull: true })
  jobId: number;

  @ApiPropertyOptional({
    example: 16,
    description: "ID of the user who has booked appointment",
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  candidateId: number;

  @ApiPropertyOptional({ example: "Room Id", description: "Room Id" })
  @Column({ type: DataType.STRING, allowNull: true })
  roomId: string;

  @ApiPropertyOptional({
    example: [],
    description: "List of interviewers Details",
  })
  @Column({ type: DataType.JSONB, allowNull: true })
  interviewers: any;

  @Column({ type: DataType.STRING, allowNull: true })
  status ?: STATUS;

  @HasMany(() => EventUser, "eventId")
  eventUsers: EventUser[];
}

enum STATUS {
  "Not-initialized" = "Not Initialized",
  "Completed" = "Completed",
  "In-Progress" = "In Progress",
}
