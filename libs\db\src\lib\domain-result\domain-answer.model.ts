import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>Type, Foreign<PERSON>ey, Model, Table } from "sequelize-typescript";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsEnum, IsJSON, IsInt, IsOptional } from "class-validator";
import { DomainResults } from "./domain-result.model";

enum QuestionsType {
  single = "single",
  multiple = "multiple",
  text = "text",
}

@Table({
  tableName: "domain-answer",
})
export class DomainAnswers extends Model<DomainAnswers> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    enum: QuestionsType,
    oneOf: [{ type: QuestionsType.multiple }, { type: QuestionsType.single }, { type: QuestionsType.text }],
  })
  @IsEnum(QuestionsType)
  @Column({
    type: DataType.ENUM("single", "multiple", "text"),
    allowNull: false,
  })
  type: QuestionsType;

  @IsJSON()
  @ApiProperty({
    example: "",
    description: "Stringify JSON data of all answers with boolean isRight for one or multiple answers",
  })
  @Column({ type: DataType.TEXT, allowNull: false })
  answers: string;

  @ApiProperty({
    example: "1",
    description: "domain question id",
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  questionId: number;

  @IsInt()
  @ApiProperty({
    example: "10",
    description: "score",
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
  score: number;

  @ApiPropertyOptional({
  example: "Test",
  description: "Feedback",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  @IsOptional()
  feedback?: string;

  @ForeignKey(() => DomainResults)
  domainResultId: number;

  @BelongsTo(() => DomainResults)
  domainResults: DomainResults;
}
