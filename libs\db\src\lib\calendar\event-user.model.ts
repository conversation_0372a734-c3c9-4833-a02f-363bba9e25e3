import {
  BelongsTo,
  Column,
  DataType,
  Model,
  Table,
  ForeignKey,
} from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { Event } from "./event.model";

@Table({ tableName: "event-user" })
export class EventUser extends Model<EventUser> {
  @ApiProperty({ example: 1, description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 1, description: "Event ID" })
  @ForeignKey(() => Event)
  @Column({ type: DataType.INTEGER, allowNull: false })
  eventId: number;

  @ApiProperty({ example: 1, description: "User ID" })
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({ example: "mdupuig4vmf5bmj3j4fa7nelek", description: "Calender Event ID" })
  @Column({ type: DataType.STRING, allowNull: true })
  calendarEventId: string;

  @BelongsTo(() => Event, "eventId")
  event: Event;
}
