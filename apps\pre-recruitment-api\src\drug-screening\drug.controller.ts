import {
    Controller,
    Post,
    Body,
    UseGuards,
    Get,
    Query,
    Patch,
    Delete,
    Param,
    Res,
  } from "@nestjs/common";
  import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
  } from "@nestjs/swagger";
  import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
  import { AuthGuard } from "@nestjs/passport";
  import {
    DrugService,
    DrugModel,
    FilterJobsDrugDto,
    FilterCandidatesDrugDto,
    DrugDto,
    ChangeBackgroundDto
  } from "@microservices/recruitment-db";

  //   companyId: user["https://urecruits.com/companyId"],
  //   tenantId: user["https://urecruits.com/tenantId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   userId: user["https://urecruits.com/userId"],

  @ApiTags("Drug Screening")
  @ApiBearerAuth("access-token")
  @Controller("drug")
  export class DrugController {
    constructor(private readonly drugService: DrugService) {}

    @ApiOperation({ summary: "Get Jobs In DrugScreening" })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/jobs")
    @Permissions("drug:view",'job-post:view') 
    getAllJobsCompanyWise(
      @Query() query: FilterJobsDrugDto,
      @AuthUser() user: any
    ) {
      return this.drugService.getAllJobsCompanyWise(
        query,
        user["https://urecruits.com/companyId"]
      );
    }

    @ApiOperation({ summary: "Get drug order list of candidates by company for dashboard" })
    @ApiResponse({ status: 200, type: DrugModel })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/company")
    @Permissions("drug:view")
    getDrugByCompanyId( @AuthUser() user: any) {
      return this.drugService.getAllDrugOrderCompanyWise(user["https://urecruits.com/companyId"]);
    }


    @ApiOperation({ summary: "Get Candidates In DrugScreening" })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/candidates/:jobId")
    @Permissions("drug:view") 
    getAllCandidatesJobWise(
      @Param("jobId") jobId: number,
      @Query() query: FilterCandidatesDrugDto,
      @AuthUser() user: any
    ) {
      return this.drugService.getAllCandidatesJobWise(
        query,
        user["https://urecruits.com/companyId"],
        jobId
      );
    }


    @ApiOperation({ summary: "Edit Drug screening status By DrugId" })
    @ApiResponse({ status: 200 })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Patch("/report/change-status")
    @Permissions("drug:edit")
    updateStatus(
      @Body() dto:ChangeBackgroundDto,
      @AuthUser() user: any,
    ) {
      return this.drugService.changeDrugStatus(
        user["https://urecruits.com/companyId"],
        dto,
      );
    }

    @ApiOperation({ summary: "Get Drug screening report By UserID" })
    @ApiResponse({ status: 200 })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/report/:userId")
    @Permissions("drug:view")
    async getReportById(
      @AuthUser() user: any,
      @Res() res: any,
      @Param("userId") userId: number
    ) {
      const pdfBuffer = await this.drugService.getDrugReport({
        userId,
        companyId: user["https://urecruits.com/companyId"]
      });

      if (pdfBuffer && pdfBuffer.length > 0) {
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'inline; filename="report.pdf"');
        res.send(pdfBuffer);
      } else {
        console.log("Received empty PDF buffer");
        res.status(404).json({message:"No report available"});
      }
    }


    @ApiOperation({ summary: "Get Drug By DrugId" })
    @ApiResponse({ status: 200, type: DrugModel })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/:drugId")
    @Permissions("drug:edit")
    getById(
      @AuthUser() user: any,
      @Param("drugId") drugId: number
    ) {
      return this.drugService.getDrugOrderById(
        drugId,
        user["https://urecruits.com/companyId"]
      );
    }
  }
