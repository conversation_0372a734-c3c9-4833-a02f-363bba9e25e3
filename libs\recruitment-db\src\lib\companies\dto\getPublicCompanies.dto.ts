import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class GetPublicCompaniesDto {
  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiPropertyOptional({ example: "test", description: "Search" })
  @IsOptional()
  search?: string;

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;
}
