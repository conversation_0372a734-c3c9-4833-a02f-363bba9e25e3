import {
  <PERSON><PERSON>s<PERSON>o,
  Column,
  <PERSON>Type,
  ForeignKey,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Jobs } from "../job/job.model";
import { User } from "../users/users.model";
import { Company } from "../companies/companies.model";
import { Location, Round } from "@microservices/recruitment-db";
import { HellosignTemplate } from "./hellosign-template.model";
import { LetterStatus } from "./offer.enum";
@Table({ tableName: "offers", createdAt: true, updatedAt: true })
export class OfferModel extends Model<OfferModel> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "1", description: "Job ID" })
  @ForeignKey(() => Jobs)
  @Column({ type: DataType.INTEGER, allowNull: false })
  jobId: number;

  @ApiProperty({ example: "1", description: "Round ID" })
  @ForeignKey(() => Round)
  @Column({ type: DataType.INTEGER, allowNull: false })
  roundId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({ example: "1", description: "Company  ID" })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({ example: "1", description: "Approver ID" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: true })
  approvedBy: number;

  @ApiPropertyOptional({ example: false, description: "Later Status" })
  @Column({
    type: DataType.STRING,
    allowNull: false,
    defaultValue: LetterStatus.NO_GENERATED,
  })
  letterStatus: string;

  @ApiPropertyOptional({ example: false, description: "Candidate Status" })
  @Column({ type: DataType.STRING, allowNull: true, defaultValue: null })
  candidateStatus: string;

  @ApiPropertyOptional({
    example: "Matthew",
    description: "Candidate First Name",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  candidateFirstname: string;

  @ApiPropertyOptional({
    example: "Matthew",
    description: "Candidate Last Name",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  candidateLastname: string;

  @ApiPropertyOptional({ example: "Matthew", description: "Candidate Email" })
  @Column({ type: DataType.STRING, allowNull: true })
  candidateEmail: string;

  @ApiPropertyOptional({
    example: "****** 323 2323",
    description: "Candidate Phone number",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  candidatePhone: string;

  @ApiPropertyOptional({
    example: "100",
    description: "Candidate ctc",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  ctc: string;

  @ApiPropertyOptional({
    example: "La mento Road",
    description: "Candidate Address",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  candidateAddress: string;

  @ApiPropertyOptional({ example: "Mongo DB", description: "Job Title" })
  @Column({ type: DataType.STRING, allowNull: true })
  jobTitle: string;

  @ApiPropertyOptional({ example: "Uber", description: "Company Name" })
  @Column({ type: DataType.STRING, allowNull: true })
  companyName: string;

  @ApiPropertyOptional({ example: "1", description: "Location id" })
  @ForeignKey(() => Location)
  @Column({ type: DataType.INTEGER, allowNull: true })
  locationId: number;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Joining Date" })
  @Column({ type: DataType.DATE, allowNull: true })
  joiningDate: Date;

  @ApiPropertyOptional({ example: "1", description: "Hello Sign Template ID" })
  @ForeignKey(() => HellosignTemplate)
  @Column({ type: DataType.INTEGER, allowNull: true })
  hellosignTemplateId: number;

  @Column({ type: DataType.STRING, allowNull: true })
  hellosignSignatureRequestId: string;

  @Column({ type: DataType.STRING, allowNull: true })
  signingTitle: string;

  @Column({ type: DataType.STRING, allowNull: true })
  signingSubject: string;

  @Column({ type: DataType.STRING, allowNull: true })
  signingMessage: string;

  @BelongsTo(() => Location)
  location: Location;

  @BelongsTo(() => User, "userId")
  user: User;

  @BelongsTo(() => User, "approvedBy")
  approver: User;

  @BelongsTo(() => Jobs)
  job: Jobs;

  @BelongsTo(() => Round)
  round: Round;

  @BelongsTo(() => HellosignTemplate)
  hellosignTemplate: HellosignTemplate;

  @BelongsTo(() => Company)
  company: Company
}
