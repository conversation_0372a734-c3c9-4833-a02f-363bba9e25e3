import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Company } from './companies.model'
import { CompaniesService } from "./companies.service";
import { Auth0Module } from "@microservices/auth";
import { AddressesModule } from "../addresses/addresses.module";
import { UsersModule } from "../users/users.module";
import { RolesModule } from "../roles/roles.module";
import { RecruitersModule } from "../recruiters/recruiters.module";
import { IntegrationsModule } from '../integrations/integrations.module';
import { EmailModule } from "@microservices/email";
import { User } from "../users/users.model";
import { Recruiter } from "../recruiters/recruiters.model";
import { Jobs } from "../job/job.model";
import {FreshsalesModule} from "../freshsales/freshsales.module";
import { SubscriptionModule } from '../subscription/subscription.module';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [SequelizeModule.forFeature([Company, User, Recruiter, Jobs]), Auth0Module, AddressesModule, UsersModule, RolesModule, RecruitersModule, IntegrationsModule, EmailModule,FreshsalesModule,SubscriptionModule,HttpModule],
  providers: [CompaniesService],
  exports: [SequelizeModule, CompaniesService],
})
export class CompaniesModule {}
