import { Module } from '@nestjs/common';
import { JobCreationChatbotService } from './job-creation-chatbot.service';
import { SequelizeModule } from '@nestjs/sequelize';
import { Company } from '../../../companies/companies.model';
import { LangchainModule } from '@microservices/integrations';
import { Jobs } from '../../job.model';
import { Workflow } from '../../../workflow/workflow.model';
import { PositionsModule } from '../../../positions/positions.module';
import { IndustriesModule } from '../../../industries/industries.module';
import { LocationsModule } from '../../../locations/locations.module';
import { UsersModule } from '../../../users/users.module';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [SequelizeModule.forFeature([Company, Jobs, Workflow]), PositionsModule, IndustriesModule, LocationsModule, UsersModule, LangchainModule, HttpModule],
  providers: [JobCreationChatbotService],
  exports: [JobCreationChatbotService],
})
export class JobCreationChatbotModule {}
