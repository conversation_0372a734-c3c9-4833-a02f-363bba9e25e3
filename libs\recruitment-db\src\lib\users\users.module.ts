import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { User } from './users.model'
import { Auth0Module } from "@microservices/auth";
import { UsersService } from "./users.service";
import { EmailModule } from "@microservices/email";
import { BackgroundModel } from '../background-screening/background.model';
import { UserRoles } from '../roles/user-roles.model';
import { Role } from '../roles/roles.model';
@Module({
  imports: [SequelizeModule.forFeature([User, Role, UserRoles,BackgroundModel]), Auth0Module, EmailModule],
  providers: [UsersService],
  exports: [SequelizeModule, UsersService],
})
export class UsersModule {}
