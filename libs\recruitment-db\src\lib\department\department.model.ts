import { Column, DataType, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";

interface DepartmentAttrs {
  value?: string;
  label?: string;
}

@Table({ tableName: "departments", createdAt: false, updatedAt: false })
export class Department extends Model<Department, DepartmentAttrs> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "administrative",
    description: "Value",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  value: string;

  @ApiProperty({
    example: "Administrative",
    description: "Label",
  })
  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  label: string;
}

