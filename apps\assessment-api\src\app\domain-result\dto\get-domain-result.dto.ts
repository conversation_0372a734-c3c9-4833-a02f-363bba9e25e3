import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional, IsString, Matches } from 'class-validator';

export class GetDomainResultsDto {
  @ApiProperty({ example: 0, description: "jobId " })
  @IsNotEmpty()
  jobId: number;

  @ApiProperty({ example: 0, description: "assessmentId " })
  @IsNotEmpty()
  assessmentId: number;

  @ApiPropertyOptional()
  @IsOptional()
  minScore: number;

  @ApiPropertyOptional()
  @IsOptional()
  maxScore: number;

  @ApiPropertyOptional({ example: "20", description: "Limit" })
  @IsOptional()
  limit: number;

  @ApiPropertyOptional({ example: "0", description: "Offset" })
  @IsOptional()
  offset: number;

  @ApiPropertyOptional({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: "desc", description: "Sort type" })
  @IsOptional()
  @Matches(/ASC|DESC|asc|desc/, { message: 'Enter correct sort; Example: DESC,ASC,asc,desc' })
  sortType?: string;
}
