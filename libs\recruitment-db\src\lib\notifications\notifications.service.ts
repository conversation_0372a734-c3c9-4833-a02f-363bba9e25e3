import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { ChangeNotificationStatusDto, CreateNotificaionDto } from "./dto/notifications.dto";
import { InjectModel } from "@nestjs/sequelize";
import { NotificationsModel } from "./notifications.model";



@Injectable()
export class NotificationsService {
  constructor(
    @InjectModel(NotificationsModel) private notificationsRepository: typeof NotificationsModel
  ) {
  }

  async create(dto:CreateNotificaionDto){
    try{
      return this.notificationsRepository.create({...dto,read:false,userId:dto.userId})
    }catch(error){
      Logger.log("Error while creating a notification: ", error);
    }
  }

  async getAllNotifications(userId:number,query){
    let where:any = {userId}
    if(query?.read !== undefined){
      where.read = query.read === 'true'
    }
    try{
      return this.notificationsRepository.findAndCountAll({where,
      order:[
        ["createdAt","DESC"]
      ],
      limit:query?.limit || 100,
      offset:query?.offset || 0
    })
    }catch(error){
      Logger.log("Error while getting notifications: ", error);
    }
  }

  async update(id:number,dto:ChangeNotificationStatusDto,userId:number){
    try{
      const read = dto.read ||false;
      return this.notificationsRepository.update({read},{where:{userId,id}})
    }catch(error){
      Logger.log("Error while updating a notification: ", error);
    }
  }

  async delete(id:number){
    try{
      return this.notificationsRepository.destroy({where:{id}})
    }catch(error){
      Logger.log("Error while deleting a notification: ", error);
    }
  }

  async markAllNotificationsRead(userId){
    try{
      return this.notificationsRepository.update({read:true},{where:{userId}})
    }catch(error){
      Logger.log("Error while marking all notifications as read: ", error);
    }
  }
}
