import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Op, Sequelize } from "sequelize";
import { Jobs } from "../job/job.model";
import { Location } from "../locations/location.model";
import { User } from "../users/users.model";
import { Subscribe } from "../subscribe/subscribe.model";
import {FilterJobsInterviews} from "./interviews.dto";
import {Round} from "../rounds/round.model";
import {Workflow} from "../workflow/workflow.model";


@Injectable()
export class InterviewsService {
  constructor(
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    @InjectModel(Subscribe) private subscribeRepository: typeof Subscribe
  ) {}

  async getAllJobsCompanyWise(dto: FilterJobsInterviews, companyId: number) {
    const where: any = { companyId,isAssessment:false ,status:'publish'};
    const whereWorkflow:any ={};
    const locationWhere: any = {};
    const order = [];
    if (dto.search) {
      where.title = {
        [Op.iLike]: `%${dto.search}%`,
      };
      where.status={
        [Op.like]: 'publish'
      }
    }

    if (dto.roundName && dto.roundName.trim()){
      whereWorkflow.workflow= {
        [Op.contains]: [{ name: `${dto.roundName.trim()}` }]
      }
    }
   
    if (dto.locations) {
      if (!Array.isArray(dto.locations)) {
        locationWhere.id = { [Op.eq]: dto.locations };
      } else {
        locationWhere.id = { [Op.in]: dto.locations };
      }
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }
    return await this.jobRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where,
      order,
      attributes: [
        "id",
        "title",
        "numberOpenings",
      ],

      include: [
        {
          model: Location,
          where: locationWhere,
        }, 
        {
            model: Round
        },
        {
            model : Workflow,
            where:whereWorkflow
        }
      ],
    });
  }

  async getAllCandidatesJobWise(
    dto: FilterJobsInterviews,
    companyId: number,
    jobId: number
  ) {
    const userWhere: any = {};
    let hasUserFilter = false;
    const order = [];
    if (dto.search) {
      userWhere[Op.or] = [
        { firstname: { [Op.iLike]: `%${dto.search}%` } },
        { lastname: { [Op.iLike]: `%${dto.search}%` } },
      ];
      hasUserFilter = true;
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["createdAt", "DESC"]);
    }

    return await this.subscribeRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where: { jobId,applyJob:true },
      order,
      include: [
        {
          model: User,
          where:userWhere,
          required:hasUserFilter,
        },
        {
          model : Round
        },
        {
          model : Jobs,
          where:{isAssessment:false},
          attributes:["authorId","title"]
        }
      ],
      attributes: { exclude: ["summary"] },
    });
  }


}
