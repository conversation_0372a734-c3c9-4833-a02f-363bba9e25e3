import {
  Controller,
  Get,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { HealthcheckService } from './healthcheck.service';

@ApiTags('Healthcheck')
@Controller('healthcheck')
export class HealthcheckController {
  constructor(private readonly healthcheckService: HealthcheckService) {}

  @ApiOperation({
    summary: 'Check availability.',
  })
  @ApiResponse({
    status: 200,
  })
  @Get('/status')
  healthcheck() {
    return this.healthcheckService.status();
  }
}
