import { Injectable, Logger } from "@nestjs/common";
import { HttpService} from "@nestjs/axios";
import * as twilio from "twilio";
import { v4 as uuidv4 } from "uuid";
import axios from "axios"; 
import { TemporalWorkflowService } from "@microservices/recruitment-db";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { interviewStartedSignal,interviewCompletedSignal } from "apps/temporal/src/app/workflow/temporal/workflows";



@Injectable()
export class TwilioVideoService {
  private readonly _client: twilio.Twilio;

  constructor(
    private readonly httpService: HttpService,
    private temporalWorkflowService: TemporalWorkflowService
  ) {
    this._client = twilio(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_AUTH_TOKEN
    );
  }

  async createVideoRoom(data) {
    let result;
    let token
    try {
     const existingRooms = await this._client.video.rooms.list({ uniqueName: data?.roomId });
    if (existingRooms.length > 0) {
      result = existingRooms[0];
      token =  this.releaseTokenForVideo(data?.userName,result?.uniqueName)
    } else {
      result = await this._client.video.rooms.create({
        statusCallback: "",
        type: "peer-to-peer",
        uniqueName: data?.roomId,
        maxParticipants: 5,
        maxParticipantDuration: 3600,
      });
      token =  this.releaseTokenForVideo(data?.userName,result?.uniqueName)
      if (data.jobId && data.candidateId) {
        const response = await this.temporalWorkflowService.find(
          data.candidateId,
          data.jobId
        );

        const client = await connectToTemporal();
        if (client) {
          const handle = client.getHandle(response.workflowid);
          await handle.signal(
            interviewStartedSignal,
            "Interview is started successfully"
          );
        } else {
          Logger.log(
            "Getting issue to connect the client in Create Twilio room"
          );
        }
      }
    }
    } catch (error) {
      Logger.error(error);
    }
    return token
  }

  releaseTokenForVideo(fullName: string, room: string) {
    const AccessToken = twilio.jwt.AccessToken;
    const VideoGrant = AccessToken.VideoGrant;
    const token = new AccessToken(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_VIDEO_API_KEY,
      process.env.TWILIO_VIDEO_API_SECRET
    );
    token.identity = fullName;
    const videoGrant = new VideoGrant({ room });
    token.addGrant(videoGrant);
    Logger.debug(token.toJwt());
    return token.toJwt();
  }

  async completeVideoRoom(data,authHeader) {
    try {
      let jobId;
      let candidateId;
      const headers ={
        Authorization:authHeader
      }
      const url = `https://wtt-dev.urecruits.com/api/calendar/room-event/${data}`;
      await axios.get(url,{headers}).then(async (res) => {
        jobId = res?.data?.jobId;
        candidateId = res?.data?.candidateId;
      });
      if (jobId && candidateId) {
        const response = await this.temporalWorkflowService.find(
          Number(candidateId),
          String(jobId)
        );

        const client = await connectToTemporal();
        if (client && response) {
          const handle = client.getHandle(response?.workflowid);
          await handle.signal(
            interviewCompletedSignal,
            "Interview is ended"
          );
        } else {
          Logger.log(
            "Getting issue to connect the client in Create Twilio room"
          );
        }
      }

      await this._client.video.rooms(data).update({ status: "completed" });
      return { message: "Twilio room has been completed successfully" };
    } catch (err) {
      Logger.error(err);
    }
  }
}

