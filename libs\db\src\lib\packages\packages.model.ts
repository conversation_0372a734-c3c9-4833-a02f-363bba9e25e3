import {
    Column,
    DataType,
    ForeignKey,
    Model,
    Table,
  } from 'sequelize-typescript';
  import { ApiProperty } from '@nestjs/swagger';
  
  
  @Table({
    tableName: 'packages',
    createdAt: true,
    updatedAt: true,
    timestamps: true,
  })
  export class Package extends Model<Package> {
    @ApiProperty({ example: '1', description: 'Primary key' })
    @Column({
      type: DataType.INTEGER,
      unique: true,
      autoIncrement: true,
      primaryKey: true,
    })
      id: number;
   
    @ApiProperty({ description: 'titles of the packages' })
    @Column({ type: DataType.STRING, allowNull: false })
      title: string;
  }
  