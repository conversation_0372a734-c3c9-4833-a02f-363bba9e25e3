import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Award } from "./awards.model";
import { UpdateAwardDto } from "./dto/update-award.dto";
import { AwardDto } from "./dto/award.dto";

@Injectable()
export class AwardsService {
  constructor(@InjectModel(Award) private awardRepository: typeof Award) {}

  async create(candidateId: number, dto?: AwardDto) {
    return await this.awardRepository.create({...dto, candidateId: candidateId });
  }

  async update(dto: UpdateAwardDto) {
    const data = await this.get(dto.id, dto.candidateId);
    Object.assign(data, dto);
    return await data.save();
  }

  async delete(dto: UpdateAwardDto) {
    const data = await this.get(dto.id, dto.candidateId);
    await data.destroy();
    return true;
  }

  async get(id: number, candidateId: number) {
    const data = await this.awardRepository.findOne({
      where: { id, candidateId },
    });
    if (!data) {
      throw new HttpException("Award not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }
}
