import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, Matches } from 'class-validator';

export class GetAssignmentDto {
  @ApiPropertyOptional({ example: "20", description: "Limit" })
  @IsOptional()
  title: string[];

  @ApiPropertyOptional({ example: "20", description: "Limit" })
  @IsOptional()
  jobId: any[];

  @ApiPropertyOptional({ example: "20", description: "Limit" })
  @IsOptional()
  assignmentType: string;

  @ApiPropertyOptional({ example: "20", description: "Limit" })
  @IsOptional()
  search: string;

  @ApiPropertyOptional({ example: "20", description: "Limit" })
  @IsOptional()
  limit: number;

  @ApiPropertyOptional({ example: "0", description: "Offset" })
  @IsOptional()
  offset: number;

  @ApiPropertyOptional({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: "desc", description: "Sort type" })
  @IsOptional()
  @Matches(/ASC|DESC|asc|desc/, { message: 'Enter correct sort; Example: DESC,ASC,asc,desc' })
  sortType?: string;
}
