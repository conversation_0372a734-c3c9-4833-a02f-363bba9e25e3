import { Module } from "@nestjs/common";
import { SequelizeModule } from "@nestjs/sequelize";
import { UserAssignments } from "./user-assignments.model";
import { UserAssignmentsService } from "./user-assignments.service";

@Module({
  imports: [
    SequelizeModule.forFeature([UserAssignments]),
  ],
  providers: [UserAssignmentsService],
  exports: [SequelizeModule, UserAssignmentsService],
})
export class UserAssignmentsModule {}
