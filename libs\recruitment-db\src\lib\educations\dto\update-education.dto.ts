import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { EducationDto } from "./education.dto";

export class UpdateEducationDto extends EducationDto {
  @ApiPropertyOptional({ example: "1", description: "Recruiter ID" })
  @IsOptional()
  readonly recruiterId?: number;

  @ApiPropertyOptional({ example: "1", description: "Candidate ID" })
  @IsOptional()
  readonly candidateId?: number;

  @ApiProperty({ example: "1", description: "Candidate ID" })
  readonly id: number;
}
