import { Body, Controller, Post,Logger } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { EmailService } from "@microservices/email";
import { EmailDto } from "./dto/email.dto";
import { FreshsalesDto} from "@microservices/recruitment-db";
import { HttpService } from "@nestjs/axios";
import { parse } from 'path';

@ApiTags('Email')
@Controller('email')
export class EmailController {
  constructor(
    private readonly emailService: EmailService,
    private readonly httpService : HttpService 
  ) {}

  @ApiOperation({ summary: 'Contact Form' })
  @ApiResponse({ status: 201, type: 'Url to file' })
  @Post('/contact-form')
  async contactForm(@Body() dto: EmailDto) {
   try{

    // "Name: Leo <br>\n          Email: <EMAIL> <br>\n          Business url: somwhere.com <br>\n          Number of employees: 5 <br>\n          Message: Hello I am from somewhere. I would like to contact to you  <br>\n        
     const contactName = dto?.contactForm?.split("Name:")[1]?.split("<br>")[0]?.trim() || null;
     const contactEmail = dto?.contactForm?.split("Email:")[1]?.split("<br>")[0]?.trim();
     const businessUrl = dto?.contactForm?.split("Business url:")[1]?.split("<br>")[0]?.trim();
     const noOfEmployees = dto?.contactForm?.split("Number of employees:")[1]?.split("<br>")[0]?.trim();
     const message = dto?.contactForm?.split("Message:")[1]?.split("<br>")[0]?.trim();
     
     const contactData : FreshsalesDto= {
      firstName : contactName,
      email : contactEmail,
      cf_business_url: businessUrl,
      cf_number_of_employees: noOfEmployees ? parseInt(noOfEmployees) : 0,
      message: message,
     }
     
     const url = `${process.env.RECRUITMENT_API_URI}/api/freshsales/upsert`
     await this.httpService.post(url, contactData).toPromise();
     await this.emailService.sendRecruitment('contactForm.html', dto, '<EMAIL>', 'uRecruits | Contact form & Support form')
     return "Done!";
   }
   catch(error){
    Logger.log("error occured while inside Email Controller having FreshsalesService",error);
   }
  }
}
