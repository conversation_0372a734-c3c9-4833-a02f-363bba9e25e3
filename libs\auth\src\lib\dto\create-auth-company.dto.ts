import { ApiProperty } from "@nestjs/swagger";
import { UserDto } from "./user.dto";

export class CreateAuthCompanyDto extends UserDto {
  @ApiProperty({ example: '123456qQ@', description: 'Password' })
  readonly password: string;

  @ApiProperty({ example: '1', description: 'company id' })
  readonly companyId: number;

  @ApiProperty({ example: '1', description: 'recruiter id' })
  readonly recruiterId: number | null;

  @ApiProperty({ example: '1', description: 'company id' })
  readonly tenantId: string;

  @ApiProperty({ example: '1', description: 'User id' })
  readonly userId: number;
}
