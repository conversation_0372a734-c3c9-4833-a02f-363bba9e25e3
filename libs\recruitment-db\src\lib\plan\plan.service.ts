import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Plans } from "./plan.model";
import { Op } from "sequelize";
import * as planData from "./plandata.json";

@Injectable()
export class PlanService {
  public constructor (
    @InjectModel(Plans) private planRepository: typeof Plans,
  ) {}

  async seed() {
    await this.planRepository
      .bulkCreate(planData, {
        validate: true,
      })
      .then(async () =>
        this.planRepository.sequelize.query(
          `ALTER SEQUENCE "${
            this.planRepository.tableName
          }_id_seq" RESTART WITH ${
            (await this.planRepository.count()) + 1
          }`
        )
      );
    return true;
  }

  async createPlan(dto){
    try{
      return await this.planRepository.create(dto)
    }catch(error){
      throw new Error("Error while creating plan"+error)
    }
  }

  async getPlanDetails(dto){
    let where:any={}
    if(dto.name){
      where.name = {
        [Op.eq] : dto.name
      }
    }
    if(dto.id){
      where.id=dto.id
    }
    if(dto.planPackageId){
      where.planPackageId=dto.planPackageId
    }
    return await this.planRepository.findOne({where})
  }

  async getAllPlan(dto){
    let where:any={}
    if(dto.name){
      where.name = {
        [Op.eq] : dto.name
      }
    }
    if(dto.id){
      where.id=dto.id
    }
    if(dto.planPackageId){
      where.planPackageId=dto.planPackageId
    }
    return await this.planRepository.findAll({where})
  }

  async updatePlan(dto){
    try{
      const plan = await this.getPlanDetails(dto)
      if (plan) {
        Object.assign(plan, { ...dto })
        await plan.save()
      } else {
        await this.planRepository.create(dto);
      }
    }catch(error){
      throw new Error("Error while updating product"+error)
    }
  }
  async deletePlan(dto){
    try{
      if(!dto.productId){
        throw new BadRequestException("Product ID is not provided")
      }
      return await this.planRepository.destroy({where:{id:dto.id}})
    }catch(error){
      throw new Error("Error while deleting product"+error)
    }

  }
}
