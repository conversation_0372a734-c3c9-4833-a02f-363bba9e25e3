import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class GetMembersDto {
  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiPropertyOptional({ example: ["1", "2"], description: "Roles" })
  @IsOptional()
  roles?: [string];

  @ApiPropertyOptional({ example: ["1"], description: "Position" })
  @IsOptional()
  position?: [string];

  @ApiPropertyOptional({ example: ["Sales Department"], description: "Department" })
  @IsOptional()
  department: [string];

  @ApiPropertyOptional({ example: ["1", "2"], description: "Location id" })
  @IsOptional()
  locationId: [string];

  @ApiPropertyOptional({ example: ["High School"], description: "Education degree" })
  @IsOptional()
  degree?: [string];

  @ApiPropertyOptional({ example: "01.01.1990", description: "Date of joining from" })
  @IsOptional()
  dateJoiningFrom?: string;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Date of joining to" })
  @IsOptional()
  dateJoiningTo?: string;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Birthday date from" })
  @IsOptional()
  birthdayFrom?: string;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Birthday date to" })
  @IsOptional()
  birthdayTo?: string;

  @ApiPropertyOptional({ example: "Rob", description: "Search field" })
  @IsOptional()
  search?: string;

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;
}
