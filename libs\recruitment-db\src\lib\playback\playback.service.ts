import { BadRequestException, ConflictException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Playback } from './playback.model';
import { SearchPlaybackDto } from './dto/searchPlayback.dto';
import { Op, where } from 'sequelize';
import { Jobs } from '../job/job.model';
import { User } from '../users/users.model';
import { connectToTemporal } from 'apps/temporal/src/app/workflow/workflow.provider';
import { TemporalWorkflowService } from '../temporalWorkflow/temporal.service';
import { roundCompletedSignal } from 'apps/temporal/src/app/workflow/temporal/workflows';

@Injectable()
export class PlaybackService {
  constructor(
    @InjectModel(Playback)
    private playbackModel: typeof Playback,
    private temporalWorkflowService: TemporalWorkflowService
  ) {}

  async findOneById(data: SearchPlaybackDto) {
    if (!data.jobId || !data.assessmentType || !data.candidateId) {
      throw new BadRequestException({
        message: "Bad Request",
        statusCode: 400,
      });
    }
  
    try {
      const playback = await this.playbackModel.findOne({
        where: {
          jobId: data.jobId,
          candidateId: data.candidateId,
          assessmentType: data.assessmentType,
        },
      }).catch(()=>{
        throw new NotFoundException({
          message: "Playback record not found",
          statusCode: 404,
        });
      })
  
      if (!playback) {
        // Handle the case where no record is found
        throw new NotFoundException({
          message: "Playback record not found",
          statusCode: 404,
        });
      }
  
      return playback;
    } catch (error) {
      Logger.error("Error:", error);
      if(error?.status===400){
        throw new BadRequestException({
          message: error?.message,
          statusCode: error?.status,
        });
      }else if(error?.status===404){
        throw new NotFoundException({
          message: error?.message,
          statusCode: error?.status,
        });
      }
     
    }
  }

  async findByPlaybackId(id){
    return await this.playbackModel.findOne({where: {playbackId:id}})
  }
  
  async findAll(dto,id) {
    try {
      const { limit, offset, search,assessmentType } = dto;
  
      const where = {}; 
  
      if (search) {
        where['$User.firstname$'] = {
          [Op.iLike]: `%${search}%`,
        };
      }
      if (assessmentType) {
        where['assessmentType'] = assessmentType;
      }
      
      const playbackEntries = await this.playbackModel.findAndCountAll({
        where, 
        limit,
        offset,
        include: [
          {
            model: Jobs,
            where: { companyId: id },
          },
          {
            model: User,
            attributes: ["firstname", "lastname"],
          },
        ],
      });
  
      const result = playbackEntries.rows.map((data) => {
        const obj = {
          "id": data?.id,
          "questionId": data?.questionId,
          "playbackId": data?.playbackId,
          "assessmentId": data?.assessmentId,
          "assessmentType": data?.assessmentType,
          "candidateId": data?.candidateId,
          "candidateName": `${data?.User?.firstname} ${data?.User?.lastname || ''}`,
          "jobId": data?.jobId,
          "title": data?.Jobs?.title,
          "companyId": data?.Jobs?.companyId,
          "questionName":data?.questionName,
        };
        return obj;
      });
  
      return {
        total: playbackEntries.count,
        data: result,
      };
    } catch (error) {
      Logger.error("Error:", error);
      if(error?.status===400){
        throw new BadRequestException({
          message: error?.message,
          statusCode: error?.status,
        });
      }else if(error?.status===404){
        throw new NotFoundException({
          message: error?.message,
          statusCode: error?.status,
        });
      }
    }
  }

  async create(dto) {
    try {
      const existingPlayback = await this.playbackModel.findOne({
        where: {
          jobId: dto.jobId,
          candidateId: dto.candidateId,
          assessmentType: dto.assessmentType,
          playbackId: dto.playbackId,
          questionId:dto.questionId,
        }
      });
  
      if (existingPlayback) {
        return null
      } else {
        await this.playbackModel.create(dto);
        if (dto.jobId && dto.candidateId && dto.assessmentType==="take-home-task") {
          const response = await this.temporalWorkflowService.find(
            dto.candidateId,
            dto.jobId
          );
  
        //   const client = await connectToTemporal();
        //   if (client && response) {
        //     const handle = client.getHandle(response.workflowid);
        //     await handle.signal(
        //       roundCompletedSignal,
        //       "Take Home Assessment is completed"
        //     );
        //   } else {
        //     Logger.log(
        //       "Getting issue to connect the client in Create Twilio room"
        //     );
        //   }
        }
        return { message: "Record created successfully", status: 201 };
      }
    } catch (error) {
      Logger.log("Error : ",error);
      if (error?.status === 400) {
        throw new BadRequestException({
          message: error?.message,
          statusCode: error?.status,
        });
      } else if (error?.status === 404) {
        throw new NotFoundException({
          message: error?.message,
          statusCode: error?.status,
        });
      }
    }
  }
  

  update(id: number, dto: Playback) {
     this.playbackModel.update({ ...dto }, { where: { id } });
     return {message : "Record updated successfully",status:200}
  }

  async remove(id: number) {
    return this.playbackModel.destroy({ where: { id } });
  }
}

