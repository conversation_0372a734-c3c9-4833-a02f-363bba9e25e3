import { Column, DataType, Has<PERSON>any, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Questions } from '../../questions/questions.model';
import { STATUS, TYPE } from '../../live-coding/live-coding.model';

@Table({
  tableName: 'take-home-drafts',
})
export class TakeHomeDrafts extends Model<TakeHomeDrafts> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    example: '98fc959b-25a3-4889-8ec3-ade6255a019a',
    description: 'UUID version 4',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    taskId: string;

  @ApiProperty({ example: 'take-home', description: 'Type of take home coding assessment' })
  @Column({ type: DataType.ENUM('live-task', 'take-home'), allowNull: false })
    assessmentType: TYPE;

  @ApiProperty({ example: 'ACTIVE', description: 'Type of take home assessment' })
  @Column({ type: DataType.ENUM('DRAFT', 'ACTIVE'), allowNull: true })
    status: STATUS;

  @ApiProperty({
    example: 'The Stolen Breakfast Drone',
    description: 'Name of take home task',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    name: string;

  @ApiProperty({ example: 'Some description for the take home task', description: 'Description of take home task' })
  @Column({ type: DataType.TEXT, allowNull: true })
    description?: string;

  @ApiProperty({ example: '1', description: 'Company Id' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;

  @HasMany(() => Questions)
    questions: Questions[];
}
