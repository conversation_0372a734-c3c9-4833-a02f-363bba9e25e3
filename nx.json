{"npmScope": "microservices", "affected": {"defaultBase": "main"}, "cli": {"defaultCollection": "@nrwl/nest"}, "implicitDependencies": {"package.json": {"dependencies": "*", "devDependencies": "*"}, ".eslintrc.json": "*"}, "tasksRunnerOptions": {"default": {"runner": "@nrwl/workspace/tasks-runners/default", "options": {"cacheableOperations": ["build", "lint", "test", "e2e"]}}}, "targetDependencies": {"build": [{"target": "build", "projects": "dependencies"}]}, "defaultProject": "assessment-api"}