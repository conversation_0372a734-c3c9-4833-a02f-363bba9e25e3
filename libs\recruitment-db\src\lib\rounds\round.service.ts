import {
  HttpException,
  HttpStatus,
  Injectable,
  <PERSON><PERSON>,
  MethodNotAllowedException,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Round } from "./round.model";
import { RoundDto } from "./dto/round.dto";
import { User } from "../users/users.model";
import { Candidate } from "../candidates/candidates.model";
import { Location } from "../locations/location.model";
import { Position } from "../positions/positions.model";
import { Skill } from "../skills/skills.model";
import { UpdateRoundDto } from "./dto/update-round.dto";
import { Jobs } from "../job/job.model";
import { Workflow } from "../workflow/workflow.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { TemporalWorkflowService } from "../temporalWorkflow/temporal.service";
import {
  domainAssessmentCompletedSignal,
  domainAssessmentRejectedSignal,
  roundCompletedSignal,
  roundRejectedSignal,
  assessmentReviewedSignal,
} from "apps/temporal/src/app/workflow/temporal/workflows";
import { ChangeStatusDto } from "./dto/change-status.dto";
import { Op } from "sequelize";
import { Company } from "../companies/companies.model";
import { AddEventDto, AddScoreToRoundDto } from "./dto/add-event.dto";
import { roundType } from "apps/temporal/src/app/workflow/enum/round.enum";
import { WorkflowNotFoundError } from "@temporalio/common";
import { UserAssignmentsService } from "../user-assignments/user-assignments.service";
import { HttpService } from "@nestjs/axios";
import { JobTargetService } from "../jobtarget/jobtarget.service";

@Injectable()
export class RoundService {
  constructor(
    @InjectModel(Round) private roundRepository: typeof Round,
    @InjectModel(Subscribe) private subscribeRepository: typeof Subscribe,
    private temporalWorkflowService: TemporalWorkflowService,
    private userAssignmentsService: UserAssignmentsService,
    private httpService:HttpService,
    private jobTargetService: JobTargetService,
  ) {}

  /**
   * Check if a round type should trigger "Interviewed" status in JobTarget
   */
  private shouldTriggerInterviewedStatus(roundName: string): boolean {
    const interviewRounds = [
      roundType.HR_AUDIO_VIDEO_INTERVIEW,
      roundType.FUNCTIONAL_DOMAIN_ASSESSMENT,
      roundType.LIVE_TASK_CODING_ASSESSMENT,
      roundType.TAKE_HOME_CODING_ASSESSMENT,
      roundType.TECHNICAL_VIDEO_AUDIO_INTERVIEW,
      roundType.SENIOR_HR_AUDIO_VIDEO_INTERVIEW,
    ];
    return interviewRounds.includes(roundName as any);
  }

  /**
   * Update JobTarget applicant status to "Interviewed" when applicable rounds are completed
   */
  private async updateJobTargetInterviewedStatus(subscribeId: number, roundName: string, authorEmail: string): Promise<void> {
    if (!this.shouldTriggerInterviewedStatus(roundName)) {
      return;
    }

    try {
      const subscribe = await this.subscribeRepository.findByPk(subscribeId);
      if (subscribe?.JT_GUID) {
        await this.jobTargetService.updateApplicantStatus(
          subscribe.JT_GUID,
          "Interviewed",
          authorEmail
        );
        Logger.log(`JobTarget applicant status updated to "Interviewed" for GUID: ${subscribe.JT_GUID}, Round: ${roundName}`);
      }
    } catch (error) {
      Logger.error(`Failed to update JobTarget applicant status to "Interviewed": ${error.message}`);
    }
  }

  /**
   * Update JobTarget applicant status to "Rejected" when any round is rejected
   */
  private async updateJobTargetRejectedStatus(subscribeId: number, roundName: string, authorEmail: string): Promise<void> {
    try {
      const subscribe = await this.subscribeRepository.findByPk(subscribeId);
      if (subscribe?.JT_GUID) {
        await this.jobTargetService.updateApplicantStatus(
          subscribe.JT_GUID,
          "Rejected",
          authorEmail
        );
        Logger.log(`JobTarget applicant status updated to "Rejected" for GUID: ${subscribe.JT_GUID}, Round: ${roundName}`);
      }
    } catch (error) {
      Logger.error(`Failed to update JobTarget applicant status to "Rejected": ${error.message}`);
    }
  }

  async create(dto: RoundDto) {
    return await this.roundRepository.create({ ...dto });
  }

  async getOne(userId: number, jobId: number) {
    return {
      round: await this.roundRepository.findOne({
        where: {
          userId,
          jobId,
        },
        include: [
          {
            model: User,
            attributes: [
              "id",
              "avatar",
              "email",
              "phone",
              "firstname",
              "middlename",
              "lastname",
            ],
            include: [
              {
                model: Candidate,
                attributes: [
                  "id",
                  "positionId",
                  "experience",
                  "locationId",
                  "cvKey",
                  "cvName",
                  "degree",
                  "experience",
                ],
                include: [
                  {
                    model: Location,
                    required: false,
                  },
                  {
                    model: Position,
                    required: false,
                  },
                  {
                    model: Skill,
                    required: false,
                  },
                ],
              },
            ],
          },
          {
            model: Jobs,
            include: [
              Workflow,
              {
                model:Company,
                attributes:["id","name","avatar","email","phone","website","avatar","tenantId"]
              }
            ],
          },
        ],
      }),
      subscribe: await this.subscribeRepository.findOne({
        attributes: ["id"],
        where: {
          jobId,
          userId,
        },
      }),
    };
  }

  async getAllByJobId(jobId: number, dto: any, candidateIds) {
    const ids = candidateIds;
    let whereClause: any = { jobId };
    const order = [];
    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }
    if (ids && ids.length > 0) {
      whereClause = {
        ...whereClause,
        userId: {
          [Op.in]: ids,
        },
      };
    }

    if (dto.candidateId) {
      whereClause = {
        ...whereClause,
        "$user.id$": dto.candidateId,
      };
    }

    if (dto.search) {
      let nameParts = dto.search.split(" ");

      if (nameParts.length === 1) {
        whereClause = {
          ...whereClause,
          [Op.or]: [
            {
              "$user.firstname$": {
                [Op.iLike]: `%${dto.search}%`,
              },
            },
            {
              "$user.lastname$": {
                [Op.iLike]: `%${dto.search}%`,
              },
            },
          ],
        };
      } else if (nameParts.length === 2) {
        whereClause = {
          ...whereClause,
          "$user.firstname$": {
            [Op.iLike]: `%${nameParts[0]}%`,
          },
          "$user.lastname$": {
            [Op.iLike]: `%${nameParts[1]}%`,
          },
        };
      } else {
        console.error("Invalid search format");
      }
    }

    const result = await this.roundRepository.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: User,
          attributes: [
            "id",
            "avatar",
            "email",
            "phone",
            "firstname",
            "middlename",
            "lastname",
          ],
        },
      ],
      limit: dto.limit || 10,
      offset: dto.offset || 0,
      order,
    });

    return result;
  }

  async delete(jobId:number,userId:number){
    try{
      await this.roundRepository.destroy({where:{jobId,userId}})
    }catch(error){
      throw new Error("Failed to delete round data")
    }

  }

  async update(dto: UpdateRoundDto, companyId: number) {
    const data = await this.roundRepository.findOne({
      where: {
        id: dto.id,
      },
      include: [
        {
          model: Jobs,
          attributes: ["isAssessment","workflowId"],
          required: true,
          where: {
            companyId,
          },
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['email']
            }
          ]
        },
      ],
    });
    let before;
    let after;
    if (data) {
      before = data?.rounds;
      Object.assign(data, dto);
    }
    else{
      throw new Error('Data not found');
    }
    if (dto.rounds) {
      let totalScoreRounds = 0;
      let allRounds = parseInt(dto.rounds.length) * 100;
      dto.rounds.forEach((item) => {
        if (item.score) {
          totalScoreRounds += parseInt(item.score);
        }
      });
      if (totalScoreRounds > 0) {
        const percentTotal = (totalScoreRounds / allRounds) * 100;
        await this.subscribeRepository.update(
          {
            totalScores: Math.ceil(percentTotal),
          },
          {
            where: {
              jobId: data.jobId,
              userId: data.userId,
            },
          }
        );
      }
    }
    after = data?.rounds;
    if (after?.length && before?.length) {
      for (let i = 0; i < after.length; i++) {
        const beforeObj = before[i];
        const afterObj = after[i];

        if (beforeObj.status !== afterObj.status) {
          const assessmentTypes = {
            [roundType.LIVE_TASK_CODING_ASSESSMENT]: {
              signal: "round",
              logMessage: "Live Task/Coding Assessment",
            },
            [roundType.HR_AUDIO_VIDEO_INTERVIEW]: {
              signal: "round",
              logMessage: "HR Audio Video Interview",
            },
            [roundType.SENIOR_HR_AUDIO_VIDEO_INTERVIEW]: {
              signal: "round",
              logMessage: "Senior HR Audio Video Interview",
            },
            [roundType.TECHNICAL_VIDEO_AUDIO_INTERVIEW]: {
              signal: "round",
              logMessage: "Technical Audio Video Interview",
            },
            [roundType.FUNCTIONAL_DOMAIN_ASSESSMENT]: {
              signal: "domainAssessment",
              logMessage: "Functional/Domain Assessment",
            },
            [roundType.TAKE_HOME_CODING_ASSESSMENT]: {
              signal: "take-home-assessment",
              logMessage: roundType.TAKE_HOME_CODING_ASSESSMENT,
            },
            [roundType.CONDITIONAL_OFFFER_LETTER_GENERATION]: {
              signal: "round",
              logMessage: roundType.CONDITIONAL_OFFFER_LETTER_GENERATION,
            },
            [roundType.BACKGROUND_SCREENING]: {
              signal: "round",
              logMessage: roundType.BACKGROUND_SCREENING,
            },
            [roundType.DRUG_SCREENING]: {
              signal: "round",
              logMessage: roundType.DRUG_SCREENING,
            }
            // Add more assessment types as needed
          };

          const assessmentType = assessmentTypes[afterObj.title];
          try {
            if (assessmentType && data.jobId && data.userId) {
              const response = await this.temporalWorkflowService.find(
                data.userId,
                data.jobId
              );
              const client = await connectToTemporal();

              if (client) {
                const handle = client.getHandle(response.workflowid);
                if (afterObj.status === "Completed") {
                  if (assessmentType.signal === "round") {
                    await handle.signal(
                      roundCompletedSignal,
                      `${assessmentType.logMessage} is completed`
                    );
                  } else if (assessmentType.signal === "domainAssessment") {
                    await handle.signal(
                      domainAssessmentCompletedSignal,
                      `${assessmentType.logMessage} is completed`
                    );
                  } else if (assessmentType.signal === "take-home-assessment") {
                    await handle.signal(
                      assessmentReviewedSignal,
                      `${assessmentType.logMessage} is completed`
                    );
                  }

                  // JobTarget tracking: Update applicant status based on round completion
                  if (data?.job?.author?.email) {
                    if (afterObj.status === "Completed") {
                      await this.updateJobTargetInterviewedStatus(
                        dto.subscribeId,
                        afterObj.title,
                        data.job.author.email
                      );
                    } else if (afterObj.status === "Rejected") {
                      await this.updateJobTargetRejectedStatus(
                        dto.subscribeId,
                        afterObj.title,
                        data.job.author.email
                      );
                    }
                  }

                  if (i == after.length - 1) {
                    if (data?.job?.isAssessment) {
                      try {
                        await this.userAssignmentsService.changeActiveAssignmentStatus({ userId: dto.userId, hasActiveAssignment: false, assignmentId: null, assignmentName: '' }, companyId)
                        const url = `https://wtt-dev.urecruits.com/api/assignment/change-status/${dto.userId}`;
                        await this.httpService
                          .patch(url, {
                            jobId: +dto.jobId,
                            workflowId: +data.job?.workflowId,
                            companyId: companyId,
                            status: "completed"
                          }).toPromise().catch(err => console.log("Error updating candidate list in assignment:", err))
                      } catch (err) {
                        Logger.log("Error when updating assignment status of a candidate:", err)
                      }
                    }
                    await handle.result();
                  }
                } else if (afterObj.status === "Rejected") {
                  const signal =
                    `${assessmentType.signal}` === "round"
                      ? roundRejectedSignal
                      : `${assessmentType.signal}` === "domainAssessment" ?
                        domainAssessmentRejectedSignal
                        : `${assessmentType.signal}` === "take-home-assessment"  && roundRejectedSignal;
                  await handle.signal(
                    signal,
                    `${assessmentType.logMessage} is Rejected`
                  );
                  client
                    .getHandle(response?.workflowid)
                    .terminate(`Rejected in ${assessmentType.logMessage}`);
                  await this.subscribeRepository.update(
                    { status: "Rejected" },
                    {
                      where: {
                        jobId: data.jobId,
                        userId: data.userId,
                      },
                    }
                  );
                  if (data?.job?.isAssessment) {
                    try{
                      await this.userAssignmentsService.changeActiveAssignmentStatus({ userId: dto.userId, hasActiveAssignment: false,assignmentId:null,assignmentName:'' }, companyId)
                      const url = `https://wtt-dev.urecruits.com/api/assignment/change-status/${dto.userId}`;
                      await this.httpService
                      .patch(url, {
                        jobId: +dto.jobId,
                        workflowId: +data.job?.workflowId,
                        companyId:companyId,
                        status:"rejected"
                      }).toPromise().catch(err=>console.log("Error updating status in assignments from rounds",err))
                    }catch(err){
                      Logger.log("Error when updating assignment status of a candidate:",err)
                    }
                  }
                }
              } else {
                Logger.log(
                  `Getting issue to complete the ${assessmentType.logMessage} round`
                );
              }

              break;
            }
          } catch (error) {
            if (error instanceof WorkflowNotFoundError) {
              Logger.error('Workflow not found or already completed.');
            } else {
              Logger.error('Failed to signal Workflow:', error);
            }
            throw new MethodNotAllowedException();
          }
        }
      }
    }
    return await data.save();
  }

  async changeStatus(dto: ChangeStatusDto) {
    try {
      const data = await this.roundRepository.findOne({
        where: {
          userId: dto.userId,
          jobId: dto.jobId,
        },
        include:[
          {
            model:Jobs,
            attributes:["companyId","workflowId","isAssessment"],
            include: [
              {
                model: User,
                as: 'author',
                attributes: ['email']
              }
            ]
          }
        ]
      });
      if (!data) {
        throw new NotFoundException("Record not found");
      }
      let isLastCompletedRound = false
      const dataRounds = (data?.rounds || [])
      const rounds = dataRounds.map((item,index) => {
        if (item?.title === dto?.roundName) {
          let date;
          if (dto?.status === "Completed") {
            if(index == dataRounds.length -1){
              isLastCompletedRound = true
            }
            date = { ...item.date, end: new Date() };
          } else if (dto?.status === "In Progress") {
            date = { ...item.date, start: new Date() };
          }
          return {
            ...item,
            status: dto.status,
            SubscribeId: dto.subscribeId,
            date,
          };
        }
        return item;
      });

      // JobTarget tracking: Update applicant status based on round completion
      if (data?.job?.author?.email) {
        if (dto?.status === "Completed") {
          await this.updateJobTargetInterviewedStatus(
            dto.subscribeId,
            dto.roundName,
            data.job.author.email
          );
        } else if (dto?.status === "Rejected") {
          await this.updateJobTargetRejectedStatus(
            dto.subscribeId,
            dto.roundName,
            data.job.author.email
          );
        }
      }

      if (data?.job?.isAssessment && isLastCompletedRound) {
        try {
          await this.userAssignmentsService.changeActiveAssignmentStatus({ userId: dto.userId, hasActiveAssignment: false, assignmentId: null, assignmentName: '' }, data?.job?.companyId)
          const url = `https://wtt-dev.urecruits.com/api/assignment/change-status/${dto.userId}`;
          await this.httpService
            .patch(url, {
              jobId: +dto.jobId,
              workflowId: +data.job?.workflowId,
              companyId: +data.job?.companyId,
              status: "completed"
            }).toPromise().catch(err => console.log("Error updating status in assignments from round change status:", err))
        } catch (err) {
          Logger.log("Error when updating assignment status of a candidate:", err)
        }
      }

      await this.roundRepository?.update(
        { rounds },
        { where: { id: data?.id } }
      );
      return { message: "Record updated successfully" };
    } catch (err) {
      Logger.log("Error from update round status : ", err);
      if (err?.response?.statusCode === 404) {
        throw new NotFoundException("Record not found");
      } else if (err?.response?.statusCode === 401) {
        throw new UnauthorizedException();
      }
    }
  }

  async getCandidateJobScoreboard(userId: number, jobId: number) {
    try {
      const roundData = await this.roundRepository.findOne({
        where: {
          jobId,
          userId,
        },
        attributes: ["id", "rounds", "jobId"],
        include: [
          {
            model: Jobs,
            attributes: ["id", "title"],
            include: [
              {
                model: Company,
                attributes: ["id", "name", "avatar"],
              },
              {
                model: Workflow,
                attributes: ["id", "title", "domainId"],
              },
            ],
          },
        ],
      });
      let rounds;
      if (roundData) {
        rounds = roundData.rounds
          ? roundData.rounds.filter(
              (i) =>
                (i.title === "Functional/Domain Assessment" ||
                  i.title === "Technical/Coding Assessment" ||
                  i.title === "TAKE HOME TASK") &&
                ["Completed", "Rejected"].includes(i.status)
            )
          : null;
        const result = rounds?.map((round, index) => {
          return {
            id: index,
            title: roundData.job.title,
            workflow: roundData.job.workflow,
            assessmentType: round.title,
            company: roundData.job.company,
            totalScores: round.score,
            result: round.status,
            comments: round?.comments,
          };
        });
        return { count: result.length, rows: result };
      } else {
        throw new Error("Round data not found for a job");
      }
    } catch (error) {
      Logger.log("Error:", error);
    }
  }

  async getCandidateRounds(userId:number){
    try {
      const roundData = await this.roundRepository.findAll({
        where: {
          userId,
        },
        attributes: ["id", "rounds", "jobId"],
        include: [
          {
            model: Jobs,
            attributes: ["id", "title"],
            include: [
              {
                model: Company,
                attributes: ["id", "name", "avatar"],
              },
              {
                model: Workflow,
              },
            ],
          },
        ],
      });
      return roundData;
    } catch (error) {
      Logger.log("Error Getting rounds details for candidates:", error);
    }
  }

  async addScoreToRound(dto: AddScoreToRoundDto) {
    const round = await this.roundRepository.findOne({
      where: {
        userId: dto.userId,
        jobId: dto.jobId,
      },
    })
    if(round && round.rounds){
      const hasRound = round.rounds.some((item) => item.title === dto.roundName);
      if(hasRound&&dto.score){
        const updatedRound = round.rounds.map((item) => {
          if (item.title === dto.roundName) {
            return {
              ...item,
              score: dto.score,
            };
          }
          return item;
        });
        await this.roundRepository.update(
          { rounds: updatedRound },
          { where: { id: round.id } }
        );
        return { message: "Score added successfully" }; 
      }
    }

  }

  async addEventId(dto: AddEventDto) {
    try {
      const data = await this.roundRepository.findOne({
        where: {
          userId: dto.userId,
          jobId: dto.jobId,
        },
      });
      if (!data) {
        throw new NotFoundException("Record not found");
      }

      const rounds = (data?.rounds || []).map((item) => {
        if (item?.title === dto?.roundName) {
          return {
            ...item,
            eventId: dto.eventId,
            interviewers: dto.interviewers,
            eventTitle: dto.eventTitle,
            roomId:dto.roomId,
            eventStartTime:dto.eventStartTime,
            eventEndTime:dto.eventEndTime
          };
        }
        return item;
      });

      await this.roundRepository?.update(
        { rounds },
        { where: { id: data?.id } }
      );
      return { message: "Event Added into Round successfully" };
    } catch (err) {
      Logger.log("Error Occured during Adding EventId", err);
    }
  }
}
