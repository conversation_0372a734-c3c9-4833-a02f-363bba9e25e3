import {
  <PERSON><PERSON><PERSON>To,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Candidate } from '../candidates/candidates.model'
import { Recruiter } from '../recruiters/recruiters.model'

@Table({ tableName: 'educations' })
export class Education extends Model<Education> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: 'Zhytomyr Polytechnic',
    description: 'University/Institute Name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  universityName: string;

  @ApiProperty({ example: 'Web Developer', description: 'Course Name' })
  @Column({ type: DataType.STRING, allowNull: true })
  courseName: string;

  @ApiProperty({ example: 'Web Developer', description: 'Specification' })
  @Column({ type: DataType.STRING, allowNull: true })
  specification: string;

  @ApiProperty({
    example: 'January 4, 2022',
    description: 'Course Periods start',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  coursePeriodStart: string;

  @ApiProperty({
    example: 'January 4, 2022',
    description: 'Course Periods end',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  coursePeriodEnd: string;

  @ApiProperty({ example: '85%', description: 'GPA / Percentage' })
  @Column({ type: DataType.STRING, allowNull: true })
  gpa: string;

  @ApiProperty({ example: false, description: 'Present education?' })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  present: boolean;

  @ApiProperty({ example: '1', description: 'Recruiter ID' })
  @ForeignKey(() => Recruiter)
  @Column({ type: DataType.INTEGER, allowNull: true })
  recruiterId: number;

  @ApiProperty({ example: '1', description: 'Candidate ID' })
  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER, allowNull: true })
  candidateId: number;

  @BelongsTo(() => Recruiter)
  recruiter: Recruiter;

  @BelongsTo(() => Candidate)
  candidate: Candidate;
}
