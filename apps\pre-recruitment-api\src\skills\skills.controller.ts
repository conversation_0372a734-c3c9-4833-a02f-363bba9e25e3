import { Body, Controller, Get, Param, Post, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  PredefinedSkillDto, PredefinedSkills, PredefinedSkillService,
} from "@microservices/recruitment-db";
import { AuthGuard } from "@nestjs/passport";
import { Permissions, PermissionsGuard } from "@microservices/auth";

@ApiTags("Skills")
@Controller("skills")
export class SkillsController {
  constructor (private readonly predefinedSkillService: PredefinedSkillService) {}

  @ApiOperation({ summary: "Get skills" })
  @ApiResponse({ status: 200, type: [PredefinedSkills] })
  @Get('/:name?')
  @Get()
  getAll (@Param('name') name?: string,) {
    return this.predefinedSkillService.findByName(name);
  }

  @ApiOperation({ summary: 'Create skill' })
  @ApiResponse({ status: 200, type: PredefinedSkills })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post()
  @Permissions("recruiter")
  create(@Body() dto: PredefinedSkillDto) {
    return this.predefinedSkillService.createSkill(dto);
  }
}
