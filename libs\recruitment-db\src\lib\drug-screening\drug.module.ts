import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { DrugService } from './drug.service';
import { Jobs } from "../job/job.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { DrugModel } from "./drug.model";
import { User } from "../users/users.model";
import { HttpModule } from '@nestjs/axios';
import { FileModule } from 'apps/pre-recruitment-api/src/file/file.module';

@Module({
  imports: [
    SequelizeModule.forFeature([Subscribe, DrugModel, Jobs, User]), HttpModule,FileModule
  ],
  providers: [DrugService],
  exports: [DrugService],
})
export class DrugModule {}
