import {
  <PERSON><PERSON>sTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { PlanPackages } from '../plan-packages/plan-packages.model';
import { Plans } from '../plan/plan.model';

interface PriceAttrs {
  priceId: string;
  productId: string;
  price: number;
  discount: number;
  freeQty: number;
  active: boolean;
  isDefaultPackagePlan: boolean;
  monthlyYearly: string;
  planPackageId: number;
  planId:number
}

@Table({ 
  tableName: 'price',
  createdAt: true,
  updatedAt: true,
  timestamps: true
 })
export class Price extends Model<Price, PriceAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'price_xxx', description: 'Price id of stripe product' })
  @Column({ type: DataType.STRING, allowNull: false })
  priceId: string;

  @ApiProperty({ example: 'prod_xxx', description: 'Product id of price' })
  @Column({ type: DataType.STRING, allowNull: false })
  productId: string;

  @ApiProperty({ example: '200', description: 'Product Price for respective quantity' })
  @Column({ type: DataType.FLOAT, allowNull: false })
  price: number;

  @ApiProperty({
    example: '10',
    description: 'Discount on product',
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  discount: number;

  @ApiProperty({ example: '50', description: 'Allowed addons in table' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  freeQty: number;

  @ApiProperty({ example: 'true', description: 'Price is active or not' })
  @Column({ type: DataType.BOOLEAN, allowNull: false })
  active: boolean;

  @ApiProperty({ example: 'true', description: 'Price is active or not' })
  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  isDefaultPackagePlan: boolean;

  @ApiProperty({ example: 'm', description: 'Defines Price is for monthly or yearly plan' })
  @Column({ type: DataType.STRING, allowNull: false })
  monthlyYearly: string;

  @ApiProperty({ example: '1', description: 'package id of plan' })
  @ForeignKey(()=>PlanPackages)
  @Column({ type: DataType.INTEGER, allowNull: true })
  planPackageId: number;

  @ApiProperty({ example: '1', description: 'Plan Id' })
  @ForeignKey(()=>Plans)
  @Column({ type: DataType.INTEGER, allowNull: true })
  planId: number;

  @BelongsTo(() => PlanPackages)
  planPackage: PlanPackages;

  @BelongsTo(()=>Plans)
  plans:Plans

}
