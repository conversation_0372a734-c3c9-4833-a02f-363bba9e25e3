import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { HttpModule } from '@nestjs/axios';
import { LangGraphController } from './langgraph.controller';
import { LangGraphService } from './langgraph.service';
import {
  JobCreationChatbotService,
  JobCreationChatbotModule,
  WorkflowChatbotService,
  WorkflowChatbotModule,
  Workflow
} from '@microservices/recruitment-db';
import { LangchainModule } from '@microservices/integrations';

@Module({
  imports: [
    JobCreationChatbotModule,
    WorkflowChatbotModule,
    LangchainModule,
    SequelizeModule.forFeature([Workflow]),
    HttpModule,
  ],
  controllers: [LangGraphController],
  providers: [
    LangGraphService,
  ],
  exports: [LangGraphService],
})
export class LangGraphModule {}

// import { Module } from '@nestjs/common';
// import { SequelizeModule } from '@nestjs/sequelize';
// import { HttpModule } from '@nestjs/axios';
// import { LangGraphController } from './langgraph.controller';
// import { LangGraphService } from './langgraph.service';
// import {
//   JobCreationChatbotService,
//   WorkflowChatbotService,
//   RecruitmentDbModule,
//   Workflow,
// } from '@microservices/recruitment-db';
// import { AssessmentChatbotService, DbModule } from '@microservices/db';
// import { LangchainModule } from '@microservices/integrations';

// @Module({
//   imports: [
//     RecruitmentDbModule,
//     LangchainModule,
//     SequelizeModule.forFeature([Workflow]),
//     HttpModule,
//     DbModule,
//   ],
//   controllers: [LangGraphController],
//   providers: [
//     LangGraphService,
//     JobCreationChatbotService,
//     WorkflowChatbotService,
//     AssessmentChatbotService,
//   ],
//   exports: [LangGraphService],
// })
// export class LangGraphModule {}