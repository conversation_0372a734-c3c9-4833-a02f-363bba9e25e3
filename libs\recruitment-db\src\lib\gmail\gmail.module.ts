import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { GmailService } from './gmail.service';
import { IntegrationAuthModule } from '../integrationAuth/integrationAuth.module';
import { FileModule } from 'apps/pre-recruitment-api/src/file/file.module';
import { NotifyPermissionsService } from '../notify-permissions/notify-permissions.service';
import { NotifyPermissionsModule } from '../notify-permissions/notify-permissions.module';

@Module({
  imports: [SequelizeModule.forFeature(),IntegrationAuthModule,FileModule, NotifyPermissionsModule],
  providers: [GmailService],
  exports: [SequelizeModule, GmailService],
})
export class GmailModule {}
