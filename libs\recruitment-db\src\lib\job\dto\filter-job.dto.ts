import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, Matches } from "class-validator";

export class FilterJobDto {
  @ApiPropertyOptional({ example: "Angular Developer", description: "Job Title" })
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ example: ["1"], description: "Job Location" })
  @IsOptional()
  locations?: [string];

  @ApiPropertyOptional({ example: "2022-10-13", description: "createdAtFrom" })
  @IsOptional()
  createdAtFrom?: string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "createdAtTo" })
  @IsOptional()
  createdAtTo?: string;

  @ApiPropertyOptional({ example: "500", description: "Salary Range(per month min)" })
  @IsOptional()
  salaryMonthMin?: number;

  @ApiPropertyOptional({ example: "500", description: "Salary Range(per month max)" })
  @IsOptional()
  salaryMonthMax?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year min)" })
  @IsOptional()
  salaryYearMin?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year max)" })
  @IsOptional()
  salaryYearMax?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year min)" })
  @IsOptional()
  salaryHourMin?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per hour max)" })
  @IsOptional()
  salaryHourMax?: number;

  @ApiPropertyOptional({ example: "1", description: "Experience Min" })
  @IsOptional()
  experienceMin?: number;

  @ApiPropertyOptional({ example: "5", description: "Experience Max" })
  @IsOptional()
  experienceMax?: number;

  @ApiPropertyOptional({ example: "Master Degree", description: "Education" })
  @IsOptional()
  education?: string;

  @ApiPropertyOptional({ example: [], description: "Skills" })
  @IsOptional()
  skills?: [string];

  @ApiPropertyOptional({ example: "1", description: "Company ID" })
  @IsOptional()
  companyId?: number;

  @ApiPropertyOptional({ example: "Full Time", description: "Job Type" })
  @IsOptional()
  jobType?: string;

  @ApiPropertyOptional({ example: "Morning Shift", description: "Preferable Shift" })
  @IsOptional()
  preferableShift?: string;

  @ApiPropertyOptional({ example: '1', description: 'Industry' })
  @IsOptional()
  industryId?: number;

  @ApiPropertyOptional({ example: "Functional Area", description: "Functional Area" })
  @IsOptional()
  functionalArea?: string;

  @ApiPropertyOptional({ example: "1", description: "Current user id" })
  @IsOptional()
  currentUserId?: number;

  @ApiPropertyOptional({ example: "matched", description: "Filter type" })
  @IsOptional()
  @Matches(/matched|saved|applied/, { message: 'Enter correct filter; Example: matched,saved,applied' })
  filterType?: string;

  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiPropertyOptional({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: "desc", description: "Sort type" })
  @IsOptional()
  @Matches(/ASC|DESC|asc|desc/, { message: 'Enter correct sort; Example: DESC,ASC,asc,desc' })
  sortType?: string;
}

export class SingleJobFilterDto {
  @ApiPropertyOptional({ example: "1", description: "Current user id" })
  @IsOptional()
  currentUserId?: number;
}

export class FilterCompanyJobsDto {
  @ApiPropertyOptional({ example: "1", description: "Job Id" })
  @IsOptional()
  jobId?: any;

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date From" })
  @IsOptional()
  dateFrom?: string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date To" })
  @IsOptional()
  dateTo?: string;

  @ApiPropertyOptional({ example: ["Publish"], description: "Status" })
  @IsOptional()
  status?: [string];

  @ApiPropertyOptional({ example: 'Designer', description: "Search" })
  @IsOptional()
  search?: string;

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;
}

export class FilterPublicCompanyJobsDto {
  @ApiPropertyOptional({ example: 'Designer', description: "Search" })
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "createdAtFrom" })
  @IsOptional()
  createdAtFrom?: string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "createdAtTo" })
  @IsOptional()
  createdAtTo?: string;

  @ApiPropertyOptional({ example: [], description: "Job Location" })
  @IsOptional()
  locations?: [string];

  @ApiPropertyOptional({ example: "1", description: "Experience Min" })
  @IsOptional()
  experienceMin?: number;

  @ApiPropertyOptional({ example: "5", description: "Experience Max" })
  @IsOptional()
  experienceMax?: number;

  @ApiPropertyOptional({ example: "500", description: "Salary Range(per month min)" })
  @IsOptional()
  salaryMonthMin?: number;

  @ApiPropertyOptional({ example: "500", description: "Salary Range(per month max)" })
  @IsOptional()
  salaryMonthMax?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year min)" })
  @IsOptional()
  salaryYearMin?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year max)" })
  @IsOptional()
  salaryYearMax?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year min)" })
  @IsOptional()
  salaryHourMin?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per hour max)" })
  @IsOptional()
  salaryHourMax?: number;

  @ApiPropertyOptional({ example: "Master Degree", description: "Education" })
  @IsOptional()
  education?: string;

  @ApiPropertyOptional({ example: [], description: "Skills" })
  @IsOptional()
  skills?: [string];

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiProperty({ example: "1", description: "Company tenantId" })
  tenantId: string;

  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiPropertyOptional({ example: "1", description: "Current user id" })
  @IsOptional()
  currentUserId?: number;
}

export class ScoreboardFilterDto {
  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiPropertyOptional({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: [], description: "Job IDs" })
  @IsOptional()
  jobId: [string];

  @ApiPropertyOptional({ example: "desc", description: "Sort type" })
  @IsOptional()
  @Matches(/ASC|DESC|asc|desc/, {
    message: "Enter correct sort; Example: DESC,ASC,asc,desc",
  })
  sortType?: string;

  @ApiPropertyOptional({ example: "Rob", description: "Search candidate" })
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({ example: [], description: "Job Location" })
  @IsOptional()
  locations?: [string];

  @ApiPropertyOptional({ example: "1", description: "Experience Min" })
  @IsOptional()
  experienceMin?: number;

  @ApiPropertyOptional({ example: "5", description: "Experience Max" })
  @IsOptional()
  experienceMax?: number;

  @ApiPropertyOptional({
    example: "6000",
    description: "Salary Range(per year min)",
  })
  @IsOptional()
  salaryYearMin?: number;

  @ApiPropertyOptional({
    example: "6000",
    description: "Salary Range(per year max)",
  })
  @IsOptional()
  salaryYearMax?: number;

  @ApiPropertyOptional({
    example: ["High School"],
    description: "Education degree",
  })
  @IsOptional()
  degree?: [string];

  @ApiPropertyOptional({ example: [], description: "Skills" })
  @IsOptional()
  skills?: [string];

  @ApiPropertyOptional({
    example: 0,
    description: "Percentage of matching min",
  })
  @IsOptional()
  percentageMin: number;

  @ApiPropertyOptional({
    example: 100,
    description: "Percentage of matching max",
  })
  @IsOptional()
  percentageMax: number;

  @ApiPropertyOptional({ example: "", description: "Status" })
  @IsOptional()
  status?: string;

  @ApiPropertyOptional({ example: "applyJob", description: "Applied job" })
  @IsOptional()
  applyJob?: string;
}
