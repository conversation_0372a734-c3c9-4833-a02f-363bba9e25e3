import {
  BadRequestException,
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  UnauthorizedException,
  forwardRef,
} from "@nestjs/common";
import * as graph from "@microsoft/microsoft-graph-client";
import * as msal from "@azure/msal-node";
import { IntegrationAuthService } from "@microservices/recruitment-db";
import fetch from "cross-fetch";
import { HttpService } from "@nestjs/axios";
import { CreateEventDTO } from "./dto/create-event.dto";
import { GetFreeSlotsDTO } from "./dto/get-free-slots.dto";
import { UtilityService } from "./utility.service";
import { v4 as uuidv4 } from "uuid";

require("isomorphic-fetch");

@Injectable()
export class MsGraphService {
  private msalClient: msal.ConfidentialClientApplication;

  private clientId = process.env.MCAL_CLIENT_ID;
  private clientSecret = process.env.MCAL_CLIENT_SECRET;
  private authorizationEndpoint =
    "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
  private tokenEndpoint =
    "https://login.microsoftonline.com/common/oauth2/v2.0/token";
  // private scope = 'offline_access Calendars.ReadWrite Calendars.Read Calendars.ReadBasic Calendars.Read.Shared User.Read';
  private scope = "Calendars.ReadWrite offline_access User.Read";
  // "Calendars.ReadWrite Calendars.Read Calendars.ReadBasic User.Read Mail.Read Mail.Send Mail.ReadWrite Mail.ReadBasic";
  private redirectUri = `${process.env.MICROSERVICES_PRA_URL}/api/integrations/oauth2-callback`;

  constructor(
    private integrationModelService: IntegrationAuthService,
    @Inject(forwardRef(() => UtilityService))
    private utilityService: UtilityService,
    private readonly httpService: HttpService
  ) {}

  async getTokens(id) {
    const tokens = {
      accessToken: "",
      refreshToken: "",
    };
    const data = await this.integrationModelService.findByProvider(id, "mcal");
    const refreshToken = data?.dataValues?.refreshToken;
    if (Number(data?.dataValues?.expiryDate) < new Date().getTime()) {
      await fetch(this.tokenEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Origin: `${process.env.MICROSERVICES_PRA_URL}`,
        },
        body: new URLSearchParams({
          scope: this.scope,
          refresh_token: refreshToken,
          grant_type: "refresh_token",
          client_id: this.clientId,
          response_type: "code",
          redirect_uri: this.redirectUri,
          response_mode: "query",
        }),
      })
        .then(function (res) {
          return res.json();
        })
        .then(async (body: any) => {
          if (!body.access_token || !body.refresh_token) {
            throw body;
          }
          tokens.accessToken = body.access_token;
          tokens.refreshToken = body.refresh_token;
          await this.integrationModelService.update(
            id,
            {
              userId: id,
              accessToken: body.access_token,
              refreshToken: body.refresh_token,
              expiryDate: new Date().getTime() + body.expires_in * 1000,
              scope: this.scope,
            },
            "mcal"
          );
        });
    } else {

      await fetch(this.tokenEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Origin: process.env.MICROSERVICES_PRA_URL,
        },
        body: new URLSearchParams({
          scope: this.scope,
          refresh_token: refreshToken,
          grant_type: "refresh_token",
          client_id: this.clientId,
          response_type: "code",
          redirect_uri: this.redirectUri,
          response_mode: "query",
        }),
      })
        .then(function (res) {
          return res.json();
        })
        .then(async (body: any) => {
          if (!body.access_token || !body.refresh_token) {
            throw body;
          }
          tokens.accessToken = body.access_token;
          tokens.refreshToken = body.refresh_token;
        });
    }

    return tokens;
  }

  async getClient(id) {
    const tokens = await this.getTokens(id);
    const client = await graph.Client.init({
      authProvider: async (callback) => {
        callback(null, tokens.accessToken);
      },
    });
    return {
      client,
    };
  }

  async getAuthenticatedUser(id) {
    try {
      const { client } = await this.getClient(id).catch((error) => {
        throw new UnauthorizedException("Invalid grant");
      });
      const user = await client.api("/me").get();
      return user;
    } catch (error) {
      Logger.error("Error fetching user email:", error);
    }
  }

  async getEvents(id, query) {
    const { client } = await this.getClient(id).catch((error) => {
      throw new UnauthorizedException("Invalid grant");
    });

    const calendar = client;
    const date = new Date() || new Date();

    const res = await calendar
      .api("/me/events")
      .filter(
        `start/dateTime ge '${new Date(
          date.getFullYear(),
          date.getMonth(),
          date.getDate(),
          0,
          0,
          0
        ).toISOString()}' and end/dateTime le '${new Date(
          date.getFullYear(),
          date.getMonth(),
          date.getDate(),
          23,
          59,
          59
        ).toISOString()}'`
      )
      .get();

    const events = res;
    return { events };
  }

  async bookAppointment(candidateId, data) {
    try {
      const timeRegex = /^(1[0-2]|0?[1-9]):[0-5][0-9] (AM|PM)$/i;

      if (!timeRegex.test(data.slot)) {
        throw new BadRequestException(
          "Invalid time format. Please use HH:MM AM/PM format."
        );
      }
      if (candidateId && data.recruiterId) {
        const recruiter = "recruiter";
        const info = {
          id: data.recruiterId,
          data: {
            ...data,
            remindMe: false,
          },
          action: recruiter,
        };
        await this.utilityService.createEvent(info);
        const candidate = "candidate";
        // await this.createEvent(candidateId, data, candidate);
        return { message: "Appointment booked successfully", status: 201 };
      } else {
        throw new BadRequestException();
      }
    } catch (error) {
      if (error.status === 400) {
        throw new BadRequestException({
          status: error.status,
          message: error.message,
        });
      } else {
        throw new UnauthorizedException(error.message);
      }
    }
  }

  async createEvent(id, body: CreateEventDTO, action) {
    try {
      let client;
      try {
        client = await this.getClient(id);
      } catch (error) {
        Logger.log(error);
      }

      const timeZone = "UTC";
      const newEvent = {
        subject: body.eventTitle,
        start: {
          dateTime: body.utcStartDate,
          timeZone,
        },
        end: {
          dateTime: body.utcEndDate,
          timeZone,
        },
        body: {
          contentType: "text",
          content: body.description || "",
        },
        reminderMinutesBeforeStart: null,
        attendees: [],
      };

      if (body.remindMe) {
        const conversionFactors = {
          minutes: 1,
          hours: 60,
          days: 24 * 60,
          weeks: 7 * 24 * 60,
        };
        let timeBefore = 0;
        if (body.timeBefore) {
          const [amount, unit] = body.timeBefore.split(" ");

          if (conversionFactors[unit]) {
            timeBefore = Number(amount) * conversionFactors[unit];
          } else {
            timeBefore = Number(amount);
          }
        }
        newEvent.reminderMinutesBeforeStart = timeBefore;
      }

      await client?.client?.api("/me/events").post(newEvent);
      if (action === "candidate") {
        const uuid = uuidv4();
        const obj = {
          startGmt: body.utcStartDate,
          endGmt: body.utcEndDate,
          title: body.eventTitle,
          description: body.description || "",
          users: [body.recruiterId],
          remindMe: body.remindMe,
          jobId: body.jobId,
          candidateId: id,
          timeBefore: null,
          roomId: uuid,
        };

        if (body.remindMe === true) {
          obj.timeBefore = `${body.timeBefore}`;
        }

        const url = `${process.env.ASSESSMENT_API_URI}/api/calendar/create-event`;
        await this.httpService.post(url, obj).toPromise();
      }
    } catch (error) {
      Logger.log("Getting error when book the appointment", error);
    }
  }

  async getFreeBusySlots(id, data: GetFreeSlotsDTO) {
    const startTime = `${data.date}T00:00:00+00:00`;
    const endTime = `${data.date}T23:59:59+00:00`;
    const allSlots = generateAllSlots(startTime, endTime);
    try {
      let client;
      try {
        client = (await this.getClient(id))?.client;
      } catch (error) {
        Logger.log(error);
      }
      if (client) {
        const profile = await this.getAuthenticatedUser(id);
        const userEmail = profile.mail;

        const scheduleInformation = {
          schedules: [userEmail],
          startTime: {
            dateTime: startTime,
            timeZone: "Local",
          },
          endTime: {
            dateTime: endTime,
            timeZone: "Local",
          },
        };
        const busySlots = [];

        const response = await client
          .api("/me/calendar/getSchedule")
          .post(scheduleInformation);

        const slots = response.value.map((data) =>
          data.scheduleItems.map((info) => {
            const busy = {
              start: "",
              end: "",
            };
            busy.start = info.start.dateTime;
            busy.end = info.end.dateTime;
            busySlots.push(busy);
          })
        );

        const freeSlots = findFreeSlots(allSlots, busySlots);
        return { freeSlots };
      } else {
        return { freeSlots: allSlots };
      }
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async appointment(candidateId, data) {
    try {
      let recruiterSlots;
      const candidateSlots = await this.getFreeBusySlots(candidateId, data);
      const info = {
        recruiterId: data.recruiterId,
        data: data,
      };
      recruiterSlots = await this.utilityService.getData(info);
      if (candidateSlots && recruiterSlots) {
        const candidateFreeSlots = candidateSlots.freeSlots;
        const recruiterFreeSlots = recruiterSlots.freeSlots;

        const similarSlots = candidateFreeSlots.filter((candidateSlot) =>
          recruiterFreeSlots.some(
            (recruiterSlot) =>
              candidateSlot.start === recruiterSlot.start &&
              candidateSlot.end === recruiterSlot.end
          )
        );
        return { similarSlots };
      }
    } catch (error) {
      if (error.status === 401) {
        throw new UnauthorizedException({
          status: error.status,
          message: error.message,
        });
      } else if (error.status === 400) {
        throw new BadRequestException({
          status: error.status,
          message: error.message,
        });
      } else {
        throw new NotFoundException(error.message);
      }
    }
  }

  isValidDate(date): boolean {
    return !isNaN(new Date(date).getTime());
  }

  isValidTime(time: string): boolean {
    const timeRegex = /^(1[0-2]|0?[1-9]):[0-5][0-9] (AM|PM)$/i;
    return timeRegex.test(time);
  }
}

function findFreeSlots(allSlots, busySlots) {
  const freeSlots = [];
  for (const slot of allSlots) {
    let isBusy = false;

    for (const busySlot of busySlots) {
      if (
        new Date(busySlot.start) < new Date(slot.end) &&
        new Date(busySlot.end) > new Date(slot.start)
      ) {
        isBusy = true;
        break;
      }
    }
    if (!isBusy) {
      freeSlots.push(slot);
    }
  }

  return freeSlots;
}

function generateAllSlots(startTime, endTime, interval = 60) {
  const slots = [];
  let currentTime = new Date(startTime);

  while (currentTime < new Date(endTime)) {
    let busyslot = { start: "", end: "" };
    const slotEndTime = new Date(currentTime.getTime() + interval * 60000);
    busyslot.start = currentTime.toISOString();
    busyslot.end = slotEndTime.toISOString();
    slots.push(busyslot);
    currentTime = slotEndTime;
  }
  return slots;
}
