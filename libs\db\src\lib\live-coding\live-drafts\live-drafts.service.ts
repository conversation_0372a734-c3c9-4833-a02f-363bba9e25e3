import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { v4 as uuidv4 } from 'uuid';
import { Op } from 'sequelize';
import { parseDate } from 'tools/utils';
import { Filter } from '../filter.interface';
import { STATUS, TYPE } from '../live-coding.model';
import { LiveCodingDrafts } from './live-drafts.model';
import { liveCodingDto } from '../live-coding.dto';

@Injectable()
export class LiveDraftsService {
  constructor(
    @InjectModel(LiveCodingDrafts)
    private liveDraftsModel: typeof LiveCodingDrafts
  ) { }

  findOneById(id: number): Promise<LiveCodingDrafts> {
    return this.liveDraftsModel.findByPk(id);
  }

  findOneByTaskId(taskId: string) {
    return this.liveDraftsModel.findOne({ where: { taskId } });
  }

  findAll(companyId: number): Promise<LiveCodingDrafts[]> {
    const find = this.liveDraftsModel.findAll({
      where: {
        companyId
      }
    });

    if (find) {
      return find;
    }
    else {
      throw new NotFoundException(`LiveCoding not found`);
    }
  }

  async create(dto: liveCodingDto, companyId: number,userId:number) {

    let liveCodingDraft:LiveCodingDrafts;
    const data = await this.liveDraftsModel.findOne({
      where: {
       ...(dto.taskId &&{taskId:dto.taskId}),
        companyId
      }
    })

    if (data) {
      Object.assign(data, {
        ...dto,
        taskId: data.taskId
      })
      liveCodingDraft = await data.save();
    } else {
      liveCodingDraft = await this.liveDraftsModel.create({
        ...dto,
        taskId: uuidv4(),
        status: STATUS.draft,
        assessmentType: TYPE.liveTask,
        companyId
      });
    }

    const recordData = this.getLiveCodingDataForQdrant(liveCodingDraft);
    return {response:liveCodingDraft,recordData};
  }

  async update(id: number, dto: LiveCodingDrafts,userId:number) {
    const liveCodingDraft= await this.liveDraftsModel.findOne({ where: { id } });
    if(liveCodingDraft){
    await liveCodingDraft.update({ ...dto });
    const recordData = this.getLiveCodingDataForQdrant(liveCodingDraft);
    return {response:liveCodingDraft,recordData};
    }
  }

  async remove(id: number,userId:number) {
    const liveCodingDraft = await this.liveDraftsModel.findOne({ where: { id } });
    if (liveCodingDraft) {
      const recordData = this.getLiveCodingDataForQdrant(liveCodingDraft,'delete');
      await liveCodingDraft.destroy();
      return {response:liveCodingDraft,recordData};
    }
  }

  async removeByTaskId(taskId, companyId: number,userId:number) {
    const liveCodingDraft = await this.liveDraftsModel.findOne({ where: { taskId, companyId } });
    if (liveCodingDraft) {
      const id = liveCodingDraft.id
      const recordData = this.getLiveCodingDataForQdrant(liveCodingDraft,'delete');
      await this.liveDraftsModel.destroy({ where: { id, companyId } });
      return {response:liveCodingDraft,recordData};
    }
  }

  async search(query: Filter, companyId: number) {
    const parsedQuery = {
      database: query.database ? query.database?.split(',').map((el) => parseInt(el)) : null,
      language: query.language ? query.language?.split(',').map((el) => parseInt(el)) : null,
      package: query.package ? query.package : null,
      createdOn: query.createdOn ? query.createdOn : null,
    };

    Object.keys(parsedQuery).forEach((key) => {
      if (parsedQuery[key] === null) {
        delete parsedQuery[key];
      }
    });

    const propertyArray = [];
    const includeArray = [];
    for (const key in parsedQuery) {
      if (key === 'language') {
        propertyArray.push({ languageId: parsedQuery[key] });
      } else if (key === 'package') {
        propertyArray.push({ packageId: parsedQuery[key] });
      } else if (key === 'createdOn') {
        propertyArray.push({
          createdAt: { [Op.gt]: parseDate(parsedQuery[key], 'start'), [Op.lt]: parseDate(parsedQuery[key], null) },
        });
      } else if (key === 'database') {
        includeArray.push({ association: 'assessment-database', where: { id: parsedQuery[key] } });
      }
    }
    let whereSearch:any[]=[]
    if(query.search){
      whereSearch.push({
        name:{ [Op.iLike]: `%${query.search}%` }
      })
      whereSearch.push({
        description:{ [Op.iLike]: `%${query.search}%` }
      })
    }
    if (parsedQuery.database) {
      return this.liveDraftsModel.findAll({
        where: {
          [Op.and]: propertyArray,
          ...(!!whereSearch?.length && {[Op.or]: whereSearch}),
          companyId
        },
        include: includeArray,
      });
    } else {
      return this.liveDraftsModel.findAll({
        where: {
          [Op.and]: propertyArray,
          ...(!!whereSearch?.length && {[Op.or]: whereSearch}),
          companyId
        },
      });
    }
  }

  async getAllLiveDrafts({limit,offset}:{limit:number,offset:number}){
    return await this.liveDraftsModel.findAll({limit,offset})
  }

  getLiveCodingDraftRecordData(liveCoding: LiveCodingDrafts, userId,action) {
    return {
      for:'assessment',
      data:{
        liveCodingDraftId: liveCoding.id,
        assessmentType: liveCoding.assessmentType,
        name: liveCoding.name,
        companyId: liveCoding.companyId,
        createdBy: userId,
        status:'Draft',
        action,
        liveCoding,
        createdAt: new Date(),
      }
    }
  }
  getLiveCodingDataForQdrant(liveCoding:LiveCodingDrafts,action='create'){
    const str = `Live Coding Assessment: ${liveCoding.name} (Type: ${liveCoding.assessmentType}, Status: ${liveCoding.status})\nDescription: ${liveCoding.description}\n`;
    return {
      for:'live-coding',
      id:liveCoding.taskId,
      action,
      ...(action === 'create' && {
        str,
      payload:{
        assessmentId:liveCoding.id,
        name:liveCoding.name,
        companyId:liveCoding.companyId,
        assessmentType:liveCoding.assessmentType,
        description:liveCoding.description,
        status:liveCoding.status,
        languageId:liveCoding.languageId,
        packageId:liveCoding.packageId,
        createdAt:liveCoding.createdAt,
        updatedAt:liveCoding.updatedAt,
      }
      })
    }
   }
}
