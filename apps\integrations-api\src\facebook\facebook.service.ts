import { Injectable } from '@nestjs/common';

@Injectable()
export class FacebookService {
  /**
   * Retrieve a Jobs XML.
   *
   * @throws Error
   *
   * @returns {string}
   */
  async jobsXml() {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>';
    xml += '<source>';
    xml += '<publisher-name><![CDATA[uRecruits]]></publisher-name>';
    xml += '<publisher-url><![CDATA[https://urecruits.com/]]></publisher-url>';
    xml += `<last-build-date><![CDATA[${  new Date().toISOString().slice(0, 19).replace('T', ' ')  } +0000]]></last-build-date>`;

    /* job item start */
    xml += '<job>';
    xml += '<!-- Basic job info -->';
    xml += '<title><![CDATA[Cashier]]></title>';
    xml += '<date><![CDATA[2022-09-30 09:31:48 +0000]]></date>';
    xml += '<id><![CDATA[job7]]></id>';
    xml += '<photo-url><![CDATA[https://urecruits-dev-pre-recruitment-api-uploads-public.s3.amazonaws.com/company/auth0%7C6334255cab2d886bb3b55e1f/1664440463882-photo_2022-08-19_09-52-49.jpg]]></photo-url>';
    xml += '<job-type>FULL_TIME</job-type>';
    xml += '<description><![CDATA[Job description]]></description>';
    xml += '<!-- Company info -->';
    xml += '<company-name><![CDATA[AutoMobile Technologies (AMT)]]></company-name>';
    xml += '<company-id><![CDATA[company3]]></company-id>';
    xml += '<company-full-address><![CDATA[1 Hacker Way, Menlo Park, CA, 94025]]></company-full-address>';
    xml += '<company-facebook-url><![CDATA[https://www.facebook.com/Jaspers-Market-909582695755501/]]></company-facebook-url>';
    xml += '<company-data-policy-url><![CDATA[https://www.jaspers-market.com/data-policy.php]]></company-data-policy-url>';
    xml += '<company-url><![CDATA[https://www.ibm.com/]]></company-url>';
    xml += '<company-page-matching-approach>STANDARD</company-page-matching-approach>';
    xml += '<!-- Location of Job -->';
    xml += '<full-address><![CDATA[1 Hacker Way, Menlo Park, CA, 94025]]></full-address>';
    xml += '<house-number><![CDATA[1]]></house-number>';
    xml += '<street-name><![CDATA[Hacker Way]]></street-name>';
    xml += '<city><![CDATA[Menlo Park]]></city>';
    xml += '<region><![CDATA[CA]]></region>';
    xml += '<country><![CDATA[US]]></country>';
    xml += '<postal-code><![CDATA[94025]]></postal-code>';
    xml += '<!-- Salary -->';
    xml += '<salary><![CDATA[55.50]]></salary>';
    xml += '<salary-currency>USD</salary-currency>';
    xml += '<salary-type>HOURLY</salary-type>';
    xml += '<!-- Integration configuration -->';
    xml += '<facebook-apply-data>';
    xml += '  <application-callback-url><![CDATA[https://urecruits.com/callback?job=unique11111]]></application-callback-url>';
    xml += '  <custom-questions-url><![CDATA[https://urecruits.com/custom-questions?job=unique11111]]></custom-questions-url>';
    xml += '  <form-config>';
    xml += '    <email-field>';
    xml += '      <optional>TRUE</optional>';
    xml += '    </email-field>';
    xml += '    <phone-number-field>';
    xml += '      <optional>FALSE</optional>';
    xml += '    </phone-number-field>';
    xml += '    <work-experience-field>';
    xml += '      <optional>TRUE</optional>';
    xml += '    </work-experience-field>';
    xml += '  </form-config>';
    xml += '</facebook-apply-data>';
    xml += '</job>';
    /* job item end */

    xml += '</source>';

    return xml;
  }
}
