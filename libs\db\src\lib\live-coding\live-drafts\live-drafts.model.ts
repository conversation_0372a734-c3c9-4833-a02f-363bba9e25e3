import { Column, DataType, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ne, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { AssessmentDatabase } from '../../assessment-database/assessmentDatabase.model';
import { STATUS, TYPE } from '../live-coding.model';

@Table({
  tableName: 'live-coding-drafts',
})
export class LiveCodingDrafts extends Model<LiveCodingDrafts> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    example: '98fc959b-25a3-4889-8ec3-ade6255a019a',
    description: 'UUID version 4',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    taskId: string;

  @ApiProperty({
    example: 'The Stolen Breakfast Drone',
    description: 'Name of live coding',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    name: string;

  @ApiProperty({ example: 'Encrypt string in AES256', description: 'Description of live coding' })
  @Column({ type: DataType.TEXT, allowNull: true })
    description?: string;

  @ApiProperty({ example: 'function someData(){}', description: 'Intro' })
  @Column({ type: DataType.TEXT, allowNull: true })
    starterCode?: string;

  @ApiProperty({ example: 'live-task', description: 'Type of live coding assessment' })
  @Column({ type: DataType.ENUM('live-task', 'take-home'), allowNull: false })
    assessmentType: TYPE;

  @ApiProperty({ example: 'DRAFT', description: 'Intro' })
  @Column({ type: DataType.ENUM('DRAFT', 'ACTIVE'), allowNull: true })
    status: STATUS;

  @ApiProperty({ example: '1', description: 'Id of related language' })
  @Column({ type: DataType.INTEGER, allowNull: true })
    languageId?: number;

  @ApiProperty({ example: '1', description: 'id of related package' })
  @Column({ type: DataType.INTEGER, allowNull: true })
    packageId?: number;

  @ForeignKey(()=>AssessmentDatabase)
  @ApiProperty({ example: '1', description: 'id of related Database' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  databaseId?: number;

  @ApiProperty({ example: '', description: 'instructions' })
  @Column({ type: DataType.TEXT, allowNull: true })
  instruction: string;

  @ApiProperty({ example: '1', description: 'Company Id' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;
}
