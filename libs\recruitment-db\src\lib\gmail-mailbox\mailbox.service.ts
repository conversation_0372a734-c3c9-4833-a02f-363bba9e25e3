import { BadRequestException, Injectable, Logger, NotFoundException, UnauthorizedException } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Mailbox } from "./mailbox.model";
import { MailFilterDto } from "./dto/mailFilter.dto";
import { Op } from "sequelize";
import { EmailResolverService, EmailService } from "@microservices/email";
import { UsersService } from "../users/users.service";

@Injectable()
export class MailBoxService {
    constructor(
        private emailService: EmailService,
        private readonly emailResolver: EmailResolverService,
        private userService: UsersService,
        @InjectModel(Mailbox)
        private mailboxModel: typeof Mailbox,
      ) {}

    async saveEmailToDb(data: any) {
        const userEmail = "<EMAIL>";

        const savedEmail = await this.mailboxModel.create({
        sender: userEmail,
        recipient: data.to,
        subject: data.subject,
        body: data?.body,
        userId: data?.userId,
        isRead:false,
        source:"inbox"
        });
        
        return savedEmail
    }  

    async saveEmail(data: any, files: any,user) {
        const id=user["https://urecruits.com/userId"]
        const html = this.emailResolver.recruitment("mailTemplate.html");
        const parsedHtml = fillTemplate(html, data);
        const {email}=await this.userService.getUserByUserId(id)
        const recipient=await this.userService.getUserByEmail(data.to)
        let attachData=[]
        if (files && files.length > 0) {
            for (const uploadedFile of files) {    
                const content = uploadedFile.buffer.toString('base64');
                attachData.push({
                  content: content,
                    filename:`${uploadedFile.originalname}`,
                    type: `${uploadedFile.mimetype}`,
                    disposition: 'attachment'
                }
              );
            }
          }
          try{
       const isMail =await this.emailService.sendRecruitment(
            "mailTemplate.html",
            {   
                recipient: data.to,
                subject: data.subject,
                body: data?.body,
                attachments: attachData,
            },
           data.to,
           data.subject
          );
        if(isMail?.status===201){
          const savedEmail = await this.mailboxModel.create({
            sender: email,
            recipient: data.to,
            subject: data.subject,
            body: JSON.stringify(parsedHtml),
            userId: id,
            isRead:false,
            source:"sent",
            s3File:data?.s3File ? data?.s3File : null
            });

            if(recipient){
                const savedEmail = await this.mailboxModel.create({
                    sender: email,
                    recipient: data.to,
                    subject: data.subject,
                    body: JSON.stringify(parsedHtml),
                    userId: recipient.id,
                    isRead: false,
                    source: "inbox",
                    s3File: data?.s3File ? data?.s3File : null
                });
            }
            return savedEmail
        }else{
            throw new BadRequestException("Something went wrong.")
        }
          }catch(err){
            Logger.log(err)
            throw new BadRequestException("Something went wrong.", err)
          }
    }  

    async getAllMails(data: MailFilterDto, userId: number): Promise<any> {
        try {
            let filter: any = {userId};
            
            if (data.search) {
                filter[Op.or] = [
                    { sender: { [Op.like]: `%${data.search}%` } },
                    { recipient: { [Op.like]: `%${data.search}%` } },
                    { subject: { [Op.like]: `%${data.search}%` } },
                    { body: { [Op.like]: `%${data.search}%` } },
                ];
            }
            if (data.newer_than) {
                const match = data.newer_than;
                const days = period[data.newer_than];
                if (days !== null) {
                  const newerThanDate = new Date();
                  newerThanDate.setDate(newerThanDate.getDate() - days);
                  filter.createdAt = { [Op.gte]: newerThanDate };
                }
              }
              
              if (data.source) {
                if (data.source === 'inbox') {
                    filter.source = 'inbox';
                } else if (data.source === 'sent') {
                    filter.source = 'sent';
                }
            }
            if(data.sortBy=='date'){
                data.sortBy = 'createdAt';
            }
            const array = await Mailbox.findAll({
                where: filter,
                order: [
                    [data.sortBy || 'subject', data.sortType || 'ASC'],
                    ['createdAt', 'DESC'],
                ],
                limit: data.limit || 10,
                offset: data.offset || 0,
            });
    
            const totalCount = await Mailbox.count({ where: filter });
    
            const formattedData = array.map((item) => ({
                id: item?.id,
                from: item.sender,
                subject: item.subject,
                body: item.body,
                isRead: item.isRead,
                date: item.createdAt,
                to: item.recipient,
            }));
    
            return {
                data: formattedData,
                totalCount,
                limit: data.limit || 10,
                offset: data.offset || 0,
            };
        } catch (error) {
            Logger.log('Error: ' + error);
            throw new UnauthorizedException('Token has been expired or revoked');
        }
    }

    async getMailById(mailId: number, userId: number): Promise<any> {
    try {
        const mail = await Mailbox.findOne({
        where: { id: mailId, userId },
        });

        if (!mail || mail===null) {
        return new NotFoundException('Mail not found');
        }      
       const type=mail.s3File ? mail.s3File.split(".").pop() :""
       let obj
        if(mail?.s3File){
            obj = {
                from: mail.sender,
                to: mail.recipient,
                subject: mail.subject || null,
                body: mail.body || null,
                date: (mail.createdAt),
                s3File: mail.s3File && mail.s3File,
                type: mail.s3File && type,
                }; 
        }  else{
            obj = {
                from: mail.sender,
                to: mail.recipient,
                subject: mail.subject || null,
                body: mail.body || null,
                date: (mail.createdAt),
                }; 
        } 
        return { data: obj };
    } catch (error) {
        Logger.log('Error: ' + error);
    }
    }

    async markEmailAsRead(data: any, userId: number) {
    try {
        const mails = await Mailbox.findAll({
        where: {
            id: { [Op.in]:data?.messageIds },
            userId,
        },
        });

        if (mails.length === 0) {
        return new NotFoundException('No matching mails found');
        }

        let promises = [];
        let request;

        for (let i = 0; i < mails.length; i++) {
        const mail = mails[i];

        if (data.flag === true) {
            request = {
            isRead: true,
            };
        } else {
            request = {
            isRead: false,
            };
        }
        
        const [updatedCount] = await Mailbox.update(request, {
            where: { id: mail.id },
        });

        if (updatedCount > 0) {
            promises.push(mail);
        }
        }

        if (promises.length > 0) {
        return {
            message: 'Data updated successfully',
        };
        } else {
        throw new NotFoundException('No mails were updated');
        }
    } catch (error) {
        Logger.log('ERROR: ' + error);

        if (error.response?.data.error.code === 404) {
        throw new NotFoundException(error.response.data.error.message);
        } else {
        throw new UnauthorizedException('Bad Request');
        }
    }
    }

    async deleteEmail(mailIds: any, userId: number) {
    try {
        const mails = await Mailbox.findAll({
        where: {
            id: { [Op.in]: mailIds?.id },
            userId,
        },
        });

        if (mails.length === 0) {
        throw new NotFoundException('No matching mails found');
        }

        let promises = [];

        for (let i = 0; i < mails.length; i++) {
        const mail = mails[i];

        promises.push(mail);

        await mail.destroy();
        }

        if (promises.length > 0) {
        return {
            message: 'Emails deleted successfully',
        };
        } else {
        throw new NotFoundException('No mails were deleted');
        }
    } catch (error) {
        Logger.log('ERROR: ' + error);

        if (error.response?.data.error.code === 404) {
        throw new NotFoundException(error.response.data.error.message);
        } else {
        throw new UnauthorizedException('Bad Request');
        }
    }
    }
    
}

enum period {
    "1 day" = 1,
    "3 days" = 3,
    "1 week" = 7,
    "2 weeks" = 14,
    "1 month" = 30,
    "2 month" = 60,
    "6 month" = 180,
    "1 year" = 365,
}

function fillTemplate(html: string, fillData: object) {
    if (typeof fillData !== 'object') return html;

    for (const [key, value] of Object.entries(fillData)) {
      html = html.replace(new RegExp(`<%${key}%>`, 'g'), value);
    }

    return html;
  }