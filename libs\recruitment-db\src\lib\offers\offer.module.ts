import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { OfferModel } from './offer.model'
import { OfferService } from "./offer.service";
import { Jobs } from '../job/job.model';
import { LocationJobs } from '../locations/location-jobs.model';
import { Subscribe } from "../subscribe/subscribe.model";
import { FileModule } from "apps/pre-recruitment-api/src/file/file.module";
import { HellosignTemplate } from "./hellosign-template.model";
import { IntegrationsModule } from "../integrations/integrations.module";
import { EmailModule } from '@microservices/email';
import { User } from '../users/users.model';
import {TemporalClientModule} from "apps/temporal/src/app/workflow/temporal-client/temporalClient.module";
import { JobTargetModule } from '../jobtarget/jobtarget.module';

@Module({
  imports: [
    SequelizeModule.forFeature([OfferModel]),
    SequelizeModule.forFeature([HellosignTemplate]),
    SequelizeModule.forFeature([Jobs]),
    SequelizeModule.forFeature([LocationJobs]),
    SequelizeModule.forFeature([Subscribe]),
    SequelizeModule.forFeature([User]),
    IntegrationsModule,
    EmailModule,
    FileModule,
    TemporalClientModule,
    JobTargetModule,
  ],
  providers: [OfferService],
  exports: [OfferService],
})
export class OfferModule {}
