import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Playback } from './playback.model';
import { PlaybackService } from './playback.service';
import { TemporalWorkflowModule } from '../temporalWorkflow/temporal.module';

@Module({
  imports: [SequelizeModule.forFeature([Playback]),TemporalWorkflowModule],
  providers:[PlaybackService],
  exports: [SequelizeModule,PlaybackService],
})
export class PlaybackModule {}
