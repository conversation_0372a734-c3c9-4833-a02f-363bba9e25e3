import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { IntegrationAuth } from './integrationAuth.model';

@Injectable()
export class IntegrationAuthService {
  constructor(
    @InjectModel(IntegrationAuth) private integrationModel: typeof IntegrationAuth,
  ) {}


  async find(dto: string)  {
    return this.integrationModel.findOne({
      where: {
        userId: dto,
      },
    });
  }

  async findByProvider(id,provider)  {
    return this.integrationModel.findOne({
      where: {
        userId: id,
        provider:provider
      },
    });
  }

  create(dto): Promise<any> {
    return this.integrationModel.create(dto);
  }

  update(id: number, dto,provider):Promise<any>  {
    return this.integrationModel.update(dto, { where: { userId:id,provider } });
  }

  async delete(id: number,provider:any) {
    return  await this.integrationModel.destroy({ where: { userId:id,provider } });
  }
}
