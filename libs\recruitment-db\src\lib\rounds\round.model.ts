import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Jobs } from "../job/job.model";
import { User } from "../users/users.model";
import { Subscribe } from "@microservices/recruitment-db";

@Table({ tableName: "round", createdAt: true, updatedAt: true })
export class Round extends Model<Round> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "1", description: "Job ID" })
  @ForeignKey(() => Jobs)
  @Column({ type: DataType.INTEGER, allowNull: false })
  jobId: number;

  @ApiProperty({ example: "1", description: "Subscribe ID" })
  @ForeignKey(() => Subscribe)
  @Column({ type: DataType.INTEGER, allowNull: true })
  subscribeId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({ example: [], description: "Summary" })
  @Column({ type: DataType.JSONB, allowNull: true })
  rounds: any;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Jobs)
  job: Jobs;

  @BelongsTo(() => Subscribe)
  Subscribe: Subscribe;
}
