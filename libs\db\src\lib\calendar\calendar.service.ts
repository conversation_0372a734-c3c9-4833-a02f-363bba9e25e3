import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Event } from "./event.model";
import { EventUser } from "./event-user.model";
import { CreateEventDto } from "./dto/create-event.dto";
import { DeleteEventDto } from "./dto/delete-event.dto";
import * as moment from "moment";
import { Op } from "sequelize";

@Injectable()
export class CalendarService {
  constructor(
    @InjectModel(Event) private eventModel: typeof Event,
    @InjectModel(EventUser) private eventUserModel: typeof EventUser
  ) { }

  //   companyId: user["https://urecruits.com/companyId"],
  //   tenantId: user["https://urecruits.com/tenantId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  // userId: user["https://urecruits.com/userId"],

  async getAllEventByUser(user, date) {
    try {
      const eventsDate = convertToUTC(date.date);
      const startOfDay = new Date(eventsDate);
      startOfDay.setUTCHours(0, 0, 0, 0);
      const endOfDay = new Date(eventsDate);
      endOfDay.setUTCHours(23, 59, 59, 999);
      let result;
      let array = [];
      let companyId = user["https://urecruits.com/companyId"];
      const userId = user["https://urecruits.com/userId"];
      let events;

      if (companyId && companyId !== null) {
        // Query events directly and join with EventUser to avoid duplicates
        events = await this.eventModel.findAll({
          where: {
            startGmt: {
              [Op.between]: [startOfDay, endOfDay],
            },
          },
          include: [
            {
              model: EventUser,
              where: {
                userId: userId,
              },
              attributes: [], // Don't select EventUser attributes to avoid confusion
            },
          ],
          // Add distinct to ensure no duplicate events
          group: ['Event.id'],
        });

        events?.forEach((eventInfo) => {
          result = {
            id: eventInfo.id, // Use event ID, not user ID
            startDate: eventInfo.startGmt,
            endDate: eventInfo.endGmt,
            title: eventInfo.title,
            description: eventInfo.description,
            jobId: eventInfo.jobId,
            candidateId: eventInfo.candidateId,
            roomId: eventInfo.roomId,
          };
          array.push(result);
        });
      } else {
        try {
          events = await this.eventModel.findAll({
            where: {
              candidateId: userId,
              startGmt: {
                [Op.between]: [startOfDay, endOfDay],
              },
            },
          });
        } catch (error) {
          // throw new NotFoundException("Event not found.");
        }

        events?.forEach((eventInfo) => {
          result = {
            id: eventInfo.id,
            startDate: eventInfo.startGmt,
            endDate: eventInfo.endGmt,
            title: eventInfo.title,
            description: eventInfo.description,
            jobId: eventInfo.jobId,
            candidateId: eventInfo.candidateId,
            roomId: eventInfo.roomId,
            recruiterId: "",
            remindMe: eventInfo.remindMe,
            timeBefore: eventInfo.timeBefore,
          };
          array.push(result);
        });
      }
      return { events: array };
    } catch (error) {
      console.log(error);
      throw new NotFoundException(error);
    }
  }

  async getEvents(user, date) {
    const id = user["https://urecruits.com/userId"];
    const eventsDate = convertToUTC(date.date);
    const startOfDay = new Date(eventsDate);
    let result;
    let array = [];
    try {
      const event = await this.eventModel
        .findAll({
          where: {
            candidateId: id,
            startGmt: {
              [Op.gte]: startOfDay,
            },
          },
        })
        .catch((e) => {
          throw new NotFoundException("Event not found." + e);
        });
      event?.map((info, index) => {
        const startDate = convertUTCToIST(info.dataValues.startGmt);
        const endDate = convertUTCToIST(info.dataValues.endGmt);
        const startTime = `${new Date(startDate).toLocaleString("en-US", {
          hour: "numeric",
          hour12: true,
        })}`;
        const endTime = `${new Date(endDate).toLocaleString("en-US", {
          hour: "numeric",
          hour12: true,
        })}`;
        result = {
          id: index + 1,
          eventId: info.id,
          startDate: startDate,
          endDate: endDate,
          startTime: startTime,
          endTime: endTime,
          title: info.title,
          description: info.description,
          jobId: info.jobId,
          roomId: info.roomId,
          recruiterId: "",
          candidateId: id,
          remindMe: info.remindMe,
          timeBefore: info.timeBefore,
        };
        array.push(result);
      });
      return array;
    } catch (error) {
      throw new NotFoundException("Error getting candidates events" + error);
    }
  }

  async getAllUsersByEvent(eventId: number) {
    try {
      return await this.eventUserModel.findAll({
        where: {
          eventId
        },
        attributes: ["userId", "calendarEventId"]
      });
    }
    catch (err) {
      Logger.log(err);
    }

  }

  async createEvent(eventData: CreateEventDto) {
    let event;
    try {
      event = await this.eventModel.create(eventData);
      await Promise.all(
        eventData.users.map(async (item) => {
          return await this.eventUserModel.create({
            eventId: event.id,
            userId: item.id,
            calendarEventId: item.calEventId,
          });
        })
      );
      return await this.getEventById(event.id);
    } catch (e) {
      if (event && event.id) {
        await this.eventModel.destroy({
          where: {
            id: event.id,
          },
        });
      }
      Logger.error(`Error in the CalendarModule.\n${e}`);
      throw new HttpException(e, HttpStatus.BAD_REQUEST);
    }
  }

  async getEventByJobId(data, user?) {
    try {
      const isRecruiter = user ? user.permissions.includes("recruiter") : false;
      const isCandidate = user ? user.permissions.includes("candidate") : false;
      const userId = user ? user["https://urecruits.com/userId"] : null;
      const event = await this.eventModel.findOne({
        where: {
          jobId: data.jobId,
          candidateId: data.candidateId,
          description: data.description,
          ...(isCandidate && userId ? { candidateId: userId } : ''),
        },
        include: [
          {
            model: EventUser,
            attributes: ["userId"],
            ...(isRecruiter && userId ? {
              where: {
                userId,
              }
            } : ''),
          },
        ],
      });

      if (!event) {
        throw new HttpException("Event not found.", HttpStatus.NOT_FOUND);
      }
      return { data: event };
    } catch (error) {
      throw new NotFoundException(error);
    }
  }

  async getEventById(eventId, user?) {
    const isRecruiter = user ? user.permissions.includes("recruiter") : false;
    const isCandidate = user ? user.permissions.includes("candidate") : false;
    const userId = user ? user["https://urecruits.com/userId"] : null;
    const event = await this.eventModel.findOne({
      where: {
        id: eventId,
        ...(isCandidate && userId ? { candidateId: userId } : '')
      },
      include: [
        {
          model: EventUser,
          attributes: ["userId"],
          ...(isRecruiter && userId ? {
            where: {
              userId,
            }
          } : ''),
        },
      ],
    });

    if (!event) {
      throw new HttpException("Event not found.", HttpStatus.NOT_FOUND);
    }

    return event;
  }

  async getEventsByUserId(userId) {
    const userEvents = await this.eventModel.findAll({
      include: [
        {
          model: EventUser,
          attributes: [],
          where: {
            userId,
          },
        },
      ],
    });

    if (!userEvents.length) {
      throw new HttpException("Events not found.", HttpStatus.NOT_FOUND);
    }

    return userEvents;
  }

  async deleteEventById(deleteEventDto: DeleteEventDto) {
    const event = await this.getEventById(deleteEventDto.id);

    if (event.eventUsers.length) {
      const userIds = [];
      for (let i = 0; i < event.eventUsers.length; i++) {
        userIds.push(event.eventUsers[i].userId);
      }
      try {
        await this.eventUserModel.destroy({
          where: {
            eventId: deleteEventDto.id,
            userId: userIds,
          },
        });
      } catch (e) {
        Logger.error(`Error in the CalendarModule.\n${e}`);
        throw new HttpException(e, HttpStatus.BAD_REQUEST);
      }
    }

    try {
      await this.eventModel.destroy({
        where: {
          id: deleteEventDto.id,
        },
      });
    } catch (e) {
      Logger.error(`Error in the CalendarModule.\n${e}`);
      throw new HttpException(e, HttpStatus.BAD_REQUEST);
    }

    return event;
  }

  async updateEvent(data) {
    try {
      await this.eventModel.update(data, {
        where: {
          // jobId: data.jobId,
          // candidateId: data.candidateId,
          id: data.eventId,
        },
      });
      return await this.getEventById(data.eventId);
    } catch (e) {
      Logger.error(`Error in the CalendarModule.\n${e}`);
      throw new HttpException(e, HttpStatus.BAD_REQUEST);
    }
  }

  async getEventByRoomId(roomId, user?) {
    const isRecruiter = user ? user.permissions.includes("recruiter") : false;
    const isCandidate = user ? user.permissions.includes("candidate") : false;
    const userId = user ? user["https://urecruits.com/userId"] : null;
    const event = await this.eventModel.findOne({
      where: {
        roomId: roomId,
        ...(isCandidate && userId ? { candidateId: userId } : '')
      },
      include: [
        {
          model: EventUser,
          attributes: ["userId"],
          ...(isRecruiter && userId ? {
            where: {
              userId,
            }
          } : ''),
        },
      ],
    });

    if (!event) {
      throw new HttpException("Event not found.", HttpStatus.NOT_FOUND);
    }

    return event;
  }

  async addInterviewers(roomId, interviewers) {
    try {
      const event = await this.getEventByRoomId(roomId);
      await event.update({
        interviewers: event.interviewers.concat(interviewers)
      })
      return await event.save();
    }
    catch (error) {
      Logger.log("error while get event by roomId in calendar service");
      throw new HttpException("Event could't be get", HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async updateEventStatusByRoomId(roomId, status) {
    try {
      const event = await this.getEventByRoomId(roomId);
      await event.update({
        status,
      })
      return await event.save();
    } catch (e) {
      Logger.error(`Error in the CalendarModule.\n${e}`);
      throw new HttpException(e, HttpStatus.BAD_REQUEST);
    }
  }
}


function convertUTCToIST(utcTimeString) {
  const utcMoment = moment.utc(utcTimeString);
  const istOffset = "+05:30";
  const istMoment = utcMoment.utcOffset(istOffset);
  const istTimeString = istMoment.format("YYYY-MM-DDTHH:mm:ss.SSS+05:30");
  return istTimeString;
}
function convertToUTC(inputDate) {
  const dateObject = new Date(inputDate);
  const utcDate = dateObject.toISOString();
  return utcDate;
}
