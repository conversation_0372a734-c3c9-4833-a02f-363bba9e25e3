export const kinesisDeliveryStreams = {
    user: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_USERS,
    // users: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_USERS,
    company: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_COMPANY,
    job: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_JOBS,
    jobs: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_JOBS,
    assessment: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_ASSESSMENTS,
    "live-coding": process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_ASSESSMENTS,
    "take-home": process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_ASSESSMENTS,
    "domain-assessments": process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_ASSESSMENTS,
    subscription: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION,
    subAddon: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_SUBSCRIPTION_ADDON,
    payment: process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME_PAYMENTS,
}