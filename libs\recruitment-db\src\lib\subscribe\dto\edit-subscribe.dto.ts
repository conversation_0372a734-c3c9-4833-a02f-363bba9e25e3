import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { SubscribeDto } from "./subscribe.dto";
import { IsOptional } from "class-validator";

export class EditSubscribeDto extends SubscribeDto {
  @ApiProperty({ example: "1", description: "Job ID" })
  jobId: number;

  @ApiProperty({ example: "1", description: "ID" })
  id: number;
}

export class EditStatusDto {
  @ApiProperty({ example: "Pending", description: "Status" })
  status: string;

  @ApiProperty({ example: "1", description: "ID" })
  id: number;
}

export class EditJobsStatus {
  @ApiPropertyOptional({ example: true, description: "change apply job status" })
  @IsOptional()
  applyJob?: boolean;

  @ApiPropertyOptional({ example: true, description: "change saved job status" })
  @IsOptional()
  saveJob?: boolean;

  @ApiPropertyOptional({ example: true, description: "change subscribe job status" })
  @IsOptional()
  subscribeJob?: boolean;

}

export class RestartWorkflowDto{
  @ApiProperty({ example: "1", description: "job Id" })
  jobId: number;

  @ApiProperty({ example: "1", description: "Candidate Id" })
  userId: number;
}