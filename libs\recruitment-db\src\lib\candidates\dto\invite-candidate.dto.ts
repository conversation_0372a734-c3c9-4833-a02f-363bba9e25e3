import { ApiProperty } from "@nestjs/swagger";

export class InviteCandidateDto {
    @ApiProperty({ example: "", description: "First name of a candidate" })
    readonly firstname: string;

    @ApiProperty({ example: "", description: "Last name of a candidate" })
    readonly lastname: string;

    @ApiProperty({ example: "1234365464", description: "Phone number of candidate" })
    readonly phone: string;

    @ApiProperty({ example: "<EMAIL>", description: "email of the candidate" })
    readonly email: string;
}

export class CandidateVerificationDto {
    @ApiProperty({ example: "123456qQ@", description: "Ticket" })
    readonly inviteLink: string;

    @ApiProperty({ example: "123456qQ@", description: "New Password" })
    readonly newPassword: string;
}

export class ResendActivationMailDto {
    @ApiProperty({ example: "12", description: "Id of a candidate" })
    readonly candidateId: number;
}
