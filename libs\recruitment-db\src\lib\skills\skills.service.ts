import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Skill } from "./skills.model";
import { UpdateSkillsDto } from "./dto/update-skills.dto";
import { SkillsDto } from "./dto/skills.dto";

@Injectable()
export class SkillsService {
  constructor(@InjectModel(Skill) private skillRepository: typeof Skill) {}

  async create(candidateId: number, dto?: SkillsDto) {
    return await this.skillRepository.create({
      ...dto,
      candidateId: candidateId,
    });
  }

  async update(dto: UpdateSkillsDto) {
    const data = await this.get(dto.id, dto.candidateId);
    Object.assign(data, dto);
    return await data.save();
  }

  async delete(dto: UpdateSkillsDto) {
    const data = await this.get(dto.id, dto.candidateId);
    await data.destroy();
    return true;
  }

  async get(id: number, candidateId: number) {
    const data = await this.skillRepository.findOne({
      where: { id, candidateId },
    });
    if (!data) {
      throw new HttpException("Skill not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }
}
