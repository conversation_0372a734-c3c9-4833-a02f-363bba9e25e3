import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { LangchainModule } from '@microservices/integrations';
import { Company } from '../companies/companies.model';
import { Workflow } from '../workflow/workflow.model';
import { Jobs } from '../job/job.model';
import { WorkflowChatbotService } from './workflow-chatbot.service';
import { HttpModule } from '@nestjs/axios';


@Module({
  imports: [SequelizeModule.forFeature([Company,Workflow,Jobs]),LangchainModule,HttpModule],
  providers: [WorkflowChatbotService],
  exports: [SequelizeModule, WorkflowChatbotService],
})
export class WorkflowChatbotModule {}
