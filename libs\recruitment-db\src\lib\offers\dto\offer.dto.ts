import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { FilterCompanyJobsDto } from "../../job/dto/filter-job.dto";

export class OfferDto {
  @ApiProperty({ example: "1", description: "Job ID" })
  jobId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  userId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  subscribeId: number;

  @ApiProperty({ example: "1", description: "Round ID" })
  @IsOptional()
  roundId: number;

  @ApiPropertyOptional({ example: "Pending", description: "Status" })
  status: string;

  @ApiPropertyOptional({
    example: "Matthew",
    description: "Candidate First Name",
  })
  @IsOptional()
  candidateFirstname: string;

  @ApiPropertyOptional({
    example: "<PERSON>",
    description: "Candidate Last Name",
  })
  @IsOptional()
  candidateLastname: string;

  @ApiPropertyOptional({ example: "Matthew", description: "Candidate Email" })
  @IsOptional()
  candidateEmail: string;

  @ApiPropertyOptional({
    example: "****** 323 2323",
    description: "Candidate Phone number",
  })
  @IsOptional()
  candidatePhone: string;

  @ApiPropertyOptional({
    example: "100",
    description: "Candidate Phone number",
  })
  @IsOptional()
  ctc: string;

  @ApiPropertyOptional({
    example: "100",
    description: "Candidate Phone number",
  })
  @IsOptional()
  candidateAddress: string;

  @ApiPropertyOptional({ example: "100", description: "Job Title" })
  @IsOptional()
  jobTitle: string;

  @ApiPropertyOptional({ example: "100", description: "Company" })
  @IsOptional()
  companyName: string;

  @ApiPropertyOptional({
    example: "Offer Letter",
    description: "Email Title during Signature Request",
  })
  @IsOptional()
  signingTitle: string;

  @ApiPropertyOptional({
    example: "Sign the Document",
    description: "Email Subject during Signature Request",
  })
  @IsOptional()
  signingSubject: string;

  @ApiPropertyOptional({
    example: "Congratulatulations!!! You received Offer from...",
    description: "Email Message during Signature Request",
  })
  @IsOptional()
  signingMessage: string;

  @ApiPropertyOptional({ example: "1", description: "Location id" })
  @IsOptional()
  locationId: number;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Joining date" })
  @IsOptional()
  joiningDate: Date;

  @ApiPropertyOptional({
    example: "https://www.youtube.com/",
    description: "pdf Url",
  })
  @IsOptional()
  pdfKey: string;

  @ApiPropertyOptional({ example: "youtube.pdf", description: "pdf Name" })
  @IsOptional()
  pdfName: string;

  @ApiPropertyOptional({ example: [], description: "Signatures" })
  @IsOptional()
  signatures: any;
}

export class FilterCompanyJobsOffersDto extends FilterCompanyJobsDto {
  @ApiPropertyOptional({ example: [], description: "Job Location" })
  @IsOptional()
  locations?: [string];
}
export class FilterCompanCandidatesOffersDto {
  @ApiPropertyOptional({
    example: ["approved"],
    description: "candidateStatus",
  })
  @IsOptional()
  candidateStatus?: [string];

  @ApiPropertyOptional({
    example: ["approved"],
    description: "candidateStatus",
  })
  @IsOptional()
  letterStatus?: [string];

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date From" })
  @IsOptional()
  joiningDateFrom?: string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date From" })
  @IsOptional()
  joiningDateTo?: string;

  @ApiPropertyOptional({ example: "Designer", description: "Search" })
  @IsOptional()
  search?: string;

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiProperty({ example: "51", description: "Approver Id" })
  @IsOptional()
  approvedBy?: number;

  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;
}

export class EditOfferDto extends OfferDto {
  @ApiProperty({ example: "1", description: "ID" })
  id: number;

  @ApiProperty({ example: 'accepted', description: 'candidate status for offer' })
  @IsOptional()
  candidateStatus?: string
}
