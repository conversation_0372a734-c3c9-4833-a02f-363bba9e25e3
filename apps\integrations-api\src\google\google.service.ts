import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import * as talent from '@google-cloud/talent';
import { JobDto } from './dto/job.dto';

/**
 * Job Search hierarchy:
 * 1. Tenant -  uRecruits.
 *              Tenant can include many Companies.
 * 2. Company - for now uRecruits only, in the future can be any company registered in uRecruits database.
 *              Each company can include many Jobs.
 * 3. Job
 */

@Injectable()
export class GoogleService {
  private readonly keyFilename = './urecruits-2021-67e4158d62b6.json';
  private readonly projectId = 'urecruits-2021';
  private readonly defaultTenantId = '2cc9ad17-3f00-0000-0000-00fce8b59334';
  private readonly defaultCompanyId = '53d2f60e-82dc-4b86-a0d5-d4c2c703a3bb';

  /**
   * Create a job.
   *
   * @param dto {JobDto}
   *
   * @throws Error
   *
   * @returns {object}
   */
  async createJob(dto: JobDto) {
    const client = new talent.JobServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedParent = client.projectPath(this.projectId);
    const job = {
      company: this.defaultCompanyId,
      requisitionId: dto.requisitionId,
      title: dto.title,
      description: dto.description,
    };
    if (dto.jobApplicationUrl) {
      job['applicationInfo'] = {
        uris: [dto.jobApplicationUrl],
      };
    }
    if (dto.address) {
      job['addresses'] = [dto.address];
    }
    if (dto.languageCode) {
      job['languageCode'] = dto.languageCode;
    }

    const request = {
      parent: formattedParent,
      job,
    };

    const response = await client.createJob(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   *
   * @param jobId
   * @param dto
   *
   */
  async updateJob(jobId: string, dto: JobDto) {
    const client = new talent.JobServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedName = client.jobPath(this.projectId, this.defaultTenantId,
      jobId);
    const job = {
      name: formattedName,
      company: this.defaultCompanyId,
      requisitionId: dto.requisitionId,
      title: dto.title,
      description: dto.description,
    };
    if (dto.jobApplicationUrl) {
      job['applicationInfo'] = {
        uris: [dto.jobApplicationUrl],
      };
    }
    if (dto.address) {
      job['addresses'] = [dto.address];
    }
    if (dto.languageCode) {
      job['languageCode'] = dto.languageCode;
    }

    const request = {
      job,
    };

    const response = await client.updateJob(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * Delete a Job.
   *
   * @param jobId {string} Job ID
   *
   * @throws Error
   *
   * @returns {object}
   */
  async deleteJob(jobId) {
    const client = new talent.JobServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedName = client.jobPath(this.projectId, this.defaultTenantId,
      jobId);
    const request = {
      name: formattedName,
    };

    const response = await client.deleteJob(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * List Jobs.
   *
   * @param requisitionId {string}
   *
   * @throws Error
   *
   * @returns {array}
   */
  async listJobs(requisitionId: string) {
    const client = new talent.JobServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedParent = client.projectPath(this.projectId);
    let filter = `companyName = "projects/${this.projectId}/companies/${this.defaultCompanyId}"`;
    if (requisitionId) {
      filter += ` AND requisitionId ="${requisitionId}"`;
    }
    const request = {
      parent: formattedParent,
      filter,
    };

    const response = await client.listJobs(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * Retrieve a Job.
   *
   * @param jobId {string} Job ID
   *
   * @throws Error
   *
   * @returns {object}
   */
  async retrieveJob(jobId: string) {
    const client = new talent.JobServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedName = client.jobPath(this.projectId, this.defaultTenantId,
      jobId);
    const request = {
      name: formattedName,
    };

    const response = await client.getJob(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * List Tenants.
   *
   * @throws Error
   *
   * @returns {array}
   */
  async listTenants() {
    const client = new talent.TenantServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedParent = client.projectPath(this.projectId);
    const request = {
      parent: formattedParent,
    };

    const response = await client.listTenants(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * Create a Company.
   * For now only uRecruits, in the future it can be any company registered in the uRecruits database.
   * Each company can include many Jobs.
   *
   * @param displayName {string} Company Name
   * @param externalId {string} Identifier of this company on uRecruits side
   *
   * @throws Error
   *
   * @returns {object}
   */
  async createCompany(displayName, externalId) {
    const client = new talent.CompanyServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedParent = client.tenantPath(this.projectId,
      this.defaultTenantId);
    const company = {
      displayName,
      externalId,
    };
    const request = {
      parent: formattedParent,
      company,
    };

    const response = await client.createCompany(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * List Companies.
   *
   * @throws Error
   *
   * @returns {array}
   */
  async listCompanies() {
    const client = new talent.CompanyServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedParent = client.tenantPath(this.projectId,
      this.defaultTenantId);
    const request = {
      parent: formattedParent,
    };

    const response = await client.listCompanies(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * Delete a Company.
   *
   * @param companyId {string} Company ID
   *
   * @throws Error
   *
   * @returns {object}
   */
  async deleteCompany(companyId) {
    const client = new talent.CompanyServiceClient({
      keyFilename: this.keyFilename,
    });

    const formattedName = client.companyPath(this.projectId,
      this.defaultTenantId, companyId);
    const request = {
      name: formattedName,
    };

    const response = await client.deleteCompany(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }
}
