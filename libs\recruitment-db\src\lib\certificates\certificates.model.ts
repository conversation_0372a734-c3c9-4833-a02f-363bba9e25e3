import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Candidate } from '../candidates/candidates.model'

interface CertificateAttrs {
  certificateName?: string;
  instituteName?: string;
  validityStart?: string;
  validityEnd?: string;
  expireDate?: string;
  notes?: string;
  fileName?: string;
  fileKey?: string;
  candidateId: number;
}

@Table({ tableName: 'certificates', createdAt: false, updatedAt: false })
export class Certificate extends Model<Certificate, CertificateAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: 'Adobe Certified Expert-Adobe Commerce',
    description: 'Certificate Name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  certificateName: string;

  @ApiProperty({
    example: 'Adobe',
    description: 'Institute name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  instituteName: string;

  @ApiProperty({
    example: '2021-12-28T22:00:00.000Z',
    description: 'Certificate validity start',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  validityStart: string;

  @ApiProperty({
    example: '2021-12-28T22:00:00.000Z',
    description: 'Certificate validity end',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  validityEnd: string;

  @ApiProperty({
    example: '2021-12-28T22:00:00.000Z',
    description: 'Expire date',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  expireDate: string;

  @ApiProperty({
    example: 'Notes',
    description: 'Notes',
  })
  @Column({ type: DataType.TEXT, allowNull: true })
  notes: string;

  @ApiProperty({
    example: 'file.pdf',
    description: 'File name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  fileName: string;

  @ApiProperty({
    example: '213122 - file.pdf',
    description: 'File key',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  fileKey: string;

  @ApiProperty({ example: '1', description: 'Candidate ID' })
  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @BelongsTo(() => Candidate)
  candidate: Candidate;
}
