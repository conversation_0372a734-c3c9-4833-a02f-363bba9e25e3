import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { OfferModel } from "./offer.model";
import {
  EditOfferDto,
  FilterCompanCandidatesOffersDto,
  FilterCompanyJobsOffersDto,
  OfferDto,
} from "./dto/offer.dto";
import { Op, Sequelize } from "sequelize";
import { Jobs } from "../job/job.model";
import { Location } from "../locations/location.model";
import { User } from "../users/users.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { FileService } from "apps/pre-recruitment-api/src/file/file.service";
import puppeteer from "puppeteer";
import { LetterStatus, CandidateStatus } from "./offer.enum";
import { HellosignTemplate } from "./hellosign-template.model";
import { Company } from "../companies/companies.model";
import { TemplateDto } from "./dto/template.dto";
import { HellosignService } from "../integrations/hellosign/hellosign.service";
import { Round } from "../rounds/round.model";
import { EmailService } from "@microservices/email";
import candidateMailData from "./mailData/candidate.mail.data";
import recuiterMailData from "./mailData/recuiter.mail.data";
import { TemporalClientService } from "apps/temporal/src/app/workflow/temporal-client/temporalClient.services";
import { roundCompletedSignal } from "apps/temporal/src/app/workflow/temporal/workflows";
import { JobTargetService } from "../jobtarget/jobtarget.service";

@Injectable()
export class OfferService {
  constructor(
    private readonly hellosignService: HellosignService,
    private readonly fileService: FileService,
    private readonly emailService: EmailService,
    @InjectModel(OfferModel) private offerRepository: typeof OfferModel,
    @InjectModel(HellosignTemplate)
    private hellosignTemplate: typeof HellosignTemplate,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    @InjectModel(Subscribe) private subscribe: typeof Subscribe,
    @InjectModel(User) private userRepository: typeof User,
    private readonly temploralClientService: TemporalClientService,
    private readonly jobTargetService: JobTargetService
  ) {}

  async create(dto: OfferDto, companyId: number) {
    const data = await this.offerRepository.findOne({
      where: {
        jobId: dto.jobId,
        roundId: dto?.roundId,
        userId: dto.userId,
        companyId,
        letterStatus: {
          [Op.not]: "closed",
        },
      },
      attributes: ["id"],
    });

    if (data) {
      throw new HttpException("offer already exists!", HttpStatus.CONFLICT);
    } else {
      const offer = await this.offerRepository.create({ ...dto, companyId });
      await this.subscribe.update(
        { offerId: offer.id },
        {
          where: {
            jobId: dto.jobId,
            userId: dto.userId,
            id: dto.subscribeId,
          },
        }
      );
      return offer;
    }
  }

  async getOffer(id: number, companyId: number) {
    return await this.offerRepository.findOne({
      where: {
        id,
        companyId,
      },
      include: [
        {
          model: User,
          as: "approver",
          attributes: ["firstname", "middlename", "lastname"],
        },
        { model: Location },
      ],
    });
  }

  async deleteOffer(id: number, companyId: number) {
    return await this.offerRepository.destroy({
      where: {
        companyId,
        id,
      },
    });
  }

  async deleteCandidateAllOffer(
    userId: number,
    jobId: number,
    companyId: number
  ) {
    return await this.offerRepository.destroy({
      where: {
        companyId,
        jobId,
        userId,
      },
    });
  }

  async getOffers(jobId: number, userId: number, companyId: number) {
    return await this.offerRepository.findAndCountAll({
      where: {
        jobId,
        userId,
        companyId,
      },
      order: [["createdAt", "ASC"]],
      include: [
        {
          model: User,
          as: "approver",
          attributes: ["firstname", "middlename", "lastname"],
        },
        { model: Location },
      ],
    });
  }

  async updateById(offerId: number, dto: EditOfferDto, companyId: number) {
    const data = await this.offerRepository.findOne({
      where: {
        id: offerId,
        companyId,
      },
    });

    Object.assign(data, {
      ...dto,
      userId: data.userId,
      jobId: data.jobId,
      companyId,
      roundId: data.roundId,
    });
    return data.save();
  }
  async edit(dto: EditOfferDto, companyId: number) {
    const data = await this.offerRepository.findOne({
      where: {
        jobId: dto.jobId,
        roundId: dto.roundId,
        userId: dto.userId,
        companyId,
      },
    });
    Object.assign(data, {
      ...dto,
      userId: data.userId,
      jobId: data.jobId,
      companyId,
      roundId: data.roundId,
    });
    return data.save();
  }

  async getAllCompanyJobsOffers(
    dto: FilterCompanyJobsOffersDto,
    companyId: number
  ) {
    const where: any = {
      companyId,
      isAssessment:false
    };
    const locationWhere: any = {};
    const order = [];
    if (dto.search) {
      where.title = {
        [Op.iLike]: `%${dto.search}%`,
      };
    }
    if (dto.status) {
      where.status = {
        [Op.iLike]: `%${dto.status}%`,
      };
    }

    if (dto.dateFrom && dto.dateTo) {
      where.createdAt = {
        [Op.between]: [new Date(dto.dateFrom), new Date(dto.dateTo)],
      };
    } else if (dto.dateFrom || dto.dateTo) {
      if (dto.dateFrom) {
        where.createdAt = {
          [Op.gte]: new Date(dto.dateFrom),
        };
      }
      if (dto.dateTo) {
        where.createdAt = {
          [Op.lte]: new Date(dto.dateTo),
        };
      }
    }

    if (dto.locations) {
      if (!Array.isArray(dto.locations)) {
        locationWhere.id = { [Op.eq]: dto.locations };
      } else {
        locationWhere.id = { [Op.in]: dto.locations };
      }
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }

    const candidateApprovedQuery = `(SELECT COUNT(*) FROM offers WHERE offers."jobId" = "Jobs"."id" AND offers."candidateStatus" ILIKE '%${CandidateStatus.ACCEPTED}%')`;
    const candidatePendingQuery = `(SELECT COUNT(*) FROM offers WHERE offers."jobId" = "Jobs"."id" AND offers."candidateStatus" ILIKE '%${CandidateStatus.PENDING}%')`;
    const candidateRejectedQuery = `(SELECT COUNT(*) FROM offers WHERE offers."jobId" = "Jobs"."id" AND offers."candidateStatus" ILIKE '%${CandidateStatus.REJECTED}%')`;
    const recruiterApprovedQuery = `(SELECT COUNT(*) FROM offers WHERE offers."jobId" = "Jobs"."id" AND offers."letterStatus" ILIKE '%${LetterStatus.APPROVED}%')`;
    const recruiterPendingQuery = `(SELECT COUNT(*) FROM offers WHERE offers."jobId" = "Jobs"."id" AND offers."letterStatus" ILIKE '%${LetterStatus.PENDING}%')`;
    const recruiterSendQuery = `(SELECT COUNT(*) FROM offers WHERE offers."jobId" = "Jobs"."id" AND offers."letterStatus" ILIKE '%${LetterStatus.SENT}%')`;

    return await this.jobRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where,
      order,
      attributes: [
        "id",
        "title",
        "salaryYearMin",
        "salaryYearMax",
        "createdAt",
        "status",
        "authorId",
        "remoteLocation",
        "workflowId",
        "numberOpenings",
        [Sequelize.literal(recruiterPendingQuery), "recruiterPendingCount"],
        [Sequelize.literal(recruiterApprovedQuery), "recruiterApprovedCount"],
        [Sequelize.literal(recruiterSendQuery), "recruiterSendCount"],
        [Sequelize.literal(candidateApprovedQuery), "candidateApprovedCount"],
        [Sequelize.literal(candidatePendingQuery), "candidatePendingCount"],
        [Sequelize.literal(candidateRejectedQuery), "candidateRejectedCount"],
      ],
      include: [
        {
          model: Location,
          where: locationWhere,
        },
        {
          model: Subscribe,
          include: [
            {
              model: User,
              as: "user",
            },
          ],
        },
      ],
    });
  }

  async getCompanyCandidatesOffers(
    dto: FilterCompanCandidatesOffersDto,
    companyId: number,
    jobId: number
  ) {
    if (
      !(await this.jobRepository.findOne({ where: { companyId, id: jobId ,isAssessment:false} }))
    ) {
      throw new NotFoundException();
    }

    const where: any = {};
    let hasFilterCriteria = false;
    const order = [];
    if (dto.search) {
      where[Op.or] = [
        { candidateFirstname: { [Op.iLike]: `%${dto.search}%` } },
        { candidateLastname: { [Op.iLike]: `%${dto.search}%` } },
      ];
    }

    if (dto.candidateStatus) {
      if (!Array.isArray(dto.candidateStatus)) {
        where.candidateStatus = { [Op.eq]: dto.candidateStatus };
      } else {
        where.candidateStatus = { [Op.in]: dto.candidateStatus };
      }
    }

    if (dto.letterStatus) {
      if (!Array.isArray(dto.letterStatus)) {
        where.letterStatus = { [Op.eq]: dto.letterStatus };
      } else {
        where.letterStatus = { [Op.in]: dto.letterStatus };
      }
    }

    if (dto.joiningDateFrom && dto.joiningDateTo) {
      where.joiningDate = {
        [Op.between]: [
          new Date(dto.joiningDateFrom),
          new Date(dto.joiningDateTo),
        ],
      };
    } else if (dto.joiningDateFrom || dto.joiningDateTo) {
      if (dto.joiningDateFrom) {
        where.joiningDate = {
          [Op.gte]: new Date(dto.joiningDateFrom),
        };
      }
      if (dto.joiningDateTo) {
        where.joiningDate = {
          [Op.lte]: new Date(dto.joiningDateTo),
        };
      }
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["createdAt", "DESC"]);
    }

    if (
      dto.search ||
      dto.candidateStatus ||
      dto.letterStatus ||
      dto.joiningDateFrom ||
      dto.joiningDateTo
    ) {
      hasFilterCriteria = true;
    }

    const offerSubQuery = `(SELECT id FROM round WHERE round."jobId" = "Subscribe"."jobId" AND round."userId" = "Subscribe"."userId" LIMIT 1)`;

    return await this.subscribe.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where: { jobId },
      attributes: {
        include: [[Sequelize.literal(offerSubQuery), "roundId"]],
      },
      include: [
        {
          model: OfferModel,
          where,
          order,
          required: hasFilterCriteria,
          include: [
            {
              model: User,
              as: "approver",
              attributes: ["authId", "firstname", "lastname"],
            },
          ],
        },
        {
          model: User,
        },
        {
          model: Round,
        },
      ],
    });
  }

  async getRecentCompanyOffer(companyId: number) {
    try {
      return await OfferModel.findAll({
        where: {
          companyId,
          letterStatus: LetterStatus.SENT,
        },
        attributes: ["id", "jobId", "candidateStatus", "createdAt"],
        include: [
          {
            model: User,
            as: "user",
            attributes: ["id", "firstname", "lastname", "avatar"],
          },
          {
            model: Jobs,
            where:{isAssessment:false},
            attributes: ["id", "title"],
          },
        ],
        order: [["createdAt", "DESC"]],
      });
    } catch (error) {
      Logger.error(error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async SendSignatureRequest(companyId, offerId) {
    const offerDetails = await this.offerRepository.findOne({
      where: {
        id: offerId,
        companyId,
      },
      include: [
        {
          model: HellosignTemplate,
        },
        {
          model: Jobs,
          include: [
            {
              model: User,
              as: 'author',
              attributes: ['email']
            }
          ]
        }
      ],
    });
    const candidateName =
      offerDetails.candidateFirstname + " " + offerDetails.candidateLastname;
    const signers = [
      {
        name: candidateName,
        role: "Candidate",
        emailAddress: offerDetails.candidateEmail,
      },
      ...offerDetails?.hellosignTemplate?.signers.map((signer) => ({
        role: signer.label,
        name: signer.label,
        emailAddress: signer.emailAddress,
      })),
    ];

    const template = await this.hellosignService.getTemplate(
      offerDetails?.hellosignTemplate?.templateId
    );

    const customFields = template?.response?.data?.template?.custom_fields;
    const mergeFields = customFields.map((field) => ({
      name: field.name,
      value: offerDetails[field.name] ? `${offerDetails[field.name]}`.substr(0, 15)||"" : "",
    }));

    try {
      const signatureResponse = await this.hellosignService.sendSignatureRequest(
        signers,
        mergeFields,
        offerDetails?.hellosignTemplate?.templateId,
        {
          title: offerDetails.signingTitle,
          subject: offerDetails.signingSubject,
          message: offerDetails.signingMessage,
        }
      );

      // JobTarget tracking: Update applicant status to "Offer Made" when offer letter is sent for signing
      if (offerDetails?.jobId && offerDetails?.userId && offerDetails?.job?.author?.email) {
        try {
          // Get the subscribe record to find JT_GUID
          const subscribe = await this.subscribe.findOne({
            where: {
              jobId: offerDetails.jobId,
              userId: offerDetails.userId
            },
            attributes: ['JT_GUID']
          });

          if (subscribe?.JT_GUID) {
            await this.jobTargetService.updateApplicantStatus(
              subscribe.JT_GUID,
              "Offer Made",
              offerDetails.job.author.email
            );
            Logger.log(`JobTarget applicant status updated to "Offer Made" for GUID: ${subscribe.JT_GUID}, Offer ID: ${offerId}`);
          }
        } catch (error) {
          Logger.error(`Failed to update JobTarget applicant status to "Offer Made": ${error.message}`);
        }
      }

      return signatureResponse;
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async getSignatureRequest(signatureRequestId) {
    try {
      const response = await this.hellosignService.getSignatureRequest(
        signatureRequestId
      );
      return response.response.data;
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async getSignatureDocumentPreview(offerId, companyId) {
    try {
      const offer = await this.offerRepository.findOne({
        where: { id: offerId, companyId },
        include: [
          {
            model: HellosignTemplate,
          },
        ],
      });

      if (offer.dataValues?.hellosignSignatureRequestId) {
        const response =
          await this.hellosignService.getSignatureDocumentPreview(
            offer.dataValues?.hellosignSignatureRequestId
          );
        return response.body;
      } else if (offer.dataValues?.hellosignTemplateId) {
        const response = await this.hellosignService.getTemplateDocumentPreview(
          offer.dataValues?.hellosignTemplate?.templateId
        );
        return response.body;
      } else {
        throw new NotFoundException("Offer Letter Is Not Generated");
      }
      return {};
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async getTemplate(id: number, companyId: number) {
    return await this.hellosignTemplate.findOne({
      where: {
        id,
        companyId,
      },
    });
  }

  async getTemplates(companyId: number) {
    return await this.hellosignTemplate.findAll({
      where: {
        companyId,
        templateId: { [Op.ne]: null },
      },
    });
  }

  async deleteTemplate(templateId: string, companyId: number) {
    try {
      const template = await this.hellosignTemplate.findOne({
        where: {
          id: templateId,
          companyId,
        },
      });

      if (!template) {
        throw new NotFoundException("Tempalte Not Found");
      }
      await this.hellosignService.templateDelete(
        template.dataValues.templateId
      );

      return await this.hellosignTemplate.destroy({
        where: {
          id: templateId,
          companyId,
        },
      });
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error,
        "Failed to Delete Template"
      );
    }
  }

  async updateTemplate(dto: TemplateDto, id: number, companyId: number) {
    const data = await this.hellosignTemplate.findOne({
      where: {
        id,
        companyId,
      },
    });

    Object.assign(data, {
      ...dto,
      companyId,
    });
    return data.save();
  }

  async createTemplate(dto: TemplateDto, companyId: number) {
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 15);
    const Key: string = `company/${companyId}/${timestamp}_${randomString}_offer_template`;
    try {
      const data =
        dto.templateId &&
        (await this.hellosignTemplate.findOne({
          where: {
            templateId: dto.templateId,
            companyId,
          },
          attributes: ["id"],
        }));

      if (data) {
        throw new HttpException(
          "Template already exists!",
          HttpStatus.CONFLICT
        );
      } else {
        const file = await this.htmlToPdf(dto.templateBody);
        const fileDetails = await this.fileService.uploadOnS3({
          Bucket: process.env.UPLOADSPRIVATE_NAME,
          Key,
          Body: file,
          ContentType: "application/pdf",
          ACL: "private",
        });

        const location = await this.fileService.getFromS3({
          Bucket: process.env.UPLOADSPRIVATE_NAME,
          Key,
        });

        const signers = [
          {
            name: "Candidate",
            order: 0,
          },
          ...dto.signers.map((signer) => ({
            name: signer.label,
            order: 1,
          })),
        ];
        const data = await this.hellosignTemplate.create({ ...dto, companyId });
        const template =
          await this.hellosignService.createEmbeddedTemplateDraft(
            signers,
            location,
            {
              title: dto.templateName,
              subject: dto.templateName,
              message: dto.templateDescription,
            }
          );
        await this.fileService.removeFile({
          Bucket: process.env.UPLOADSPRIVATE_NAME,
          Key,
        });
        return { template, data };
      }
    } catch (error) {
      Logger.log("Error : ", error);
      await this.fileService.removeFile({
        Bucket: process.env.UPLOADSPRIVATE_NAME,
        Key,
      });
      throw new InternalServerErrorException(error);
    }
  }

  async htmlToPdf(htmlContent) {
    try {
      const browser = await puppeteer.launch({
        executablePath:
          "/usr/bin/chromium-browser" || "/usr/lib/chromium/" || null,
        args: ["--no-sandbox","--disable-gpu","--disable-dev-shm-usage"],
        dumpio: true,
      });
      const page = await browser.newPage();
      await page.setContent(htmlContent);
      const pdfBuffer = await page.pdf({ format: "A4" });
      await browser.close();
      return pdfBuffer;
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(error);
    }
  }

  async getHellosignUrl(offerId: number, userId: number, companyId?: number) {
    const offer = await this.offerRepository.findOne({
      where: {
        id: offerId,
        hellosignSignatureRequestId: {
          [Op.not]: null,
        },
      },
    });
    const user = await this.userRepository.findOne({
      where: {
        id: userId,
      },
    });
    if (!offer) {
      throw new NotFoundException("Offer not found");
    }
    const {
      response: {
        data: {
          signature_request: { signatures },
        },
      },
    } = await this.hellosignService.getSignatureRequest(
      offer.dataValues.hellosignSignatureRequestId
    );

    const signPerson = signatures.find(
      (person) => person.signer_email_address === user.dataValues.email
    );

    const {
      response: {
        data: { embedded },
      },
    } = await this.hellosignService.getSignUrl(signPerson.signature_id);

    return embedded;
  }

  async sendMailOnHelloSignEvent(body) {
      try {
        const event = body?.event;
        const signers = body?.signature_request?.signatures;

        const offerData = await this.offerRepository.findOne({
          where: {
            hellosignSignatureRequestId:
              body?.signature_request?.signature_request_id,
          },
          include: [{ model: Company, attributes: ["id", "name", "avatar"] }],
        });
        if (event.event_type === "signature_request_all_signed") {
          //TODO: send singal -> roundCompletd
          console.log("All signers have signed!!!!!!");
          const handle = await this.temploralClientService.getHandle(
            offerData.jobId,
            offerData.userId
          );
          await handle.signal(roundCompletedSignal, "Offer round completed signal");
          return "Hello API Event Received";
        }

        const candidate = signers.find(
          (signer) => signer.signer_role === "Candidate"
        );

        const getSignUrl = (role) => {
          return `${process.env.WEB_APPS_URL}/${role}/offers/sign/${offerData.id}`;
        };

        if (event.event_type === "signature_request_sent") {
          console.log("signature_request_sent");
          //sent email to candidate
          this.emailService.sendRecruitmentActivityService(
            "jobDetail.html",
            {
              companyLogo: offerData.dataValues.company.avatar,
              position: "",
              body: candidateMailData(
                "Offer Letter",
                candidate.signer_name,
                offerData.jobTitle,
                getSignUrl("candidate")
              ),
              userId:offerData.userId
            },
            candidate.signer_email_address,
            "Offer Letter Sign Request", {
              link:   `/candidate/offers/sign/${offerData.id}`,
              notificationTitle: "Your Offer Letter is Ready for Signing",
              notificationMsg:`Congratulations! An offer letter has been sent for the ${offerData?.jobTitle} position at ${offerData?.company?.name}. Please review and sign it to confirm your acceptance.`,
          }
          );
        } else if (
          event.event_type === "signature_request_signed" &&
          signers.find((signer) => signer.status_code !== "signed")
        ) {
          if (
            event.event_metadata.related_signature_id === candidate.signature_id
          ) {
            //change canidateStatus in the offer table
            await this.offerRepository.update(
              { candidateStatus: CandidateStatus.ACCEPTED },
              {
                where: {
                  id: offerData.id,
                },
              }
            );
          }

          //sent email to all other authorized persons
          const recuiter = signers.find(
            (signer) => signer.status_code !== "signed"
          );

          const user = await this.userRepository.findOne({where:{email:recuiter.signer_email_address},attributes:["id","firstname","lastname"]});
          
          this.emailService.sendRecruitmentActivityService(
            "jobDetail.html",
            {
              companyLogo: offerData.company.avatar,
              position: "",
              body: recuiterMailData(
                "Offer Letter",
                recuiter.signer_name,
                offerData.jobTitle,
                getSignUrl("recruitment")
              ),
              userId:user.id
            },
            recuiter.signer_email_address,
            "Offer Letter Signed by Candidate",{
              link: `/recruitment/offers/sign/${offerData.id}`,
              notificationTitle:'Candidate Has Signed – Your Signature Needed',
              notificationMsg:`${candidate.signer_name} has signed the offer letter for the ${offerData?.jobTitle} position at ${offerData?.company?.name}. Your approval is required to complete the process.`
            }
          );
        }
        return "Hello API Event Received";
      } catch (error) {
        Logger.log("Error occured while processing hellosign webhook: ", error);
        return "Hello API Event Received";
      }    
  }
}
