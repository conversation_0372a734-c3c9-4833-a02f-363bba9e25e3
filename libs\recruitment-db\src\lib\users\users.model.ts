import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>any,
  <PERSON>umn,
  <PERSON>Type, Foreign<PERSON>ey, HasMany, HasOne,
  Model,
  Table,
} from 'sequelize-typescript'
import { ApiProperty } from '@nestjs/swagger';
import { Role } from '../roles/roles.model'
import { UserRoles } from '../roles/user-roles.model'
import { Candidate } from '../candidates/candidates.model'
import { Recruiter } from '../recruiters/recruiters.model'
import { Company } from '../companies/companies.model'
import { OfferModel } from '../offers/offer.model';
import { BackgroundModel } from '../background-screening/background.model';
import { DrugModel } from '../drug-screening/drug.model';
import { Subscribe } from "../subscribe/subscribe.model";
import { NotifyPermissionsModel } from '../notify-permissions/notify-permissions.model';

interface UserCreationAttrs {
  authId?: string;
  avatar?: string;
  email: string;
  phone: string;
  firstname: string;
  middlename?: string;
  lastname: string;
  password?: string;
  candidatePro<PERSON>leId?: number;
  recruiterProfileId?: number;
  companyId?: number;
}

@Table({ tableName: "users" })
export class User extends Model<User, UserCreationAttrs> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "auth0|61a1102ba9ee1000738655a0",
    description: "Auth0 user_id",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  authId: string;

  @ApiProperty({
    example: "https://domain.com/avatar.jpg",
    description: "Avatar",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  avatar: string;

  @ApiProperty({ example: "<EMAIL>", description: "Email address" })
  @Column({
    type: DataType.STRING,
    allowNull: false,
    unique: true,
  })
  email: string;

  @ApiProperty({ example: "8622365552", description: "Phone" })
  @Column({ type: DataType.STRING, allowNull: false })
  phone: string;

  @ApiProperty({ example: "Matthew", description: "First Name" })
  @Column({ type: DataType.STRING, allowNull: false })
  firstname: string;

  @ApiProperty({ example: "James", description: "Middle Name" })
  @Column({ type: DataType.STRING, allowNull: true })
  middlename: string;

  @ApiProperty({ example: "Stark", description: "Last Name" })
  @Column({ type: DataType.STRING, allowNull: false })
  lastname: string;

  @ApiProperty({ example: "123456qQ@", description: "Password" })
  @Column({ type: DataType.STRING, allowNull: true })
  password: string;

  @ApiProperty({ example: "1", description: "Company ID" })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;

  @ApiProperty({ example: "7982265", description: "JobTarget ID associated with user" })
  @Column({
    type: DataType.STRING,
    allowNull: true,
    field: 'job_target_id',
  })
  jobTargetId: string;

  @BelongsToMany(() => Role, () => UserRoles)
  roles: Role[];

  @HasOne(() => Candidate)
  candidate: Candidate;

  @HasOne(() => Recruiter)
  recruiter: Recruiter;

  @HasMany(() => UserRoles)
  userRoles: UserRoles;

  @HasMany(() => OfferModel)
  offers: OfferModel;

  @HasOne(() => Company)
  company: Company;

  @HasMany(() => NotifyPermissionsModel)
  notifyPermissions: NotifyPermissionsModel;

  @HasMany(() => BackgroundModel)
  backgroundOrders: BackgroundModel;

  @HasMany(() => DrugModel)
  drugOrders: DrugModel;
}
