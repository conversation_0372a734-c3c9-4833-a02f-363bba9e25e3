import { Injectable } from "@nestjs/common";
import * as dataJson from "./zipdata.json";
import { InjectModel } from "@nestjs/sequelize";
import { Location } from "./location.model";
import { Op } from "sequelize";

@Injectable()
export class LocationsService {
  constructor(
    @InjectModel(Location) private locationsRepository: typeof Location
  ) {}

  async seed() {
    await this.locationsRepository
      .bulkCreate(dataJson, {
        validate: true,
      })
      .then(async () =>
        this.locationsRepository.sequelize.query(
          `ALTER SEQUENCE "${
            this.locationsRepository.tableName
          }_id_seq" RESTART WITH ${
            (await this.locationsRepository.count()) + 1
          }`
        )
      );
    return true;
  }

  async findByCity(find?: string) {
    let where = {};
    if (find && find.length > 0) {
      where = {
        city: {
          [Op.iLike]: `%${find}%`,
        },
      };
    }
    return await this.locationsRepository.findAll({
      where,
      limit: 30,
    });
  }

  async findById(locationID?: number) {
    return await this.locationsRepository.findOne({
      where: {
        id: {
          [Op.eq]: locationID,
        },
      },
    });
  }

  async findByCityWithLimit(data: any) {
    let where:any= {};
    if (data.find && data.find.length > 0) {
      const location = data.find.split(",")?.map(i=>i.trim());
      if(location){
        where.city= {
            [Op.iLike]: `%${location[0]}%`,
          };
        if(location[1]){
         where.state={
            [Op.iLike]: `%${location[1]}%`,
         }
        }
      }
    }
    return await this.locationsRepository.findAll({
      where,
      limit: 30,
      offset: data.offset || 0,
    });
  }
}
