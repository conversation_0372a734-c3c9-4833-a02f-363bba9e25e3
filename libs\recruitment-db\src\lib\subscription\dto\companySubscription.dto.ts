import { ApiProperty, OmitType } from '@nestjs/swagger';

export class CompanySubscriptionDto {
  @ApiProperty({ example: 'sub_xxx', description: 'Subscription ID' })
  subId : string;

  @ApiProperty({ example: 'active', description: 'Subscription status' })
  status : string;

  @ApiProperty({
    example: '1644412647',
    description: 'Current period end timestamp',
  })
  currentPeriodEnd : number;

  @ApiProperty({ example: 'cap1ats', description: 'Capability ID' })
  capabilities : string;

  @ApiProperty({ example: 'ATS', description: 'Package Id' })
  packageId : number;

  @ApiProperty({ example: 'ATS', description: 'Package Name' })
  packageName : string;

  @ApiProperty({ example: 'Plan 1 ATS', description: 'Plan Id' })
  planId : number;

  @ApiProperty({ example: 'Plan 1 ATS', description: 'Plan Name' })
  planName : string;

  @ApiProperty({ example: '4242', description: 'Last 4' })
  last4 : string;

  @ApiProperty({ example: 'ATS', description: 'features string' })
  features : string[] | null;

  @ApiProperty({ example: '', description: 'prices object' })
  prices : any;

  @ApiProperty({ example: 'prod_xxx', description: 'Product ID' })
  productId : string;

  @ApiProperty({ example: 'fullcycle', description: 'Product ID' })
  packageType : string;

  @ApiProperty({ example: 'fullcycle', description: 'Product ID' })
  acceptedPackageType : string[] | null;

  @ApiProperty({ example: 'pr_xxx', description: 'Product ID' })
  priceId: string;

  @ApiProperty({ example: 'main/addon', description: 'subscription type' })
  type: string;

}

export class CompanyAddonsDto{

  @ApiProperty({ example: 'job', description: 'Addon product type' })
  addonName: string;

  @ApiProperty({ example: 'increase', description: 'quantity increase or decrease' })
  type :'increase'|'decrease';
}

export class GetCompanySubscriptionBaseDto{
  @ApiProperty({ example: 'sub_xxx', description: 'Subscription ID' })
  subId?: string;

  @ApiProperty({ example: 34, description: 'Company ID' })
  companyId?: number;

  @ApiProperty({ example: 1, description: 'Plan Id' })
  planId?: number;

  @ApiProperty({ example: 'm/y', description: 'Subscription period' })
  period?: string;

  @ApiProperty({ example: '', description: 'Starting from date' })
  startFrom?: Date;

  @ApiProperty({ example: '', description: 'Ending at date' })
  endTo?: Date;

  @ApiProperty({ example: '', description: 'Sort By property' })
  sortBy?: string;

  @ApiProperty({ example: 'ASC/DESC', description: 'Sort Type' })
  sortType?: string;

  @ApiProperty({ example: 'main/addon', description: 'subscription type' })
  type?: string;
}
export class GetCompanySubscriptionDto extends GetCompanySubscriptionBaseDto{
  @ApiProperty({ example: 'active', description: 'Subscription status' })
  status?: string|string[];
}

export class GetAllCompanySubscriptionDto extends GetCompanySubscriptionBaseDto{
  @ApiProperty({ example: 'active', description: 'Subscription status' })
  status?: string | string[];
}