{"root": true, "ignorePatterns": ["**/*"], "plugins": ["@nrwl/nx"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nrwl/nx/enforce-module-boundaries": ["error", {"enforceBuildableLibDependency": true, "allow": [], "depConstraints": [{"sourceTag": "*", "onlyDependOnLibsWithTags": ["*"]}]}]}}, {"files": ["*.ts", "*.tsx"], "extends": ["plugin:@nrwl/nx/typescript", "@macpaw/eslint-config-base", "@macpaw/eslint-config-typescript"], "rules": {"indent": ["error", 2]}}, {"files": ["*.js", "*.jsx"], "extends": ["plugin:@nrwl/nx/javascript", "@macpaw/eslint-config-base"], "rules": {}}]}