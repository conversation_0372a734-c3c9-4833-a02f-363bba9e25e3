import * as AWS from "aws-sdk";
import { kinesisDeliveryStreams } from "./streamEnvs";

AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || "us-east-1",
});

const Firehose = new AWS.Firehose();

export const putRecordOnKinesis = async (data, DeliveryStreamName = '') => {
    try {
        const resolvedStreamName = DeliveryStreamName || process.env.AWS_FIREHOSE_DELIVERY_STREAM_NAME;


        if (!resolvedStreamName) {
            console.error("Missing DeliveryStreamName! Cannot send data to Firehose.");
            throw new Error("DeliveryStreamName is required but not provided.");
        }

        const params = {
            DeliveryStreamName: resolvedStreamName,
            Record: {
                Data: JSON.stringify(data),
            },
        };
        const response = await Firehose.putRecord(params).promise();
        return response;
    } catch (error) {
        console.error('putRecordOnKinesis: Error calling Firehose.putRecord:', error);
        throw error;
    }
};

export const putRecord = async ({ records }: { records: { for: keyof typeof kinesisDeliveryStreams, data?: any, payload?: any }[] }) => {
    try {

        for (let i of records) {

            if (!i?.for) {
                continue;
            }

            const deliverStream = kinesisDeliveryStreams[i.for];
            if (!deliverStream) {
                continue;
            }

            const recordData = i.data || i.payload;
            if (!recordData) {
                continue;
            }

            await putRecordOnKinesis(recordData, deliverStream);
        }
    } catch (error) {
        console.error('Error putting record on Kinesis:', error);
    }
};
