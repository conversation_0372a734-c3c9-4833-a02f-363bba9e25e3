import { AuthUser, PermissionsGuard } from "@microservices/auth";
import { MsGraphService } from "@microservices/calendars";
import { TemporalWorkflowService } from "@microservices/recruitment-db";
import {
    Body,
    Controller,
    Get,
    Logger,
    Post,
    Query,
    UseGuards
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { CreateEventDTO } from "libs/calendars/src/lib/dto/create-event.dto";
import { GetFreeSlotsDTO } from "libs/calendars/src/lib/dto/get-free-slots.dto";
import { appointmentBookingSignal } from "apps/temporal/src/app/workflow/temporal/workflows";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";

@ApiTags("mcal")
@Controller("mcal")
export class MCalController {
  constructor(
    private msGraphService: MsGraphService,
    private temporalWorkflowService: TemporalWorkflowService,
  ) {}

  @ApiOperation({ summary: "create the google auth url" })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/book-appointment")
  async bookAppointment(@Body() data: CreateEventDTO, @AuthUser() user) {
   try{
    await this.msGraphService.bookAppointment(
      user["https://urecruits.com/userId"],
      data
    );
    const response = await this.temporalWorkflowService.find(
      user["https://urecruits.com/userId"],
      data.jobId
    );
    const client = await connectToTemporal()
    if(client && response?.workflowid){
    const handle = client.getHandle(response.workflowid);
    await handle.signal(
      appointmentBookingSignal,
            "Book the Appointment"
    );
   }else{
    Logger.log("Getting issue to connect the client in bookAppointment of mcal")
   }
   return {message: "Appointment booked successfully"}
   }catch(error){
    console.log({error});
    
   }
  }

  @ApiOperation({ summary: "create the google auth url" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/get-calendar")
  async getEvents(@Query() query: any, @AuthUser() user) {
    return this.msGraphService.getEvents(
      user["https://urecruits.com/userId"],
      query
    );
  }

  @ApiOperation({ summary: "create the google auth url" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get("/appointment")
  async appointment(@Query() data: GetFreeSlotsDTO, @AuthUser() user) {
    return this.msGraphService.appointment(
      user["https://urecruits.com/userId"],
      data
    );
  }
}
