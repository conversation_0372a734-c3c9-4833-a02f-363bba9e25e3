import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Query,
  Patch,
  Delete,
  Param,
  Res,
  UseInterceptors,
  UploadedFiles,
  Logger,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  EditOfferDto,
  FilterCompanCandidatesOffersDto,
  FilterCompanyJobsOffersDto,
  HellosignTemplate,
  OfferDto,
  OfferModel,
  OfferService,
  TemplateDto,
} from "@microservices/recruitment-db";
import { FileInterceptor } from "@nestjs/platform-express";

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("Offer")
@ApiBearerAuth("access-token")
@Controller("offer")
export class OfferController {
  constructor(private readonly offerService: OfferService) {}

  @ApiOperation({ summary: "Create Offer" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post()
  @Permissions("offer:add")
  create(@Body() dto: OfferDto, @AuthUser() user: any) {
    return this.offerService.create(
      dto,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get Offer By Id" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get(":id")
  @Permissions("offer:view")
  getOffer(@Param("id") id: number, @AuthUser() user: any) {
    return this.offerService.getOffer(
      id,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Delete Offer By Id" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete(":id")
  @Permissions("offer:edit")
  deleteOffer(@Param("id") id: number, @AuthUser() user: any) {
    return this.offerService.deleteOffer(
      id,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Delete Offer By Id" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/candidate/offers/:userId/:jobId")
  @Permissions("offer:edit")
  deleteCandidateAllOffer(
    @Param("userId") userId: number,
    @Param("jobId") jobId: number,
    @AuthUser() user: any
  ) {
    return this.offerService.deleteCandidateAllOffer(
      userId,
      jobId,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get Offer By JOB Id and UserID" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/offers/:jobId/:userId")
  @Permissions("offer:view")
  getOffers(
    @Param("jobId") jobId: number,
    @Param("userId") userId: number,
    @AuthUser() user: any
  ) {
    return this.offerService.getOffers(
      jobId,
      userId,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Edit Offer" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch()
  @Permissions("offer:edit")
  edit(@Body() dto: EditOfferDto, @AuthUser() user: any) {
    return this.offerService.edit(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: "Edit Offer By OfferId" })
  @ApiResponse({ status: 200, type: OfferModel })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/:offerId")
  @Permissions("offer:edit")
  updateById(
    @Body() dto: EditOfferDto,
    @AuthUser() user: any,
    @Param("offerId") offerId: number
  ) {
    return this.offerService.updateById(
      offerId,
      dto,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get all company jobs Offers" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/jobs/offers")
  @Permissions("offer:view")
  getAllJobsOffer(
    @Query() query: FilterCompanyJobsOffersDto,
    @AuthUser() user: any
  ) {
    return this.offerService.getAllCompanyJobsOffers(
      query,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get company candidate particular jobs Offers" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/candidate-offers/:jobId")
  @Permissions("offer:view")
  getCompanyCandidatesOffers(
    @Param("jobId") jobId: number,
    @Query() query: FilterCompanCandidatesOffersDto,
    @AuthUser() user: any
  ) {
    return this.offerService.getCompanyCandidatesOffers(
      query,
      user["https://urecruits.com/companyId"],
      jobId
    );
  }

  @ApiOperation({ summary: "Get recent campnay offer" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/company/recent")
  @Permissions("recruiter")
  getCompanyRecentOffer(
    @AuthUser() user: any
  ) {
    return this.offerService.getRecentCompanyOffer(user["https://urecruits.com/companyId"])
  }
  @ApiOperation({ summary: "Send Offer For Signing" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("hellosign/send-signature-request/:offerId")
  @Permissions("offer:edit")
  sendSignatureRequest(
    @AuthUser() user: any,
    @Param("offerId") offerId: number
  ) {
    return this.offerService.SendSignatureRequest(
      user["https://urecruits.com/companyId"],
      offerId
    );
  }

  @ApiOperation({ summary: "Get Signature Request Data By SignatureRequestID" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("hellosign/signature-request/:signatureRequestId")
  @Permissions("offer:view")
  getSignatureRequest(
    @AuthUser() user: any,
    @Param("signatureRequestId") signatureRequestId: string
  ) {
    return this.offerService.getSignatureRequest(signatureRequestId);
  }

  @ApiOperation({
    summary: "Get Signature Request Document By SignatureRequestID",
  })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("hellosign/signature-document/:offerId")
  @Permissions("offer:view")
  async getSignatureDocumentPreview(
    @AuthUser() user: any,
    @Res() res: any,
    @Param("offerId") offerId: string
  ) {
    const fileBuffer = await this.offerService.getSignatureDocumentPreview(
      offerId,
      user["https://urecruits.com/companyId"]
    );
    res.set({
      "Content-Type": "application/pdf",
      "Content-Disposition": 'inline; filename="file.pdf"',
    });
    res.send(fileBuffer);
  }

  @ApiOperation({ summary: "Get Template By Id" })
  @ApiResponse({ status: 200, type: HellosignTemplate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/hellosign/template/:id")
  @Permissions("offer:view")
  getTemplate(@Param("id") id: number, @AuthUser() user: any) {
    return this.offerService.getTemplate(
      id,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get Templates" })
  @ApiResponse({ status: 200, type: HellosignTemplate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/hellosign/templates")
  @Permissions("offer:view")
  getTemplates(@AuthUser() user: any) {
    return this.offerService.getTemplates(
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "delete Template By Id" })
  @ApiResponse({ status: 200, type: HellosignTemplate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/hellosign/template/:templateId")
  @Permissions("offer:edit")
  deleteTemplate(
    @Param("templateId") templateId: string,
    @AuthUser() user: any
  ) {
    return this.offerService.deleteTemplate(
      templateId,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Create Template" })
  @ApiResponse({ status: 201, type: HellosignTemplate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/hellosign/template")
  @Permissions("offer:add")
  createTemplate(@Body() dto: TemplateDto, @AuthUser() user: any) {
    return this.offerService.createTemplate(
      dto,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Update Templates" })
  @ApiResponse({ status: 201, type: HellosignTemplate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/hellosign/template/:id")
  @Permissions("offer:edit")
  updateTemplate(
    @Body() dto: TemplateDto,
    @Param("id") id: number,
    @AuthUser() user: any
  ) {
    return this.offerService.updateTemplate(
      dto,
      id,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get Hellosign sign_url by offer id " })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/hellosign/sign-url/:offerId")
  getHellosignUrl(
    @Param('offerId') offerId: number,
    @AuthUser() user: any
  ) {
    const userId = user["https://urecruits.com/userId"]
    return this.offerService.getHellosignUrl(+offerId, userId, user["https://urecruits.com/companyId"])
  }

  @ApiOperation({ summary: "Hellosign Webhook" })
  @ApiResponse({ status: 201 })
  @Post("/hellosign/status")
  @UseInterceptors(FileInterceptor('files'))
  hellosignStatus(
    @Body() body: any,
    @UploadedFiles() files: any
  ) {
      const hookBody = JSON.parse(body.json);
      return this.offerService.sendMailOnHelloSignEvent(hookBody)
  }

}
