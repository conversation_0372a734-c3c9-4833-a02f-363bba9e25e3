import { ApiProperty } from '@nestjs/swagger';

class PaymentMethodCardDto {
  @ApiProperty({
    example: '12',
    description: 'Number representing the card’s expiration month.',
    required: false,
  })
  readonly exp_month: number;

  @ApiProperty({
    example: '25',
    description: 'Number representing the card’s expiration year.',
    required: false,
  })
  readonly exp_year: number;
}

class PaymentMethodBillingDetailsAddressDto {
  @ApiProperty({
    example: 'San Marcos',
    description: 'City, district, suburb, town, or village.',
    required: false,
  })
  readonly city: string;

  @ApiProperty({
    example: 'US',
    description: 'Two-letter country code (ISO 3166-1 alpha-2).',
    required: false,
  })
  readonly country: string;

  @ApiProperty({
    example: '78666',
    description: 'ZIP or postal code.',
    required: false,
  })
  readonly postal_code: string;

  @ApiProperty({
    example: '1550 Interstate 35',
    description: 'Address line 1 (e.g., street, PO Box, or company name).',
    required: false,
  })
  readonly line1: string;

  @ApiProperty({
    example: '',
    description: 'Address line 2 (e.g., apartment, suite, unit, or building).',
    required: false,
  })
  readonly line2: string;

  @ApiProperty({
    example: 'Texas',
    description: 'State, county, province, or region.',
    required: false,
  })
  readonly state: string;
}

class PaymentMethodBillingDetailsDto {
  @ApiProperty({
    example: 'John Doe',
    description: 'Full name.',
    required: false,
  })
  readonly name: string;

  @ApiProperty({
    description: 'Billing address.',
    required: false,
  })
  readonly address: PaymentMethodBillingDetailsAddressDto;
}

export class PaymentMethodDto {
  @ApiProperty({ example: 'pm_xxx', description: 'Payment method ID' })
  readonly id: string;

  @ApiProperty({
    description: 'Contains the user’s card details.',
    required: false,
  })
  readonly card: PaymentMethodCardDto;

  @ApiProperty({
    description:
      'Billing information associated with the PaymentMethod that may be used or required by particular types of payment methods.',
    required: false,
  })
  readonly billing_details: PaymentMethodBillingDetailsDto;
}
