import { Injectable, Logger } from '@nestjs/common';
import { SubmissionReq } from './submissions.types';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { ConfigService } from '@nestjs/config';
import * as vm from 'vm';

@Injectable()
export class OpenAIExecutorService {
    private readonly openAIApiKey: string;
    private readonly openAIEndpoint = 'https://api.openai.com/v1/chat/completions';

    constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService
    ) {
        this.openAIApiKey = this.configService.get<string>('OPEN_AI_SECRET_KEY');
    }

    /**
     * Analyzes code using GPT-4 instead of compiling and running it
     */
    async analyzeCode(submission: SubmissionReq) {
        try {
            // Ensure we have a valid submission object
            if (!submission) {
                return this.createErrorResponse(null, "Invalid submission object");
            }

            // Ensure language_id is a number
            if (submission.language_id === undefined || submission.language_id === null) {
                submission.language_id = 61; // Default to JavaScript
            }

            // Check if source_code is undefined
            if (!submission.source_code) {
                return this.createErrorResponse(
                    submission.language_id,
                    "The code is empty or undefined",
                    "Please provide a valid code snippet for analysis."
                );
            }

            // Decode source code if it's base64 encoded
            let sourceCode = submission.source_code;
            try {
                if (this.isBase64(sourceCode)) {
                    sourceCode = Buffer.from(sourceCode, 'base64').toString('utf-8');
                }
            } catch (error) {
            }

            // Extra check after decoding - if we have an empty string, return error
            if (!sourceCode || sourceCode.trim() === '') {
                return this.createErrorResponse(
                    submission.language_id,
                    "The code is empty after decoding",
                    "Please provide non-empty code for analysis."
                );
            }

            // Determine language name from language_id
            const languageName = this.getLanguageName(submission.language_id);
            // Check if test cases were provided
            const hasProvidedTestCases = submission.test_cases && submission.test_cases.length > 0;

            // Format test cases prompt based on whether they were provided
            let testCasesPrompt = '';
            if (hasProvidedTestCases) {
                testCasesPrompt = `\nPlease evaluate the code against the following test cases:

${submission.test_cases.map(tc =>
                    `Test ID: ${tc.id}
Input: ${tc.input}
Expected Output: ${tc.expected_output}
`).join('\n')}`;
            } else {
                testCasesPrompt = `\nNo test cases were provided. Please just compile the code and return the output if any`;
            }

            // Construct prompt for OpenAI
            const prompt = `You are an expert programmer tasked with analyzing code and running test cases. 

            Please analyze the following ${languageName} code:

\`\`\`${languageName}
${sourceCode}
\`\`\`

${testCasesPrompt}
${submission.question ? `\nProblem statement:\n${submission.question}\n` : ''}
For your analysis:
1. FIRST, verify if the code can be executed as ${languageName}. Only respond with the language mismatch JSON if the code CANNOT be executed in ${languageName} at all. Common patterns like:
   - JavaScript/TypeScript/Node.js are compatible
   - Python 2.x/3.x are compatible
   - Different versions of the same language are compatible
If you're unsure, proceed with the execution rather than returning a language mismatch error.

2. If the code CANNOT be executed in ${languageName}, respond with this exact JSON:
{
    "success": false,
    "errors": ["Syntax Error: The code cannot be executed in ${languageName}"],
    "warnings": [],
    "feedback": "Code appears to be written in a different programming language",
    "execution_result": null,
    "test_cases": [],
    "overall_test_result": "Failed"
}

3. If the code CAN be executed in ${languageName}, then:
   - Check for syntax errors, logical errors, and potential bugs
   - EXECUTE each test case with the provided input and capture the actual output
   - DO NOT return "Not executed" for any test case
   - TRY mapping the input to the function parameters instead of saying "invalid input"
   - For each test case:
     - Run the code with the test input
     - Compare actual output with expected output
     - Set status to "passed" if they match exactly (ignoring "Error: " prefix), "failed" otherwise
     - Always provide the actual output from execution
     - If the output contains "Error: " prefix, remove it before comparison

4. For error handling:
    - If the code throws an error, return the error message without "Error: " prefix
    - If the code returns an error string starting with "Error: ", remove the prefix
    - Compare outputs after removing any "Error: " prefix
    - Focus on the actual error message content rather than the prefix

    - Always provide the actual output from execution
    - If the output contains "Error: " prefix, remove it before comparison
    - Make sure to capture and display any console.log output from the code

5. For console output:
    - Capture ALL console.log statements as they appear during execution
    - Include console output as part of the actual output in test results
    - Do not modify or format console output except to remove trailing/leading whitespace
    - Make sure console messages are shown in the exact order they appear during execution

Respond with a JSON object with the following structure (ONLY if code can be executed):
{
    "success": boolean,
    "errors": string[],
    "warnings": string[],
    "feedback": string,
    "execution_result": string,
    "test_cases": [
        {
            "id": string,
            "input": string,
            "status": "passed" | "failed",
            "expected_output": string,
            "actual_output": string
        }
    ],
    "overall_test_result": string
}

5. Important Considerations:
    - Ensure that the actual outputs are derived from executing the code, not from any assumptions.
    - Capture and include ALL console.log/print statements in the actual_output field
    - Console output is a critical part of the execution result - do not omit any console messages
    - Console output should appear in the actual_output exactly as it would when the code runs
    - For code with both return values and console output, include both in the actual_output
    - Do not include any answers that state "Not executed" - every test case must have an actual result
    - Be lenient with language detection but remain rigorous in code execution
    - Follow the provided JSON structure exactly
`;


            const openAIResponse = await this.callOpenAI(prompt);

            try {
                // Parse the response to get the JSON object
                const responseContent = openAIResponse.choices[0]?.message?.content || '';
                const jsonMatch = responseContent.match(/\{[\s\S]*\}/);

                if (jsonMatch) {
                    const jsonResponse = JSON.parse(jsonMatch[0]);

                    // Check if this is a language mismatch response
                    if (!jsonResponse.success && jsonResponse.errors?.[0]?.includes("Syntax Error: The code cannot be executed in")) {
                        return {
                            token: `openai-${Date.now()}`,
                            status: { id: 11, description: "Error" },
                            stdout: null,
                            stderr: jsonResponse.errors[0],
                            compile_output: null,
                            message: jsonResponse.feedback,
                            time: null,
                            memory: null,
                            language_id: submission.language_id,
                            created_at: new Date().toISOString(),
                            finished_at: new Date().toISOString(),
                            test_cases: [],
                            overall_result: "Failed",
                            generated_test_cases: false
                        };
                    }

                    // Process test cases if present
                    if (jsonResponse.test_cases) {
                        for (const testCase of jsonResponse.test_cases) {
                            const actualOutput = await this.executeTestCase(
                                sourceCode,
                                testCase.input,
                                languageName,
                                submission.question
                            );
                            testCase.actual_output = actualOutput;
                            testCase.status = this.cleanOutput(actualOutput) === this.cleanOutput(testCase.expected_output) ? 'passed' : 'failed';
                        }
                    }

                    // Determine overall result based on multiple factors
                    const hasErrors = jsonResponse.errors && jsonResponse.errors.length > 0;
                    const hasFailedTests = jsonResponse.test_cases && jsonResponse.test_cases.some(tc => tc.status === 'failed');
                    const overallResult = (!jsonResponse.success || hasErrors || hasFailedTests) ? "Failed" : "Accepted";

                    return {
                        token: `openai-${Date.now()}`,
                        status: overallResult === "Accepted" ?
                            { id: 3, description: "Accepted" } :
                            { id: 11, description: "Error" },
                        stdout: jsonResponse.execution_result || null,
                        stderr: jsonResponse.errors && jsonResponse.errors.length > 0 ?
                            jsonResponse.errors.join('\n') : null,
                        compile_output: jsonResponse.warnings && jsonResponse.warnings.length > 0 ?
                            jsonResponse.warnings.join('\n') : null,
                        message: jsonResponse.feedback,
                        time: null,
                        memory: null,
                        language_id: submission.language_id,
                        created_at: new Date().toISOString(),
                        finished_at: new Date().toISOString(),
                        test_cases: jsonResponse.test_cases || [],
                        overall_result: overallResult,
                        generated_test_cases: !hasProvidedTestCases
                    };
                } else {
                    throw new Error('Could not parse OpenAI response');
                }
            } catch (error) {
                return this.createErrorResponse(
                    submission.language_id,
                    "Error parsing analysis result",
                    "Failed to analyze code. Please try again."
                );
            }
        } catch (error) {
            return this.createErrorResponse(
                submission?.language_id,
                error.message || "Unknown error occurred",
                "Failed to analyze code due to an internal error."
            );
        }
    }

    /**
     * Helper method to create consistent error responses
     */
    private createErrorResponse(languageId: number | null, stderr: string, message: string = stderr) {
        return {
            token: `openai-${Date.now()}`,
            status: { id: 11, description: "Error" },
            stdout: null,
            stderr: stderr,
            compile_output: null,
            message: message,
            time: null,
            memory: null,
            language_id: languageId,
            created_at: new Date().toISOString(),
            finished_at: new Date().toISOString(),
            test_cases: [],
            overall_result: "Failed",
            generated_test_cases: false
        };
    }

    /**
     * Call OpenAI API with the provided prompt
     */
    private async callOpenAI(prompt: string) {
        try {
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${this.openAIApiKey}`
            };

            const data = {
                model: 'gpt-4.1',
                messages: [
                    {
                        role: 'system',
                        content: 'You are a code analysis assistant that responds only with JSON.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0,
                top_p: 0,
                max_tokens: 2000
            };

            const response = await firstValueFrom(
                this.httpService.post(this.openAIEndpoint, data, { headers })
            );

            return response.data;
        } catch (error) {
            throw new Error(`Failed to call OpenAI: ${error.message}`);
        }
    }

    /**
     * Check if a string is base64 encoded
     */
    private isBase64(str: string): boolean {
        const base64Regex = /^[A-Za-z0-9+/=]+$/;
        return base64Regex.test(str);
    }

    /**
     * Get language name from language ID
     */
    private getLanguageName(languageId: number): string {
        // Check if languageId is undefined or null
        if (languageId === undefined || languageId === null) {
            return 'JavaScript';
        }

        const languageMap = {
            43: 'Plain Text',
            44: 'Executable',
            45: 'Assembly (NASM 2.14.02)',
            46: 'Bash (5.0.0)',
            47: 'Basic (FBC 1.07.1)',
            48: 'C (GCC 7.4.0)',
            49: 'C (GCC 8.3.0)',
            50: 'C (GCC 9.2.0)',
            51: 'C# (Mono *********)',
            52: 'C++ (GCC 7.4.0)',
            53: 'C++ (GCC 8.3.0)',
            54: 'C++ (GCC 9.2.0)',
            55: 'Common Lisp (SBCL 2.0.0)',
            56: 'D (DMD 2.089.1)',
            57: 'Elixir (1.9.4)',
            58: 'Erlang (OTP 22.2)',
            59: 'Fortran (GFortran 9.2.0)',
            60: 'Go (1.13.5)',
            61: 'Haskell (GHC 8.8.1)',
            62: 'Java (OpenJDK 13.0.1)',
            63: 'JavaScript (Node.js 12.14.0)',
            64: 'Lua (5.3.5)',
            65: 'OCaml (4.09.0)',
            66: 'Octave (5.1.0)',
            67: 'Pascal (FPC 3.0.4)',
            68: 'PHP (7.4.1)',
            69: 'Prolog (GNU Prolog 1.4.5)',
            70: 'Python (2.7.17)',
            71: 'Python (3.8.1)',
            72: 'Ruby (2.7.0)',
            73: 'Rust (1.40.0)',
            74: 'TypeScript (3.7.4)',
            75: 'C (Clang 7.0.1)',
            76: 'C++ (Clang 7.0.1)',
            77: 'COBOL (GnuCOBOL 2.2)',
            78: 'Kotlin (1.3.70)',
            79: 'Objective-C (Clang 7.0.1)',
            80: 'R (4.0.0)',
            81: 'Scala (2.13.2)',
            82: 'SQL (SQLite 3.27.2)',
            83: 'Swift (5.2.3)',
            84: 'Visual Basic.Net (vbnc 0.0.0.5943)',
            85: 'Perl (5.28.1)',
            86: 'Clojure (1.10.1)',
            87: 'F# (.NET Core SDK 3.1.202)',
            88: 'Groovy (3.0.3)',
            89: 'Multi-file program',
            90: 'Dart (2.19.2)',
            91: 'Java (JDK 17.0.6)',
            92: 'Python (3.11.2)',
            93: 'JavaScript (Node.js 18.15.0)',
            94: 'TypeScript (5.0.3)',
            95: 'Go (1.18.5)'
        };

        const language = languageMap[languageId];

        if (!language) {
            return 'JavaScript';
        }

        return language;
    }

    private async executeTestCase(code: string, input: string, languageName: string, question?: string): Promise<string> {
        try {
            // Create a sandbox environment to execute the code
            const sandbox = {
                input: input,
                output: '',
                console: {
                    log: (...args: any[]) => {
                        sandbox.output += args.join(' ') + '\n';
                    }
                }
            };

            // Execute the code in the sandbox
            const result = await this.executeInSandbox(code, sandbox, languageName, question);
            return result.trim();
        } catch (error) {
            return `Error: ${error.message}`;
        }
    }

    private async executeInSandbox(code: string, sandbox: any, languageName: string, question?: string): Promise<string> {
        try {
            // Execute code based on language
            switch (languageName.toLowerCase()) {
                case 'javascript':
                case 'typescript':
                    // For JS/TS, we can use eval in the sandbox
                    const context = vm.createContext(sandbox);
                    vm.runInContext(code, context);
                    return this.cleanOutput(sandbox.output);

                default:
                    // For other languages, we'll use OpenAI to simulate execution
                    const executionPrompt = `Execute this ${languageName} code with the following input and return ONLY the raw output value (no JSON, no code blocks, no quotes unless they're part of the actual output):
                    
${question ? `Problem statement:
${question}

` : ''}Code:
${code}

Input:
${sandbox.input}

Important:
1. Return ONLY the raw output value
2. Do NOT wrap the output in JSON
3. Do NOT use code blocks or markdown formatting
4. Do NOT add quotes unless they're part of the actual output
5. If the output is a number, return just the number
6. If the output is an error message, return just the message
7. Remove any trailing/leading whitespace

Example good outputs:
1.5
Hello World
Error: Invalid input
true

Example bad outputs:
\`\`\`json\\n{"output": 1.5}\\n\`\`\`
"1.5"
{ value: "Hello World" }
\`\`\`\\ntrue\\n\`\`\`

Example good outputs:
1.5
Hello World
Error: Invalid input
true
Hello from console.log!

Example bad outputs:
\`\`\`json\\n{"output": 1.5}\\n\`\`\`
"1.5"
{ value: "Hello World" }
\`\`\`\\ntrue\\n\`\`\`

Be sure to capture and include ALL console.log statements in your output exactly as they would appear during execution!`;

                    const response = await this.callOpenAI(executionPrompt);
                    return this.cleanOutput(response.choices[0]?.message?.content || '');
            }
        } catch (error) {
            throw new Error(`Execution failed: ${error.message}`);
        }
    }

    private cleanOutput(output: string): string {
        // Remove code blocks
        output = output.replace(/```[a-z]*\n/g, '').replace(/```/g, '');

        // Remove JSON formatting
        try {
            const jsonMatch = output.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                if (parsed.output !== undefined) {
                    output = parsed.output.toString();
                }
            }
        } catch (e) {
            // If JSON parsing fails, continue with original output
        }

        // Clean up whitespace and newlines
        output = output.trim();

        // Remove Error: prefix for comparison
        output = output.replace(/^Error:\s+/, '');

        // If the output is a quoted string and quotes aren't part of the content,
        // remove the quotes
        if (output.startsWith('"') && output.endsWith('"')) {
            const unquoted = output.slice(1, -1);
            // Only remove quotes if they're not part of the actual content
            if (!unquoted.includes('"')) {
                output = unquoted;
            }
        }

        return output;
    }
} 