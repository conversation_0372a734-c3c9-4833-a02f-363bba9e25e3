import { IntegrationAuthModule, IntegrationsModule } from "@microservices/recruitment-db";
import { Module } from '@nestjs/common';
import { GoogleAuthService } from './googleAuth.service';
import { MsGraphService } from './msGraph.service';
import { CalendarModule } from "@microservices/db";
import { HttpModule } from "@nestjs/axios";
import { UtilityService } from "./utility.service";


@Module({
  imports: [IntegrationAuthModule,CalendarModule,HttpModule,IntegrationsModule],
  providers: [ GoogleAuthService, MsGraphService,UtilityService],
  exports: [GoogleAuthService, MsGraphService,UtilityService]
})
export class CalendarsModule {}
