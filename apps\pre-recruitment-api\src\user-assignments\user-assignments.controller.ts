import { Controller, Body, Patch, Get, Param, UseGuards, Post, Delete } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import { AuthGuard } from "@nestjs/passport";
import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { CreateUserAssignmentDto, UserAssignments, UserAssignmentsService } from "@microservices/recruitment-db";

@ApiTags('User-assignments')
@ApiBearerAuth('access-token')
@Controller('user-assignments')
export class UserAssignmentsController {
    constructor(private readonly userAssignmentsService: UserAssignmentsService) { }

    @ApiOperation({ summary: 'Create entry for active assignment status of a user' })
    @ApiResponse({ status: 201, type: UserAssignments })
    @UseGuards(AuthGuard('jwt'))
    @Post()
    update(@Body() dto: CreateUserAssignmentDto, @AuthUser() user: any) {
        return this.userAssignmentsService.create(dto, user['https://urecruits.com/companyId'])
    }

    @ApiOperation({ summary: "Get user's assignment status by company" })
    @ApiResponse({ status: 200, type: UserAssignments })
    @UseGuards(AuthGuard('jwt'))
    @Get("/:userId")
    getStatus(@Param('userId') userId:number, @AuthUser() user: any) {
        return this.userAssignmentsService.getAssignmentStatusByUser(userId, user['https://urecruits.com/companyId'])
    }

    @ApiOperation({ summary: 'Change the status of an active assignments' })
    @ApiResponse({ status: 201, type: UserAssignments })
    @UseGuards(AuthGuard('jwt'))
    @Patch("/change-status")
    changeAssignmentStatus(@Body() dto:CreateUserAssignmentDto, @AuthUser() user: any) {
        return this.userAssignmentsService.changeActiveAssignmentStatus(dto, user['https://urecruits.com/companyId'])
    }

    @ApiOperation({ summary: 'Change the status of an active assignments' })
    @ApiResponse({ status: 200, type: UserAssignments })
    @UseGuards(AuthGuard('jwt'))
    @Delete("/:userId")
    deleteRecord(@Param('userId') userId:number, @AuthUser() user: any) {
        return this.userAssignmentsService.delete(userId, user['https://urecruits.com/companyId'])
    }
}
