// opensearch.module.ts

import { Module, Global } from "@nestjs/common";
import { Client } from "@opensearch-project/opensearch";
import { OpensearchService } from "./opensearch.service";

@Global()
@Module({
  providers: [
    {
      provide: "OpenSearchClient",
      useFactory: () =>
        new Client({
          node: process.env.AWS_OPENSEARCH_NODE,
          auth: {
            username: process.env.AWS_OPENSEARCH_USERNAME,
            password: process.env.AWS_OPENSEARCH_PASSWORD,
          },
        }),
    },
    OpensearchService,
  ],
  exports: ["OpenSearchClient", OpensearchService],
})
export class OpenSearchModule {}
