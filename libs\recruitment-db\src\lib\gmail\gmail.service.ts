import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
  PayloadTooLargeException,
  UnauthorizedException,
} from "@nestjs/common";
import { randomBytes, scrypt, createCipheriv, createDecipheriv } from "crypto";
import { Auth, google } from "googleapis";
import { promisify } from "util";
import mime = require("mime");
import { IntegrationAuthService } from "../integrationAuth/integrationAuth.service";
import { MailFilterDto } from "./dto/mailFilter.dto";
import { SendMailDto } from "./dto/sendMail.dto";
import { FileService } from "apps/pre-recruitment-api/src/file/file.service";

@Injectable()
export class GmailService {
  private oauth2Client: Auth.OAuth2Client;
  private scope ="https://www.googleapis.com/auth/gmail.send"
  
  constructor(
    private integrationModelService: IntegrationAuthService,
    private fileService: FileService
  ) {
    this.oauth2Client = new google.auth.OAuth2({
      clientId: process.env.GMAIL_AUTH_CLIENT_ID,
      clientSecret: process.env.GMAIL_AUTH_CLIENT_SECRET,
      redirectUri: `${process.env.MICROSERVICES_PRA_URL}/api/integrations/oauth2-callback`,
    });
  }

  async getClient(id) {
    try {
      const data = await this.integrationModelService.findByProvider(id,"gmail");
      const token = JSON.parse(data.refreshToken);
      const decrypt = await this.decrypt(JSON.stringify(token));
      const refreshToken = decrypt[0];
      if (Number(data.expiryDate) < new Date().getTime()) {
        this.oauth2Client.setCredentials({  
          refresh_token: refreshToken,
        });
        const newToken = await this.oauth2Client.refreshAccessToken();

        await this.integrationModelService.update(id, {
          userId: id,
          accessToken: await this.encrypt([newToken.credentials.access_token]),
          refreshToken: await this.encrypt([
            newToken.credentials.refresh_token,
          ]),
          expiryDate: newToken.credentials.expiry_date,
          scope: newToken.credentials.scope,
        },"gmail");

        this.oauth2Client.setCredentials({
          refresh_token: newToken.credentials.refresh_token,
        });
      } else {
        this.oauth2Client.setCredentials({
          refresh_token: refreshToken,
        });
      }

      return {
        client: google.gmail({ version: "v1", auth: this.oauth2Client }),
      };
    } catch (error) {
      throw new UnauthorizedException("Invalid or expired token.");
    }
  }

  async listMessages(data: MailFilterDto, id: any): Promise<any> {
    try {
      const { client } = await this.getClient(id).catch((error) => {
        throw new UnauthorizedException("Invalid grant");
      });

      const gmail = client;
      let search;
      let array = [];

      if (data.search) {
        if (data.source === "draft") {
          search = `in:${data.source} ${data.search} newer_than:7d`;
        } else {
          search = `in:${data.source} urecruits ${data.search} newer_than:${
            period[data.newer_than]
          }`;
        }
      } else {
        if (data.source === "draft") {
          search = `in:${data.source}`;
        } else {
          search = `in:${data.source} urecruits`;
        }
      }

      const res = await gmail.users.messages.list({
        userId: "me",
        q: search,
      });

      if (res.data.messages) {
        const messageIds = res.data.messages.map((message) => message.id);
        const messages = await Promise.all(
          messageIds.map(async (messageId) => {
            const response = await gmail.users.messages.get({
              userId: "me",
              id: messageId,
              format: "full",
            });
            return response.data;
          })
        );

        messages.forEach((message) => {
          const obj = {
            id: message.id,
            from: message.payload.headers.find(
              (header) => header.name === "From"
            ).value,
            subject: message.payload.headers.find(
              (header) => header.name === "Subject"
            ).value,
            isRead: message.labelIds[0] === "UNREAD" ? false : true,
            date: convertEpochToHumanReadable(message.internalDate),
          };
          array.push(obj);
        });

        if (data.sortBy === "subject") {
          const sortType = data.sortType === "asc" ? 1 : -1;
          array.sort((a, b) => sortType * a.subject.localeCompare(b.subject));
        } else if (data.sortBy === "date") {
          const sortType = data.sortType === "asc" ? 1 : -1;
          array.sort(
            (a, b) => sortType * (Date.parse(a.date) - Date.parse(b.date))
          );
        }
      }
      let limit;
      let offset;
      let paginatedArray = array;
      if (data.limit || data.offset) {
        limit = Number(data.limit) || 10;
        offset = Number(data.offset) || 0;
        paginatedArray = array.slice(offset, offset + limit);
      }

      return {
        data: paginatedArray,
        totalCount: array.length,
        limit: limit,
        offset: offset,
      };
    } catch (error) {
      Logger.log("Error: " + error)
      throw new UnauthorizedException("Token has been expired or revoked");
    }
  }

  async sendEmail(data: SendMailDto, files: any, id) {
    try {
      const { client } = await this.getClient(id);
      const gmail = client;

      const profile = await gmail.users.getProfile({
        userId: "me",
      });
      const userEmail = profile.data.emailAddress;
      const messageParts = [];

      messageParts.push(
        `From: ${userEmail}`,
        `To: ${data.to}`,
        `Subject: ${data.subject}`,
        'Content-Type: multipart/mixed; boundary="boundary"',
        ""
      );

      messageParts.push(
        "--boundary",
        "Content-Type: text/plain; charset=UTF-8",
        "",
        data.body,
        ""
      );

      if (files && files.length > 0) {
        for (const uploadedFile of files) {
          messageParts.push(
            "--boundary",
            `Content-Type: ${uploadedFile.mimetype}`,
            "Content-Transfer-Encoding: base64",
            `Content-Disposition: attachment; filename="${uploadedFile.originalname}"`,
            "",
            uploadedFile.buffer.toString("base64"),
            ""
          );
        }
      }

      let s3Files;
      if(typeof (data.s3File)==="string"){
        s3Files = JSON.parse(data.s3File as any);
      }else{
        s3Files=data.s3File
      }
      if (s3Files && s3Files.length > 0) {
        for(let s3File of s3Files){
          const fileBuffer = await this.fileService.downloadFileFromS3(
            s3File.key
          );
           // @ts-ignore
          const mimeType = mime.lookup(s3File.fileName);
          const attachment = [
            "--boundary",
            `Content-Type: ${mimeType}`,
            "Content-Transfer-Encoding: base64",
            `Content-Disposition: attachment; filename="${s3File.fileName}"`,
            "",
            fileBuffer.toString("base64"),
            "",
          ];
          messageParts.push(...attachment);
        }
      }

      const encodedEmail = Buffer.from(messageParts.join("\r\n")).toString(
        "base64"
      );
      let res;
      if (data.action === "send") {
        res = await gmail.users.messages.send({
          userId: "me",
          requestBody: {
            raw: encodedEmail,
          },
        });
      } else {
        res = await gmail.users.drafts.create({
          userId: "me",
          requestBody: {
            message: {
              raw: encodedEmail,
            },
          },
        });
        return res.data.message;
      }

      return res.data;
    } catch (error) {
      console.error("Error while sending email:", error);
      if (error.response.status === 401) {
        throw new UnauthorizedException(
          "Unauthorized: Please ensure you have valid credentials."
        );
      } else if (error.response.status === 403) {
        throw new ForbiddenException(
          "Forbidden: The app is not authorized to send emails on behalf of the user."
        );
      } else if (error.response.status === 413) {
        throw new PayloadTooLargeException(
          "Payload too large: The email or attachments exceed the size limit."
        );
      } else if (error.response.status === 429) {
        throw new BadRequestException(
          "Rate limit exceeded: Too many requests to Gmail API. Please try again later."
        );
      } else{
        throw new BadRequestException(error.response.data.error_description);
      }
    }
  }

  async getMailById(data, id): Promise<any> {
    try {
      const { client } = await this.getClient(id);
      const gmail = client;
      const response = await gmail.users.messages.get({
        userId: "me",
        id: data,
        format: "full",
      });
      
      let obj = { from: "", subject: "", body: "", date: "" , to:""} as any;
      obj.subject = response.data.payload.headers.find(
        (header) => header.name === "Subject"
      ).value;
      obj.body = response.data.snippet;
      obj.body = response.data.snippet;
      obj.from = response.data.payload.headers.find(
        (header) => header.name === "From"
      ).value;
      obj.to = response.data.payload.headers.find(
        (header) => header.name === "To"
      ).value;
      obj.date = convertEpochToHumanReadable(response.data.internalDate);
      console.log(obj);

      return { data: obj };
    } catch (error) {
      if (error.response?.data.error.code === 404) {
        throw new NotFoundException(error.response.data.error.message);
      } else if (error.response.data.error.code === 400) {
        throw new BadRequestException(error.response.data.error);
      } else {
        throw new UnauthorizedException("Bad Request");
      }
    }
  }

  async getOutboxEmails(data: MailFilterDto, id) {
    try {
      const { client } = await this.getClient(id);
      const gmail = client;
      let search;
      let limit;
      let offset;
      let paginatedArray = [];
      let array = [];
      if (data.search) {
        search = `in:sent urecruits ${data.search}`;
      } else {
        search = `in:sent urecruits`;
      }
      const res = await gmail.users.messages.list({
        userId: "me",
        q: `${search} newer_than:${
          data.newer_than ? period[data.newer_than] : period["1 year"]
        }`,
      });
      if (res.data.messages) {
        const messageIds = res.data.messages.map((message) => message.id);
        const messages = await Promise.all(
          messageIds.map(async (messageId) => {
            const response = await gmail.users.messages.get({
              userId: "me",
              id: messageId,
              format: "metadata",
              metadataHeaders: ["Subject", "From"],
            });
            return response.data;
          })
        );

        messages.map((message) => {
          let obj = {
            id: "",
            from: "",
            subject: "",
            body: "",
            date: "",
          } as any;
          obj.subject = message.payload.headers.find(
            (header) => header.name === "Subject"
          ).value;
          obj.id = message.id;
          obj.body = message.snippet;
          obj.from = message.payload.headers.find(
            (header) => header.name === "From"
          ).value;
          obj.date = convertEpochToHumanReadable(message.internalDate);
          array.push(obj);
        });

        if (data.sortBy === "subject") {
          if (data.sortType === "desc") {
            const sortedMessagesAsc = [...array].sort((a, b) =>
              a.subject.toLowerCase().localeCompare(b.subject.toLowerCase())
            );
            array = sortedMessagesAsc;
          } else if (data.sortType === "asc") {
            const sortedMessagesDesc = [...array].sort((a, b) =>
              b.subject.toLowerCase().localeCompare(a.subject.toLowerCase())
            );
            array = sortedMessagesDesc;
          }
        } else if (data.sortBy === "date") {
          if (data.sortType === "asc") {
            const sortedMessagesAsc = [...array].sort(
              (a, b) => Date.parse(a.date) - Date.parse(b.date)
            );
            array = sortedMessagesAsc;
          }
          if (data.sortType === "desc") {
            const sortedMessagesDesc = [...array].sort(
              (a, b) => Date.parse(b.date) - Date.parse(a.date)
            );
            array = sortedMessagesDesc;
          }
        }
      }
      if (data.limit || data.offset) {
        limit = Number(data.limit) || 10;
        offset = Number(data.offset) || 0;
        paginatedArray = array.slice(offset, offset + limit);
      } else {
        paginatedArray = array;
      }

      return {
        data: paginatedArray,
        totalCount: array.length,
        limit: limit,
        offset: offset,
      };
    } catch (error) {
      if (error.response.data.error.code === 404) {
        throw new NotFoundException(error.response.data.error.message);
      } else {
        throw new UnauthorizedException("Bad Request");
      }
    }
  }

  async markEmailAsRead(data: MailFilterDto, message, id) {
    try {
      const { client } = await this.getClient(id);
      const gmail = client;
      let promises = [];
      let request;
      for (let i = 0; i < message.messageIds.length; i++) {
        const res = await gmail.users.messages.get({
          userId: "me",
          id: message.messageIds[i],
        });
        promises.push(res);
        if (message.flag === false) {
          request = {
            addLabelIds: ["UNREAD"],
          };
        } else {
          request = {
            removeLabelIds: ["UNREAD"],
          };
        }
        await gmail.users.messages.modify({
          userId: "me",
          id: message.messageIds[i],
          requestBody: request,
        });
      }
      const result = await Promise.all(promises);
      if (result)
        return {
          message: "Data send successfully",
        };
    } catch (error) {
      Logger.log("ERROR: " + error)
      if (error.response.data.error.code === 404) {
        throw new NotFoundException(error.response.data.error.message);
      } else {
        throw new UnauthorizedException("Bad Request");
      }
    }
  }

  async deleteEmail(data, id) {
    try {
      const { client } = await this.getClient(id);
      const gmail = client;
      let promises = [];
      for (let i = 0; i < data.id.length; i++) {
        const res = await gmail.users.messages.get({
          userId: "me",
          id: data.id[i],
        });
        promises.push(res);
        await gmail.users.messages.delete({
          userId: "me",
          id: data.id[i],
        });
      }
      const result = await Promise.all(promises);
      if (result)
        return {
          message: "Email deleted Successfully",
        };
    } catch (error) {
      if (error.response.data.error.code === 404) {
        throw new NotFoundException(error.response.data.error.message);
      } else {
        throw new UnauthorizedException("Bad Request");
      }
    }
  }

  async updateDraftEmail(
    draftId: string,
    data: SendMailDto,
    files,
    id
  ): Promise<any> {
    try {
      const { client } = await this.getClient(id);
      const gmail = client;
      const profile = await gmail.users.getProfile({
        userId: "me",
      });
      const userEmail = profile.data.emailAddress;
      const messageParts = [];

      messageParts.push(
        `From: ${userEmail}`,
        `To: ${data.to}`,
        `Subject: ${data.subject}`,
        'Content-Type: multipart/mixed; boundary="boundary"',
        ""
      );

      messageParts.push(
        "--boundary",
        "Content-Type: text/plain; charset=UTF-8",
        "",
        data.body,
        ""
      );

      if (files && files.length > 0) {
        for (const uploadedFile of files) {
          messageParts.push(
            "--boundary",
            `Content-Type: ${uploadedFile.mimetype}`,
            "Content-Transfer-Encoding: base64",
            `Content-Disposition: attachment; filename="${uploadedFile.originalname}"`,
            "",
            uploadedFile.buffer.toString("base64"),
            ""
          );
        }
      }

      const encodedEmail = Buffer.from(messageParts.join("\r\n")).toString(
        "base64"
      );
      let res;
      if (data.action === "send") {
        res = await gmail.users.drafts.send({
          userId: "me",
          requestBody: {
            message: {
              raw: encodedEmail,
            },
          },
        });
      } else {
        res = await gmail.users.drafts.update({
          userId: "me",
          id: draftId,
          requestBody: {
            message: {
              raw: encodedEmail,
            },
          },
        });
      }
      await this.deleteEmail(draftId, id);
      return res.data;
    } catch (error) {
      console.error("Error while sending email:", error);
      if (error.response.status === 401) {
        throw new UnauthorizedException(
          "Unauthorized: Please ensure you have valid credentials."
        );
      } else if (error.response.status === 403) {
        throw new ForbiddenException(
          "Forbidden: The app is not authorized to send emails on behalf of the user."
        );
      } else if (error.response.status === 413) {
        throw new PayloadTooLargeException(
          "Payload too large: The email or attachments exceed the size limit."
        );
      } else if (error.response.status === 429) {
        throw new BadRequestException(
          "Rate limit exceeded: Too many requests to Gmail API. Please try again later."
        );
      } else if (error.response.status === 400) {
        throw new BadRequestException(error.response.data.error_description);
      }
      throw new NotFoundException(error.response.data.error_description);
    }
  }

  async encrypt(arr) {
    const iv = randomBytes(16);
    const key = (await promisify(scrypt)(
      process.env.CRYPTO_PASS,
      "salt",
      32
    )) as Buffer;
    const cipher = createCipheriv("aes-256-ctr", key, iv);
    const encrypted = Buffer.concat([
      cipher.update(arr.join(",")),
      cipher.final(),
    ]);

    return JSON.stringify({
      i: iv.toString("hex"),
      t: encrypted.toString("hex"),
    });
  }

  async decrypt(json) {
    const hash = JSON.parse(json);
    const key = (await promisify(scrypt)(
      process.env.CRYPTO_PASS,
      "salt",
      32
    )) as Buffer;
    const decipher = createDecipheriv(
      "aes-256-ctr",
      key,
      Buffer.from(hash.i, "hex")
    );
    const decrypted = Buffer.concat([
      decipher.update(Buffer.from(hash.t, "hex")),
      decipher.final(),
    ]);

    return decrypted.toString().split(",");
  }
}

function convertEpochToHumanReadable(epoch): string {
  const epoch1 = parseInt(epoch);
  const date = new Date(epoch1);

  const year = date.getFullYear();
  const month = ("0" + (date.getMonth() + 1)).slice(-2);
  const day = ("0" + date.getDate()).slice(-2);
  const hours = ("0" + date.getHours()).slice(-2);
  const minutes = ("0" + date.getMinutes()).slice(-2);
  const seconds = ("0" + date.getSeconds()).slice(-2);

  const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  return formattedDate;
}

enum period {
  "1 day" = "1d",
  "3 days" = "3d",
  "1 week" = "7d",
  "2 weeks" = "14d",
  "1 month" = "1m",
  "2 months" = "2m",
  "6 months" = "6m",
  "1 year" = "1y",
}