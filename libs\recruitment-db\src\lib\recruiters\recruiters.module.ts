import { Modu<PERSON> } from "@nestjs/common";
import { SequelizeModule } from "@nestjs/sequelize";
import { <PERSON>cruiter } from "./recruiters.model";
import { RecruitersService } from "./recruiters.service";
import { UsersModule } from "../users/users.module";
import { RolesModule } from "../roles/roles.module";
import { Auth0Module } from "@microservices/auth";
import { EducationsModule } from "../educations/educations.module";
import { ExperiencesModule } from "../experiences/experiences.module";
import { EmailModule } from "@microservices/email";

@Module({
  imports: [
    SequelizeModule.forFeature([Recruiter]), UsersModule, RolesModule, Auth0Module, EducationsModule,
    ExperiencesModule, EmailModule],
  providers: [RecruitersService],
  exports: [SequelizeModule, RecruitersService],
})
export class RecruitersModule { }
