import {
  Controller,
  Post,
  Body,
  UseGuards,
  Get,
  Query,
  Patch,
  Delete,
  Param,
  Res,
  Logger,
  UnauthorizedException,
  InternalServerErrorException,
  HttpCode,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  UniversalService,
  UniversalDto,
  UniversalIntegration,
  UniversalIntegrationDto,
} from "@microservices/recruitment-db";

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("Universal Screening")
@ApiBearerAuth("access-token")
@Controller("universal")
export class UniversalController {
  constructor(private readonly universalService: UniversalService) {}

  @ApiOperation({ summary: "Order Report Status" })
  @Post("/order/status")
  @HttpCode(200)
  universalWebHook(@Body() body: any) {
    this.universalService.handleUniversalEvents(body)
    return "Universal Order Status Received"
  }

  @ApiOperation({ summary: "Order Report Status" })
  @Get("/order/status")
  universalGetWebHook(@Query() query: any) {
    Logger.log("Universal Webhook Order Status Data", query);
    return "Universal Order Status Received"
  }


  @ApiOperation({ summary: "Initiate Screening" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/initiate-screening")
  @Permissions("OR","background:add","drug:add")
  async initiateScreening(@Body() dto: UniversalDto, @AuthUser() user: any) {
    try {
      const accountDetails = await this.universalService.getAccountDetails(
        user["https://urecruits.com/companyId"]
      );
      Logger.log(accountDetails, 'accountDetails')
      if(!accountDetails){
        throw new UnauthorizedException({status:401, message:'The universal services is not integrated in the profile'});
      }

      return this.universalService.initiateScreening(
        dto,
        user["https://urecruits.com/companyId"],
        accountDetails
      );
    } catch (error) {
      Logger.log("Error encountered during intiating screening", error);
      throw new InternalServerErrorException(error);
    }
  }

  @ApiOperation({ summary: "Integrate Universal Account" })
  @ApiResponse({ status: 200, type: UniversalIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/account/integrate")
  @Permissions("OR","background:add","drug:add") 
  addUniversalAccount(
    @Body() dto: UniversalIntegrationDto,
    @AuthUser() user: any
  ) {
    return this.universalService.addUniversalAccount(
      dto,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get Universal Account Details" })
  @ApiResponse({ status: 200, type: UniversalIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/account/get")
  @Permissions("OR","background:view","drug:view") 
  getAccountDetails(@AuthUser() user: any) {
    return this.universalService.getAccountDetails(
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Remove Universal Acount" })
  @ApiResponse({ status: 200, type: UniversalIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/account/remove")
  @Permissions("OR","background:delete","drug:delete") 
  removeUniversalAccount(@AuthUser() user: any) {
    return this.universalService.removeUniversalAccount(
      user["https://urecruits.com/companyId"]
    );
  }
}
