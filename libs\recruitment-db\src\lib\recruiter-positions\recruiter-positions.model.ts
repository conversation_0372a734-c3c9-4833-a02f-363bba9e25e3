import { Before<PERSON><PERSON>, Column, DataType, <PERSON><PERSON><PERSON>, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { Recruiter } from "../recruiters/recruiters.model";
import { slugify } from "../hooks/hooks";

interface RecruiterPositionAttrs {
  value?: string;
  label?: string;
}

@Table({ tableName: "recruiter-positions", createdAt: false, updatedAt: false })
export class RecruiterPositions extends Model<RecruiterPositions, RecruiterPositionAttrs> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "administrative-assistant",
    description: "Value",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  value: string;

  @ApiProperty({
    example: "Administrative assistant",
    description: "Label",
  })
  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  label: string;

  @HasMany(() => Recruiter)
  recruiter: Recruiter[];

  @BeforeCreate
  static beforeCreateHook(instance: RecruiterPositions): void {
    instance.value = slugify(instance.value);
  }
}

