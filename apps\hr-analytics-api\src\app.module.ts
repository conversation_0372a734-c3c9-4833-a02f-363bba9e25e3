import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { AssessmentsAnalyticsModule, JobsAnalyticsModule, OpenSearchModule, UsersAnalyticsModule } from "@microservices/hr-analytics";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { Auth0Module } from "@microservices/auth";
import { JobsAnalyticsController } from "./jobs-analytics/jobs-analytics.controller";
import { AppController } from "./app.controler";
import { UsersAnalyticsController } from "./users-analytics/users-analytics.controller";
import { AssessmentsAnalyticsController } from "./assessments-analytics/assessments-analytics.controller";

@Module({
  imports: [
    ClientsModule.register([
      {
        name: "HR_ANALYTICS_SERVICE",
        transport: Transport.REDIS,
        options: {
          url: process.env.REDIS_URL || "redis://localhost:6379",
        },
      },
    ]),
    Auth0Module,
    OpenSearchModule,
    JobsAnalyticsModule,
    UsersAnalyticsModule,
    AssessmentsAnalyticsModule
  ],
  controllers: [App<PERSON>ontroll<PERSON>, JobsAnalyticsController, UsersAnalyticsController,AssessmentsAnalyticsController],
})
export class AppModule {}
