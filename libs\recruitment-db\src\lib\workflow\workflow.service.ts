import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Workflow } from "./workflow.model";
import { WorkflowDto } from "./dto/workflow.dto";
import { WorkflowFilterDto } from "./dto/workflowFilter.dto";
import { Op } from "sequelize";
import { User } from "../users/users.model";
import { EditWorkflowDto } from "./dto/edit-workflow.dto";
import { Company } from "../companies/companies.model";

@Injectable()
export class WorkflowService {
  constructor (
    @InjectModel(Workflow) private workflowRepository: typeof Workflow,
  ) {}

  async create (dto: WorkflowDto, userId: number, companyId: number) {
    return await this.workflowRepository.create({ ...dto, authorId: userId, companyId: companyId });
  }

  async getAll (dto: WorkflowFilterDto, companyId: number) {
    const where: any = {
      companyId,
    };
    const order = [];
    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }
    if (dto.search) {
      where.title = {
        [Op.iLike]: `%${dto.search}%`,
      };
    }
    return await this.workflowRepository.findAndCountAll({
      distinct: true,
      where,
      limit: dto.limit,
      offset: dto.offset,
      include: [
        {
          model: User,
          attributes: ["firstname", "middlename", "lastname", "email", "avatar"],
        },
      ],
      order,
    });
  }

  async delete (dto: EditWorkflowDto, companyId: number) {
    return await this.workflowRepository.destroy({
      where: {
        id: dto.id,
        companyId: companyId,
      },
    });
  }

  async edit (dto: EditWorkflowDto, companyId: number) {
    const data = await this.workflowRepository.findOne({
      where: {
        id: dto.id,
        companyId: companyId,
      },
    });
    Object.assign(data, { ...dto, companyId: companyId, authorId: data.authorId });
    return await data.save();
  }

  async getOne (id: number, companyId: number) {
    return await this.workflowRepository.findOne({
      where: {
        id: id,
        companyId: companyId,
      },
      include: [
        {
          model: User,
          attributes: ["firstname", "middlename", "lastname", "email", "avatar"],
        },
        {
          model: Company,
          attributes: ["id","name", "email", "avatar"],
        },
      ],
    });
  }

  async getCompanyWorkflowToJob(companyId: number) {
    return await this.workflowRepository.findAll({
      where: {
        companyId,
      },
      attributes: ["title", "id"],
      order: [["id", "DESC"]]
    })
  }
}
