import { Injectable, Logger } from "@nestjs/common";
import { request } from "undici";
import { SubmissionReq,BatchSumissions } from "./submissions.types";

@Injectable()
export class CodeExecutorService {
  private readonly key =  "0bc9e789b7mshdf4bc415f14d489p1afb2cjsn3a260c3467bf";
  private readonly url= process.env.JUDGE0_URL || "https://judge0-ce.p.rapidapi.com";

  private getRapidApiHeaders() {
    return {
      "Accept": "application/json",
      "Content-Type": "application/json",
      "X-RapidAPI-Key": this.key,
      "X-RapidAPI-Host": process.env.JUDGE0_HOST || "judge0-ce.p.rapidapi.com",
    };
  }

  private async makeRequest(url: string, method: any, headers: Record<string, string>, body?: any) {
    try {
      const { body: responseBody } = await request(url, {
        method,
        headers,
        body: JSON.stringify(body),
      });

      return await responseBody.json();
    } catch (error) {
      Logger.log(error);
      throw error;
    }
  }

  async getSubmission(token: string) {
    const url = `${this.url}/submissions/${token}?fields=status,stdout,stderr,message,compile_output,token,time,memory,created_at,finished_at,number_of_runs,language_id`;
    const headers = this.getRapidApiHeaders();
    return await this.makeRequest(url, "GET", headers);
  }

  async getLanguages() {
    const url = `${this.url}/languages`;
    const headers = this.getRapidApiHeaders();
    return await this.makeRequest(url, "GET", headers);
  }

  async postSingleSubmission(submission: SubmissionReq) {
    const url = `${this.url}/submissions?base64_encoded=true&wait=false`;
    const headers = this.getRapidApiHeaders();
    return await this.makeRequest(url, "POST", headers, submission);
  }

  async postSharedMultiple(zipedFiles: string) {
    const url = `${this.url}/submissions?base64_encoded=false&wait=false`;
    const headers = this.getRapidApiHeaders();
    return await this.makeRequest(url, "POST", headers, {
      language_id: 89,
      additional_files: zipedFiles,
    });
  }

  async postBatchSubmissions(submissions:BatchSumissions){
    const url = `${this.url}/submissions/batch?base64_encoded=true`;
    const headers = this.getRapidApiHeaders();
    return await this.makeRequest(url, "POST", headers, {submissions});
  }

  async getBatchSubmissions(tokens:string[]){
    if (!tokens.length) {
      throw new Error("No tokens provided");
    }
    
   const url = `${this.url}/submissions/batch?tokens=${Array.isArray(tokens) ? tokens.join(",") : tokens}&base64_encoded=false&fields=status,stdout,stderr,message,compile_output,token,time,memory,created_at,finished_at,number_of_runs,language_id`;
   const headers = this.getRapidApiHeaders();
   return await this.makeRequest(url, "GET", headers);
  }
}
