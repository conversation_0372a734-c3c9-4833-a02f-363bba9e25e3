import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
export class FreshsalesDto{
     
    @ApiProperty({example:"<EMAIL>", description:"Company Email"})
    email : string;
    
    @ApiPropertyOptional({ example: 'John', description: 'Firstname of compnay representative' })
    firstName ?:string;

    @ApiPropertyOptional({ example: 'Smith', description: 'Lastname of compnay representative' })
    lastName ?:string;
    
    @ApiPropertyOptional({ example: '+1 *********', description: 'Company Phone' })
    companyPhone ?:string;

    @ApiPropertyOptional({ example: 'Company Registration', description: 'It can be Company Registration or Contact Us from where the contact is reacting' })
    first_source ?:string;

    @ApiPropertyOptional({ example: 'Company Registration', description: 'It is set when user comes to register its company and he had comtacted us before from Contact Us page' })
    latest_source ?:string;

    @ApiPropertyOptional({ example: 'urecruits.com', description: 'Business url' })
    cf_business_url ?:string;

    @ApiPropertyOptional({ example: 10, description: 'Numbers of employees' })
    cf_number_of_employees ?:number;

    @ApiPropertyOptional({ example: 'message', description: 'Message' })
    message ?:string;

}