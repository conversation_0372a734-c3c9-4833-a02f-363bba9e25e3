{"root": "libs/code-executor", "sourceRoot": "libs/code-executor/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/code-executor/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/code-executor"], "options": {"jestConfig": "libs/code-executor/jest.config.js", "passWithNoTests": true}}}, "tags": []}