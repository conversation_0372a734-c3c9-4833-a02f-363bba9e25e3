import * as archiver from "archiver";
import { decode } from "base-64";
import * as fs from "fs";
import { MultiFileSub } from "./multi-file.interface";
import { streamToString } from "./readStream.service";
import { sleep } from "./utils";

export class ArchivateSqliteService {
  constructor() {}
  async generateZip(data: MultiFileSub) {
    const output = fs.createWriteStream(__dirname + "/assets/my.zip");
    const archive = archiver("zip");
    archive.on("error", function (err) {
      throw err;
    });

    archive.pipe(output);
    const decodedString = decode(data.content);
    const mainBuffer = Buffer.from(decodedString);


    if ([76, 52, 53, 54].includes(data.language)) {
      //CPP
      this.CPP(archive, mainBuffer);
    } else if ([75, 48, 49, 50].includes(data.language)){ 
      //C language
      this.CLang(archive, mainBuffer);
    } else if([91, 62].includes(data.language)) {
      //Java
      this.java(archive, mainBuffer);
    } else if ([70, 92, 71].includes(data.language)){
      //Python
      this.python(archive, mainBuffer);
    }else if(data.language==72){
      //RUBY
      this.Ruby(archive, output, mainBuffer);
    }else if([63,93].includes(data.language)){
      //Js
      this.javascript(archive, output, mainBuffer);
    }else if ([88].includes(data.language)){ 
      //Groovy language
      this.groovy(archive, mainBuffer);
    } else if ([60, 95].includes(data.language)){ 
      //Go language
      this.go(archive, mainBuffer);
    } else if ([74, 94].includes(data.language)){ 
      //Typescript language
      this.typescript(archive, mainBuffer);
    }else if ([73].includes(data.language)) {
      //rust
      this.rust(archive, mainBuffer);
    }
    await sleep(3000);
    const zipContent = fs.createReadStream(__dirname + "/assets/my.zip");
    const zipParse = await streamToString(zipContent);

    return zipParse;
  }

  private rust(archive: archiver.Archiver, mainBuffer: Buffer) {
    const compileBuffer = Buffer.from(`#!/bin/bash
      /usr/local/rust-1.40.0/bin/rustc  -rusqlite  main.rs`);

    const runBuffer = Buffer.from(`#!/bin/bash
      ./main`);

    archive
      .append(compileBuffer, { name: 'compile.sh' })
      .append(runBuffer, { name: 'run.sh' })
      .append(mainBuffer, { name: 'main.rs' })
      .finalize();
  }

  private typescript(archive: archiver.Archiver, mainBuffer: Buffer) {
    const compileBuffer = Buffer.from(`#!/bin/bash
    /usr/bin/tsc main.ts`);

    const runBuffer = Buffer.from(`#!/bin/bash
    /usr/local/node-12.14.0/bin/node script.js`);

    archive
      .append(compileBuffer.toString(), { name: "compile" })
      .append(runBuffer, { name: "run" })
      .append(mainBuffer, { name: "main.ts" })
      .finalize();
  }

  private go(archive: archiver.Archiver, mainBuffer: Buffer) {
    const compileBuffer = Buffer.from(`#!/bin/bash
      go build main.go`);

    const runBuffer = Buffer.from(`#!/bin/bash
      ./main`);
    archive
      .append(compileBuffer, { name: "compile.sh" })
      .append(runBuffer, { name: "run.sh" })
      .append(mainBuffer, { name: "main.go" })
      .finalize();
  }

  private groovy(archive: archiver.Archiver, mainBuffer: Buffer) {
    const runBuffer = Buffer.from(`#!/bin/bash
      /usr/local/bin/java -cp \".:/usr/local/groovy-3.0.3/lib/*\":sqlite-jdbc-********.jar main"`);

    const compileBuffer = Buffer.from(`#!/bin/bash
      /usr/local/groovy-3.0.3/bin/groovyc main.groovy`);

    // Appending scripts and files to the archive
    archive
      .append(compileBuffer, { name: 'compile' })
      .append(runBuffer, { name: 'run' })
      .append(mainBuffer, { name: 'main.groovy' })
      .append(fs.createReadStream(__dirname + `/assets/db.sqlite`), {
        name: `db.sqlite`,
      })
      .append(
        fs.createReadStream(__dirname + `/assets/sqlite-jdbc-********.jar`),
        { name: `sqlite-jdbc-********.jar` }
      )
      .finalize();
  }

  private javascript(archive: archiver.Archiver, output: fs.WriteStream, mainBuffer: Buffer) {
    archive.pipe(output);

    const runBuffer = Buffer.from(`#!/bin/bash
      /usr/local/node-12.14.0/bin/node script.js`);

    archive
      .append(runBuffer, { name: 'run' })
      .append(mainBuffer, { name: 'script.js' })
      .finalize();
  }

  private Ruby(archive: archiver.Archiver, output: fs.WriteStream, mainBuffer: Buffer) {
    archive.pipe(output);

    const runBuffer = Buffer.from(`#!/bin/bash
      /usr/local/ruby-2.7.0/bin/ruby script.rb`);

    archive
      .append(runBuffer, { name: 'run' })
      .append(mainBuffer, { name: 'script.rb' })
      .finalize();
  }

  private python(archive: archiver.Archiver, mainBuffer: Buffer) {
    const runBuffer = Buffer.from(`#!/bin/bash
          /usr/local/python-3.8.1/bin/python3 main.py`);

    archive
      .append(fs.createReadStream(__dirname + `/assets/db.sqlite`), {
        name: `db.sqlite`,
      })
      .append(runBuffer, { name: "run" })
      .append(mainBuffer, { name: "main.py" })
      .finalize();
  }

  private java(archive: archiver.Archiver, mainBuffer: Buffer) {
    const runBuffer = Buffer.from(`#!/bin/bash
          /usr/local/openjdk13/bin/java -classpath ".:sqlite-jdbc-********.jar" Main`);
    const compileBuffer = Buffer.from(`#!/bin/bash
          /usr/local/openjdk13/bin/javac Main.java`);

    archive
      .append(compileBuffer, { name: "compile" })
      .append(runBuffer, { name: "run" })
      .append(mainBuffer, { name: "Main.java" })
      .append(fs.createReadStream(__dirname + `/assets/db.sqlite`), {
        name: `db.sqlite`,
      })
      .append(
        fs.createReadStream(__dirname + `/assets/sqlite-jdbc-********.jar`),
        {
          name: `sqlite-jdbc-********.jar`,
        }
      )
      .finalize();
  }

  private CLang(archive: archiver.Archiver, mainBuffer: Buffer) {
    const runBuffer = Buffer.from(`#!/bin/bash
        ./a.out`);
    const compileBuffer = Buffer.from(`#!/bin/bash
        gcc main.c -o a.out -lsqlite3`);

    archive
      .append(compileBuffer, { name: "compile.sh" })
      .append(runBuffer, { name: "run.sh" })
      .append(fs.createReadStream(__dirname + `/assets/db.sqlite`), {
        name: `db.sqlite`,
      })
      .append(mainBuffer, { name: "main.c" })
      .finalize();
  }

  private CPP(archive: archiver.Archiver, mainBuffer: Buffer) {
    const runBuffer = Buffer.from(`#!/bin/bash
      /usr/local/gcc-7.4.0/lib64 ./a.out`);
    const compileBuffer = Buffer.from(`#!/bin/bash
      /usr/local/gcc-7.4.0/bin/g++ main.cpp`);

    //Adding files to archive separetly
    archive
      .append(compileBuffer, { name: "compile" })
      .append(runBuffer, { name: "run" })
      .append(mainBuffer, { name: "main.cpp" })
      .finalize();
  }
}