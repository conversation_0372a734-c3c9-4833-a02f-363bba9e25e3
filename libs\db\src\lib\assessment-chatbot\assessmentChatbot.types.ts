export enum AssessmentType {
  TAKE_HOME = 'take-home-task',
  LIVE_CODING = 'live-task',
  DOMAIN = 'domain',
}

export class ConversationDto {
  userInput: string;
  sessionId: string;
  companyId?: number;
  userId?: number;
  taskType?: 'take-home-task' | 'live-task' | 'domain';
  workflowContext?: {
    isFromWorkflow: boolean;
    assessmentType?: 'domain' | 'take-home' | 'live-coding';
    workflowSteps?: string[];
    workflowTitle?: string;
    returnToWorkflow?: boolean;
  };
}

export interface AssessmentInfo {
  name?: string;
  numQuestions?: number;
  language?: string;
  languageId?: number;
  includeTestCases?: boolean;
  includeJdbc?: boolean;
  questions?: string; // Stores generated questions as a string, per Python code
  jsonOutput?: {
    name: string;
    description: string;
    questions?: Array<{
      name: string;
      description?: string;
      outputDescription?: string;
      outputType?: string;
      candidateInstruction?: string;
      starterCode?: string;
      testCaseInputs?: string | null;
      languageId?: number;
      useAIGeneratedTestCases?: boolean;
      packageId?: string | null;
      databaseId?: number;
      testcases?: Array<{ inputs: Array<{ name: string; value: any }>; output: any }>;
    }>;
  };
}

export interface ConversationState {
  userId?: number;
  companyId?: number;
  sessionId: string;
  taskType: 'take-home-task' | null;
  assessmentInfo: AssessmentInfo;
  conversationHistory: Array<{ role: string; content: string }>;
  currentStep: string;
}

export interface DomainQuestion {
  text?: string;
  type?: 'multiple' | 'single' | 'text';
  options?: string[];
  correctAnswer?: string | string[];
  score?: number;
}

export interface StructuredQuestion {
  nameQuestion: string;
  questionDescription: string;
  candidateInstruction: string;
  starterCode: string;
  outputType: string;
  outputDescription: string;
  testCaseInputs: Array<{
    name: string;
    type: string;
    description: string;
  }>;
  testCases: Array<{
    input: string;
    output: string;
  }>;
}

export interface ChatSession {
  sessionId: string;
  assessmentType?: AssessmentType;
  currentStep: string;
  data: {
    name?: string;
    numQuestions?: number;
    language?: string;
    languageId?: number;
    includeTestCases?: boolean;
    includeJdbc?: boolean;
    jdbcPackage?: string | null;
    packageId?: number;
    questions?: string;
    structuredQuestions?: StructuredQuestion[];
    selectedTopic?: string;
    jsonOutput?: any & { id?: number };
    companyId?: number;
    userId?: number;
    // Additional properties for live coding and domain assessments
    description?: string;
    duration?: number;
    passingScore?: number;
    // Domain assessment specific properties
    industry?: string;
    department?: string;
    industryId?: number;
    departmentId?: number;
    additionalDepartment?: string;
    useExistingQuestions?: boolean;
    useAIGenerated?: boolean;
    newQuestions?: DomainQuestion[];
    aiGeneratedQuestions?: DomainQuestion[];
    currentQuestionIndex?: number;
    // Workflow assignment properties
    id?: number;
    assessmentId?: number;
    workflowAssignment?: {
      assessmentId: number;
      assessmentName: string;
      assessmentType: string;
      assigned: boolean;
    };
  };
  completed: boolean;
  createdAt?: Date;
  isFromWorkflow?: boolean;
  workflowContext?: {
    isFromWorkflow: boolean;
    assessmentType?: 'domain' | 'take-home' | 'live-coding';
    workflowSteps?: string[];
    workflowTitle?: string;
    returnToWorkflow?: boolean;
  };
}

export interface ChatResponse {
  response: string;
  sessionId: string;
  currentStep: string;
  assessmentType?: string;
  completed: boolean;
  data: ChatSession['data'];
  assessmentCreated?: boolean;
  assessmentId?: number;
  assessmentName?: string;
  workflowAssignment?: {
    shouldReturnToWorkflow: boolean;
    workflowTitle?: string;
  };
}