{"version": 2, "projects": {"assessment-api": "apps/assessment-api", "auth": "libs/auth", "calendars": "libs/calendars", "code-executor": "libs/code-executor", "db": "libs/db", "editor-manager": "libs/editor-manager", "email": "libs/email", "hr-analytics": "libs/hr-analytics", "hr-analytics-api": "apps/hr-analytics-api", "integrations": "libs/integrations", "integrations-api": "apps/integrations-api", "pre-recruitment-api": "apps/pre-recruitment-api", "qdrant": "libs/qdrant", "kinesis": "libs/kinesis", "recruitment-db": "libs/recruitment-db", "temporal": "apps/temporal", "twilio": "libs/twilio"}}