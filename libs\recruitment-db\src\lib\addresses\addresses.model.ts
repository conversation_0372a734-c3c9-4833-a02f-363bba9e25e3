import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../companies/companies.model'

interface AddressAttrs {
  street?: string;
  location?: string;
  country?: string;
  email?: string;
  phone?: string;
  companyId: number;
}

@Table({ tableName: 'addresses', createdAt: false, updatedAt: false })
export class Address extends Model<Address, AddressAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: '19 W. 34th Street Suite',
    description: 'Company Street',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  street: string;

  @ApiProperty({
    example: 'New York',
    description: 'Company Location',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  location: string;

  @ApiProperty({
    example: 'USA',
    description: 'Country',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  country: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Company Email',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  email: string;

  @ApiProperty({
    example: '+380978760605',
    description: 'Company Phone',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  phone: string;

  @ApiProperty({ example: '1', description: 'Company ID' })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @BelongsTo(() => Company)
  company: Company;
}
