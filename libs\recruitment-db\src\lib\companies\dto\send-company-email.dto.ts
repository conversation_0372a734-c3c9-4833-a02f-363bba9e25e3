import { ApiProperty } from '@nestjs/swagger';

export class SendCompanyEmailDto {
  @ApiProperty({
    example: 'gmail',
    description: 'gmail or outlook',
  })
    provider: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Recipient\'s email address. Multiple addresses can be separated by commas',
  })
    to: any;

  @ApiProperty({
    example: 'jobDetail',
    description: 'Template name for email',
  })
    template: string;

  @ApiProperty({
    example: 'Job detail',
    description: 'Subject',
  })
    subject: string;

  @ApiProperty({
    example: 'Technical Sales Specialist',
    description: 'Position',
  })
    position: string;

  @ApiProperty({
    example: 'An International Product Specialist (IPS)...',
    description: 'Body content',
  })
    body: string;

  @ApiProperty({
    example: 'https://app.urecruits.com/header/bc0647aa912761dc444d.svg,https://urecruits-dev-marketing-cms-marketing-cms-bucket.s3.amazonaws.com/facebook_fb55e26764.svg',
    description: 'Comma separated URLs',
  })
    attachments: string;
}
