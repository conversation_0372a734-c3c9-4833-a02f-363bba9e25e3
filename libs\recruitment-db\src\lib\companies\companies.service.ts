import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { SendCompanyEmailDto } from './dto/send-company-email.dto';
import { EditPermissionsDto } from './dto/editPermissionsForRole.dto';
import { IntegrationsService } from '../integrations/integrations.service';
import { EmailService } from '@microservices/email';
import { Company } from "./companies.model";
import { InjectModel } from "@nestjs/sequelize";
import { AddressesService } from "../addresses/addresses.service";
import { GetAllCompaniesDto, RegisterCompanyDto } from "./dto/register-company.dto";
import { UsersService } from "../users/users.service";
import { Auth0Service } from "@microservices/auth";
import { RolesService } from "../roles/roles.service";
import { UpdateCompanyDto } from "./dto/update-company.dto";
import { Industry } from "../industries/industries.model";
import { User } from "../users/users.model";
import { GetCompaniesForJobDto } from "./dto/getCompaniesForJob.dto";
import { Op, Sequelize } from "sequelize";
import { GetPublicCompaniesDto } from "./dto/getPublicCompanies.dto";
import { Jobs } from "../job/job.model";
import { Location } from "../locations/location.model";
import { Recruiter } from "../recruiters/recruiters.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { FreshsalesService } from '../freshsales/freshsales.service';
import { FreshsalesDto } from "../freshsales/freshsales.dto";
import { SubscriptionService } from "../subscription/subscription.service";
import { HttpService } from "@nestjs/axios";

@Injectable()
export class CompaniesService {
  constructor(
    @InjectModel(Company) private companyRepository: typeof Company,
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(Recruiter) private recruiterRepository: typeof Recruiter,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    private addressesService: AddressesService,
    private usersService: UsersService,
    private auth0Service: Auth0Service,
    private rolesService: RolesService,
    private integrationsService: IntegrationsService,
    private emailService: EmailService,
    private subscriptionService: SubscriptionService,
    private freshsalesService: FreshsalesService,
    private httpService: HttpService
  ) { }

  async getAllCompanies(dto: GetAllCompaniesDto) {
    const companies = await this.companyRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      include:[
        {
          model:User,
          as:"owner",
          attributes:["id","firstname","lastname","middlename","email","phone","avatar"]
        },
        {
          model:Industry,
          attributes:["id","value","label"]
        }
      ]
    })
    return companies
  }

  async registerCompany(dto: RegisterCompanyDto) {
    await this.isExistCompany(dto.tenantId);
    await this.usersService.isExistUser(dto.email);
    const user = await this.usersService.createUser(dto);
    const company = await this.companyRepository.create({
      name: dto.name,
      email: dto.email,
      phone: dto.companyPhone,
      firstname: dto.firstname,
      lastname: dto.lastname,
      middlename: dto.middlename,
      company_type: dto.company_type,
      tenantId: dto.tenantId,
      avatar: dto.avatar,
      gallery: dto.gallery,
      ownerId: user.id,
      industryId: dto.industryId,
      addresses: [
        {
          street: "",
          location: "",
          country: "",
          email: dto.email,
          phone: dto.phone,
        }],
    });

    

    //create default roles for a company
    await this.createCompanyRole('Company Owner', company.tenantId, company.id)

    const ownerRole = await this.rolesService.getRoleByLabel(`Company Owner_${company.tenantId}`, company.id,);
    if (ownerRole) {
      await user.$add("roles", [ownerRole.id]);
    }

    const auth0User = await this.auth0Service.createCompanyMember(
      { ...dto, companyId: company.id, recruiterId: null, userId: user.id });

    if (ownerRole) {
      await this.auth0Service.assignRoles(auth0User.user_id, [`${ownerRole.value}`]);

      Object.assign(user, { authId: auth0User.user_id, companyId: company.id });
      await user.save();
    }

    const recordData = [
      this.getUserDataForQdrant(user),
      this.getCompanyDataForQdrant(company)
    ]
    const contactData: FreshsalesDto = {
      firstName: dto.firstname,
      lastName: dto.lastname,
      email: dto.email,
      companyPhone: dto.companyPhone
    }

    try {
      await this.freshsalesService.upsertContact(contactData, 'company');
    } catch (er) {
      console.log("Error in freshsale upsert contact", er)
    }
    const createdCompany = await this.companyRepository.findOne({
      where:{
        id:company.id
      },
      include:[
        {
          model:User,
          as:"owner",
        },
        Industry
      ]
    });
    if (company.id) {
      try {
        const subscription = await this.subscriptionService.createDefaultSubscriptionWithoutCard({ companyId: company.id, checkCompany: true })
        if (subscription && subscription?.recordData) {
          recordData.push(subscription.recordData)
        }
      } catch (error) {
        console.log("Error creating subscription for a company", error)
      }

      try{
        const dummyJobs = this.httpService.post(`${process.env.RECRUITMENT_API_URI}/api/job/dummy`, { company:createdCompany }).toPromise().then(data=>console.log("created dummy jobs for company"))
      }catch(err){
        console.log("Error creating dummy jobs for company",err)
      }

      try{
        const dummyTakeHome  = this.httpService.post(`${process.env.ASSESSMENT_API_URI}/api/take-home-task/dummy`, {companyId:company.id}).toPromise().then(data=>console.log("created dummy take home for company"))
      }catch(err){
        console.log("Error creating dummy take home for company",err)
      }

      try{
        const dummyLiveTask  = this.httpService.post(`${process.env.ASSESSMENT_API_URI}/api/live-coding/dummy`, {companyId:company.id}).toPromise().then(data=>console.log("created dummy live task for company"))
      }catch(err){
        console.log("Error creating dummy live task for company",err)
      }
      try{
        const dummyDomainAssessments  = this.httpService.post(`${process.env.ASSESSMENT_API_URI}/api/domain-questions/dummy`, {companyId:createdCompany.id}).toPromise().then(data=>console.log("created dummy domain assessments for company"))
      }catch(err){
        console.log("Error creating dummy domain assessments for company",err)
      }
    }

    return { response: company, recordData }
  }

  async createCompanyRole(roleName: string, tenantId, companyId) {
    try {
      const isExistRole = await this.rolesService.getRoleByLabel(`${roleName}_${tenantId}`, companyId)
      if (!isExistRole) {
        const role = await this.rolesService.getDefaultCompanyRole(roleName)
        if (role) {
          await this.auth0Service.setDefaultPermissionsToCompanyRole(role, tenantId)
          await this.rolesService.createDefaultCompanyRole(roleName, tenantId, companyId)
        }
      }
    } catch (error) {
      Logger.log("Error while creating default company Roles", error)
    }
  }

  async getCompany(id: number) {
    return await this.companyRepository.findOne({
      where: {
        id: id,
      },
      include: [
        {
          model: Industry,
        },
      ],
    });
  }

  async getCompanyByEmail(email: number) {
    return await this.companyRepository.findOne({
      where: {
        email: email,
      },
      include: [
        {
          model: Industry,
        },
      ],
    });
  }

  async getCompanyForPayment(id: number) {
    return await this.companyRepository.findOne({
      where: { id },
      include: [
        {
          model: User,
          as: "owner",
        },
      ],
    });
  }

  async updateStripeId(stripeId: string, companyId) {
    const company = await this.companyRepository.findByPk(companyId, { include: [Industry,User] });
    company.stripeId = stripeId;
    await company.save();
    return { response: company?.dataValues, recordData: this.getCompanyDataForQdrant(company) }
  }

  async getCompanyByStripeId(stripeId) {
    return await this.companyRepository.findOne({
      where: { stripeId }, include: [
        {
          model: User,
          as: "owner",
        },
      ]
    })
  }

  async addDataonOpensearch(companyId) {
    let recordData = [];
    const company = await this.getCompany(companyId);
    if (company) {
      recordData.push({
        for: 'company',
        data: company
      })
      if (company?.ownerId) {
        const user = await this.usersService.getUserByUserId(company.ownerId);
        if (user) {
          recordData.push({
            for: 'user',
            data: {
              userId: user.id,
              user_type: 'company_owner',
              companyId: company.id,
              email: user.email,
              phone: user.phone,
              name: {
                firstname: user?.firstname,
                middleName: user?.middlename,
                lastName: user?.lastname
              },
              company: company,
              avatar: user?.avatar,
              role: user?.roles?.map(i => ({ id: i.id, name: i.label })),
              profileStatus: 'Active',
              createdAt: Date.now(),
            }
          })

        }
      }
    }
    return { response: "", recordData }
  }

  async isExistCompany(tenantId: string) {
    const find = await this.companyRepository.findOne({
      where: { tenantId },
      attributes: ["id", "tenantId"],
    });
    if (!!find) {
      throw new HttpException("Subdomain already exists!", HttpStatus.CONFLICT);
    }
  }

  async updateCompany(dto: UpdateCompanyDto) {
    const company = await this.companyRepository.findByPk(dto.id, { include: [Industry] });
    dto.stripeId = company.stripeId;
    Object.assign(company, dto);
    await company.save();
    return { response: company?.dataValues, recordData: { for: 'company', data: company?.dataValues } }
  }

  async aim(companyId: number) {
    const roles = await this.rolesService.getAIM(companyId);
    const result = [];
    for (const role of roles) {
      const count = await this.usersService.countUserOnRole(companyId, role.id);
      result.push({
        count, role
      });
    }
    return result;
  }

  async getCompaniesForJobs(dto: GetCompaniesForJobDto) {
    const where: any = {}
    if (dto.search && dto.search.length > 0) {
      where.name = {
        [Op.iLike]: `%${dto.search}%`,
      }
    }
    return await this.companyRepository.findAndCountAll({
      where,
      attributes: ["id", "name", "tenantId"],
      limit: dto.limit,
      offset: dto.offset
    })
  }

  async getPublicCompanies(dto: GetPublicCompaniesDto) {
    const order = [];
    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType])
    } else {
      order.push(["id", "DESC"]);
    }
    const where: any = {};
    if (dto.search && dto.search.length > 0) {
      where.name = {
        [Op.iLike]: `%${dto.search}%`,
      }
    }
    const count = await this.companyRepository.findAndCountAll({ where })
    return {
      rows: await this.companyRepository.findAll({
        limit: dto.limit,
        offset: dto.offset,
        order,
        where,
        attributes: [
          "id", "name", "tenantId", "avatar", "addresses", "about",
          [Sequelize.fn('COUNT', Sequelize.col('jobs.id')), 'jobCount'],
        ],
        include: [
          {
            model: Industry,
            attributes: ["id", "value", "label"],
          },
          {
            model: Jobs,
            where: {
              isAssessment: false,
              status: {
                [Op.iLike]: `%publish%`,
              }
            },
            required: false,
            duplicating: false,
            attributes: [],
          },
        ],
        group: ['Company.id', "industry.id"],
      }),
      count: count.count,
    };
  }

  async sendEmail(dto: SendCompanyEmailDto, companyId: number) {
    const required = [
      'provider',
      'to',
      'template',
      'subject',
      'position',
      'body',
    ];
    for (let i = 0; i < required.length; i++) {
      if (!dto[required[i]]) {
        throw new HttpException(`Required parameter '${required[i]}' is missing`, HttpStatus.BAD_REQUEST);
      }
    }

    const company = await this.companyRepository.findOne({
      where: {
        id: companyId,
      },
      attributes: ['avatar'],
    });
    const companyLogo = company && company.avatar ? company.avatar : '';

    const providerClient = await this.integrationsService.getProviderClient(dto.provider, companyId);
    if (!providerClient) {
      throw new HttpException('Connection not found', HttpStatus.NOT_FOUND);
    }

    switch (dto.provider) {
      case 'gmail':
        return await this.emailService.sendGmail(providerClient, dto.template, companyLogo, dto.to, dto.subject, dto.position, dto.body, dto.attachments);
      case 'outlook':
        return await this.emailService.sendOutlook(providerClient, dto.template, companyLogo, dto.to, dto.subject, dto.position, dto.body, dto.attachments);
      default:
        throw new HttpException('Provider not found', HttpStatus.NOT_FOUND);
    }
  }

  async getPublicCompanyByTenantId(tenantId: string) {
    const company = await this.companyRepository.findOne({
      where: {
        tenantId,
      },
      attributes: ["id", "name", "email", "phone", "tenantId", "about", "avatar", "gallery", "addresses", "website"],
      include: [
        {
          model: Jobs,
          where: {
            status: {
              [Op.iLike]: `%publish%`,
            },
            isAssessment: false
          },
          separate: true,
          limit: 4,
          attributes: ["id", "title", "experienceMin", "experienceMax", "salaryMonthMin", "salaryMonthMax", "salaryYearMin", "salaryYearMax", "skills"],
          required: false,
          order: [["id", "DESC"]],
          include: [
            {
              model: Location,
              attributes: ["id", "city", "state"],
            },
          ],
        },
      ],
    });

    if (!company) {
      throw new HttpException("Company not found", HttpStatus.NOT_FOUND);
    }

    return company;
  }

  async getStatistic(companyId: number) {
    const addedToCompany = await this.userRepository.findAll({
      attributes: ["createdAt", "id"],
      order: [["createdAt", "DESC"]],
      where: {
        createdAt: {
          [Op.between]: [new Date(Date.now() - *********), new Date()],
        },
        companyId,
      }
    })

    const allEmployers = await this.userRepository.count({
      where: {
        companyId,
      }
    })

    const lastEmployers = await this.userRepository.findAll({
      attributes: ["createdAt", "id", "firstname", "lastname", "middlename", "avatar"],
      order: [["createdAt", "DESC"]],
      limit: 5,
      where: {
        companyId,
      }
    })
    const publishJobs = await this.jobRepository.count({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%publish%`,
        },
        isAssessment: false
      }
    })
    const onHold = await this.jobRepository.count({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%On hold%`,
        },
        isAssessment: false
      }
    })
    const closed = await this.jobRepository.count({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%Closed%`,
        },
        isAssessment: false
      }
    })
    const pending = await this.jobRepository.count({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%Pending%`,
        },
        isAssessment: false
      }
    })
    const draftJobs = await this.jobRepository.count({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%Draft%`,
        },
        isAssessment: false
      }
    })
    const allJobs = await this.jobRepository.count({
      where: {
        companyId,
        isAssessment: false
      }
    })
    const jobsSummaryData = {
      totalJobsCount: allJobs,
      summary: [
        {
          value: publishJobs,
          name: "Publish",
        },
        {
          value: onHold,
          name: "On hold",
        },
        {
          value: closed,
          name: "Closed",
        },
        {
          value: pending,
          name: "Pending",
        },
        {
          value: draftJobs,
          name: "Draft",
        },
      ],
    }

    const pendingJobs30Days = await this.jobRepository.findAll({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%Pending%`,
        },
        createdAt: {
          [Op.between]: [new Date(Date.now() - 18144000000), new Date()],
        },
        isAssessment: false
      },
      attributes: ["id", "createdAt", "title"]
    })

    const lastPublicJobs = await this.jobRepository.findAll({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%publish%`,
        },
        isAssessment: false
      },
      attributes: ["id", "createdAt", "title", "status"],
      include: [{
        model: Subscribe,
        required: false,
        attributes: ["id", "userId", "jobId", "match", "saveJob", "applyJob", "subscribeJob"],
        include: [
          {
            model: User,
            attributes: ["id", "avatar"]
          }
        ]
      }],
      limit: 3,
    })

    const lastPublicJobsWeek = await this.jobRepository.findAll({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%publish%`,
        },
        createdAt: {
          [Op.between]: [new Date(Date.now() - 18144000000), new Date()],
        },
        isAssessment: false
      },
      attributes: ["id", "createdAt", "title", "status"],
    })

    const allPublicJobs = await this.jobRepository.count({
      where: {
        companyId,
        status: {
          [Op.iLike]: `%publish%`,
        },
        isAssessment: false
      },
    })

    return {
      addedToCompany,
      allEmployers,
      lastEmployers,
      jobsSummaryData,
      pendingJobsData: pendingJobs30Days,
      lastPublicJobs,
      lastPublicJobsWeek,
      allPublicJobs
    }
  }

  async addPermissionsToCompanyRole(query: EditPermissionsDto) {
    try {
      await this.auth0Service.addPermissionsByRole(query.roleId, query.permissions)
      return await this.rolesService.updateRolePermissions(query.roleId, query.dbPermissions)
    } catch (error) {
      Logger.log(`Error while adding permission to role`, error)
    }
  }

  async removePermissionsToCompanyRole(query: EditPermissionsDto) {
    try {
      await this.auth0Service.removePermissionsByRole(query.roleId, query.permissions)
      return await this.rolesService.updateRolePermissions(query.roleId, query.dbPermissions)
    } catch (error) {
      Logger.log(`Error while removing permission to role`, error)
    }
  }

  async getAllCompanyRoles(companyId) {
    try {
      return await this.rolesService.getCompanyRoles(companyId)
    } catch (error) {
      Logger.log(`Error while getting company role`, error)
    }
  }

  getCompanyDataForQdrant(company:Company,action='create'){
    const vectorStr = action === 'create' ? [
      company?.id ? `CompanyId:${company.id}` : '',
      company?.name ? `Name:${company.name}` : '',
      company?.avatar ? `ProfilePicture:${company.avatar}` : '',
      company?.about ? `About:${company.about}` : '',
      company?.tenantId ? `TenantId:${company.tenantId}` : '',
      company?.email ? `CompanyEmail:${company.email}` : '',
      company?.phone ? `CompanyPhone:${company.phone}` : '',
      company?.company_type ? `CompanyType:${company.company_type}` : '',
      company?.ownerId ? `OwnerId:${company.ownerId}` : '',
      company?.owner?.firstname ? `OwnerFirstName:${company.owner.firstname}` : '',
      company?.owner?.lastname ? `OwnerLastName:${company?.owner?.lastname}` : '',
      company?.industry ? `Industry:${company?.industry?.label}` : '',
      company?.addresses?.length && (company?.addresses[0]?.street||company?.addresses[0]?.location||company?.addresses[0]?.country) ? `Address:${company.addresses[0].street}, ${company.addresses[0].location}, ${company.addresses[0].country}` : '',
      company?.website ? `Website:${company?.website}` : '',
      company.stripeId ? `StripeId:${company.stripeId}` : '',
    ].filter(Boolean).join(', ') : '';

    return {
      for:'company',
      id:company.id,
      action,
      ...(action === 'create' && {
        str:vectorStr,
        payload:{
        id:company?.id,
        name:company?.name,
        avatar:company?.avatar,
        about:company?.about,
        tenantId:company?.tenantId,
        email:company?.email,
        phone:company?.phone,
        company_type:company?.company_type,
        ownerId:company?.ownerId,
        ownerFirstName:company?.owner?.firstname,
        ownerLastName:company?.owner?.lastname,
        industryId:company?.industryId,
        industryLabel:company?.industry?.label,
        addresses:company?.addresses,
        website:company?.website,
        stripeId:company?.stripeId,
        createdAt: company?.createdAt,
        }
      })
    }

  }

  getUserDataForQdrant(user:User,action='create'){
    const vectorStr = action === 'create' ? [
      user?.id ? `UserId:${user.id}` : '',
      user?.firstname ? `FirstName:${user.firstname}` : '',
      user?.lastname ? `LastName:${user.lastname}` : '',
      user?.middlename ? `MiddleName:${user.middlename}` : '',
      user?.avatar ? `ProfilePicture:${user.avatar}` : '',
      user?.email ? `Email:${user.email}` : '',
      user?.phone ? `Phone:${user.phone}` : '',
      user?.roles?.length ? `Roles:${user.roles.map(role => role.label.split('_')[0]).join(', ')}` : '',
      `${user?.company?.name ? ` - Owner of ${user?.company?.name}` : ''}`
    ].filter(Boolean).join(', ') : '';
    
    return {
      for:'users',
      id:user.id,
      action,
      ...(action === 'create' && {
        str:vectorStr,
      payload:{
        id:user.id,
        user_type: 'company_owner',
        firstname:user?.firstname,
        lastname:user?.lastname,
        middlename:user?.middlename,
        avatar:user?.avatar,
        email:user?.email,
        phone:user?.phone,
        role:user?.roles,
        companyId:user?.companyId,
        createdAt:user?.createdAt,
      }
      })
    }
  }
}
