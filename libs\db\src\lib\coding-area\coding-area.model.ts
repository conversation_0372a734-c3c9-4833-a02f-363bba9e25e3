import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';

@Table({
  tableName: 'coding_area',
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class CodingArea extends Model<CodingArea> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    example: '10',
    description: 'Id number of the assignment task',
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
    assignmentId: number;

  @ApiProperty({
    example: '10',
    description: 'Timestemp when ',
  })
  @Column({ type: DataType.DATE, allowNull: false })
    timestamp: Date;

  @ApiProperty({
    example: 'Piece of code',
    description: 'Coding Area Content',
  })
  @Column({ type: DataType.TEXT, allowNull: false })
    editorState: string;

  @ApiProperty({
    example: 'Piece of code',
    description: 'Coding Area Content',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    actionType: string;

  @ApiProperty({ example: '10', description: 'Coding area basic information' })
  @Column({ type: DataType.JSON, allowNull: false })
    data: JSON;
}
