import { Controller, Get, Post, Query, UseGuards } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import { UsersAnalyticsService } from "@microservices/hr-analytics";
import { MembersAnalyticsQueryDto } from "./dto/members-analytics-query.dto";

@ApiTags("Users Analytics")
@ApiBearerAuth("access-token")
@Controller("analytics/users")
export class UsersAnalyticsController {
  constructor(private readonly usersAnalyticsService: UsersAnalyticsService) {}

  @ApiOperation({ summary: "Company Members Analytics" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("team-members:view")
  async getUsersAnalytics(
    @Query() queryParams:MembersAnalyticsQueryDto,
    @AuthUser() user: any
  ) {
    return await this.usersAnalyticsService.getMembersAnalytics(
      queryParams,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Members's Age Pie Graph" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("pie")
  @Permissions("team-members:view")
  async getJobsAnalyticsPie(
    @AuthUser() user: any
  ) {
    return await this.usersAnalyticsService.getMembersAgeAnalyticsPie(
      user["https://urecruits.com/companyId"]
    );
  }
}
