import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SequelizeModule } from '@nestjs/sequelize';
import { AssessmentChatbot } from './assessmentChatbot.model';
import { AssessmentChatbotService } from './assessmentChatbot.service';
import { QuestionsModule } from '../questions/questions.module';
import { TakeHomeTaskModule } from '../take-home/take-home-task.module';
import { Questions } from '../questions/questions.model';
import { TakeHomeTask } from '../take-home/take-home-task.model';
import { LiveCodingModule } from '../live-coding/live-coding.module';
import { LiveCoding } from '../live-coding/live-coding.model';
import { DomainQuestions } from '../domain-questions/domain-questions.model';
import { DomainQuestionsModule } from '../domain-questions/domain-questions.module';
@Module({
  imports: [
    ConfigModule,
    SequelizeModule.forFeature([AssessmentChatbot, Questions, TakeHomeTask, LiveCoding, DomainQuestions]), // Register models
    QuestionsModule,
    TakeHomeTaskModule,
    LiveCodingModule,
    DomainQuestionsModule,
  ],
  providers: [AssessmentChatbotService],
  exports: [AssessmentChatbotService],
})
export class AssessmentChatbotModule {}