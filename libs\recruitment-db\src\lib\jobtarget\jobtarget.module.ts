import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { JobTargetIntegration } from './jobtarget.model';
import { JobTargetService } from './jobtarget.service';
import { Company } from '../companies/companies.model';
import { User } from '../users/users.model';
import { Jobs } from '../job/job.model';

@Module({
  imports: [SequelizeModule.forFeature([JobTargetIntegration, Company, User, Jobs])],
  providers: [JobTargetService],
  exports: [JobTargetService]
})
export class JobTargetModule {}
