import { BadRequestException, HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { CompaniesService } from "../companies/companies.service";
import { InjectStripe } from "nestjs-stripe";
import Stripe from "stripe";
import { PaymentMethodDto } from "./dto/paymentMethod.dto";
import { PaymentMethodIdDto } from "./dto/paymentMethodId.dto";
import { InjectModel } from "@nestjs/sequelize";
import { Payment } from "./payment.model";
import { UsersService } from "../users/users.service";
import { SubscriptionService } from "../subscription/subscription.service";
import { CompanySubscriptionDto } from "../subscription/dto/companySubscription.dto";
import { ProductService } from "../product/product.service";
import { PriceService } from "../price/price.service";
import { PlanPackageService } from "../plan-packages/plan-packages.service";
import { PlanService } from "../plan/plan.service";
import { EmailService } from "@microservices/email";
import { SubscriptionAddon } from "../subscription/subscription-addon.model";
import { Op } from "sequelize";

@Injectable()
export class PaymentService {
  public constructor(
    @InjectModel(Payment) private paymentRepository: typeof Payment,
    @InjectModel(SubscriptionAddon) private subscriptionAddonRepository: typeof SubscriptionAddon,
    private companiesService: CompaniesService,
    private userService: UsersService,
    private subscriptionService: SubscriptionService,
    private productService: ProductService,
    private priceService: PriceService,
    private planPackageService: PlanPackageService,
    private planService: PlanService,
    private emailService: EmailService,
    @InjectStripe() private readonly stripeClient: Stripe,
  ) { }

  async getCompany(companyId: number) {
    const company = await this.companiesService.getCompanyForPayment(companyId);

    if (!company.email) {
      throw new HttpException(
        "Company email is empty.",
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      id: company.id,
      stripeId: company.stripeId,
      email: company.owner.email,
      createdAt: company.createdAt,
    };
  }

  async getPaymentMethods(companyId: number) {
    const result = [];
    const company = await this.getCompany(companyId);
    const stripeCustomer = await this.validStripeCustomer(
      company.stripeId,
      company.email,
    );
    if (stripeCustomer) {
      const defaultPaymentMethod =
        stripeCustomer.invoice_settings.default_payment_method;

      const paymentMethods =
        await this.stripeClient.customers.listPaymentMethods(company.stripeId, {
          type: "card",
        });

      for (let i = 0; i < paymentMethods.data.length; i++) {
        result.push({
          id: paymentMethods.data[i].id,
          brand: paymentMethods.data[i].card.brand,
          expMonth: paymentMethods.data[i].card.exp_month,
          expYear: paymentMethods.data[i].card.exp_year,
          last4: paymentMethods.data[i].card.last4,
          default: defaultPaymentMethod == paymentMethods.data[i].id,
          billing_details: paymentMethods.data[i].billing_details,
        });
      }
    }

    return result;
  }

  async createSetupIntent(companyId: number) {
    const company = await this.getCompany(companyId);

    if (!(await this.validStripeCustomer(company.stripeId, company.email))) {
      const stripeId = await this.createStripeCustomer(company.email);
      await this.companiesService.updateStripeId(stripeId, company.id);
    }

    const setupIntents = await this.stripeClient.setupIntents.create({
      customer: company.stripeId,
      usage: "off_session",
    });

    return setupIntents.client_secret;
  }

  async setDefaultPaymentMethod(dto: PaymentMethodIdDto, companyId: number) {
    const company = await this.getCompany(companyId);

    if (!(await this.validStripeCustomer(company.stripeId, company.email))) {
      throw new HttpException(
        "Invalid Stripe Customer.",
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.id) {
      throw new HttpException(
        "Payment method ID is empty.",
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.stripeClient.customers.update(company.stripeId, {
      invoice_settings: {
        default_payment_method: dto.id,
      },
    });

    return true;
  }

  async editPaymentMethod(dto: PaymentMethodDto, companyId: number) {
    const company = await this.getCompany(companyId);

    if (!(await this.validStripeCustomer(company.stripeId, company.email))) {
      throw new HttpException(
        "Invalid Stripe Customer.",
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.id) {
      throw new HttpException(
        "Payment method ID not provided.",
        HttpStatus.BAD_REQUEST,
      );
    }

    const data: any = {};

    if (dto.hasOwnProperty("card")) {
      const card: any = {};
      if (dto.card.hasOwnProperty("exp_month")) {
        card.exp_month = dto.card.exp_month;
      }
      if (dto.card.hasOwnProperty("exp_year")) {
        card.exp_year = dto.card.exp_year;
      }
      if (Object.keys(card).length) {
        data.card = card;
      }
    }

    if (dto.hasOwnProperty("billing_details")) {
      const billing_details: any = {};
      if (dto.billing_details.hasOwnProperty("name")) {
        billing_details.name = dto.billing_details.name;
      }
      if (dto.billing_details.hasOwnProperty("address")) {
        const address: any = {};
        if (dto.billing_details.address.hasOwnProperty("city")) {
          address.city = dto.billing_details.address.city;
        }
        if (dto.billing_details.address.hasOwnProperty("country")) {
          address.country = dto.billing_details.address.country;
        }
        if (dto.billing_details.address.hasOwnProperty("postal_code")) {
          address.postal_code = dto.billing_details.address.postal_code;
        }
        if (dto.billing_details.address.hasOwnProperty("line1")) {
          address.line1 = dto.billing_details.address.line1;
        }
        if (dto.billing_details.address.hasOwnProperty("line2")) {
          address.line2 = dto.billing_details.address.line2;
        }
        if (dto.billing_details.address.hasOwnProperty("state")) {
          address.state = dto.billing_details.address.state;
        }
        if (Object.keys(address).length) {
          billing_details.address = address;
        }
      }
      if (Object.keys(billing_details).length) {
        data.billing_details = billing_details;
      }
    }

    await this.stripeClient.paymentMethods.update(dto.id, data);

    return true;
  }

  async deletePaymentMethod(dto: PaymentMethodIdDto, companyId: number) {
    const company = await this.getCompany(companyId);

    if (!(await this.validStripeCustomer(company.stripeId, company.email))) {
      throw new HttpException(
        "Invalid Stripe Customer.",
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.id) {
      throw new HttpException(
        "Payment method ID is empty.",
        HttpStatus.BAD_REQUEST,
      );
    }

    await this.stripeClient.paymentMethods.detach(dto.id);

    return true;
  }

  async validStripeCustomer(stripeId: string, email: string) {
    if (stripeId) {
      try {
        const stripeCustomer: any = await this.stripeClient.customers.retrieve(
          stripeId,
        );
        if (
          !(
            !stripeCustomer ||
            stripeCustomer.deleted ||
            stripeCustomer.email !== email
          )
        ) {
          return stripeCustomer;
        }
      } catch (e) { }
    }

    return false;
  }

  async createStripeCustomer(email: string) {
    const stripeCustomer = await this.stripeClient.customers.create({
      email: email,
    });

    return stripeCustomer.id;
  }

  async getPlans() {
    const productsAPI = await this.stripeClient.products.list({
      active: true,
      limit: 100,
    });

    const pricesAPI = await this.stripeClient.prices.list({
      active: true,
      limit: 100,
      type: "recurring",
    });

    const products: any = {};
    for (let i = 0; i < productsAPI.data.length; i++) {
      const product = productsAPI.data[i];

      // check if exist all necessary metadata
      if (
        !product.metadata.hasOwnProperty("capabilities") ||
        !product.metadata.hasOwnProperty("features") ||
        !product.metadata.hasOwnProperty("package") ||
        !product.metadata.hasOwnProperty("order")
      )
        continue;

      // get prices for current product
      const prices: any = {};
      for (let ii = pricesAPI.data.length - 1; ii >= 0; ii--) {
        const price = pricesAPI.data[ii];

        if (product.id == price.product) {
          if (!prices.hasOwnProperty(price.recurring.interval)) {
            prices[price.recurring.interval] = [];
          }
          prices[price.recurring.interval].push({
            id: price.id,
            unit_amount: price.unit_amount,
          });
          pricesAPI.data.splice(ii, 1);
        }
      }
      if (!Object.keys(prices).length) continue;

      // product data
      if (!products.hasOwnProperty(product.metadata.package)) {
        products[product.metadata.package] = [];
      }
      products[product.metadata.package].push({
        id: product.id,
        name: product.name,
        features: this.split(product.metadata.features),
        order: product.metadata.order,
        prices,
      });
    }

    for (const k in products) {
      // sort products by order
      products[k] = products[k].sort(function (a, b) {
        return a.order < b.order ? -1 : 1;
      });

      // delete unnecessary data
      products[k].forEach((product) => {
        delete product.order;
      });
    }
    return products;
  }

  split(string: string) {
    return string.split(";").map(function (i) {
      return i.trim();
    });
  }

  async createPaymentDataOnDb(data: {
    subId: string;
    amount?: number;
    invoiceOnDate?: string;
    invoicePdf?: string;
    invoiceUrl?: string;
    status?: string;
    start_duration?: Date;
    end_duration?: Date;
    companyId?: number;
    userId?: number;
    invoiceId?: string;
  }) {
    const { subId, amount, invoiceOnDate, invoicePdf, invoiceUrl, status, start_duration, end_duration, companyId, userId, invoiceId } = data;

    if (!subId) {
      throw new BadRequestException("Subscription ID is not provided.");
    }

    const subscription = await this.subscriptionService.getSubscriptionBySubId(subId)
    if (subscription) {
      const payment = await this.paymentRepository.findOne({ where: { subId: subscription.id, invoiceId: { [Op.iLike]: `%${invoiceId}%` } } });

      const updatedData = {
        amount: amount || 0,
        ...(invoiceOnDate && { invoiceOnDate }),
        ...(invoicePdf && { invoicePdf }),
        ...(invoiceUrl && { invoiceUrl }),
        ...(status && { status }),
        ...(start_duration && { startDuration: start_duration }),
        ...(end_duration && { endDuration: end_duration }),
      };
      if (payment) {
        Object.assign(payment, updatedData);
        await payment.save();
        return payment;
      } else {
        if (!companyId || !userId || !invoiceId) {
          throw new BadRequestException("Missing required data for creating payment.");
        }

        const newPayment = await this.paymentRepository.create({
          companyId,
          userId,
          invoiceId,
          subId: subscription.id,
        });

        Object.assign(newPayment, updatedData)
        await newPayment.save()
        return newPayment;
      }
    }
  }

  async getCardDetailsFromInvoice(invoiceId) {
    try {
      // Retrieve the invoice
      const invoice = await this.stripeClient.invoices.retrieve(invoiceId);

      // Retrieve the charge associated with the invoice
      const chargeId = invoice?.charge;
      if (!chargeId) {
        return null;
      }

      const charge = await this.stripeClient.charges.retrieve(chargeId as string);

      // Get the payment card details
      const paymentMethodDetails = charge.payment_method_details;
      if (paymentMethodDetails && paymentMethodDetails.card) {
        const cardBrand = paymentMethodDetails.card.brand;
        const last4 = paymentMethodDetails.card.last4;

        return { cardBrand, last4 };
      } else {
        return null;
      }
    } catch (error) {
      console.error('Error retrieving card details:', error.message);
      throw error;
    }
  }

  async getupcomingCompanyInvoice(companyId: number) {
    try {
      const company = await this.getCompany(companyId);
      if (company && company.stripeId) {
        const subscriptions = await this.subscriptionService.getAllCompanySubscription({ companyId, status: ['active', 'trialing'], sortBy: 'id', sortType: 'ASC' });
        const upcomingSubscriptionInvoices = []
        if (subscriptions && subscriptions.length) {
          for (let sub of subscriptions) {
            const data: any[] = []
            const upcomingInvoice = await this.stripeClient.invoices.retrieveUpcoming({ subscription: sub.subId ,expand: ['lines.data.price.tiers','lines.data.price.product'] });
            if (upcomingInvoice && upcomingInvoice.lines.data?.length) {
              const invoiceItems = upcomingInvoice.lines.data
              for (let invoice of invoiceItems) {
                const description = invoice?.price?.product["name"]
                const currentTier = invoice?.price?.tiers?.find(tier=>tier.up_to === null)
                const perUnitPrice = ((currentTier?.unit_amount || invoice?.price?.unit_amount || 0)/100).toFixed(2)+`${(sub.status == 'trialing'&&!invoice?.price?.unit_amount) ? ' (Trial)' : ''}` 
                // console.log({upto:invoice?.price?.tiers?.find(tier=>tier.up_to !== null)?.up_to/30})
                // console.log({perUnitPrice,des:invoice?.price?.product["name"]})
                const subAddon = await this.subscriptionAddonRepository.findAll({
                  where: {
                    companyId,
                    subId: invoice.subscription,
                    subItemId: invoice.subscription_item,
                    quantity: { [Op.gte]: 1 },
                    createdAt: { [Op.gte]: sub.startDate, [Op.lte]: sub.endDate }
                  },
                  order: [['id', 'ASC']]
                });
                data.push({
                  name: description,
                  duration: { start: invoice.period.start, end: invoice.period.end },
                  amount: invoice.amount, 
                  perUnitPrice,
                  quantity: invoice.quantity,
                  subscription: invoice.subscription,
                  subscription_item: invoice.subscription_item,
                  ...(subAddon && subAddon.length ? { add_on: subAddon } : null)
                });
              }
              const a = {
                count: data.length,
                rows: data,
                payableAmount: upcomingInvoice.subtotal,
                tax: upcomingInvoice.total_tax_amounts[0]?.amount || 0,
                total: upcomingInvoice.total,
                willInvoiceOn: upcomingInvoice.next_payment_attempt,
              }
              upcomingSubscriptionInvoices.push(a);
            }

          }
          return upcomingSubscriptionInvoices
        }
      }
    } catch (error) {
      console.log("Error while getting upcoming invoice:" + error)
    }
  }

  async getCompanyInvoices(companyId, query) {

    const invoices = await this.paymentRepository.findAndCountAll({
      where: {
        companyId,
      },
      attributes: ["id", "invoicePdf", "status", "amount", "invoiceOnDate"],
      order: [['invoiceOnDate', 'DESC']]
    })

    return invoices;
  }


  async handleProducts(product, type, eventPrevData) {
    try {
      const productData = {
        productId: product.id,
        name: product.name,
        active: product.active,
        defaultPriceId: product.default_price,
        productType: 'main',
        planId: 0,
        planPackageId: 0,
        packageType: null
      }
      if (product.metadata?.hasOwnProperty("addon")) {
        productData.productType = product.metadata.addon
      }
      // {
      //   eventPrevData: { metadata: { package: 'ATS' }, updated: 1730954765 }
      // }
      if (product.metadata?.hasOwnProperty("package") && !product.metadata?.hasOwnProperty("addon")) {
        // console.log('in package');
        const packagePlan = await this.planPackageService.getPlanPackageDetails({ name: eventPrevData && eventPrevData.metadata?.hasOwnProperty("package") ? eventPrevData.metadata.package : product.metadata.package })
        // console.log({packagePlan});
        let companyId = null;
        if (product.metadata?.hasOwnProperty("company_mail")) {
          const company = await this.companiesService.getCompanyByEmail(product.metadata.company_mail);
          if (company) {
            companyId = company.id;
          }
        }
        if (product.metadata?.hasOwnProperty("package_type") && product.metadata.package_type != '') {
          productData.packageType = product.metadata.package_type;
        }
        if (packagePlan) {
          if (companyId) {
            Object.assign(packagePlan, { companyId: companyId, isCustom: true });
          } else {
            Object.assign(packagePlan, { companyId: null, isCustom: false }); // this is to remove company id from package plan if it is not custom
          }
          productData.planPackageId = packagePlan.id;
          await this.planPackageService.updatePlanPackage(packagePlan);
        }
        else {
          const packagePlan = await this.planPackageService.createPlanPackage({ name: product.metadata.package, ...(companyId ? { companyId, isCustom: true } : '') });
          productData.planPackageId = packagePlan.id
        }

      }

      if (product.metadata?.hasOwnProperty("tier") && !product.metadata?.hasOwnProperty("addon")) {
        const plan = await this.planService.getPlanDetails({ name: product.metadata.tier })
        if (plan) {
          productData.planId = plan.id
        } else {
          const plan = await this.planService.createPlan({ name: product.metadata.tier, description: '' })
          productData.planId = plan.id
        }
      }

      if (type === 'product.created') {
        const productCreated = await this.productService.createProduct(productData);
      } else if (type === 'product.updated') {
        const productUpdated = await this.productService.updateProduct(productData);
        if (productUpdated && !product.metadata?.hasOwnProperty("addon") && productData.planId && productData.planPackageId) {
          const priceUpdated = await this.priceService.updateAllPricePlanPackage(productData);
        }
      } else if (type === 'product.deleted') {
        this.productService.deleteProduct(productData)
      }
    } catch (error) {
      console.log("Error while handling product", error)
    }
  }


  async handlePrices(price: any, type: string, eventPrevData: any) {
    const freeQty = 0;
    // Initial data object setup
    const data: {
      priceId: string,
      productId: string,
      price: number,
      freeQty: number,
      active: boolean,
      isDefaultPackagePlan: boolean,
      monthlyYearly: string,
      meterId: string | null,
      planId: number,
      planPackageId: number
    } = {
      priceId: price.id,
      productId: price.product,
      price: (price.unit_amount ?? 0) / 100,
      freeQty: freeQty ?? 0,
      active: price.active ?? false,
      isDefaultPackagePlan: false,
      monthlyYearly: 'y', // Default to yearly if no logic provided
      meterId: null,
      planId: 0,
      planPackageId: 0
    };

    try {
      if (price.id) {
        //delete price data if price is deleted 
        if (type === 'price.deleted') {
          const deletedPrice = await this.priceService.deletePrice({ priceId: price.id })
          return
        }
        // Fetch the price details from Stripe API with expanded tiers
        const priceData = await this.stripeClient.prices.retrieve(price.id, {
          expand: ['tiers'],
        });

        // Handle recurring prices
        if (priceData.type === 'recurring') {
          if (priceData.tiers && priceData.tiers.length > 0) {
            // Process tiered pricing if available
            const freeTier = priceData.tiers.find(tier => tier.unit_amount === 0);
            const paidTier = priceData.tiers.find(tier => tier.up_to === null);

            data.freeQty = freeTier?.up_to ?? 0;
            data.price = (paidTier?.unit_amount ?? priceData.unit_amount) / 100;
          } else {
            // No tiers, use default unit amount
            data.freeQty = 0;
            data.price = (priceData.unit_amount ?? 0) / 100;
          }
          // Assign other recurring-specific values
          data.meterId = price.recurring?.meter ?? null;
        }
        if (priceData.nickname) {
          const notes = priceData.nickname.split(",")
          const obj = notes.map(i => i.split(":"))
          const period = obj.find(i => i[0] === 'period')
          const defaultPackagePlan = obj.find(i => i[0] === 'defaultPackagePlan')
          const duration = obj.find(i => i[0] === 'duration')
          if (period) {
            data.monthlyYearly = period[1] === 'monthly'
              ? `${duration && duration[1] || ''}m`
              : period[1] === 'yearly'
                ? `${duration && duration[1] || ''}y`
                : 'm';
          }

          const planPackage = obj.find(i => i[0] === 'package');
          const planTier = obj.find(i => i[0] === 'tier');

          const product = await this.stripeClient.products.retrieve(price.product as string);

          if (product && product.metadata) {
            const packageName = product.metadata.hasOwnProperty("addon") ? planPackage ? planPackage[1] : null : product.metadata.hasOwnProperty("package") ? product.metadata.package : null;
            const planName = product.metadata.hasOwnProperty("addon") ? planTier ? planTier[1] : null : product.metadata.hasOwnProperty("tier") ? product.metadata.tier : null;

            if (defaultPackagePlan) {
              data.isDefaultPackagePlan = !product.metadata.hasOwnProperty("addon") && defaultPackagePlan[1] === 'true' ? true : false;
            }
            if (packageName) {
              const packagePlan = await this.planPackageService.getPlanPackageDetails({ name: packageName })
              if (packagePlan) {
                data.planPackageId = packagePlan.id;
              }
            }

            if (planName) {
              const plan = await this.planService.getPlanDetails({ name: planName });
              if (plan) {
                data.planId = plan.id
              }
            }
          }
        }

        // Handle one-time prices
        if (priceData.type === 'one_time') {
          data.price = (priceData.unit_amount ?? 0) / 100;
          data.freeQty = 0;
        }
        // Insert or update the price data in the database
        if (type === 'price.created' && data.planId && data.planPackageId) {
          await this.priceService.createPrice(data);
        } else if (type === 'price.updated' && data.planId && data.planPackageId) {
          await this.priceService.updatePrice(data)
        }
      }

      return 0; // Successfully handled
    } catch (error) {
      // Handle any errors gracefully
      console.log(`Error while handling price: ${error.message}`);
    }
  }


  async handleInvoices(invoice: any, type: string) {

    let company: any = {}
    let subscription: any = {}
    try {
      if (!invoice.id) {
        throw new BadRequestException('Invoice ID is missing');
      }

      const data: any = {
        invoiceId: invoice.id,
      };

      if (invoice.customer) {
        company = await this.companiesService.getCompanyByStripeId(invoice.customer);
        if (company) {
          data.companyId = company.id;
          data.stripeId = company.stripeId;
          data.email = company.email;
        }
      }

      if (type === 'invoice.payment_failed') {
        // const invoice = event.data.object;
        const subscriptionId = invoice.subscription;

        // Retrieve the customer to check for payment methods
        const stripeCustomer = await this.validStripeCustomer(
          data.stripeId,
          data.email,
        );
        const defaultPaymentMethod = stripeCustomer.invoice_tings?.default_payment_method;

        // Check if a payment method is assigned
        // if (!defaultPaymentMethod) {
        //   // Cancel the subscription
        //   await this.stripeClient.subscriptions.del(subscriptionId);
        //   console.log(`Subscription ${subscriptionId} canceled due to payment failure and no payment method.`);
        // }
      }

      if (invoice.customer_email) {
        const user = await this.userService.getUserByEmail(invoice.customer_email);
        if (user) {
          data.userId = user.id;
          data.companyId = user.companyId
        }
      }

      if (invoice.subscription) {
        data.subId = invoice.subscription;
        subscription = await this.subscriptionService.getCompanySubscription({ subId: data.subId });
      }

      data.amount = invoice.amount_paid || 0;

      if (invoice.status_transitions?.paid_at) {
        const invoiceDate = new Date(invoice.status_transitions.paid_at * 1000);
        data.invoiceOnDate = invoiceDate;
      }

      if (invoice.lines?.data?.length > 0) {
        const line = invoice.lines.data[0];
        if (line?.period) {
          data.start_duration = new Date(line.period.start * 1000);
          data.end_duration = new Date(line.period.end * 1000);
        }
      }

      data.invoicePdf = invoice.invoice_pdf || null;
      data.invoiceUrl = invoice.hosted_invoice_url || null;

      data.status = invoice.paid ? 'paid' : 'In progress';

      const payment = await this.createPaymentDataOnDb(data);
      let recordData: any[] = [];
      if (subscription && payment) {
        const cardDetails = await this.getCardDetailsFromInvoice(invoice.id)
        if (cardDetails && cardDetails.cardBrand && cardDetails.last4) {
          recordData.push({
            for: 'payment',
            data: {
              invoiceId: payment.invoiceId,
              invoiceOn: payment?.invoiceOnDate,
              status: payment?.status,
              companyId: payment.companyId,
              invoicePdf: payment?.invoicePdf,
              subId: invoice.subscription,
              plan: subscription.plans?.dataValues,
              package: subscription.packages?.dataValues,
              amount: payment?.amount,
              last4: +cardDetails?.last4,
              cardBrand: cardDetails?.cardBrand,
            }
          })
        }
      }
      if (type === 'invoice.payment_succeeded' && company) {
        invoice.amount_paid && this.sendPaymentSuccessEmail(invoice, company, data)
        if (subscription && (subscription.status === 'trialing' || subscription.status === 'active')) {
          const addon = await this.subscriptionService.setSubscriptionAddons(company.id)
          addon?.filter(i => i != null)?.forEach(i => {
            recordData.push({
              for: 'subAddon',
              data: {
                subscriptionId: i?.subId,
                subscriptionItemId: i?.subItemId,
                plan: { id: subscription.planId, name: subscription?.plans?.name },
                package: { id: subscription.packageId, name: subscription?.packages?.name },
                actionType: 'SET',
                units: i?.units,
                addonName: i?.addonName,
                companyId: i?.companyId,
                createdAt: Date.now()
              }
            })
          })
        }
      }
      console.log('recordDataInInvoice', recordData);
      return { recordData, response: "Successfully stored invoice details" }
    } catch (error) {
      console.log('Error while handling the invoice.', error);
    }
  }

  private async sendPaymentSuccessEmail(invoice: any, company: any, data: any) {
    await this.emailService.sendRecruitmentActivityService(
      "trialEnds.html",
      {
        body: `
        <h2>Your payment has been processed</h2>
        <div style="margin-bottom: 34px">
        <p>Dear ${company.owner.firstname + " " + company.owner.lastname},</p>
        <p>Thanks for choosing uRecruits. This email is to confirm that we’ve received your payment for the invoice ${invoice.number}.Click the below button to view and download invoice.</p>
        </div>
        <a href="${data.invoiceUrl}"
          style="
            text-decoration: none;
            color: inherit;
            display: block;
            width: 100%;
            height: 100%;
          ">
          <button style="
          font-size: 14px;
          font-weight: 900;
          line-height: 100%;
          color: white;
          padding: 16px 28px;
          border: none;
          cursor: pointer;
          background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
          border-radius: 4px;
          ">View Invoice</button>
        </a>`,
        userId: company.ownerId
      },
      company.email,
      "Your payment has been processed for uRecruits"
    );
  }

  async handleCustomerSubscription(subscription: any, type: string) {
    const data: {
      subId?: string;
      productId?: string;
      period?: string;
      status?: string;
      companyId?: number;
      startDate?: string;
      endDate?: string;
      subEndDate?: string;
      customer_email?: string
    } = {};

    try {
      if (subscription?.id && subscription?.object === 'subscription') {
        // subEndDate: new Date(stripeSubscription.cancel_at * 1000),
        const { id, plan, items, status, customer, current_period_start, current_period_end, latest_invoice, cancel_at } = subscription;
        data.subId = id;
        let company = null;
        this.extractProductAndPeriod(plan, items, data);

        if (status) {
          data.status = status;
        }

        if (customer) {
          company = await this.companiesService.getCompanyByStripeId(customer);
          if (company) {
            data.companyId = company.id;
          }
        }

        this.addPeriodDates(current_period_start, current_period_end, cancel_at, data);

        switch (type) {
          case 'customer.subscription.created':
            await this.storeSubscriptionData(data, latest_invoice);
            const addonRecordData = await this.createSubscriptionAddons(id, data.companyId, items);
            return { response: "Successfully stored subscription data", recordData: addonRecordData.filter(i => !!i) };

          case 'customer.subscription.deleted':
            await this.storeSubscriptionData(data, null);
            await this.sendCancelSubscriptionMail(company)
            return await this.deleteSubscriptionAddon(data.companyId);

          case 'customer.subscription.updated':
            return await this.storeSubscriptionData(data, latest_invoice);

          default:
            return null;
        }
      }
      return null;
    } catch (error) {
      console.error("Error while handling customer subscription:", error.message);
    }
  }

  async sendCancelSubscriptionMail(company){
    try {
      if (company) {
        await this.emailService.sendRecruitmentActivityService("trialEnds.html", {
          body: `
          <h2>Your uRecruits Subscription Has Ended – Come Back Anytime!</h2>
          <div style="margin-bottom: 34px">
            <p>Hi ${company?.owner?.firstname + " " + company?.owner?.lastname},</p>
            <p>We noticed that your subscription for ${company?.name} at uRecruits has been canceled, and we already miss having you with us!</p>
            <p><strong>Your access to the platform has ended as of today, but reactivating your subscription is just one click away.</strong></p>
            <p>We’d love to have you back and help you continue streamlining your recruitment process with ease. By reactivating now, you can pick up right where you left off—no hassle, no interruptions.</p>
            <p>Click the button below to restart your subscription and unlock all the powerful tools of uRecruits again.</p>
          </div>
          <a href="${process.env.WEB_APP_URI}/profile-setting?active=subscriptions"
            style="
              text-decoration: none;
              color: inherit;
              display: block;
              width: 100%;
              height: 100%;
            ">
            <button style="
              font-size: 14px;
              font-weight: 900;
              line-height: 100%;
              color: white;
              padding: 16px 28px;
              border: none;
              cursor: pointer;
              background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
              border-radius: 4px;
            ">Reactivate My Subscription</button>
          </a>
          <p>We hope to see you back soon!</p>
          `,
          userId: company.ownerId
        }, company.email, "We Miss You at uRecruits – Reactivate Your Account Today!",
        {
          link: `${process.env.WEB_APP_URI}/profile-setting?active=subscriptions`,
          "notificationTitle": "Your Subscription Has Been Canceled",
          "notificationMsg": `Your subscription for ${company.name} at uRecruits has been canceled. We already miss having you! Reactivate now to regain access and continue your hiring journey.`
        }
        );
      }
    } catch (error) {
      console.log("Error while sending mail of cancel subscrpition:", error)
    }
  }

  private addPeriodDates(start: number, end: number, subEnd: number, data: any) {
    if (start) {
      data.startDate = new Date(start * 1000);
    }
    if (end) {
      data.endDate = new Date(end * 1000);
    }
    if (subEnd) {
      data.subEndDate = new Date(subEnd * 1000);
    }
  }

  private extractProductAndPeriod(plan: any, items: any, data: any) {
    if (plan?.product) {
      data.productId = plan.product;
    }
    if (plan?.interval) {
      data.period = plan.interval === 'month' ? 'm' : 'y';
    }

    if (items?.data?.length) {
      const firstItem = items.data[0];
      data.productId = firstItem.plan?.product;
      data.period = firstItem.plan?.interval === 'month' ? 'm' : 'y';
    }
  }

  private async storeSubscriptionData(data: any, invoiceId: string) {
    try {
      if (data.productId) {
        await this.subscriptionService.createSubscriptionDataOnDb(data);
      }

      if (invoiceId) {
        await this.subscriptionService.addPaymentIntentDetails({ invoiceId });
      }

    } catch (error) {
      console.log("Error storing subscription data", error)
    }
  }

  private async createSubscriptionAddons(subId: string, companyId: number, items: any) {
    const { data: itemData } = items;
    const cancelSubscription = await this.subscriptionService.getCompanySubscription({
      status: 'canceled',
      companyId,
      sortBy: 'id',
      sortType: 'DESC',
    });

    const activeSubscription = await this.subscriptionService.getCompanySubscription({
      status: ['active', 'trialing'],
      companyId,
      sortBy: 'id',
      sortType: 'DESC',
    });
    const response = await Promise.all(
      itemData.map(async (item) => {
        const data: any = { subId, subItemId: item.id };
        if (item?.price?.id && item?.plan?.product) {
          data.priceId = item.price.id;
          const product = await this.productService.getProduct(item?.plan?.product);
          if (product && product.productType !== 'main') {
            let addon = null;

            if (cancelSubscription) {
              addon = await this.subscriptionAddonRepository.findOne({
                where: { subId: cancelSubscription.subId, addonName: product.productType },
                order: [['id', 'DESC']],
              });
            }

            if (addon) {
              data.addonName = addon.addonName;
              data.quantity = addon.units * 30;
              data.units = addon.units;
            } else {
              data.addonName = product.productType;
              data.quantity = 0;
              data.units = 0;
            }

            data.companyId = companyId;

            if (Object.keys(data).length && activeSubscription) {
              const newAddon = await this.subscriptionAddonRepository.create(data);
              if (newAddon) {
                const recordData = {
                  for: 'subAddon',
                  data: {
                    subscriptionId: newAddon.subId,
                    subscriptionItemId: newAddon.subItemId,
                    companyId: newAddon.companyId,
                    units: newAddon?.units,
                    actionType: 'CREATE',
                    createdAt: Date.now(),
                    addonName: newAddon?.addonName,
                    plan: activeSubscription.plans?.dataValues,
                    package: activeSubscription.packages?.dataValues
                  }
                }
                return recordData
              }
            }
          }
        }
        return null;
      }).filter(i => i != null)
    );
    return response;
  }

  private async deleteSubscriptionAddon(companyId: number) {
    if (companyId) {
      const currentDate = new Date();
      const lastMonthDate = new Date(currentDate);

      lastMonthDate.setMonth(currentDate.getMonth() - 2);

      await this.subscriptionAddonRepository.destroy({ where: { companyId, createdAt: { [Op.lte]: lastMonthDate } } })
    }
  }

  async handleTrialSubscription(subscription) {
    if (subscription.customer) {
      try {
        const company = await this.companiesService.getCompanyByStripeId(subscription.customer);
        if (company) {
          await this.emailService.sendRecruitmentActivityService("trialEnds.html", {
            body: `
            <h2>Your Trial Period is Ending Soon</h2>
            <div style="margin-bottom: 34px">
            <p>Hi ${company.owner.firstname + " " + company.owner.lastname}!</p>
            <p>Thank you for creating a company ${company.name} at uRecruits.We hope you have been enjoying your free trial.</p>
            <p><strong>Unfortunately, your free trial is ending in 3 days </strong></p>
            <p>We’d love to keep you as a customer, and there is still time to complete your subscription! Simply Add your payment method to your account to continue subscription.</p>
            </div>
            <a href="${process.env.WEB_APP_URI}/profile-setting?active=paymentsMethods"
              style="
                text-decoration: none;
                color: inherit;
                display: block;
                width: 100%;
                height: 100%;
              ">
              <button style="
              font-size: 14px;
              font-weight: 900;
              line-height: 100%;
              color: white;
              padding: 16px 28px;
              border: none;
              cursor: pointer;
              background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
              border-radius: 4px;
              ">Add Payment Method</button>
            </a>
            <p>If you’ve already added a payment method, you can ignore this email.</p>
            `,
            userId: company.ownerId
          }, company.email, "Your Free Trial Ends Soon – Don't Miss Out on Full Access to uRecruits");
        }
      } catch (error) {
        console.log("Error while handling trials event", error)
      }
    }
  }


  async handleStripeEvents(data) {
    // console.log({data})
    const event_type = data?.type
    const eventBody = data?.data?.object
    const eventPrevData = data?.data?.previous_attributes
    if (event_type && eventBody) {
      console.log(`----------------------eventype: ${event_type} ----------------------------`)
      switch (event_type) {
        case 'product.created':
        case 'product.updated':
        case 'product.deleted':
          // handle product updation/creation
          return await this.handleProducts(eventBody, event_type, eventPrevData)

        case 'price.created':
        case 'price.updated':
        case 'price.deleted':
          // handle price updation of the created product
          return await this.handlePrices(eventBody, event_type, eventPrevData)

        case 'customer.subscription.created':
        case 'customer.subscription.updated':
        case 'customer.subscription.deleted':
          //handle customer subscription data
          return await this.handleCustomerSubscription(eventBody, event_type);

        case 'customer.subscription.trial_will_end':
          return await this.handleTrialSubscription(eventBody)

        case 'invoice.payment_succeeded':
        case 'invoice.payment_failed':
          //handle invoices data
          return await this.handleInvoices(eventBody, event_type)
      }
    }
  }
}
