import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { Body, Controller, Delete, Get, Logger, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { DomainResultService } from "libs/db/src/lib/domain-result/domain-result.service";
import { CreateDomainResultsDto } from "./dto/create-domain-result.dto";
import { GetDomainResultsDto } from "./dto/get-domain-result.dto";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { HttpService } from '@nestjs/axios';
import { assessmentSubmittedSignal,assessmentReviewedSignal } from "apps/temporal/src/app/workflow/temporal/workflows";
import {TemporalClientService} from "apps/temporal/src/app/workflow/temporal-client/temporalClient.services";

@ApiTags("Domain")
@Controller('domain-result')
@ApiBearerAuth("access-token")
export class DomainResultController {
  constructor(
    private readonly questionsService: DomainResultService,
    private readonly httpService: HttpService,
    private readonly temporalClientService: TemporalClientService
    ) {}


  @ApiOperation({ summary: 'Get all domain assessment with pagination' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200})
  @Get("all")
  @Permissions('recruiter', 'assessment:view')
  findAll(@Query() data:GetDomainResultsDto,@AuthUser() user) {
    return this.questionsService.findAllAssessment(data, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get domain assessment by domain-result Id' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200})
  @Get(":id")
  @Permissions("recruiter",'assessment:view')
  findById(@Param("id") id:number,@AuthUser() user) {
    return this.questionsService.findById(id, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get domain assessment result for candidate by assessmentId candidate side' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiBearerAuth('access-token')
  @ApiResponse({ status: 200 })
  @Get("candidate/:jobId/:assessmentId")
  @Permissions("candidate")
  findCandidateDomainResultById(@Param("assessmentId") id: number, @Param("jobId") jobId: number, @AuthUser() user: any) {
    const candidateId = user["https://urecruits.com/userId"]
    return this.questionsService.findByAssessmentId(id, candidateId, jobId);
  }

  @ApiOperation({ summary: 'Get domain assessment result for candidate by assessmentId recruiter side' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiBearerAuth('access-token')
  @ApiResponse({ status: 200 })
  @Get("candidate/:jobId/:assessmentId/:candidateId")
  @Permissions("recruiter", 'assessment:view', 'job-post:view')
  findDomainResult(@Param("assessmentId") assessmentId: number, @Param("jobId") jobId: number,@Param("candidateId") candidateId:number,@AuthUser() user) {
    return this.questionsService.findByAssessmentId(assessmentId,candidateId,jobId,user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Create Domain Result' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post()
  @Permissions("candidate")
  async create(@Body() createQuestionsDto: CreateDomainResultsDto, @AuthUser() user: any) {
    const candidateId=user["https://urecruits.com/userId"]
    createQuestionsDto.candidateId = candidateId
    const domainResult = await this.questionsService.create(createQuestionsDto);
    const {jobId}=createQuestionsDto
    if (jobId && candidateId) {
      const url = `https://recruitment-micro.urecruits.com/api/temporal-workflow?jobId=${jobId}&id=${candidateId}`;
      let response
      try {
        const res = await this.httpService.get(url).toPromise()
        response= res?.data
      } catch (error) {
        console.error("Error fetching data:", error);
      }

      const client = await connectToTemporal();
      if (client && response) {
        const handle = client.getHandle(response?.workflowid);
        await handle.signal(
          assessmentSubmittedSignal,
          {message:"Domain Assessment is Submitted", data:{domainResultId : domainResult.id}}
          
        );
      } else {
        Logger.log(
          "Getting issue to connect the client in domain result"
        );
      }
    }
    return {message: "You have successfully completed the domain assessment"}

  }

  @ApiOperation({ summary: 'Add feedback as a recruiter' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200})
  @Patch(":id")
  @Permissions("recruiter")
  update(@Param("id") id:any, @Body() data:any, @AuthUser() user: any) {
   try{
    return this.questionsService.update(id,data,user["https://urecruits.com/companyId"]);
   }catch(error){
    Logger.log("Error : ",error)
   }
  }

  @ApiOperation({ summary: 'Add score as a recruiter' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200})
  @Patch("totalScore/:id")
  @Permissions("recruiter")
  async updateScore(@Param("id") id:any, @Body() data:any, @AuthUser() user: any) {
   try{
    const domainResult = await this.questionsService.findById(id,user["https://urecruits.com/companyId"]);
    const candidateId= domainResult.candidateId;
    const jobId=domainResult.jobId;
    const handle = await this.temporalClientService.getHandle(jobId,candidateId);
    await handle.signal(
      assessmentReviewedSignal,
     "Domain Assessment is Reviewed"
    );
    return this.questionsService.updateScore(id,data);
   }catch(error){
    Logger.log("Error : ",error)
   }
  }

  @ApiOperation({ summary: 'Delete feedback as a recruiter' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200})
  @Delete(":id")
  @Permissions('recruiter')
  deleteFeedback(@Param("id") id:any) {
   try{
    return this.questionsService.deleteFeedback(id);
   }catch(error){
    Logger.log("Error : ",error)
   }
  }
}
