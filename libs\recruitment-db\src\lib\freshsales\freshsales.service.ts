import { HttpService } from "@nestjs/axios";
import { HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { FreshsalesDto } from './freshsales.dto';
@Injectable()
export class FreshsalesService {
  private freshsalesNotesAPI = process.env.FRESHSALES_NOTES_API;
  private freshsalesSearchAPI = process.env.FRESHSALES_SEARCH_CONTACT_API;
  private freshsalesUpserthAPI = process.env.FRESHSALES_CONTACT_UPSERT_API;
  private freshsalesToken = process.env.FRESHSALES_AUTH_TOKEN;
  private config = {
    headers: {
      'Authorization': "Token token="+this.freshsalesToken,
      'Content-Type': "application/json"
    }
  }

  constructor(private readonly httpService: HttpService,) { }

  async upsertContact(dto: FreshsalesDto, contactType) {

    let data = {
      unique_identifier: {
        emails: dto.email
      },
      contact: {}
    }
    let alreadyExist = false;
    try {
      const params = {
        filter_rule: [{
          attribute: "contact_email.email",
          operator: "is_in",
          value: dto.email
        }]
      }

      const searchContact = await this.httpService
        .post(this.freshsalesSearchAPI, params, this.config)
        .toPromise();
      if (searchContact?.data.meta.total > 0) {
        alreadyExist = true;
      }
    }
    catch (error) {
      console.log(error);
      throw new HttpException("Error happened while executing freshsales search contact service", HttpStatus.BAD_REQUEST);
    }

    if (contactType === 'contactUs') {

      data.contact = {
        first_name: dto.firstName,
        email: dto.email,
        custom_field: {
          cf_business_url: dto.cf_business_url,
          cf_number_of_employees: dto.cf_number_of_employees
        },
        first_source: 'Contact Us'
      }

    } else if (contactType === 'company') {
        data.contact = {
          first_name: dto.firstName,
          last_name: dto.lastName || '',
          emails: dto.email,
          mobile_number: dto.companyPhone,
          ...(alreadyExist ? { latest_source: 'Company Registration', first_source: 'Contact Us' } : { first_source: 'Company Registration' })

        }
    }

    try {
      const response = await this.httpService
        .post(this.freshsalesUpserthAPI, data, this.config)
        .toPromise();
      if (response.status === 201 && contactType === 'contactUs' && dto.message && !alreadyExist) {
        try {
          const notes = {
            note: {
              description: dto.message,
              targetable_type: "Contact",
              targetable_id: response.data.contact.id
            }
          }

          const notesResponse = await this.httpService
            .post(this.freshsalesNotesAPI, notes, this.config)
            .toPromise();
        }
        catch (error) {
          console.log(error);
          throw new HttpException("Error happened while executing freshsales service", HttpStatus.BAD_REQUEST);
        }
      }
    }
    catch (error) {
      console.log(error);
      throw new HttpException("Error happened while executing freshsales service", HttpStatus.BAD_REQUEST);
    }

  }
}