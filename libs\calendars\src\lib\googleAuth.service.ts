import { IntegrationAuthService } from "@microservices/recruitment-db";
import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { createCipheriv, createDecipheriv, randomBytes, scrypt } from "crypto";
import { Auth, google } from "googleapis";
import { promisify } from "util";
import { CreateEventDTO } from "./dto/create-event.dto";
import { GetFreeSlotsDTO } from "./dto/get-free-slots.dto";
import { HttpService } from "@nestjs/axios";
import { UtilityService } from "./utility.service";
import { v4 as uuidv4 } from "uuid";
import { roundType } from "apps/temporal/src/app/workflow/enum/round.enum";

@Injectable()
export class GoogleAuthService {
  oauth2Client: Auth.OAuth2Client;
  private scope =
    "https://www.googleapis.com/auth/calendar https://www.googleapis.com/auth/calendar.events";
  constructor(
    private integrationModelService: IntegrationAuthService,
    private utilityService: UtilityService,
    private readonly httpService: HttpService
  ) {
    this.oauth2Client = new google.auth.OAuth2({
      clientId: process.env.GCAL_AUTH_CLIENT_ID,
      clientSecret: process.env.GCAL_AUTH_CLIENT_SECRET,
      redirectUri: `${process.env.MICROSERVICES_PRA_URL}/api/integrations/oauth2-callback`,
    });
  }

  async getClient(id) {
    try {
      const data = await this.integrationModelService.findByProvider(
        id,
        "gcal"
      );
      const token = JSON.parse(data.refreshToken);
      const decrypt = await this.decrypt(JSON.stringify(token));
      const refreshToken = decrypt[0];
      if (Number(data.expiryDate) < new Date().getTime()) {
        this.oauth2Client.setCredentials({
          refresh_token: refreshToken,
        });
        const newToken = await this.oauth2Client.refreshAccessToken();

        await this.integrationModelService.update(
          id,
          {
            userId: id,
            accessToken: await this.encrypt([
              newToken.credentials.access_token,
            ]),
            refreshToken: await this.encrypt([
              newToken.credentials.refresh_token,
            ]),
            expiryDate: newToken.credentials.expiry_date,
            scope: newToken.credentials.scope,
          },
          "gcal"
        );

        this.oauth2Client.setCredentials({
          refresh_token: newToken.credentials.refresh_token,
          access_token: newToken.credentials.access_token,
        });
      } else {
        const token1 = JSON.parse(data.accessToken);
        const decrypt1 = await this.decrypt(JSON.stringify(token1));
        const refreshToken1 = decrypt1[0];
        this.oauth2Client.setCredentials({
          refresh_token: refreshToken,
          access_token: refreshToken1,
        });
      }
      return {
        client: google.calendar({ version: "v3", auth: this.oauth2Client }),
      };
    } catch (error) {
      Logger.log(error);
      throw new UnauthorizedException("Invalid or expired token.");
    }
  }

  async generateGoogleAuthURL() {
    return this.oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: "https://www.googleapis.com/auth/calendar.readonly",
    });
  }

  async bookAppointment(recruiterId, data) {
    console.log('Starting to book in Google Calendar')
    try {
      if (recruiterId && data.candidateId) {
        let response: any = { data: {} };

        const participants = [
          Number(recruiterId),
          Number(data.candidateId),
          ...data?.interviewers?.map((item) => Number(item.id)),
        ];

        const uuid = uuidv4();
        const users = [];
        const calendarEventData = this.getCalendarEventData(data, uuid);

        await Promise.all(
          participants.map(async (userId) => {
            try {
              const client = await this.getClient(userId).catch();
              const calendar = client?.client;
              if (!calendar) return null;

              const response = await calendar.events.insert({
                calendarId: "primary",
                requestBody: calendarEventData as any,
              });
              users.push({ id: userId, calEventId: response.data.id });
            } catch (err) {
              users.push({ id: userId, calEventId: null });
            }
          })
        );

        const obj = {
          startGmt: data.utcStartDate,
          endGmt: data.utcEndDate,
          title: data.eventTitle,
          description: data.description || "Job Data",
          users: users,
          remindMe: data.remindMe,
          interviewers: data.interviewers,
          jobId: data?.jobId,
          candidateId: data.candidateId,
          timeBefore: null,
          roomId: uuid,
        };

        if (data.remindMe === true) {
          obj.timeBefore = `${data.timeBefore}`;
        }

        const url = `${process.env.ASSESSMENT_API_URI}/api/calendar/create-event`;
        response = (await this.httpService.post(url, obj).toPromise()).data;
        console.log(response, 'response')
        console.log('Booked in Google Calendar')
        return response;
      } else {
        throw new BadRequestException();
      }
    } catch (error) {
      console.log("🚀 ~ GoogleAuthService ~ bookAppointment ~ error:", error)
      if (error.status === 400) {
        throw new BadRequestException({
          status: error.status,
          message: error.message,
        });
      } else {
        throw new BadRequestException(error.message);
      }
    }
  }

  async rescheduleAppointment(userId, data, eventData) {
    try {
      if (userId && data.recruiterId) {
        let response: any = { data: {} };

        const url = `${process.env.ASSESSMENT_API_URI}/api/calendar/get-users/${data.eventId}`;
        const participantsDetails = (
          await this.httpService.get(url).toPromise()
        ).data;

        const participants = participantsDetails.filter(
          (item) => !!item.calendarEventId
        );

        // get roomId here
        const roomId = eventData?.roomId;
        const calendarEventData = this.getCalendarEventData(data, roomId);
        await Promise.all(
          participants.map(async ({ userId, calendarEventId }) => {
            const client = await this.getClient(userId).catch();
            const calendar = client?.client;
            if (!calendar) return null;

            const response = await calendar.events.update({
              calendarId: "primary",
              eventId: calendarEventId,
              requestBody: calendarEventData,
            });
          })
        );

        const obj = {
          startGmt: data.utcStartDate,
          endGmt: data.utcEndDate,
          title: data.eventTitle,
          description: data.description || "Job Data",
          remindMe: data.remindMe,
          interviewers: data.interviewers,
          jobId: data.jobId,
          candidateId: Number(data.candidateId), // needs to change
          timeBefore: null,
        };

        if (data.remindMe === true) {
          obj.timeBefore = `${data.timeBefore}`;
        }
        const updateUrl = `${process.env.ASSESSMENT_API_URI}/api/calendar/event`;
        response.data.eventData = (
          await this.httpService
            .patch(updateUrl, { ...obj, eventId: data.eventId })
            .toPromise()
        ).data;
        return response.data;
      } else {
        throw new BadRequestException("no candidateId or recruiterId is provided");
      }
    } catch (error) {
      console.log({ error })
      if (error.status === 400) {
        throw new BadRequestException({
          status: error.status,
          message: error.message,
        });
      } else {
        throw new BadRequestException(error.message);
      }
    }
  }

  async createEvent(userId, body: CreateEventDTO) {
    const { client } = await this.getClient(userId).catch();
    const calendar = client;

    const event = this.getCalendarEventData(body, 1);

    if (calendar) {
      try {
        return await calendar.events.insert({
          calendarId: "primary",
          requestBody: event as any,
        });
      } catch (error) {
        if (error.status === 401) {
          throw new UnauthorizedException({
            status: error.status,
            message: error.message,
          });
        } else if (error.status === 400) {
          throw new BadRequestException({
            status: error.status,
            message: error.message,
          });
        } else {
          throw new BadRequestException(
            "Error creating event.Please check your input data."
          );
        }
      }
    }
  }

  async updateEvent(id, body: CreateEventDTO, action) {
    try {
      const { client } = await this.getClient(id).catch();
      const calendar = client;

      const event = this.getCalendarEventData(body, 1);
      let response: any = { data: {} };

      if (action === "candidate") {
        const uuid = uuidv4();
        const obj = {
          startGmt: body.utcStartDate,
          endGmt: body.utcEndDate,
          title: body.eventTitle,
          description: body.description || "Job Data",
          users: [body.recruiterId],
          remindMe: body.remindMe,
          interviewers: body.interviewers,
          jobId: body.jobId,
          candidateId: id, // needs to change
          timeBefore: null,
          roomId: uuid,
        };

        if (body.remindMe === true) {
          obj.timeBefore = `${body.timeBefore}`;
        }
        const url = `${process.env.ASSESSMENT_API_URI}/api/calendar/event`;
        response.data.eventData = (
          await this.httpService
            .patch(url, { ...obj, eventId: body.eventId })
            .toPromise()
        ).data;
      }

      if (calendar) {
        try {
          response = await calendar.events.update({
            calendarId: "primary",
            eventId: body.calEventId,
            requestBody: event,
          });
        } catch (error) {

          Logger.log(error);
        }
      }
      return response.data;
    } catch (error) {

      if (error.status === 401) {
        throw new UnauthorizedException({
          status: error.status,
          message: error.message,
        });
      } else if (error.status === 400) {
        throw new BadRequestException({
          status: error.status,
          message: error.message,
        });
      } else {
        throw new BadRequestException(
          "Error creating event.Please check your input data."
        );
      }
    }
  }

  getCalendarEventData(body: CreateEventDTO, roomId) {
    const isLiveCoding = body.description === roundType.LIVE_TASK_CODING_ASSESSMENT;
    const event = {
      summary: body.eventTitle,
      description: `${body.description || ""} Click to join the meeting: ${process.env.WEB_APP_URI}/live-coding/${isLiveCoding ? 'online' : 'interview'}/${roomId}?type=${isLiveCoding ? 'coding' : 'interview'}`,
      start: {
        dateTime: body.utcStartDate,
        timeZone: "UTC",
      },
      end: {
        dateTime: body.utcEndDate,
        timeZone: "UTC",
      },
      reminders: {
        useDefault: false,
        overrides: [],
      },
    };

    if (body.remindMe) {
      const conversionFactors = {
        minutes: 1,
        hours: 60,
        days: 24 * 60,
        weeks: 7 * 24 * 60,
      };
      let timeBefore = 0;
      if (body.timeBefore) {
        const [amount, unit] = body.timeBefore.split(" ");

        if (conversionFactors[unit]) {
          timeBefore = Number(amount) * conversionFactors[unit];
        } else {
          timeBefore = Number(amount);
        }
      }
      event.reminders.overrides.push({
        method: "popup",
        minutes: timeBefore,
      });
    }

    return event;
  }

  async getEvents(id, query) {
    const { client } = await this.getClient(id).catch((error) => {
      throw new UnauthorizedException("Invalid grant");
    });

    const calendar = client;
    const date = new Date(query.date) || new Date();
    const res = await calendar.events.list({
      calendarId: "primary",
      timeMin: new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        0,
        0,
        0
      ).toISOString(),
      timeMax: new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
        23,
        59,
        59
      ).toISOString(),
      singleEvents: true,
      orderBy: "startTime",
    });

    const events = res.data.items.map((info) => {
      return {
        summary: info.summary,
        description: info.description,
        startDate: info.start.dateTime,
        endDate: info.end.dateTime,
      };
    });
    return {
      events,
    };
  }

  async getFreeBusySlots(id, data: GetFreeSlotsDTO) {
    const startTime = `${data.date}T00:00:00Z`;
    const endTime = `${data.date}T23:59:59Z`;
    const allSlots = generateAllSlots(startTime, endTime);
    try {
      const client = await this.getClient(id).catch();

      const calendar = client?.client;
      const response = await calendar.freebusy.query({
        requestBody: {
          timeMin: startTime,
          timeMax: endTime,
          timeZone: "UTC",
          items: [{ id: "primary" }],
        },
      });

      const busySlots = response.data.calendars.primary.busy;
      const freeSlots = findFreeSlots(allSlots, busySlots);
      return { freeSlots };
    } catch (error) {
      return { freeSlots: allSlots };
    }

    function findFreeSlots(allSlots, busySlots) {
      const freeSlots = [];
      for (const slot of allSlots) {
        let isBusy = false;

        for (const busySlot of busySlots) {
          if (
            new Date(busySlot.start) < new Date(slot.end) &&
            new Date(busySlot.end) > new Date(slot.start)
          ) {
            isBusy = true;
            break;
          }
        }
        if (!isBusy) {
          freeSlots.push(slot);
        }
      }

      return freeSlots;
    }

    function generateAllSlots(startTime, endTime, interval = 60) {
      const slots = [];
      let currentTime = new Date(startTime);

      while (currentTime < new Date(endTime)) {
        let busyslot = { start: "", end: "" };
        const slotEndTime = new Date(currentTime.getTime() + interval * 60000);
        busyslot.start = currentTime.toISOString();
        busyslot.end = slotEndTime.toISOString();
        slots.push(busyslot);
        currentTime = slotEndTime;
      }
      return slots;
    }
  }

  async appointment(candidateId, data) {
    try {
      let candidateSlots;
      let recruiterSlots;
      candidateSlots = await this.getFreeBusySlots(candidateId, data);
      const info = {
        recruiterId: data.recruiterId,
        data: data,
      };
      recruiterSlots = await this.utilityService.getData(info);
      if (candidateSlots && recruiterSlots) {
        const candidateFreeSlots = candidateSlots.freeSlots;
        const recruiterFreeSlots = recruiterSlots.freeSlots;

        const similarSlots = candidateFreeSlots.filter((candidateSlot) =>
          recruiterFreeSlots.some(
            (recruiterSlot) =>
              candidateSlot.start === recruiterSlot.start &&
              candidateSlot.end === recruiterSlot.end
          )
        );
        return { similarSlots };
      }
    } catch (error) {
      console.log(error);
      if (error.status === 401) {
        throw new UnauthorizedException({
          status: error.status,
          message: error.message,
        });
      } else if (error.status === 400) {
        throw new BadRequestException({
          status: error.status,
          message: error.message,
        });
      } else {
        throw new NotFoundException(error.message);
      }
    }
  }

  async encrypt(arr) {
    const iv = randomBytes(16);
    const key = (await promisify(scrypt)(
      process.env.CRYPTO_PASS,
      "salt",
      32
    )) as Buffer;
    const cipher = createCipheriv("aes-256-ctr", key, iv);
    const encrypted = Buffer.concat([
      cipher.update(arr.join(",")),
      cipher.final(),
    ]);

    return JSON.stringify({
      i: iv.toString("hex"),
      t: encrypted.toString("hex"),
    });
  }

  async decrypt(json) {
    const hash = JSON.parse(json);
    const key = (await promisify(scrypt)(
      process.env.CRYPTO_PASS,
      "salt",
      32
    )) as Buffer;
    const decipher = createDecipheriv(
      "aes-256-ctr",
      key,
      Buffer.from(hash.i, "hex")
    );
    const decrypted = Buffer.concat([
      decipher.update(Buffer.from(hash.t, "hex")),
      decipher.final(),
    ]);

    return decrypted.toString().split(",");
  }

  isValidDate(date): boolean {
    return !isNaN(new Date(date).getTime());
  }

  isValidTime(time: string): boolean {
    const timeRegex = /^(1[0-2]|0?[1-9]):[0-5][0-9] (AM|PM)$/i;
    return timeRegex.test(time);
  }
}
