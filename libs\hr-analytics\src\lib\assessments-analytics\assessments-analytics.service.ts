import {
  Injectable,
  Inject,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import * as moment from "moment";
import {
  assessementsAnalyticsQueryBody,
  assessmentPieAnalyticsQueryBody,
  assessmentsAreaAnalyticsQueryBody,
} from "./assessments-analytics.query";
import { OpensearchService } from "../opensearch/opensearch.service";
import { getStartEndDate } from "../utils/getDateOfJoiningRanges"

@Injectable()
export class AssessmentsAnalyticsService {
  constructor(
    private readonly opensearchService: OpensearchService
  ) { }

  async getAssessmentsPieAnalytics(companyId) {
    try {
      const queryBody = assessmentPieAnalyticsQueryBody(companyId);
      const response = await this.opensearchService.searchIndexSpecificData(queryBody, 'assessments');
      return response.body.aggregations;
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(error, "Failed to fetch data");
    }
  }

  async getAssessmentsArea(duration: 'week' | 'month' | 'year', companyId: number) {
    try {
      const startOf = moment().startOf(duration).toDate();
      const endOf = moment().endOf(duration).toDate();
      const interval = duration === 'week' || duration === 'month' ? 'day' : 'month';

      const queryBody = assessmentsAreaAnalyticsQueryBody(startOf, endOf, interval, companyId);
      const response = await this.opensearchService.searchIndexSpecificData(queryBody, "assessments");

      return response.body.aggregations;
    } catch (error) {
      Logger.error("Error in getAssessmentsArea:", error);
      throw new InternalServerErrorException(error, "Failed to fetch assessment analytics data");
    }
  }

  async getMembersAnalytics(queryParams, companyId) {
    try {
      const {
        search,
        sortBy,
        sortType,
        limit,
        offset,
      } = queryParams;

      const body: any = assessementsAnalyticsQueryBody(limit, offset, companyId, queryParams);
      const response = await this.opensearchService.searchIndexSpecificData(body, 'assessments');

      return response.body;
    } catch (error) {
      Logger.error('Error:', error);
      throw new InternalServerErrorException(error, 'Failed to fetch data');
    }
  }
}
