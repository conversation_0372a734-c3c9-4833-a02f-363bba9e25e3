# Changes by Dhruv
FROM public.ecr.aws/docker/library/node:20.19.3 as build-stage
# FROM public.ecr.aws/docker/library/node:18 as build-stage

RUN apt-get update && apt-get install -y \
    python3 \
    python3-dev \
    build-essential \
    ca-certificates && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

# Set Python for node-gyp 
ENV PYTHON /usr/bin/python3

WORKDIR /app

COPY . .

RUN yarn

RUN yarn run build-pre-rec

# Changes by Dhruv
FROM public.ecr.aws/docker/library/node:20.19.3-alpine
# FROM public.ecr.aws/docker/library/node:18-alpine

RUN apk add --no-cache \
    chromium \
    nss \
    freetype \
    harfbuzz \
    ca-certificates \
    ttf-freefont

WORKDIR /app

COPY --from=build-stage /app/dist/apps/pre-recruitment-api .

COPY ./apps/temporal/src/app/certs /app/certs

# Set environment variables for Temporal Cloud connection
ENV TEMPORAL_SERVER=recruitment-prod.lrnb6.tmprl.cloud:7233
ENV TEMPORAL_NAMESPACE=recruitment-prod.lrnb6
ENV CLIENT_CA_PATH=/app/certs/client.pem
ENV CLIENT_KEY_PATH=/app/certs/client.key

RUN yarn --production
RUN yarn add pg

EXPOSE 3001

CMD ["node", "main.js"]
