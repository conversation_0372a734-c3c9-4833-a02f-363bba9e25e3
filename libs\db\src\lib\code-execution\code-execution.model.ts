import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';

@Table({
  tableName: 'code-execution',
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class CodeExecution extends Model<CodeExecution> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    example:"91",
    description: 'Id of language',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    language_id?: string;

  @ApiProperty({
    example: "91f499b4-1918-4499-b505-0142709ff0e1",
    description: 'Token of the submission',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    judge0_id: string;

  @ApiProperty({
    example: 'script.js',
    description: 'name and extension of the script',
  })
  @Column({ type: DataType.STRING, allowNull: true })
   judge0_created_at: string;

  @ApiProperty({
    example: 'script.js',
    description: 'name and extension of the script',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  judge0_finished_at: string;

  @ApiProperty({
    example: 'script.js',
    description: 'name and extension of the script',
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  judge0_status_id: number;
}
