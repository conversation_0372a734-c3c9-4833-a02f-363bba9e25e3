import { Test, TestingModule } from '@nestjs/testing';
import { CodingAreaGateway } from './coding-area.gateway';

describe('CodingAreaGateway', () => {
  let gateway: CodingAreaGateway;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CodingAreaGateway],
    }).compile();

    gateway = module.get<CodingAreaGateway>(CodingAreaGateway);
  });

  it('should be defined', () => {
    expect(gateway).toBeDefined();
  });
});
