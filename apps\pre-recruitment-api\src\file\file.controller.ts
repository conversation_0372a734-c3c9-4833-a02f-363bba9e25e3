import { Body, Controller, Get, Post, Req, Res, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { FileService } from "./file.service";

@ApiTags('File')
@Controller('file')
export class FileController {
  constructor(private readonly fileService: FileService) {}

  @ApiOperation({ summary: 'Upload file to s3, you need post form data and key: "upload"', })
  @ApiResponse({ status: 201, type: 'Url to file' })
  @UseGuards(AuthGuard('jwt'))
  @Post('/public')
  async avatar(@Req() request, @Res() response) {
    try {
      await this.fileService.avatar(request, response);
    } catch (error) {
      return response
        .status(500)
        .json(`Failed to upload image file: ${error.message}`);
    }
  }

  @ApiOperation({ summary: 'Upload file to s3, you need post form data and key: "upload"', })
  @ApiResponse({ status: 201, type: 'Url to file' })
  @UseGuards(AuthGuard('jwt'))
  @Post('/public-multi')
  async multipleUpload(@Req() request, @Res() response) {
    try {
      await this.fileService.multipleUpload(request, response);
    } catch (error) {
      return response
        .status(500)
        .json(`Failed to upload image file: ${error.message}`);
    }
  }

  @ApiOperation({
    summary:
      'Upload private file to s3, you need post form data and key: "upload"',
  })
  @ApiResponse({ status: 201, type: 'Url to file' })
  @UseGuards(AuthGuard('jwt'))
  @Post('/private')
  async candidatePrivateUpload(@Req() request, @Res() response) {
    try {
      await this.fileService.privateFile(request, response);
    } catch (error) {
      return response
        .status(500)
        .json(`Failed to upload file: ${error.message}`);
    }
  }

  @ApiOperation({
    summary: 'get url to private file',
  })
  @ApiResponse({ status: 201, type: 'Url to file' })
  @UseGuards(AuthGuard('jwt'))
  @Post('/get-private')
  async getPrivateFileUrl(@Body() data: { key: string }, @Res() response) {
    await this.fileService.getPrivateFileUrl(data, response);
  }

  @ApiOperation({
    summary: 'get url to private file',
  })
  @Get('all-files')
  @UseGuards(AuthGuard('jwt'))
  async getAllFilesFromS3(@Req() request) {
    return await this.fileService.getAllFilesFromS3Bucket(request);
  }
}
