import {
  <PERSON><PERSON>sTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Candidate } from '../candidates/candidates.model'
import { Recruiter } from '../recruiters/recruiters.model'

interface ExperienceAttrs {
  companyName?: string;
  position?: string;
  location?: string;
  start?: string;
  end?: string;
  skills?: any;
  present?: boolean;
  jobDescription?: string;
  recruiterId?: number;
  candidateId?: number;
}

@Table({ tableName: 'experiences' })
export class Experience extends Model<
  Experience,
  ExperienceAttrs
  > {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'urecruits', description: 'Company Name' })
  @Column({ type: DataType.STRING, allowNull: true })
  companyName: string;

  @ApiProperty({ example: 'Web Developer', description: 'Position' })
  @Column({ type: DataType.STRING, allowNull: true })
  position: string;

  @ApiProperty({ example: 'New York', description: 'Location' })
  @Column({ type: DataType.STRING, allowNull: true })
  location: string;

  @ApiProperty({
    example: 'January 12, 2022',
    description: 'Service Periods start',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  start: string;

  @ApiProperty({
    example: 'January 12, 2022',
    description: 'Service Periods end',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  end: string;

  @ApiProperty({ example: false, description: 'Present job?' })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  present: boolean;

  @ApiPropertyOptional({ example: "Description", description: "Job Description" })
  @Column({ type: DataType.TEXT, allowNull: true })
  jobDescription: string;

  @ApiPropertyOptional({ example: [], description: "Skills" })
  @Column({ type: DataType.JSONB, allowNull: true })
  skills: any;

  @ApiProperty({ example: '1', description: 'Recruiter ID' })
  @ForeignKey(() => Recruiter)
  @Column({ type: DataType.INTEGER, allowNull: true })
  recruiterId: number;

  @ApiProperty({ example: '1', description: 'Candidate ID' })
  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER, allowNull: true })
  candidateId: number;

  @BelongsTo(() => Recruiter)
  recruiter: Recruiter;

  @BelongsTo(() => Candidate)
  candidate: Candidate;
}
