import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

const LocationExample = [{ id: 1, value: 'location', label: 'Location' }];

export class JobDto {
  @ApiPropertyOptional({ example: "Angular Developer", description: "Job Title" })
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({ example: "Recruits", description: "Employer" })
  @IsOptional()
  employer?: string;

  @ApiPropertyOptional({ example: "", description: "Lead user email" })
  @IsOptional()
  approvalEmail?: string;

  @ApiPropertyOptional({ example: "", description: "Lead user name" })
  @IsOptional()
  approvalName?: string;

  @ApiPropertyOptional({ example: "1", description: "Approver id" })
  @IsOptional()
  approverId?: number;

  @ApiPropertyOptional({ example: "Consultancy", description: "Consultancy" })
  @IsOptional()
  consultancy?: string;

  @ApiPropertyOptional({ example: LocationExample, description: "Job Location" })
  @IsOptional()
  locations?: typeof LocationExample;

  @ApiPropertyOptional({ example: "false", description: "Remote Location" })
  @IsOptional()
  location?:string

  @ApiPropertyOptional({ example: "false", description: "Remote Location" })
  @IsOptional()
  remoteLocation?: boolean;

  @ApiPropertyOptional({ example: "500", description: "Salary Range(per month min)" })
  @IsOptional()
  salaryMonthMin?: number;

  @ApiPropertyOptional({ example: "500", description: "Salary Range(per month max)" })
  @IsOptional()
  salaryMonthMax?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year min)" })
  @IsOptional()
  salaryYearMin?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year max)" })
  @IsOptional()
  salaryYearMax?: number;

  @ApiPropertyOptional({ example: "600", description: "Salary Range(per Hour min)" })
  @IsOptional()
  salaryHourMin?: number;

  @ApiPropertyOptional({ example: "600", description: "Salary Range(per Hour max)" })
  @IsOptional()
  salaryHourMax?: number;

  @ApiPropertyOptional({ example: "false", description: "Negotiable" })
  @IsOptional()
  negotiable?: boolean;

  @ApiPropertyOptional({ example: "TEXT", description: "Job Description" })
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ example: "TEXT", description: "Job Short Description" })
  @IsOptional()
  shortDescription?: string;

  @ApiPropertyOptional({ example: "65", description: "№ of Openings" })
  @IsOptional()
  numberOpenings?: string;

  @ApiPropertyOptional({ example: "Full Time", description: "Job Type" })
  @IsOptional()
  jobType?: string;

  @ApiPropertyOptional({ example: "Morning Shift", description: "Preferable Shift" })
  @IsOptional()
  preferableShift?: string;

  @ApiPropertyOptional({ example: 1, description: "Position Id" })
  @IsOptional()
  positionId?: number;

  @ApiPropertyOptional({ example: '1', description: 'Industry' })
  @IsOptional()
  industryId?: number;

  @ApiPropertyOptional({ example: "Functional Area", description: "Functional Area" })
  @IsOptional()
  functionalArea?: string;

  @ApiPropertyOptional({ example: "2 Months", description: "Notice Period" })
  @IsOptional()
  noticePeriod?: string;

  @ApiPropertyOptional({ example: ["Photoshop", "Figma", "Adobe XD"], description: "Skills" })
  @IsOptional()
  skills?: any;

  @ApiPropertyOptional({ example: "1", description: "Experience Min" })
  @IsOptional()
  experienceMin?: number;

  @ApiPropertyOptional({ example: "5", description: "Experience Max" })
  @IsOptional()
  experienceMax?: number;

  @ApiPropertyOptional({ example: "Master Degree", description: "Education" })
  @IsOptional()
  education?: string;

  @ApiPropertyOptional({ example: [], description: "Screening Questions" })
  @IsOptional()
  screeningQuestions?: any;

  @ApiPropertyOptional({ example: [], description: "Benefit(s)" })
  @IsOptional()
  benefits?: any;

  @ApiPropertyOptional({ example: "TEXT", description: "About Company" })
  @IsOptional()
  aboutCompany?: string;

  @ApiPropertyOptional({ example: "1", description: "Position Workflow ID" })
  @IsOptional()
  workflowId?: number;

  @ApiPropertyOptional({ example: "Enter employer", description: "Career Portal" })
  @IsOptional()
  careerPortal?: string;

  @ApiPropertyOptional({ example: "url", description: "Linkedin" })
  @IsOptional()
  linkedin?: string;

  @ApiPropertyOptional({ example: "url", description: "Facebook" })
  @IsOptional()
  facebook?: string;

  @ApiPropertyOptional({ example: "url", description: "Twitter" })
  @IsOptional()
  twitter?: string;

  @ApiPropertyOptional({ example: "url", description: "Instagram" })
  @IsOptional()
  instagram?: string;

  @ApiPropertyOptional({ example: "true", description: "Career Page" })
  @IsOptional()
  careerPage?: boolean;

  @ApiPropertyOptional({ example: "true", description: "Public Search" })
  @IsOptional()
  publicSearch?: boolean;

  @ApiPropertyOptional({ example: [], description: "Job Boards" })
  @IsOptional()
  jobBoards?: any;

  @ApiPropertyOptional({ example: "Json", description: "Application Form Json" })
  @IsOptional()
  applicationForm?: any;

  @ApiPropertyOptional({ example: "draft", description: "Status" })
  @IsOptional()
  status?: string;

  @ApiPropertyOptional({ example: false, description: "Whether the source of job creation is assessment or not" })
  @IsOptional()
  isAssessment?: boolean;
}

export class DeleteJobDto {
  @ApiProperty({ example: "6000", description: "Job id" })
  id: number;
}
