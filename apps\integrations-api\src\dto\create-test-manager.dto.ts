import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { Column, DataType } from 'sequelize-typescript';

export enum AssessmentCategory {
  domainAssessment = "Domain Assessment",
  codingAssessment = "Coding Assessment",
  // thirdPartyAssessment = "Third Party Assessment",
}

export class TestManagerDto {
  @ApiProperty({ example: "1", description: "User ID" })
    candidateId: number;
  
  @ApiProperty({ example: "1", description: "Job ID" })
    jobId: number;
  
  @ApiProperty({ example: "2023/12/12", description: "Date of deadline" })
    deadline: Date;
  
  @ApiProperty({ example: "2 Hrs", description: "Time Duration of the test" })
    timeDuration: number;

    @ApiProperty({
      enum: AssessmentCategory,
      oneOf: [
        { type: AssessmentCategory.domainAssessment },
        { type: AssessmentCategory.codingAssessment },
        // { type: AssessmentCategory.thirdPartyAssessment },
      ],
    })
    @IsOptional()
    @IsEnum(AssessmentCategory)
    @Column({
      type: DataType.ENUM(
        AssessmentCategory.domainAssessment,
        AssessmentCategory.codingAssessment,
        // AssessmentCategory.thirdPartyAssessment,
      ),
      allowNull: false,
    })
    assessmentType?: AssessmentCategory;

  @ApiProperty({
    example: '323',
    description: 'Assessment Id',
  })
    assessmentId: string;
}
