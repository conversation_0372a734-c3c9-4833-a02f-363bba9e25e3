import { Module } from '@nestjs/common'
import { SequelizeModule } from '@nestjs/sequelize'
import { ConfigModule } from '@nestjs/config'
import { UsersModule } from './users/users.module'
import { User } from './users/users.model'
import { Company } from './companies/companies.model'
import { Candidate } from './candidates/candidates.model'
import { Award } from './awards/awards.model'
import { Project } from './projects/projects.model'
import { Certificate } from './certificates/certificates.model'
import { CompaniesModule } from './companies/companies.module'
import { CandidatesModule } from './candidates/candidates.module'
import { AwardsModule } from './awards/awards.module'
import { ProjectsModule } from './projects/projects.module'
import { CertificatesModule } from './certificates/certificates.module'
import { SkillsModule } from './skills/skills.module'
import { Experience } from './experiences/experiences.model'
import { Skill } from './skills/skills.model'
import { ExperiencesModule } from './experiences/experiences.module'
import { EducationsModule } from './educations/educations.module'
import { Education } from './educations/educations.model'
import { IndustriesModule } from './industries/industries.module'
import { Industry } from './industries/industries.model'
import { Recruiter } from './recruiters/recruiters.model'
import { RecruitersModule } from './recruiters/recruiters.module'
import { Role } from './roles/roles.model'
import { RolesModule } from './roles/roles.module'
import { AddressesModule } from './addresses/addresses.module'
import { Address } from './addresses/addresses.model'
import { UserRoles } from './roles/user-roles.model'
import { CandidateIndustry } from './industries/candidates-industries.model'
import { Auth0Module } from '@microservices/auth';
import { GoogleModule } from "@microservices/integrations";
import { Location } from './locations/location.model'
import { LocationsModule } from './locations/locations.module'
import { Position } from './positions/positions.model'
import { PositionsModule } from './positions/positions.module'
import { SubscriptionModule } from "./subscription/subscription.module";
import { Subscriptions } from "./subscription/subscriptions.model";
import { SubscriptionAddon } from './subscription/subscription-addon.model'
import { PaymentModule } from "./payment/payment.module";
import { PlanPackages } from './plan-packages/plan-packages.model'
import { Plans } from './plan/plan.model'
import { StripeModule } from 'nestjs-stripe';
import { JobModule } from "./job/job.module";
import { Jobs } from "./job/job.model";
import { LocationJobs } from "./locations/location-jobs.model";
import { RecruiterPositions } from "./recruiter-positions/recruiter-positions.model";
import { RecruiterPositionsModule } from "./recruiter-positions/recruiter-positions.module";
import { Department } from "./department/department.model";
import { DepartmentModule } from "./department/department.module";
import { PredefinedSkillModule } from "./predefined-skills/predefined-skill.module";
import { EmailModule } from "@microservices/email";
import { PredefinedSkills } from "./predefined-skills/predefined-skill.model";
import { Integrations } from './integrations/integrations.model';
import { IntegrationsModule } from './integrations/integrations.module';
import { Workflow } from "./workflow/workflow.model";
import { WorkflowModule } from "./workflow/workflow.module";
import { Subscribe } from "./subscribe/subscribe.model";
import { SubscribeModule } from "./subscribe/subscribe.module";
import { ProductModule } from "./product/product.module";
import { PlanPackageModule } from './plan-packages/plan-packages.module'
import { PlanModule } from './plan/plan.module'
import { PriceModule } from "./price/price.module";
import { RoundModule } from "./rounds/round.module";
import { Round } from "./rounds/round.model";
import { OfferModel } from "./offers/offer.model";
import { HellosignTemplate } from "./offers/hellosign-template.model";
import { OfferModule } from "./offers/offer.module";
import { FileModule } from '../../../../apps/pre-recruitment-api/src/file/file.module';
import { IntegrationAuthModule } from './integrationAuth/integrationAuth.module';
import { GmailModule } from './gmail/gmail.module';
import { IntegrationAuth } from './integrationAuth/integrationAuth.model';
import { TemporalWorkflowModule } from './temporalWorkflow/temporal.module'
import { TemporalWorkflow } from './temporalWorkflow/temporal.model'
import { OutlookModule } from './outlook/outlook.module'
import { Playback } from './playback/playback.model'
import { PlaybackModule } from './playback/playback.module'
import { MailboxModule } from './gmail-mailbox/mailbox.module'
import { Mailbox } from './gmail-mailbox/mailbox.model'
import { HttpModule } from '@nestjs/axios'
import { HellosignIntegration } from "./hellosign/hellosign-integration.model";
import { HellosignIntegrationModule } from "./hellosign/hellosign-integration.module";
import { BackgroundModel } from './background-screening/background.model';
import { BackgroundModule } from './background-screening/background.module';
import { InterviewsModule } from "./interviews/interviews.module";
import { CandidateOffersModule } from "./candidateoffers/candidate-offers.module";
import { DrugModel } from "./drug-screening/drug.model";
import { DrugModule } from "./drug-screening/drug.module";
import { UniversalModule } from './universal/universal.module';
import { UniversalIntegration } from './universal/universal.model';
import { JobTargetModule } from './jobtarget/jobtarget.module'
import { JobTargetIntegration } from './jobtarget/jobtarget.model'
import { FreshsalesModule } from "./freshsales/freshsales.module";
import { NotifyPermissionsModel } from './notify-permissions/notify-permissions.model'
import { NotificationsModel } from './notifications/notifications.model'
import { NotificationsModule } from './notifications/notifications.module'
import { Payment } from './payment/payment.model'
import { Product } from './product/product.model'
import { Price } from './price/price.model'
import { UserAssignments } from './user-assignments/user-assignments.model'
import { WorkflowChatbotModule } from './workflow-chatbot/workflow-chatbot.module'

@Module({
  imports: [
    ConfigModule.forRoot(),
    SequelizeModule.forRoot({
      dialect: "postgres",
      host: JSON.parse(process.env.PRERECRUITMENTMICRO_SECRET).host,
      port: JSON.parse(process.env.PRERECRUITMENTMICRO_SECRET).port,
      username: JSON.parse(process.env.PRERECRUITMENTMICRO_SECRET).username,
      password: JSON.parse(process.env.PRERECRUITMENTMICRO_SECRET).password,
      database: JSON.parse(process.env.PRERECRUITMENTMICRO_SECRET).dbname,
      autoLoadModels: false,
      logging: false,
      models: [
        NotifyPermissionsModel,
        NotificationsModel,
        User,
        UserRoles,
        CandidateIndustry,
        Company,
        Candidate,
        UserAssignments,
        Award,
        Project,
        Certificate,
        Skill,
        Experience,
        Education,
        Industry,
        Recruiter,
        Role,
        Address,
        Location,
        Position,
        Jobs,
        LocationJobs,
        RecruiterPositions,
        Department,
        PredefinedSkills,
        Integrations,
        Workflow,
        Subscribe,
        Round,
        HellosignTemplate,
        HellosignIntegration,
        OfferModel,
        BackgroundModel,
        DrugModel,
        UniversalIntegration,
        JobTargetIntegration,
        IntegrationAuth,
        TemporalWorkflow,
        Playback,
        Mailbox,
        Subscriptions,
        SubscriptionAddon,
        PlanPackages,
        Plans,
        Payment,
        Product,
        Price
      ],
      synchronize: true,
    }),
    UsersModule,
    CompaniesModule,
    CandidatesModule,
    AwardsModule,
    ProjectsModule,
    CertificatesModule,
    SkillsModule,
    ExperiencesModule,
    EducationsModule,
    IndustriesModule,
    RecruitersModule,
    RolesModule,
    AddressesModule,
    Auth0Module,
    LocationsModule,
    PositionsModule,
    PaymentModule,
    JobModule,
    RecruiterPositionsModule,
    DepartmentModule,
    PredefinedSkillModule,
    EmailModule,
    WorkflowModule,
    SubscribeModule,
    ProductModule,
    PlanPackageModule,
    PlanModule,
    PriceModule,
    GoogleModule,
    IntegrationsModule,
    RoundModule,
    HellosignIntegrationModule,
    OfferModule,
    CandidateOffersModule,
    BackgroundModule,
    DrugModule,
    UniversalModule,
    JobTargetModule,
    InterviewsModule,
    FreshsalesModule,
    FileModule,
    IntegrationAuthModule,
    GmailModule,
    FileModule,
    TemporalWorkflowModule,
    OutlookModule,
    PlaybackModule,
    MailboxModule,
    HttpModule,
    NotificationsModule,
    SubscriptionModule,
    WorkflowChatbotModule,
    StripeModule.forRoot({
      apiKey: process.env.STRIPE_SECRET_KEY,
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      apiVersion: "2020-08-27",
    }),
  ],
  exports: [
    CompaniesModule,
    JobModule
  ],
})
export class RecruitmentDbModule { }
