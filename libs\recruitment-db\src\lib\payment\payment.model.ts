import {
  Column,
  DataType,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../companies/companies.model';
import { Subscriptions } from '../subscription/subscriptions.model';
import { User } from '../users/users.model';

interface PaymentAttrs {
  subId: number,
  status: string,
  transactionId: string,
  amount: number,
  companyId: number,
  userId: number,
  startDuration: Date,
  endDuration: Date,
  invoiceId:string,
}

@Table({ 
  tableName: 'payment',
  createdAt:true,
  updatedAt: true,
  timestamps:true
 })
export class Payment extends Model<Payment, PaymentAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'completed', description: 'Payment status' })
  @Column({ type: DataType.STRING, allowNull: true })
  status: string;

  @ApiProperty({
    example: '17-09-2024',
    description:
      'paymet date that the subscription has been invoiced on.',
  })
  @Column({ type: DataType.DATE, allowNull: true })
  invoiceOnDate: Date


  @ApiProperty({
    example: '686',
    description:
      'payment amount.',
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  amount: number;

  @ApiProperty({ example: '1', description: 'Subscription ID' })
  @ForeignKey(() => Subscriptions)
  @Column({ type: DataType.INTEGER, allowNull: false })
  subId: number;

  @ApiProperty({ example: '1', description: 'Company ID' })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({ example: '1', description: 'User ID' })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({ example: '1', description: 'Start date of subscription' })
  @Column({ type: DataType.DATE, allowNull: true })
  startDuration: Date;

  @ApiProperty({ example: '1', description: 'End date of subscription' })
  @Column({ type: DataType.DATE, allowNull: true })
  endDuration: Date;

  @ApiProperty({
    example: '',
    description: 'Stripe payment invoice url',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  invoiceUrl: string;

  @ApiProperty({
    example: '',
    description: 'Stripe payment invoice pdf download link.',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  invoicePdf: string;

  @ApiProperty({
    example: '',
    description: 'Stripe payment invoice Id',
  })
  @Column({ type: DataType.STRING, allowNull: false })
  invoiceId: string;

  @BelongsTo(() => Subscriptions)
  subscription: Subscriptions;

  @BelongsTo(() => Company)
  company: Company;

  @BelongsTo(() => User)
  user: User;
}
