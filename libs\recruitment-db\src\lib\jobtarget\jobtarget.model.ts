import {
    Column,
    DataType,
    Model,
    Table,
    ForeignKey,
    BelongsTo,
} from 'sequelize-typescript';
import { Company } from '../companies/companies.model';

@Table({
    tableName: 'jobtarget_integration',
    createdAt: 'created_at',
    updatedAt: 'updated_at',
})
export class JobTargetIntegration extends Model<JobTargetIntegration> {
    @Column({ type: DataType.INTEGER, primaryKey: true, autoIncrement: true })
    id: number;

    @ForeignKey(() => Company)
    @Column({ field: 'company_id', type: DataType.INTEGER, allowNull: false })
    companyId: number;

    @Column({ field: 'token', type: DataType.TEXT, allowNull: false })
    token: string;

    @BelongsTo(() => Company)
    company: Company;
    sso_link: any;
}
