import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>Type,
  ForeignKey,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Jobs } from "../job/job.model";
import { User } from "../users/users.model";
import { OfferModel } from "../offers/offer.model";
import { Round } from "../rounds/round.model";
import { BackgroundModel } from "../background-screening/background.model";
import {DrugModel} from "../drug-screening/drug.model"

@Table({ tableName: "subscribe", createdAt: true, updatedAt: true })
export class Subscribe extends Model<Subscribe> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "1", description: "Job ID" })
  @ForeignKey(() => Jobs)
  @Column({ type: DataType.INTEGER, allowNull: false })
  jobId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @ApiProperty({ example: "1", description: "Offer ID" })
  @ForeignKey(() => OfferModel)
  @Column({ type: DataType.INTEGER, allowNull: true })
  offerId: number;

  @ApiProperty({ example: "1", description: "Background Order Id" })
  @ForeignKey(() => BackgroundModel)
  @Column({ type: DataType.INTEGER, allowNull: true })
  backgroundOrderId: number;

  @ApiProperty({ example: "1", description: "Drug Order Id" })
  @ForeignKey(() => DrugModel)
  @Column({ type: DataType.INTEGER, allowNull: true })
  drugOrderId: number;

  @ApiProperty({ example: "1", description: "Round ID" })
  @ForeignKey(() => Round)
  @Column({ type: DataType.INTEGER, allowNull: true })
  roundId: number;

  @ApiPropertyOptional({ example: 87, description: "Match" })
  @Column({ type: DataType.INTEGER, allowNull: true, defaultValue: 0 })
  match: number;

  @ApiPropertyOptional({ example: false, description: "Save job status" })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  saveJob: boolean;

  @ApiPropertyOptional({ example: false, description: "Apply job status" })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  applyJob: boolean;

  @ApiPropertyOptional({ example: false, description: "Subscribe job status" })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  subscribeJob: boolean;

  @ApiPropertyOptional({ example: "", description: "Status" })
  @Column({ type: DataType.STRING, allowNull: true })
  status: string;

  @ApiPropertyOptional({ example: 0, description: "Total Scores" })
  @Column({ type: DataType.INTEGER, allowNull: true, defaultValue: 0 })
  totalScores: number;

  @ApiProperty({ example: [], description: "Summary" })
  @Column({ type: DataType.JSONB, allowNull: true })
  summary: any;

  @ApiPropertyOptional({ example: "", description: "Offer letter status" })
  @Column({
    type: DataType.STRING,
    allowNull: true,
    defaultValue: "Not Initiated",
  })
  letterStatus: string;

  @ApiPropertyOptional({ example: "64413de7-439f-41a8-bb4c-1a72b000705e", description: "JobTarget GUID" })
  @Column({ type: DataType.STRING, allowNull: true })
  JT_GUID: string;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Jobs)
  job: Jobs;

  @BelongsTo(() => OfferModel)
  offer: OfferModel;

  @BelongsTo(()=>BackgroundModel)
  backgroundOrder : BackgroundModel

  @BelongsTo(()=>DrugModel)
  drugOrder : DrugModel

  @BelongsTo(() => Round)
  round: Round;
}
