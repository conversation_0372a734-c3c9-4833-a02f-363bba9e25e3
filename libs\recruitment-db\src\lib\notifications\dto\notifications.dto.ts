import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class CreateNotificaionDto{
  @ApiProperty({ example: "123456qQ@", description: "Notification title" })
  @IsOptional()
  title?:string;

  @ApiProperty({ example: "123456qQ@", description: "Notification body" })
  @IsOptional()
  description?:string;

  @ApiProperty({ example: "123456qQ@", description: "Notification image" })
  @IsOptional()
  image?:string;

  @ApiProperty({ example: "123456qQ@", description: "Notification userId" })
  @IsOptional()
  userId?:number;
  
  @ApiProperty({ example: "123456qQ@", description: "Notification link" })
  @IsOptional()
  link?:string;
}


export class ChangeNotificationStatusDto {
  @ApiProperty({ example: "1", description: "Notification id" })
  id:number
  
  @ApiProperty({ example: true, description: "Read notification status" })
  read:boolean
}