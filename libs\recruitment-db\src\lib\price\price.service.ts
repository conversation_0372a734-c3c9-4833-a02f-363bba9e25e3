import { BadRequestException, HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { CompaniesService } from "../companies/companies.service";
import { InjectStripe } from "nestjs-stripe";
import Stripe from "stripe";
// import { PaymentMethodDto } from "./dto/paymentMethod.dto";
// import { PaymentMethodIdDto } from "./dto/paymentMethodId.dto";
import { InjectModel } from "@nestjs/sequelize";
import { Price } from "./price.model";
import { Op } from "sequelize";
import { CreatePriceDto } from "./dto/create-price.dto";
import { GetPricesDto, UpdatePriceDto, UpdateAllPricePlanPackageDto } from "./dto/update-price.dto";
import { Plans } from "../plan/plan.model";
import { PlanPackages } from "../plan-packages/plan-packages.model";
import * as priceData from "./pricedata.json";

@Injectable()
export class PriceService {
  public constructor(
    @InjectModel(Price) private priceRepository: typeof Price,
    @InjectStripe() private readonly stripeClient: Stripe,
  ) {}

  async seed() {
    await this.priceRepository
      .bulkCreate(priceData, {
        validate: true,
      })
      .then(async () =>
        this.priceRepository.sequelize.query(
          `ALTER SEQUENCE "${
            this.priceRepository.tableName
          }_id_seq" RESTART WITH ${
            (await this.priceRepository.count()) + 1
          }`
        )
      );
    return true;
  }

  formateQuery(dto:GetPricesDto) {

    let where: any = {}
      let order: any = []

      if (dto.id) {
        where.id = dto.id
      }
      
      if (dto.priceId) {
        where.priceId = dto.priceId
      }

      if (dto.productId) {
        where.productId = dto.productId
      }

      if (dto.planId) {
        where.planId = dto.planId
      }

      if (dto.planPackageId) {
        where.planPackageId = dto.planPackageId
      }
      
      if (dto.monthlyYearly) {
        where.monthlyYearly = dto.monthlyYearly
      }

      if (dto.hasOwnProperty('active')) {
        where.active = dto.active
      }

      if (dto.hasOwnProperty('isDefaultPackagePlan')) {
        where.isDefaultPackagePlan = dto.isDefaultPackagePlan
      }

      if(dto.sortBy && dto.sortType){
        order.push([`${dto.sortBy}`,`${dto.sortType}`])
      }

      return {where,order,include:[Plans,PlanPackages]};
  };
  async getAllPrice(dto:GetPricesDto) {
    try {
      const queryFormat = this.formateQuery(dto);
      return await this.priceRepository.findAll(queryFormat)
    } catch (error) {
      throw new Error("Error while retriving price" + error)
    }
  }

  async createPrice(dto:CreatePriceDto) {
    try {
      if (!dto.priceId) {
        throw new BadRequestException("Price ID is not provided")
      }
      const price = await this.priceRepository.findOne({ where: { priceId: dto.priceId } })
      if (price) {
        return price
      }
      return await this.priceRepository.create(dto)
    } catch (error) {
      throw new Error("Error while creating price" + error)
    }
  }

  async getPrice(dto:GetPricesDto){
    try {
      const queryFormat = this.formateQuery(dto);
      return await this.priceRepository.findOne(queryFormat)
    } catch (error) {
      throw new Error("Error while retriving price" + error)
    }
  }

  async changePriceStatusInStripe({ productId, priceId, active, newDefaultPriceId }: { productId: string, priceId: string, active: boolean, newDefaultPriceId:string }){
    try {
      // Update the price to set it as active/inactive
      const existProduct = await this.stripeClient.products.retrieve(productId);
      if(existProduct && existProduct.default_price === priceId){
        await this.stripeClient.products.update(existProduct.id,{
          default_price: newDefaultPriceId
        })
      }
      await this.stripeClient.prices.update(priceId,{
        active
      })
      
    } catch (error) {
      throw new Error(`Failed to archive the price: ${error.message}`);
    }
  }
  
  async getPriceByPriceId(priceId: string) {
    return await this.priceRepository.findOne({ where: { priceId: { [Op.iLike]: `%${priceId}%` } } ,include:[Plans,PlanPackages]})
  }

  async updatePrice(dto:UpdatePriceDto) {
    try {
      if (!dto.priceId) {
        throw new BadRequestException("Price ID is not provided")
      }
      const price = await this.priceRepository.findOne({ where: { priceId: dto.priceId } })
      if (price) {
        Object.assign(price, { ...dto })
        await price.save()
      } else {
        await this.priceRepository.create(dto);
      }
    } catch (error) {
      throw new Error("Error while updating price" + error)
    }
  }


  //update all price based on productId
  async updateAllPricePlanPackage(dto:UpdateAllPricePlanPackageDto) {
    try {
      if (!dto.productId && !dto.planId && !dto.planPackageId ) {
        throw new BadRequestException("Price ID is not provided")
      }
      const price = await this.priceRepository.update(
        { 
          planId: dto.planId,
          planPackageId: dto.planPackageId
        },
        { where: { productId: dto.productId } } 
      );
      if (price) {
        
      } else {
      }
    } catch (error) {
      throw new Error("Error while updating price" + error)
    }
  }

  async deletePrice(dto:{priceId:string}) {
    const {priceId} = dto
    try {
      if (!priceId) {
        throw new BadRequestException("Price ID is not provided")
      }
      const price = await this.priceRepository.findOne({ where: { priceId: priceId } })
      if (price) {
        await price.destroy()
      }
    } catch (error) {
      throw new Error("Error while deleting price" + error)
    }

  }

}
