import * as crypto from 'crypto';

const iv = crypto.randomBytes(16).toString('hex').slice(0,16)
const key = 'Some random string for key';


export function encodeToken(token: string) {
    const encrypter = crypto.createCipheriv('aes-256-ccm', key, iv);
    let encrypterMessage =  encrypter.update(token, 'utf8', 'hex');
    encrypterMessage += encrypter.final('hex')

    return encrypterMessage;
}

export function decodeToken(token: string) {
    const decrypter = crypto.createDecipheriv('aes-256-ccm', key, iv);

    let decryptedMessage = decrypter.update(token, 'hex', 'utf8')
    decryptedMessage += decrypter.final('utf8');

    return decryptedMessage
}