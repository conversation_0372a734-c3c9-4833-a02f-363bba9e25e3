import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional } from 'class-validator';
import { CreateEventDto } from './create-event.dto';

export class UpdateEventDto {
  @ApiProperty({ example: "GHVWAS672330DHJBHJQW87YW", description: 'TWILIO room Id', required: true })
  @IsOptional()
  roomId?: string;

  @ApiProperty({ example: '11', description: 'Job ID' })
  @IsNotEmpty()
  jobId: any;

  @ApiProperty({ example: '16', description: 'ID of the user who has booked appointment' })
  @IsNotEmpty()
  candidateId: any;

  @ApiProperty({ example: 'Completed', description: 'Status of the room' })
  @IsOptional()
  status?: STATUS;
}

export class UpdateEventStatusDto{
  @ApiProperty({ example: 'Completed', description: 'Status of the room' })
  status?: STATUS;
}

enum STATUS{
  "Not-initialized"= "Not Initialized",
  "Completed"="Completed",
  "In-Progress"="In Progress"
}
