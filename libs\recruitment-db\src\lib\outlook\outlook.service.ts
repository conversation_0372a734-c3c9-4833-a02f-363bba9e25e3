import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotFoundException,
  PayloadTooLargeException,
  Query,
  UnauthorizedException,
} from "@nestjs/common";
import { randomBytes, scrypt, createCipheriv, createDecipheriv } from "crypto";
import { promisify } from "util";
import mime = require("mime");
import { IntegrationAuthService } from "../integrationAuth/integrationAuth.service";
import { MailFilterDto } from "./dto/mailFilter.dto";
import { SendMailDto } from "./dto/sendMail.dto";
import { FileService } from "apps/pre-recruitment-api/src/file/file.service";
import { Client } from "@microsoft/microsoft-graph-client";
import fetch from "cross-fetch";

require("isomorphic-fetch");

@Injectable()
export class OutlookService {
  private clientId = process.env.OUTLOOK_CLIENT_ID;
  private clientSecret = process.env.OUTLOOK_CLIENT_SECRET;
  private authorizationEndpoint =
    "https://login.microsoftonline.com/common/oauth2/v2.0/authorize";
  private tokenEndpoint =
    "https://login.microsoftonline.com/common/oauth2/v2.0/token";
    // private scope = 'Mail.Read Mail.Send Mail.ReadWrite Mail.ReadBasic';
    private scope = 'offline_access Mail.Read Mail.Send Mail.ReadWrite Mail.ReadBasic';
    private redirectUri = `${process.env.MICROSERVICES_PRA_URL}/api/integrations/oauth2-callback`;

  constructor(
    private integrationModelService: IntegrationAuthService,
    private fileService: FileService
  ) {}

  async getTokens2(id) {
    
    const tokens = {
      accessToken: "",
      refreshToken: "",
    };
    const data = await this.integrationModelService.findByProvider(id,"outlook");
    const refreshToken=data.refreshToken
    
    if (Number(data.expiryDate) < new Date().getTime() ) {
    await fetch(this.tokenEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Origin: `${process.env.MICROSERVICES_PRA_URL}`,
      },
      body: new URLSearchParams({
        scope: this.scope,
        refresh_token: refreshToken,
        grant_type: "refresh_token",
        client_id: this.clientId,
        response_type: "code",
        redirect_uri: this.redirectUri,
        response_mode: "query",
      }),
    })
      .then(function (res) {
        return res.json();
      })
      .then(async(body: any)=> {
        if (!body.access_token || !body.refresh_token) {
          throw body;
        }
        tokens.accessToken = body.access_token;
        tokens.refreshToken = body.refresh_token;
        await this.integrationModelService.update(id, {
          userId: id,
          accessToken: body.access_token,
          refreshToken: body.refresh_token,
          expiryDate:new Date().getTime() + body.expires_in * 1000,
          scope: this.scope,
        },"outlook");
      });
    }else{
      await fetch(this.tokenEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
          Origin:process.env.MICROSERVICES_PRA_URL,
        },
        body: new URLSearchParams({
          scope: this.scope,
          refresh_token: refreshToken,
          grant_type: "refresh_token",
          client_id: this.clientId,
          response_type: "code",
          redirect_uri: this.redirectUri,
          response_mode: "query",
        }),
      }) .then(function (res) {
        return res.json();
      })
      .then(async(body: any)=> {
        if (!body.access_token || !body.refresh_token) {
          throw body;
        }
        tokens.accessToken = body.access_token;
        tokens.refreshToken = body.refresh_token;  
      })
    }
    return tokens;
  }

  async getClient2(id) {
    const tokens = await this.getTokens2(id);
    return {
      client: await Client.init({
        authProvider: async (callback) => {
          callback(null, tokens.accessToken);
        },
      }),
    };
  }


  async getTokens(refreshToken) {
    const tokens = {
      accessToken: "",
      refreshToken: "",
    };
    await fetch(this.tokenEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Origin: process.env.MICROSERVICES_PRA_URL,
      },
      body: new URLSearchParams({
        client_id: this.clientId,
        redirect_uri: this.redirectUri,
        scope: this.scope,
        refresh_token: refreshToken.refreshToken,
        grant_type: "refresh_token",
      }),
    })
      .then(function (res) {
        return res.json();
      })
      .then(async (body: any) => {
        if (!body.refresh_token) {
          throw body;
        }
      })
      .then(function (res) {
        return res;
      })
      .then(function (body: any) {
        if (!body.access_token || !body.refresh_token) {
          throw body;
        }
        tokens.accessToken = body.access_token;
        tokens.refreshToken = body.refresh_token;
      });

    return tokens;
  }

  async listMessages(data:MailFilterDto,id): Promise<any> {
    const client  = await this.getClient2(id).catch((error) => {
      throw new UnauthorizedException("Invalid grant");
    });  
    let messagesEndpoint;
    switch (data.source) {
      case 'inbox':
        messagesEndpoint = '/me/mailFolders/inbox/messages';
        break;
      case 'sent':
        messagesEndpoint = '/me/mailFolders/sentitems/messages';
        break;
      case 'draft':
        messagesEndpoint = '/me/mailFolders/drafts/messages';
        break;
      default:
        messagesEndpoint = '/me/mailFolders/inbox/messages';   
       }

    try {
      let array=[]
      let res
      if (data.search) {
        res = await client.client
        .api(`${messagesEndpoint}`)
        .search(data.search)
        .get();
      } else {
        res = await client.client
        .api(messagesEndpoint)
        .get();
      }

      if (res && res.value && res.value.length > 0) {
        res.value.forEach((message) => {
          const obj = {
            id: message.id,
            from: message?.toRecipients[0]?.emailAddress?.address || "",
            subject: message.subject || "",
            isRead: message.isRead ,
            date: message.receivedDateTime,
          };
          array.push(obj);
        });
        if (data.sortBy === "subject") {
          const sortType = data.sortType === "asc" ? 1 : -1;
          array.sort((a, b) => sortType * a.subject.localeCompare(b.subject));
        } else if (data.sortBy === "date") {
          const sortType = data.sortType === "asc" ? 1 : -1;
          array.sort(
            (a, b) => sortType * (a.date.getTime() - b.date.getTime())
          );
        }
      }

      let limit;
      let offset;
      let paginatedArray = array;

      if (data.limit || data.offset) {
        limit = Number(data.limit) || 10;
        offset = Number(data.offset) || 0;
        paginatedArray = array.slice(offset, offset + limit);
      }

      return {
        data: paginatedArray,
        totalCount: array.length,
        limit: limit,
        offset: offset,
      };
    } catch (error) {
      Logger.error("Error fetching emails:", error);
      throw error;
    }
  }

  async sendEmail(data, files: any, id: string) {
    try {
      const {client}  = await this.getClient2(id).catch((error) => {
        throw new UnauthorizedException("Invalid grant");
      });  
      const outlook=client
      const attachments = [];
  
      if (files && files.length > 0) {
        for (const uploadedFile of files) {
          const attachment = {
            "@odata.type": "#microsoft.graph.fileAttachment",
            "name": uploadedFile.originalname,
            "contentBytes": uploadedFile.buffer.toString('base64'),
          };
          attachments.push(attachment);
        }
      }
  
      let s3Files;
      if (typeof data.s3File === "string") {
        s3Files = JSON.parse(data.s3File);
      } else {
        s3Files = data.s3File;
      }
      if (s3Files && s3Files.length > 0) {
        for (let s3File of s3Files) {
          const fileBuffer = await this.fileService.downloadFileFromS3(
            s3File.key
          );
          const attachment = {
            "@odata.type": "#microsoft.graph.fileAttachment",
            "name": s3File.fileName,
            "contentBytes": fileBuffer.toString("base64"),
          };
          attachments.push(attachment);
        }
      }
  
      const message = {
        toRecipients: [{
          emailAddress: {
            address: data.to
          }
        }],
        subject: data.subject || '',
        body: {
          contentType: "Text",
          content: data.body || ''
        },
        attachments: attachments
      };
  
      let res;
      if (data.action === 'send') {
        res = await outlook.api('/me/sendMail').post({
          message: message,
        });
      } else {
        res = await outlook.api('/me/messages').post(message);
        return res;
      }
  
      return res;
  
  
    } catch (error) {
      Logger.error("Error while sending email:", error);
      if (error.statusCode === 401) {
        throw new UnauthorizedException(
          "Unauthorized: Please ensure you have valid credentials."
        );
      } else if (error.statusCode === 403) {
        throw new ForbiddenException(
          "Forbidden: The app is not authorized to send emails on behalf of the user."
        );
      } else if (error.statusCode === 413) {
        throw new PayloadTooLargeException(
          "Payload too large: The email or attachments exceed the size limit."
        );
      } else if (error.statusCode === 429) {
        throw new BadRequestException(
          "Rate limit exceeded: Too many requests to Graph API. Please try again later."
        );
      } else if (error.statusCode === 400) {
        throw new BadRequestException(error.message);
      } else if (error.code === "UnableToDeserializePostBody") {
        throw new BadRequestException(
          "Unable to deserialize the request body. Please check your email and attachment data."
        );
      }
    }
  }

  async getMailById(mailId: string, id: string): Promise<any> {
    try {
      const {client}  = await this.getClient2(id).catch((error) => {
        throw new UnauthorizedException("Invalid grant");
      });  
      const outlook=client
  
      const response = await outlook.api(`/me/messages/${mailId}`).get();
      const obj = {
        from: response.from?.emailAddress.address || "",
        subject: response.subject || "",
        body: response.body.content || "",
        date: (response.sentDateTime) || "",
        to:response.toRecipients[0].emailAddress?.name || ""
      };
      
      await outlook.api(`/me/messages/${mailId}`).update({isRead: true})
      return { data: obj };
    } catch (error) {
      if(error.statusCode === 401){
        throw new UnauthorizedException("Bad Request");
      } else if (error.statusCode === 400) {
        throw new BadRequestException(error.message);
      } else {
        throw new NotFoundException(error.message);
      }
    }
  }

  async markEmailAsRead(message, id) {
    try {
      const { client } = await this.getClient2(id).catch((error) => {
        throw new UnauthorizedException("Invalid grant");
      });      const outlook = client;

      const promises = [];
      let request;
      for (let i = 0; i < message.messageIds.length; i++) {
        const messageId = message.messageIds[i];
        promises.push(outlook.api(`/me/messages/${messageId}`).get());
      }

      const results = await Promise.all(promises);

      for (let i = 0; i < results.length; i++) {
        const messageResult = results[i];
        const messageId = message.messageIds[i];

        if (message.flag === false) {
          request = {
            isRead: false,
          };
        } else {
          request = {
            isRead: true,
          };
        }
      await outlook.api(`/me/messages/${messageId}`).update(request);
      }
      return {
        message: "Data sent successfully",
      };
    } catch (error) {
      if (error.statusCode === 404) {
        throw new NotFoundException(error.message);
      } else {
        throw new UnauthorizedException("Bad Request");
      }
    }
  }
  
  async deleteEmail(data, id: string) {
    try {
      const {client}  = await this.getClient2(id).catch((error) => {
        throw new UnauthorizedException("Invalid grant");
      });  
      const outlook=client     

      const promises = [];

      for (let i = 0; i < data.id.length; i++) {
        const messageId = data.id[i];
        promises.push(outlook.api(`/me/messages/${messageId}`).get());
      }

      const results = await Promise.all(promises);

      for (let i = 0; i < results.length; i++) {
        const messageResult = results[i];
        const messageId = data.id[i];

        await outlook.api(`/me/messages/${messageId}`).delete();
      }

      return {
        message: "Email deleted successfully",
      };
    } catch (error) {
      if (error.statusCode === 404) {
        throw new NotFoundException(error.message);
      } else {
        throw new UnauthorizedException("Bad Request");
      }
    }
  }

  async updateDraftEmail(
    draftId: string,
    data: SendMailDto,
    files: any,
    id: string
  ): Promise<any> {
    try {
      const {client}  = await this.getClient2(id).catch((error) => {
        throw new UnauthorizedException("Invalid grant");
      });  
      const outlook = client;

      const profile = await outlook.api("/me").get();
      const userEmail = profile.mail;

      const messageParts = [];
      messageParts.push(
        `From: ${userEmail}`,
        `To: ${data.to}`,
        `Subject: ${data.subject || ""}`,
        'Content-Type: multipart/mixed; boundary="boundary"',
        ""
      );

      messageParts.push(
        "--boundary",
        "Content-Type: text/plain; charset=UTF-8",
        "",
        data.body || "",
        ""
      );

      if (files && files.length > 0) {
        for (const uploadedFile of files) {
          messageParts.push(
            "--boundary",
            `Content-Type: ${uploadedFile.mimetype}`,
            "Content-Transfer-Encoding: base64",
            `Content-Disposition: attachment; filename="${uploadedFile.originalname}"`,
            "",
            uploadedFile.buffer.toString("base64"),
            ""
          );
        }
      }

      const encodedEmail = Buffer.from(messageParts.join("\r\n")).toString(
        "base64"
      );

      let res;
      if (data.action === "send") {
        res = await outlook.api("/me/sendMail").post({
          message: {
            raw: encodedEmail,
          },
        });
      } else {
        res = await outlook.api(`/me/messages/${draftId}`).patch({
          body: {
            contentType: "Text",
            content: data.body || "",
          },
          subject: data.subject || "",
          toRecipients: [
            {
              emailAddress: {
                address: data.to,
              },
            },
          ],
          attachments: files.map((uploadedFile) => ({
            "@odata.type": "#microsoft.graph.fileAttachment",
            name: uploadedFile.originalname,
            contentType: uploadedFile.mimetype,
            contentBytes: uploadedFile.buffer.toString("base64"),
          })),
        });
      }

      await outlook.api(`/me/messages/${draftId}`).delete();

      return res.data;
    } catch (error) {
      console.error("Error while sending email:", error);
      if (error.statusCode === 401) {
        throw new UnauthorizedException(
          "Unauthorized: Please ensure you have valid credentials."
        );
      } else if (error.statusCode === 403) {
        throw new ForbiddenException(
          "Forbidden: The app is not authorized to send emails on behalf of the user."
        );
      } else if (error.statusCode === 413) {
        throw new PayloadTooLargeException(
          "Payload too large: The email or attachments exceed the size limit."
        );
      } else if (error.statusCode === 429) {
        throw new BadRequestException(
          "Rate limit exceeded: Too many requests to Graph API. Please try again later."
        );
      } else if (error.statusCode === 400) {
        throw new BadRequestException(error.message);
      } else {
        throw new NotFoundException(error.message);
      }
    }
  }

  async encrypt(arr) {
    const iv = randomBytes(16);
    const key = (await promisify(scrypt)(process.env.CRYPTO_PASS, 'salt', 32)) as Buffer;
    const cipher = createCipheriv('aes-256-ctr', key, iv);
    const encrypted = Buffer.concat([
      cipher.update(arr.join(',')),
      cipher.final(),
    ]);

    return JSON.stringify({
      i: iv.toString('hex'),
      t: encrypted.toString('hex'),
    });
  }

  async decrypt(json) {
    const hash = JSON.parse(json);
    const key = (await promisify(scrypt)(process.env.CRYPTO_PASS, 'salt', 32)) as Buffer;
    const decipher = createDecipheriv('aes-256-ctr', key, Buffer.from(hash.i, 'hex'));
    const decrypted = Buffer.concat([decipher.update(Buffer.from(hash.t, 'hex')), decipher.final()]);

    return decrypted.toString().split(',');
  }

}

enum period {
  "1 day" = "1d",
  "3 days" = "3d",
  "1 week" = "7d",
  "2 weeks" = "14d",
  "1 month" = "1m",
  "2 months" = "2m",
  "6 months" = "6m",
  "1 year" = "1y",
}