import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class MembersAnalyticsQueryDto {
  @ApiPropertyOptional({ example: "", description: "search value to be find in the members data" })
  @IsOptional()
  search?: string;

  @ApiProperty({ example: "recruiterId", description: "sort field" })
  sortBy: string;

  @ApiProperty({ example: "asc", description: "sorting order" })
  sortType: string;

  @ApiProperty({ example: 10, description: "Limit" })
  limit: number;

  @ApiProperty({ example: 0, description: "Last Name" })
  offset: number;

  @ApiPropertyOptional({ example: "", description: "department to be find in the members data" })
  @IsOptional()
  department?: string;

  @ApiPropertyOptional({ example: "1", description: "job location to be find in the members data" })
  @IsOptional()
  jobLocation?: string;

  @ApiPropertyOptional({ example: "", description: "Date of Birth From" })
  @IsOptional()
  dateOfBirthFrom?: Date;

  @ApiPropertyOptional({ example: "", description: "Date of Birth To" })
  @IsOptional()
  dateOfBirthTo?: Date;


  @ApiPropertyOptional({ example: "", description: "Date of Joining From" })
  @IsOptional()
  dateOfJoiningFrom?: Date;

  @ApiPropertyOptional({ example: "", description: "Date of Joining To" })
  @IsOptional()
  dateOfJoiningTo?: Date;


  @ApiPropertyOptional({ example: "All time", description: "Date of Joining duration" })
  @IsOptional()
  dateOfJoiningDuration?: string;

  @ApiPropertyOptional({ example: '', description: "Array of roles id" })
  @IsOptional()
  roles?: string;

  @ApiPropertyOptional({ example: "", description: "List of education" })
  @IsOptional()
  education?: string;
}