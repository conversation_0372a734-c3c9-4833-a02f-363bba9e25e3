{"root": "apps/assessment-api", "sourceRoot": "apps/assessment-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/assessment-api", "main": "apps/assessment-api/src/main.ts", "tsConfig": "apps/assessment-api/tsconfig.app.json", "assets": ["apps/assessment-api/src/assets"], "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/assessment-api/src/environments/environment.ts", "with": "apps/assessment-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "assessment-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/assessment-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/assessment-api"], "options": {"jestConfig": "apps/assessment-api/jest.config.js", "passWithNoTests": true}}}, "tags": []}