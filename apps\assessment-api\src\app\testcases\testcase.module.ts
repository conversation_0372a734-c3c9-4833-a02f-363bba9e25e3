import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { TestCaseController } from './testcase.controller';
import { TestCaseGeneratorService } from './testcase-generator.service';
import { DbModule } from '@microservices/db';

@Module({
    imports: [
        ConfigModule.forRoot(),
        HttpModule,
        DbModule
    ],
    controllers: [TestCaseController],
    providers: [TestCaseGeneratorService],
    exports: [TestCaseGeneratorService]
})
export class TestCaseModule { } 