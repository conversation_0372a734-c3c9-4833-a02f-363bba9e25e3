import { ApiProperty } from "@nestjs/swagger";
import { CompanyDto } from "./company.dto";

export class RegisterCompanyDto extends CompanyDto {
  @ApiProperty({ example: 'company-name', description: 'uRecruits TenantId' })
  tenantId: string;

  @ApiProperty({ example: '123456qQ@', description: 'Password' })
  password: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Contact Person Email',
  })
  email: string;

  @ApiProperty({ example: "+*********", description: "Phone" })
  phone: string;

  @ApiProperty({ example: "+*********", description: "company Phone" })
  companyPhone: string;

  @ApiProperty({ example: "Rob", description: "First Name" })
  firstname: string;

  @ApiProperty({ example: "Stark", description: "Last Name" })
  lastname: string;
}

export class GetAllCompaniesDto {
  @ApiProperty({ example: 10, description: "Limit" })
  limit: number;

  @ApiProperty({ example: 0, description: "Offset" })
  offset: number;
}