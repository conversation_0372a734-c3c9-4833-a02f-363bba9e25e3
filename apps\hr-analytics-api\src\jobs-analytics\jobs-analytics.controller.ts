import { Controller, Get, Post, Query, UseGuards } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import { JobsAnalyticsService } from "@microservices/hr-analytics";
import { JobsAnalyticsDto, JobsAreaAnalyticsQueryDto, JobsPieAnalyticsQueryDto } from "./dto/jobs-analytics.dto";

@ApiTags("Jobs Analytics")
@ApiBearerAuth("access-token")
@Controller("analytics/jobs")
export class JobsAnalyticsController {
  constructor(private readonly jobsAnalyticsService: JobsAnalyticsService) { }

  @ApiOperation({ summary: "Company Jobs Analytics" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("job-post:view")
  async getJobsAnalytics(
    @Query() queryParams: JobsAnalyticsDto,
    @AuthUser() user: any
  ) {
    return await this.jobsAnalyticsService.getJobsAnalytics(
      queryParams,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Company Jobs Pie Graph" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("pie")
  @Permissions("job-post:view")
  async getJobsAnalyticsPie(
    @Query() { duration }: JobsPieAnalyticsQueryDto,
    @AuthUser() user: any
  ) {
    return await this.jobsAnalyticsService.getJobsAnalyticsPie(
      duration,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Company Jobs Area Graph" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/area")
  @Permissions("job-post:view")
  async getJobsAnalyticsArea(
    @Query() { duration, Jobstatus }: JobsAreaAnalyticsQueryDto,
    @AuthUser() user: any
  ) {
    return await this.jobsAnalyticsService.getJobsAnalyticsArea(
      duration,
      Jobstatus,
      user["https://urecruits.com/companyId"]
    );
  }
}
