import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { TestCase } from './testcase.model';

@Injectable()
export class TestCaseService {
  constructor(
    @InjectModel(TestCase)
    private testCaseModel: typeof TestCase
  ) { }

  findOneById(id: number): Promise<TestCase> {
    return this.testCaseModel.findByPk(id);
  }

  findAllByAssessment(id: number): Promise<TestCase[]> {
    return this.testCaseModel.findAll({
      where: {
        id,
      },
    });
  }

  findAllByQuestionId(questionId: number): Promise<TestCase[]> {
    return this.testCaseModel.findAll({
      where: {
        questionId,
      },
    });
  }

  find(dto: number): Promise<TestCase> {
    return this.testCaseModel.findByPk(dto);
  }

  findAll(): Promise<TestCase[]> {
    return this.testCaseModel.findAll();
  }

  create(dto: TestCase): Promise<TestCase> {
    return this.testCaseModel.create(dto);
  }

  async update(id: number, dto: TestCase) {
    const testCase = await this.testCaseModel.findByPk(id);
    if (testCase) {
      return this.testCaseModel.update({ ...dto }, { where: { id } });
    } else {
      delete dto.id;
      return this.testCaseModel.create(dto);
    }
  }

  async remove(id: number) {
    return this.testCaseModel.destroy({ where: { id } });
  }

  // async search(query: string) {
  //   return this.testCaseModel.findAll({
  //     where: { name: { [Op.like]: `%${  query  }%` },
  //     },
  //   });
  // }
}
