import {
  Bel<PERSON>sTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Candidate } from '../candidates/candidates.model'

interface AwardAttrs {
  name?: string;
  companyName?: string;
  date?: string;
  notes?: string;
  fileName?: string;
  fileKey?: string;
  candidateId: number;
}

@Table({ tableName: 'awards' })
export class Award extends Model<Award, AwardAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: 'Leadership',
    description: 'Award Name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  name: string;

  @ApiProperty({
    example: 'Adobe',
    description: 'Company Name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  companyName: string;

  @ApiProperty({
    example: '2021-12-28T22:00:00.000Z',
    description: 'Award Date',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  date: string;

  @ApiProperty({
    example: 'Notes',
    description: 'Notes',
  })
  @Column({ type: DataType.TEXT, allowNull: true })
  notes: string;

  @ApiProperty({
    example: 'file.pdf',
    description: 'File name',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  fileName: string;

  @ApiProperty({
    example: '213122 - file.pdf',
    description: 'File key',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  fileKey: string;

  @ApiProperty({ example: '1', description: 'Candidate ID' })
  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @BelongsTo(() => Candidate)
  candidate: Candidate;
}
