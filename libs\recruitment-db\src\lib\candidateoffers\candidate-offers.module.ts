import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { OfferModel } from '../offers/offer.model';
import { CandidateOffersService } from './candidate-offers.service';
import { Subscribe } from "../subscribe/subscribe.model";
import { IntegrationsModule } from "../integrations/integrations.module";
import { OfferModule } from '../offers/offer.module';

@Module({
  imports: [
    SequelizeModule.forFeature([OfferModel]),
    SequelizeModule.forFeature([Subscribe]),
    IntegrationsModule,
    OfferModule
  ],
  providers: [CandidateOffersService],
  exports: [CandidateOffersService],
})
export class CandidateOffersModule { }
