import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class ExperienceDto {
  @ApiPropertyOptional({ example: "urecruits", description: "Company Name" })
  @IsOptional()
  readonly companyName?: string;

  @ApiPropertyOptional({ example: "Web Developer", description: "Position" })
  @IsOptional()
  readonly position?: string;

  @ApiPropertyOptional({ example: "New York", description: "Location" })
  @IsOptional()
  readonly location?: string;

  @ApiPropertyOptional({ example: "January 12, 2022", description: "Service Periods start" })
  @IsOptional()
  readonly start?: string;

  @ApiPropertyOptional({ example: "January 12, 2022", description: "Service Periods end" })
  @IsOptional()
  readonly end?: string;

  @ApiPropertyOptional({ example: [], description: "Skills" })
  @IsOptional()
  skills?: any;

  @ApiPropertyOptional({ example: false, description: "present job?" })
  @IsOptional()
  present?: boolean;

  @ApiPropertyOptional({ example: "Description", description: "Job Description" })
  @IsOptional()
  jobDescription?: string;
}
