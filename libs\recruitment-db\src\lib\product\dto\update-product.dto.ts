import { ApiProperty, PartialType } from "@nestjs/swagger";

export class UpdateProductDto{
  @ApiProperty({example:'prod_xxx',description:"Stripe product ID"})
  productId:string

  @ApiProperty({example:'Tier 1',description:"Name of the Stripe Product"})
  name?:string

  @ApiProperty({example:1,description:"Plan Id of product"})
  planId?:number

  @ApiProperty({example:'main',description:"Type of the product"})
  productType?:string

  @ApiProperty({example:true,description:"Active status of the product"})
  active?:boolean
}

export class GetProductsDto{
  @ApiProperty({example:'prod_xxx',description:"Stripe product ID"})
  productId?:string

  @ApiProperty({example:'Tier 1',description:"Name of the Stripe Product"})
  name?:string

  @ApiProperty({example:1,description:"Plan Id of product"})
  planId?:number

  @ApiProperty({example:'main',description:"Type of the product"})
  productType?:string

  @ApiProperty({example:true,description:"Active status of the product"})
  active?:boolean  
}