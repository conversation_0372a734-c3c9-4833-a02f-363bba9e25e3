Parameters:
  App:
    Type: String
    Description: Your application's name.
  Env:
    Type: String
    Description: The environment name your service, job, or workflow is being deployed to.
  Name:
    Type: String
    Description: The name of the service, job, or workflow being deployed.
Resources:
  uploadsprivateBucket:
    Metadata:
      'aws:copilot:description': 'An Amazon S3 bucket to store and retrieve objects for uploads-private'
    Type: AWS::S3::Bucket
    Properties:
      AccessControl: Private
      BucketName: !Sub '${App}-${Env}-${Name}-uploads-private'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
        - ServerSideEncryptionByDefault:
            SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true

  uploadsprivateBucketPolicy:
    Metadata:
      'aws:copilot:description': 'A bucket policy to deny unencrypted access to the bucket and its contents'
    Type: AWS::S3::BucketPolicy
    DeletionPolicy: Retain
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ForceHTTPS
            Effect: Deny
            Principal: '*'
            Action: 's3:*'
            Resource:
              - !Sub ${ uploadsprivateBucket.Arn}/*
              - !Sub ${ uploadsprivateBucket.Arn}
            Condition:
              Bool:
                "aws:SecureTransport": false
      Bucket: !Ref uploadsprivateBucket

  uploadsprivateAccessPolicy:
    Metadata:
      'aws:copilot:description': 'An IAM ManagedPolicy for your service to access the uploads-private bucket'
    Type: AWS::IAM::ManagedPolicy
    Properties:
      Description: !Sub
        - Grants CRUD access to the S3 bucket ${Bucket}
        - { Bucket: !Ref uploadsprivateBucket }
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: S3ObjectActions
            Effect: Allow
            Action:
              - s3:GetObject
              - s3:PutObject
              - s3:PutObjectACL
              - s3:PutObjectTagging
              - s3:DeleteObject
              - s3:RestoreObject
            Resource: !Sub ${ uploadsprivateBucket.Arn}/*
          - Sid: S3ListAction
            Effect: Allow
            Action: s3:ListBucket
            Resource: !Sub ${ uploadsprivateBucket.Arn}

Outputs:
  uploadsprivateName:
    Description: "The name of a user-defined bucket."
    Value: !Ref uploadsprivateBucket
  uploadsprivateAccessPolicy:
    Description: "The IAM::ManagedPolicy to attach to the task role"
    Value: !Ref uploadsprivateAccessPolicy
