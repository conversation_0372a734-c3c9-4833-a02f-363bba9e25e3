import { ApiProperty } from '@nestjs/swagger';

export class CreateSubscriptionWithoutCardDto {

  @ApiProperty({
    example: '3',
    description:
      'Subscription Package ID. Do not required when Subscription ID is provided.',
  })
  readonly packageId: number;

  @ApiProperty({
    example: '5',
    description:
      'Subscription Plan ID. Do not required when Subscription ID is provided.',
  })
  readonly planId: number;

  @ApiProperty({
    example: 'sub_xxx',
    description:
      "Subscription ID. We'll try to update this subscription instead of create new one.",
  })
  readonly duration: string;

  @ApiProperty({
    example: 'false',
    description:
      "Do not provide free trial if freeTrial is false.",
  })
  readonly freeTrial?: boolean;
}
