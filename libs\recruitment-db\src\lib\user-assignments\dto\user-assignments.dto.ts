import { ApiProperty } from '@nestjs/swagger';

export class CreateUserAssignmentDto {
    @ApiProperty({
        example: '1',
        description:
            'User id',
    })
    userId: number;

    @ApiProperty({
        example: '1',
        description:
            'Assignment id',
    })
    assignmentId: number;

    @ApiProperty({
        example: '1',
        description:
            'Name of the assignment',
    })
    assignmentName: string;

    @ApiProperty({
        example: false,
        description:"Status of the user's active assignment",
    })
    hasActiveAssignment: boolean;
}

export class ChangeUserAssignmentStatusDto{
    @ApiProperty({
        example: '1',
        description:
            'User id',
    })
    userId: number;

    @ApiProperty({
        example: false,
        description: "Status of the user's active assignment",
    })
    hasActiveAssignment: boolean;
}
