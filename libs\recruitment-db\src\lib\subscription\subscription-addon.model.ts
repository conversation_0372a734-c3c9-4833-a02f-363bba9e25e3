import {
  Column,
  DataType,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../companies/companies.model';

interface SubscriptionAddonAttr {
  subId: string;
  companyId: number;
  priceId:string;
  subItemId:string;
  quantity:number;
  units:number;
  addonName:string;
}

@Table({
  tableName: 'subscription-addon',
  createdAt: true,
  updatedAt: true,
  timestamps: true
 })
export class SubscriptionAddon extends Model<SubscriptionAddon, SubscriptionAddonAttr> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'sub_xxx', description: 'stripe Subscription ID' })
  @Column({ type: DataType.STRING, allowNull: false })
  subId: string;

  @ApiProperty({ example: '1', description: 'Company ID' })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({ example: 'si_xxx', description: 'Subscription Item Id of a purchased subscription' })
  @Column({ type: DataType.STRING, allowNull: false })
  subItemId: string;

  @ApiProperty({ example: 'price_xxx', description: 'Price Id of Subscription item' })
  @Column({ type: DataType.STRING, allowNull: false })
  priceId: string;

  @ApiProperty({ example: 'job', description: 'Name of subscription addon' })
  @Column({ type: DataType.STRING, allowNull: false })
  addonName: string;

  @ApiProperty({ example: '1', description: 'Quantity of subscription item' })
  @Column({ type: DataType.INTEGER, allowNull: false })
  quantity: number;

  @ApiProperty({ example: '150', description: 'Units of subscription item' })
  @Column({ type: DataType.INTEGER, allowNull: false })
  units: number;


  @BelongsTo(() => Company)
  company: Company;
}
