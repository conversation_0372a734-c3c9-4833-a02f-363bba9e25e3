import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { roundType } from "apps/temporal/src/app/workflow/enum/round.enum";
import { IsNotEmpty, IsOptional } from "class-validator";

export class AddEventDto{
    @ApiProperty({ example: 87, description: "job Id" })
    @IsNotEmpty()
    jobId: number;
  
    @ApiProperty({ example: 87, description: "user Id" })
    @IsNotEmpty()
    userId: number;

    @ApiProperty({ example: 87, description: "event Id" })
    @IsNotEmpty()
    eventId: number;

    @ApiProperty({ example: 'HR Telephone Interview', description: "round name" })
    @IsNotEmpty()
    roundName: string;

    @ApiProperty({ example: [], description: "List of Interviewers Details" })
    @IsOptional()
    interviewers: Array<any>;

    @ApiProperty({ example: "First Interview", description: "event Title" })
    @IsNotEmpty()
    eventTitle: Array<any>;

    @ApiProperty({ example: 'sdEt-3Ghu8', description: "RoomId of meeting" })
    @IsOptional()
    roomId: string;

    @ApiProperty({ example: '', description: "Start time of event" })
    @IsOptional()
    eventStartTime: any;

    @ApiProperty({ example: '', description: "End time of event" })
    @IsOptional()
    eventEndTime: any;


}

export class AddScoreToRoundDto{
    @ApiProperty({ example: 87, description: "job Id" })
    jobId: number;

    @ApiProperty({ example: 14, description: "user Id" })
    userId: number;
    
    @ApiProperty({ example: roundType.FUNCTIONAL_DOMAIN_ASSESSMENT, description: "Name of the round which score is to be updated" })
    roundName: roundType;
    
    @ApiProperty({ example: 30, description: "Score of the round" })
    score: number;
}