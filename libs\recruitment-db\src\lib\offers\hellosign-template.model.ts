import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Company } from "../companies/companies.model";


@Table({ tableName: "hellosign-template", createdAt: true, updatedAt: true })
export class HellosignTemplate extends Model<HellosignTemplate> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "1", description: "Company  ID" })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiPropertyOptional({
    example: "5f47efec03ccc85fd8ad882b2e3c0e977aa0c246",
    description: "Hello Sign Template ID",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  templateId: string;

  @ApiPropertyOptional({
    example: "templte",
    description: "template name",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  templateName: string;

  @ApiPropertyOptional({
    example: "template descriptions",
    description: "template descriptions",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  templateDescription: string;

  @ApiPropertyOptional({ example: "", description: "Signer" })
  @Column({ type: DataType.JSONB, allowNull: false })
  signers: any;

  @ApiPropertyOptional({ example: "", description: "Template Body" })
  @Column({ type: DataType.TEXT, allowNull: false })
  templateBody: string;

  @BelongsTo(() => Company)
  company: Company;
}
