export const assessmentPieAnalyticsQueryBody = (companyId) => (
  {
    query: {
      bool: {
        must: [
          { term: { companyId: companyId } },
        ]
      }
    },
    aggs: {
      total_assessments: {
        cardinality: {
          field: "_id",
        },
      },
      assessments_types: {
        filters: {
          filters: {
            "Domain": {
              term: {
                "assessmentType.keyword": "domain"
              }
            },
            "Live Coding": {
              term: {
                "assessmentType.keyword": "live-task"
              }
            },
            "Take Home": {
              term: {
                "assessmentType.keyword": "take-home"
              }
            }
          }
        }
      }
    },
  }
);

export const assessmentsAreaAnalyticsQueryBody = (startOf, endOf, interval, companyId) => ({
  size: 0,
  query: {
    bool: {
      must: [
        { term: { companyId } },
        {
          range: {
            createdAt: {
              gte: startOf,
              lte: endOf
            }
          }
        }
      ]
    }
  },
  aggs: {
    assessments_by_type: {
      terms: {
        field: "assessmentType.keyword",
        size: 10
      },
      aggs: {
        assessments_over_time: {
          date_histogram: {
            field: "createdAt",
            interval,
            time_zone: "+00:00",
            min_doc_count: 0,
            extended_bounds: {
              min: startOf,
              max: endOf
            }
          }
        }
      }
    }
  }
});

export const assessementsAnalyticsQueryBody = (limit, offset, companyId, queryParams) => {
  const mustClauses: any[] = [
    { term: { companyId: companyId } }
  ];

  if (queryParams.search) {
    mustClauses.push({
      multi_match: {
        query: queryParams.search,
        fields: ["name", "description"],
        fuzziness: "AUTO"
      }
    });
  }

  return {
    size: limit,
    from: offset,
    query: {
      bool: {
        must: mustClauses
      }
    },
    sort: [
      (queryParams.sortBy && queryParams.sortType)
        ? { [queryParams.sortBy]: { order: queryParams.sortType } }
        : { createdAt: { order: "desc" } }
    ]
  };
};


