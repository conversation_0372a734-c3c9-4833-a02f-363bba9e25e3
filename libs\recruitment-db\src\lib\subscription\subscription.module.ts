import { Module } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { CompaniesModule } from '../companies/companies.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { Subscriptions } from './subscriptions.model';
import { ProductModule } from '../product/product.module';
import { PriceModule } from '../price/price.module';
import { SubscriptionAddon } from './subscription-addon.model';
import { Company } from '../companies/companies.model';
// import { Plans } from '../plan/plan.model';
import { PlanPackageModule } from '../plan-packages/plan-packages.module';
import { PlanModule } from '../plan/plan.module';

@Module({
  imports: [SequelizeModule.forFeature([Subscriptions,SubscriptionAddon,Company]),ProductModule,PriceModule,PlanModule,PlanPackageModule],
  providers: [SubscriptionService],
  exports: [SequelizeModule, SubscriptionService]
})
export class SubscriptionModule {}
