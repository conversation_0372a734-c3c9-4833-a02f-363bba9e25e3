import * as AWS from "aws-sdk";
import { QdrantClient } from "@qdrant/qdrant-js"
import { LangchainService } from '@microservices/integrations'

const qdrantClient = new QdrantClient({
    url: process.env.QDRANT_DATABASE_URL,
    apiKey: process.env.QDRANT_API_KEY,
});

const langchainService = new LangchainService();

AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || "us-east-1",
});

const getVectorPoints = async (str, id, payload) => {
    try {
        const vector = await langchainService.getEmbeddings(str);
        return {
            id: id,
            vector: vector,
            payload: payload
        }
    } catch (error) {
        console.log('Error getting embeddings in middleware', error)
    }
}

export const putRecordOnQdrant = async (data) => {
    for (let i of data) {
        let points = [];
        const collectionName = i.for;
        if (i.action === 'delete') {
            const data = await qdrantClient.delete(collectionName, {
                points: [i.id]
            });
            continue;
        }
        if (i.str) {
                const point = await getVectorPoints(i.str, i.id, i.payload);
                points.push(point);

            try {
                if (points.length > 0) {
                    await qdrantClient.upsert(collectionName, { wait: true, points: points });
                    console.log("points upserted for " + collectionName)
                }
            }
            catch (error) {
                console.log('Error putting record on qdrant in middleware', error)
            }
        }
    }
}