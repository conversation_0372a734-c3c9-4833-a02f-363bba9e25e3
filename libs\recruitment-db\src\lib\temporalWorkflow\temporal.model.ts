import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';

@Table({ tableName: 'temporalworkflow' })
export class TemporalWorkflow extends Model<TemporalWorkflow> {
  @ApiProperty({ example: 1, description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({ example: 1, description: 'User ID' })
  @Column({ type: DataType.INTEGER })
  userid: number;

  @ApiProperty({ example: '2', description: 'jobId' })
  @Column({ type: DataType.STRING })
  jobid: string;

  @ApiProperty({ example: 'ur - b3499d64-fd8d-4350-ba7b-7bb6578f7859 - 6 - 67 - 16', description: 'Current workflow ID' })
  @Column({ type: DataType.STRING })
  workflowid: string;
}
