import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { TwilioConversationService  } from './twilio.conversation.service';
import { TwilioVideoService } from './twilio.video.service';
import { CalendarModule } from '@microservices/db';
import { TemporalWorkflowModule, TemporalWorkflowService } from '@microservices/recruitment-db';

@Module({
  imports:[CalendarModule,HttpModule,TemporalWorkflowModule],
  providers: [TwilioConversationService, TwilioVideoService,TemporalWorkflowService],
  exports: [TwilioConversationService, TwilioVideoService],
})
export class TwilioModule {}
