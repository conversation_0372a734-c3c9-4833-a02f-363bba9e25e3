import { Controller, Get,Post,Body, HttpException, HttpStatus, UseGuards, Param} from '@nestjs/common';
import { AssessmentDatabase} from "../../../../../libs/db/src/lib/assessment-database/assessmentDatabase.model"
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PermissionsGuard, Permissions, AuthUser } from '@microservices/auth';
import { AssessmentDatabaseService } from '@microservices/db';
import { AuthGuard } from '@nestjs/passport';
@Controller('assesment-database')
@ApiBearerAuth("access-token")
export class AssessmentDatabaseController {
  constructor(private readonly assessmentService: AssessmentDatabaseService) {}


  @ApiOperation({ summary: 'Get Assessment-database' })
  @ApiResponse({ status: 200})
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("getById/:id")
  async getById(@Param("id") id:any) {
    return this.assessmentService.getById(id);
  }

  @ApiOperation({ summary: 'Get Assessment-database' })
  @ApiResponse({ status: 200})
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  async get(@AuthUser() user) {
    return this.assessmentService.findAssessmentDatabase(user["https://urecruits.com/tenantId"]);
  }

  @ApiOperation({ summary: 'create the assessment-database ' })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post()
  async create(@Body() databaseDto: AssessmentDatabase,@AuthUser() user) {
      try {
        if (!databaseDto || !databaseDto.script || !databaseDto.title) {
          throw new HttpException('Invalid request body', HttpStatus.BAD_REQUEST);
        }
        return await this.assessmentService.createAssessmentDatabase(databaseDto,user["https://urecruits.com/tenantId"]);
      } catch (error) {
        if (error.name === 'SequelizeValidationError' && error.errors[0]?.path === 'script') {
          throw new HttpException('Assessment database script is required', HttpStatus.BAD_REQUEST);
        }
        throw new HttpException('Error creating assessment database', HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }  }
 

