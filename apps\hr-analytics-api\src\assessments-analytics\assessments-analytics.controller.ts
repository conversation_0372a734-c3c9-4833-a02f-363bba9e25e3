import { Controller, Get, Post, Query, UseGuards } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import { AssessmentsAnalyticsService } from "@microservices/hr-analytics";
import { AssessmentsAnalyticsQueryDto } from "./dto/assessments-analytics.dto";

@ApiTags("Assessments Analytics")
@ApiBearerAuth("access-token")
@Controller("analytics/assessments")
export class AssessmentsAnalyticsController {
  constructor(private readonly assessmentsAnalyticsService: AssessmentsAnalyticsService) { }

  @ApiOperation({ summary: "Assessments Area Graph" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("area")
  @Permissions("assessment:view")
  async getAssessmentsAnalyticsArea(
    @Query() { duration }: AssessmentsAnalyticsQueryDto,
    @AuthUser() user: any
  ) {
    return await this.assessmentsAnalyticsService.getAssessmentsArea(
      duration,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Company's Assessments Analytics" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("assessment:view")
  async getUsersAnalytics(
    @Query() queryParams: AssessmentsAnalyticsQueryDto,
    @AuthUser() user: any
  ) {
    return await this.assessmentsAnalyticsService.getMembersAnalytics(
      queryParams,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Assessment's Pie Graph" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("pie")
  @Permissions("assessment:view")
  async getAssessmentsAnalyticsPie(
    @AuthUser() user: any
  ) {
    return await this.assessmentsAnalyticsService.getAssessmentsPieAnalytics(
      user["https://urecruits.com/companyId"]
    );
  }
}