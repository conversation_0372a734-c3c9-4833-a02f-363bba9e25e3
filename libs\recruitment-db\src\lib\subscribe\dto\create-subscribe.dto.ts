import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { SubscribeDto } from "./subscribe.dto";

export class CreateSubscribeDto extends SubscribeDto {
  @ApiProperty({ example: "1", description: "Job ID" })
  jobId: number;
}

export class StartWorkflowDto{
  @ApiProperty({ example: "1", description: "Job ID" })
  jobId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  userId: number;

  @ApiPropertyOptional({ example: "64413de7-439f-41a8-bb4c-1a72b000705e", description: "JobTarget GUID for tracking applicant status" })
  @IsOptional()
  JT_GUID?: string;
}
