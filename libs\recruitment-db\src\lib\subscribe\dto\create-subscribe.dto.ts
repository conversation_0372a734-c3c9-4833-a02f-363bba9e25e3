import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { SubscribeDto } from "./subscribe.dto";

export class CreateSubscribeDto extends SubscribeDto {
  @ApiProperty({ example: "1", description: "Job ID" })
  jobId: number;
}

export class StartWorkflowDto{
  @ApiProperty({ example: "1", description: "Job ID" })
  jobId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  userId: number;
}
