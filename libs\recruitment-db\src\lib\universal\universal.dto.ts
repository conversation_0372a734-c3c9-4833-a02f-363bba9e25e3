import { ApiProperty } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export type PersonType = {
  ApplicantID: string;
  FirstName: string;
  MiddleName: string;
  LastName: string;
  Email: string;
};

export class UniversalDto {
  @ApiProperty({ example: "1", description: "User Id" })
  userId: number;

  @ApiProperty({ example: "1", description: "Job Id" })
  jobId: number;

  @ApiProperty({ example: "1", description: "Company Id" })
  companyId: number;

  @ApiProperty({
    example: "Universal",
    description: "Vendor Selected for Drug Screening",
  })
  vendor: string;


  @ApiProperty({
    example: "background | drug | combo",
    description: "screening type",
  })
  screeningType: string;
}

export class UniversalIntegrationDto{
  @ApiProperty({ example: "123456", description: "Account Number" })
    accountNumber: string;
  
    @ApiProperty({ example: "talexndar", description: "User Name" })
    userName: string;

    @ApiProperty({ example: "xxxxxxxx", description: "Account Password" })
    @IsOptional()
    password?: string;
}




