import { Injectable } from '@nestjs/common';
import * as data from './data.json';
import { InjectModel } from '@nestjs/sequelize';
import { PredefinedSkills } from './predefined-skill.model';
import { Op } from "sequelize";
import { PredefinedSkillDto } from "./dto/predefined-skill.dto";

@Injectable()
export class PredefinedSkillService {
  constructor(
    @InjectModel(PredefinedSkills) private predefinedSkillsRepository: typeof PredefinedSkills,
  ) {}

  async seed() {
    await this.predefinedSkillsRepository.bulkCreate(data, {
      validate: true,
    }).then(async () => this.predefinedSkillsRepository.sequelize.query(`ALTER SEQUENCE "${this.predefinedSkillsRepository.tableName}_id_seq" RESTART WITH ${await this.predefinedSkillsRepository.count() + 1}`));
    return true;
  }

  async createSkill(dto: PredefinedSkillDto) {
    return await this.predefinedSkillsRepository.create(dto);
  }

  async findByName(find?: string) {
    let where = {}
    if (find && find.length > 0) {
      where = {
        name: {
          [Op.iLike]: `%${find}`,
        },
      }
    }
    return await this.predefinedSkillsRepository.findAll({
      where,
      limit: 40,
    });
  }
}
