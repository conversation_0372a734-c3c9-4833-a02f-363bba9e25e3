import {
  Column,
  DataType,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Company } from '../companies/companies.model';
import { Plans } from '../plan/plan.model';
import { PlanPackages } from '../plan-packages/plan-packages.model';

interface SubscriptionsAttrs {
  subId: string;
  companyId: number;
  status: string;
  startDate: Date;
  endDate: Date;
  subEndDate: Date;
  period: string;
  planId : number;
  piStatus:string;
  piId:string;
  piCs:string;
  // capabilities: string;
  // packageName: string;
  last4: string;
  type:string;
}

@Table({
  tableName: 'subscriptions',
  createdAt: true,
  updatedAt: true,
  timestamps: true
 })
export class Subscriptions extends Model<Subscriptions, SubscriptionsAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'sub_xxx', description: 'stripe Subscription ID' })
  @Column({ type: DataType.STRING, unique: true, allowNull: false })
  subId: string;

  @ApiProperty({ example: '1', description: 'Company ID' })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({ example: '1', description: 'Plan Id of a purchased subscription' })
  @ForeignKey(()=>Plans)
  @Column({ type: DataType.INTEGER, allowNull: false })
  planId: number;

  @ApiProperty({ example: '1', description: 'Plan Id of a purchased subscription' })
  @ForeignKey(() => PlanPackages)
  @Column({ type: DataType.INTEGER, allowNull: false})
  packageId: number;

  @ApiProperty({ example: 'active', description: 'Subscription status' })
  @Column({ type: DataType.STRING, allowNull: true })
  status: string;

  @ApiProperty({ example: 'active', description: 'Subscription start date' })
  @Column({ type: DataType.DATE, allowNull: true })
  startDate: Date;

  @ApiProperty({ example: 'active', description: 'Subscription end date' })
  @Column({ type: DataType.DATE, allowNull: true })
  endDate: Date;

  @ApiProperty({ example: 'active', description: 'Subscription end date' })
  @Column({ type: DataType.DATE, allowNull: true })
  subEndDate: Date;


  @ApiProperty({ example: 'y', description: 'Subscription is monthly or yearly ' })
  @Column({ type: DataType.STRING, allowNull: false })
  period: string;

  @ApiProperty({ example: 'main/addon', description: 'Subscription for main product or addons product' })
  @Column({ type: DataType.STRING, allowNull: true })
  type: string;

  // @ApiProperty({
  //   example: '1646030615',
  //   description:
  //     'End of the current period that the subscription has been invoiced for. At the end of this period, a new invoice will be created.',
  // })
  // @Column({ type: DataType.INTEGER, allowNull: false })
  // currentPeriodEnd: number;

  // @ApiProperty({
  //   example: 'cap1ats',
  //   description: 'Capabilities of the subscription.',
  // })
  // @Column({ type: DataType.STRING, allowNull: true })
  // capabilities: string;

  // @ApiProperty({ example: 'ATS Package', description: 'Package name.' })
  // @Column({ type: DataType.STRING, allowNull: true })
  // packageName: string;

  // @ApiProperty({ example: 'Tier 1', description: 'Plan name.' })
  // @Column({ type: DataType.STRING, allowNull: false })
  // planName: string;

  @ApiProperty({ example: '4242', description: 'CC last 4.' })
  @Column({ type: DataType.STRING, allowNull: true })
  last4: string;

  // @ApiProperty({
  //   example: 'requires_payment_method',
  //   description: 'Payment Intent status.',
  // })
  // @Column({ type: DataType.STRING, allowNull: true })
  // piStatus: string;

  // @ApiProperty({
  //   example: 'pi_xxx',
  //   description: 'Payment Intent Id.',
  // })
  // @Column({ type: DataType.STRING, allowNull: true })
  // piId: string;

  // @ApiProperty({
  //   example: '',
  //   description: 'Payment Intent client secret.',
  // })
  // @Column({ type: DataType.STRING, allowNull: true })
  // piCs: string;

  @BelongsTo(() => Company)
  company: Company;
  
  @BelongsTo(()=>Plans)
  plans:Plans

  @BelongsTo(() => PlanPackages)
  packages: PlanPackages
}
