import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsOptional } from 'class-validator';

export class updateAssignmentDTO {
  @IsNotEmpty()
  @IsNumber()
  jobId: number;

  @IsOptional()
  workflowId: number;
};

export class UpdateCandidateStatusListDto extends updateAssignmentDTO{
  @ApiProperty({example:"active/rejected/completed",description:'Status of the candidate assignment.'})
  status: string;

  @ApiProperty({ example: "1", description: 'Company Id' })
  companyId: number;

  @ApiProperty({ example: "1", description: 'job Id' })
  jobId: number;

  @ApiProperty({ example: "1", description: 'Workflow Id' })
  workflowId: number;
}