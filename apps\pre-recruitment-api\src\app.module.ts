import {  Module } from "@nestjs/common";
import { ClientsModule, Transport } from "@nestjs/microservices";
import {
  AddressesModule,
  AwardsModule,
  CandidatesModule,
  CertificatesModule,
  CompaniesModule,
  EducationsModule,
  ExperiencesModule,
  IndustriesModule,
  ProjectsModule,
  RecruitersModule,
  RecruitmentDbModule,
  RolesModule,
  SkillsModule,
  UsersModule,
  LocationsModule,
  PositionsModule,
  PaymentModule,
  JobModule,
  WorkflowModule,
  RecruiterPositionsModule,
  DepartmentModule,
  PredefinedSkillModule,
  IntegrationsModule,
  SubscribeModule,
  RoundModule,
  OfferModule,
  GmailModule,
  IntegrationAuthModule,
  TemporalWorkflowModule,
  OutlookModule,
  PlaybackModule,
  MailboxModule,
  BackgroundModule,
  InterviewsModule,
  DrugModule,
  FreshsalesModule,
  NotifyPermissionsModule,
  NotificationsModule,
  UserAssignmentsModule,
  SubscriptionModule,
  JobCreationChatbotModule,
  PriceModule,
  ProductModule,
  PlanModule,
  PlanPackageModule
} from "@microservices/recruitment-db";
import { GoogleModule, LangchainModule } from '@microservices/integrations';
import { CandidateController } from "./candidate/candidates.controller";
import { Auth0Module } from '@microservices/auth';
import { SeedController } from "./seed/seed.controller";
import { UsersController } from "./user/users.controller";
import { IndustryController } from "./industry/industries.controller";
import { PositionController } from "./position/position.controller";
import { LocationsController } from "./location/locations.controller";
import { FileModule } from "./file/file.module";
import { CompanyController } from "./company/company.controller";
import { RecruiterController } from "./recruiter/recruiter.controller";
import { PaymentController } from "./payment/payment.controller";
import { SubscriptionController } from "./subscription/subscription.controller";
import { JobController } from "./job/job.controller";
import { RecruiterPositionController } from "./recruiter-position/recruiter-position.controller";
import { DepartmentsController } from "./department/departments.controller";
import { SkillsController } from "./skills/skills.controller";
import { EmailModule } from "@microservices/email";
import { IntegrationsController } from './integrations/integrations.controller';
import { WorkflowController } from "./workflow/workflow.controller";
import { SubscribeController } from "./subscribe/subscribe.controller";
import { EmailController } from "./email/email.controller";
import { RoundController } from "./round/round.controller";
import { OfferController } from "./offer/offer.controller";
import { GmailController } from "./gmail/gmail.controller";
import { CalendareController } from "./calendar/calendar.controller";
import {CalendarsModule} from "@microservices/calendars"
import { TwilioController } from "./twilio/twilio.controller";
import {TwilioModule} from "@microservices/twilio"
import { CalendarModule } from "@microservices/db";
import { OutlookController } from "./outlook/outlook.controller";
import { MCalController } from "./calendar/mcal.controller";
import { PlaybackController } from "./playback/playback.controller";
import { TemporalWorkflowController } from "./temporal-workflow/temporal-workflow.controller";
import { HttpModule } from "@nestjs/axios";
import { HellosignIntegrationController } from "./hellosign/hellosign-integration.controller";
import { HellosignIntegrationModule } from "@microservices/recruitment-db";
import { BackgroundController } from "./background-screening/background.controller";
import {InterviewsController} from "./interviews/interviews.controller";
import { CandidateOffersModule } from "libs/recruitment-db/src/lib/candidateoffers/candidate-offers.module";
import { CandidateOffersController } from "./candidateoffers/candidate-offers.controller";
import {DrugController} from "./drug-screening/drug.controller";
import {UniversalModule} from "@microservices/recruitment-db";
import {UniversalController} from "./universal/universal.controller";
import {JobTargetModule} from "@microservices/recruitment-db";
import {JobTargetController} from "./jobtarget/jobtarget.controller";
import { LangGraphModule } from "./langgraph/langgraph.module";
import {LangGraphController} from "./langgraph/langgraph.controller";
import {FreshsalesController} from "./freshsales/freshsales.controller";
import { NotifyPermissionsController } from "./notify-permissions/notify-permissions.controller";
import { NotificationsController } from "./notifications/notifications.controller";
import { UserAssignmentsController } from "./user-assignments/user-assignments.controller";
import { OpenSearchModule } from "@microservices/hr-analytics";
import { WorkflowChatbotModule } from "libs/recruitment-db/src/lib/workflow-chatbot/workflow-chatbot.module";
@Module({
  imports: [
    ClientsModule.register([
      {
        name: "RECRUITMENT_SERVICE",
        transport: Transport.REDIS,
        options: {
          url: process.env.REDIS_URL || "redis://localhost:6379",
        },
      },
    ]),
    RecruitmentDbModule,
    UsersModule,
    CompaniesModule,
    CandidatesModule,
    AwardsModule,
    ProjectsModule,
    CertificatesModule,
    SkillsModule,
    ExperiencesModule,
    EducationsModule,
    IndustriesModule,
    RecruitersModule,
    RolesModule,
    AddressesModule,
    Auth0Module,
    LocationsModule,
    PositionsModule,
    FileModule,
    PaymentModule,
    JobModule,
    RecruiterPositionsModule,
    DepartmentModule,
    PredefinedSkillModule,
    EmailModule,
    IntegrationsModule,
    WorkflowModule,
    SubscribeModule,
    GoogleModule,
    RoundModule,
    HellosignIntegrationModule,
    OfferModule,
    CandidateOffersModule,
    BackgroundModule,
    DrugModule,
    UniversalModule,
    JobTargetModule,
    LangGraphModule,
    InterviewsModule,
    FreshsalesModule,
    GmailModule,
    IntegrationAuthModule,
    CalendarsModule,
    TemporalWorkflowModule,
    TwilioModule,
    CalendarModule,
    OutlookModule,
    PlaybackModule,
    MailboxModule,
    HttpModule,
    NotifyPermissionsModule,
    NotificationsModule,
    SubscriptionModule,
    UserAssignmentsModule,
    JobCreationChatbotModule,
    LangchainModule,
    WorkflowChatbotModule,
    ProductModule,
    PlanModule,
    PlanPackageModule,
    PriceModule
  ],
  controllers: [
    CandidateController,
    SeedController,
    UsersController,
    IndustryController,
    PositionController,
    LocationsController,
    CompanyController,
    RecruiterController,
    PaymentController,
    SubscriptionController,
    JobController,
    RecruiterPositionController,
    DepartmentsController,
    SkillsController,
    IntegrationsController,
    WorkflowController,
    SubscribeController,
    EmailController,
    RoundController,
    OfferController,
    GmailController,
    CandidateOffersController,
    BackgroundController,
    DrugController,
    UniversalController,
    JobTargetController,
    LangGraphController,
    InterviewsController,
    FreshsalesController,
    GmailController,
    CalendareController,
    TwilioController,
    OutlookController,
    MCalController,
    PlaybackController,
    TemporalWorkflowController,
    TemporalWorkflowController,
    HellosignIntegrationController,
    NotifyPermissionsController,
    NotificationsController,
    UserAssignmentsController,
  ],
})
export class AppModule {}
