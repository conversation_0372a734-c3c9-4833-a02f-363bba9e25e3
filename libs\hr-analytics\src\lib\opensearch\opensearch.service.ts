import {
  Inject,
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { Client } from "@opensearch-project/opensearch";
import { eventType } from "../../consts";

@Injectable()
export class OpensearchService {
  constructor(
    @Inject("OpenSearchClient") private readonly openSearchClient: Client
  ) { }

  async search(queryBody: any, companyId: number): Promise<any> {
    try {
      queryBody.query.bool.filter.push({
        term: {
          [`${eventType.JOB_POSTED}.companyId`]: companyId,
        },
      });
      const query = {
        index: process.env.AWS_OPENSEARCH_INDEX,
        body: queryBody,
      };

      return await this.openSearchClient.search(query);
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error,
        "Failed to execute search query"
      );
    }
  }

  async searchIndexSpecificData(queryBody, indexName) {
    try {
      const query = {
        index: indexName,
        body: queryBody,
      };

      return await this.openSearchClient.search(query);
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(
        error,
        "Failed to execute search query on index:" + indexName
      );
    }
  }

  async update(key, keyId, eventType, data) {
    try {
      const updateByQuery = {
        index: process.env.AWS_OPENSEARCH_INDEX,
        body: {
          query: {
            term: {
              [key]: keyId,
            },
          },
          script: {
            source: `ctx._source.${eventType} = params.newStatus`,
            lang: "painless",
            params: {
              newStatus: data,
            },
          },
        },
      };

      const result = await this.openSearchClient.updateByQuery(updateByQuery);
      return result;
    } catch (error) {
      console.error(`Failed to update document for key: ${key}, keyId: ${keyId}`, error);
      throw new InternalServerErrorException(
        error,
        "Failed to execute update query"
      );
    }
  }

  async delete(key, keyId) {
    try {
      const deleteQuery = {
        index: process.env.AWS_OPENSEARCH_INDEX,
        body: {
          query: {
            term: {
              [key]: keyId,
            },
          },
        },
      };

      const result = await this.openSearchClient.deleteByQuery(deleteQuery);
      return result;
    } catch (error) {
      console.error(`Failed to delete document for key: ${key}, keyId: ${keyId}`, error);
      throw new InternalServerErrorException(
        error,
        "Failed to execute delete query"
      );
    }
  }
}