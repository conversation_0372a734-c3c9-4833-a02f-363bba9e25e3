  import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Questions } from './questions.model';

@Injectable()
export class QuestionsService {
  constructor(
    @InjectModel(Questions)
    private questionsModel: typeof Questions
  ) { }

  findOneById(id: number): Promise<Questions> {
    return this.questionsModel.findByPk(id);
  }

  findAll(): Promise<Questions[]> {
    return this.questionsModel.findAll();
  }

  create(dto: Questions): Promise<Questions> {
    return this.questionsModel.create(dto);
  }

  update(id: number, dto: Questions) {
    return this.questionsModel.update({ ...dto }, { where: { id } });

  }

  async remove(id: number) {
    if (id) {
      const question = await this.findOneById(id);
      if (question) {
        return this.questionsModel.destroy({ where: { id } });
      }
    }
  }
}
