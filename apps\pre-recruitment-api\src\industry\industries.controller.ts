import { Controller, Get, Param, Post, Query, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  IndustriesService, Industry,
} from "@microservices/recruitment-db";
import { IndustryDto } from "./dto/industry.dto";
import { AuthGuard } from "@nestjs/passport";
import { PermissionsGuard } from "@microservices/auth";
// import { QdrantClient } from "@qdrant/qdrant-js";
// import { LangchainService } from "@microservices/integrations";

// const langchainService = new LangchainService();

@ApiTags("Industry")
@Controller("industry")
export class IndustryController {
  constructor (private readonly industriesService: IndustriesService) {}

  @ApiOperation({ summary: "Get industries" })
  @ApiResponse({ status: 200, type: [Industry] })
  // @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/label/:label?')
  getAll (@Param('label') label?: string,) {
    return this.industriesService.findByLabel(label);
  }

  @ApiOperation({ summary: "Get industry" })
  @ApiResponse({ status: 200, type: Industry })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/id/:id')
  getOne (@Param('id') id: string) {
    return this.industriesService.getByPk(+id);
  }

  @ApiOperation({ summary: "Get industries by ids" })
  @ApiResponse({ status: 200, type: [Industry] })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/array')
  getArray (@Query() query: IndustryDto) {
    if (query.ids instanceof Array) {
      return this.industriesService.getByIdArrays(query.ids)
    } else {
      return this.industriesService.getByIdArrays([query.ids])
    }
  }

  // @ApiOperation({ summary: "Feed Industries to Qdrant" })
  // @ApiResponse({ status: 200 })
  // @Post('/feed-industries')
  // async feedIndustriesToQdrant() {
  //   const client = new QdrantClient({ 
  //     url: process.env.QDRANT_DATABASE_URL, 
  //     apiKey: process.env.QDRANT_API_KEY 
  //   });
  
  //   // Process positions in batches of 100
  //   const batchSize = 50;
  //   let offset = 0;
  //   let hasMorePositions = true;
  
  //   while (hasMorePositions) {
  //     // Get positions batch from your API/database
  //     const positions = await this.industriesService.findByLabel(null,batchSize,offset);
      
  //     if (!positions?.length) {
  //       hasMorePositions = false;
  //       break;
  //     }
  
  //     const points = [];
  //     for (let position of positions) {
  //       // Format position data for embedding
  //       const positionText = `PositionName: ${position.label}`;
  //       console.log({positionText});
        
  //       try {
  //         const vector = await langchainService.getEmbeddings(positionText);
  //         points.push({
  //           id: position.id,
  //           vector: vector,
  //           payload: {
  //             id: position.id,
  //             label: position.label,
  //             value: position.value,
  //           }
  //         });
  //       } catch (error) {
  //         console.error("Error generating embeddings:", error);
  //       }
  
  //       // Rate limiting
  //       await new Promise(resolve => setTimeout(resolve, 200));
  //     }
  
  //     // Batch upsert to Qdrant
  //     try {
  //       await client.upsert("industries", { 
  //         wait: true, 
  //         points 
  //       });
  //       console.log("Industries upserted successfully");
  //     } catch (error) {
  //       console.error("Failed to upsert industries:", error);
  //     }
  
  //     offset += batchSize;
  //   }
  // }
}
