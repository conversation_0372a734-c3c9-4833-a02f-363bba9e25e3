import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Company } from "../companies/companies.model";
import { ChatMessage } from '@langchain/core/messages';
import { ChatOpenAI, OpenAI } from "@langchain/openai";
import { QdrantClient } from '@qdrant/qdrant-js';
import { LangchainService } from "@microservices/integrations";
import { Workflow } from "../workflow/workflow.model";
import { Jobs } from "../job/job.model";
import { HttpService } from "@nestjs/axios";
import { v4 as uuidv4 } from 'uuid';


@Injectable()
export class WorkflowChatbotService {
  private chatModel: ChatOpenAI;
  private qdrantClient: QdrantClient;
  constructor(
    @InjectModel(Company) private companyRepository: typeof Company,
    @InjectModel(Workflow) private workflowRepository: typeof Workflow,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    private readonly langchainService: LangchainService,
    private readonly httpService: HttpService
  ) {
    this.chatModel = new ChatOpenAI({
      modelName: process.env.OPEN_AI_MODEL || "gpt-4",
      temperature: 0,
      openAIApiKey: process.env.OPEN_AI_SECRET_KEY,
    });
    this.qdrantClient = new QdrantClient({
      url: process.env.QDRANT_DATABASE_URL,
      apiKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOiJtIn0.mi3m3jbDElbTB6AG1KuVEXKLzDBLmYCG3I9dPDIb4OQ'
    });
  }

  //Generate the system prompt for the workflow chatbot
  getSystemPrompt(company, userId) {
    return `
    You are an advanced AI Workflow Assisstant for the uRecruits platform, designed to assist user with the workflow creation and assignment. Your primary mission is to assist users in creating, customizing, and managing hiring workflows with precision and efficiency.

    ## Core Capabilities:
    - Create and customize hiring workflows
    - Assign workflows to specific job positions
    - Retrieve and display existing workflows
    - Guide users through the workflow creation process
    - Provide intelligent recommendations based on job requirements

    ## Workflow Components:
    
    1. **Interview Tasks**:
      - HR Audio Video Interview
      - Senior HR Audio Video Interview
      - Technical Audio Video Interview
    
    2. **Assessment Tasks**:
      - Functional/Domain Assessment
      - Live Task/Coding Assessment
      - Take Home/Coding Assessment
    
    3. **Screening and Hiring Tasks**:
      - Conditional Offer Letter Generation
      - Background Screening
      - Drug Screening

    ## Response Guidelines:
    - Use a friendly, helpful tone throughout the conversation
    - Always ask for explicit confirmation before Confirming the workflow creation and assignment
    - Present workflow details in an organized, readable format
    - When confirming the workflow creation and assignment, include ALL the gathered workflows tasks in the response
    - Always handle modifications one at a time with clear confirmation
    - If user wants to end the conversation, say goodbye and thank them for using uRecruits
    - If user ask about updating existing workflow, then say you are not capable of editing workflow yet. In the future it will be available.
    - Always use job and workflow IDs rather than index numbers.
    - NEVER expose internal JSON structures or raw data to the user   

    ## Response Format:
    - Always respond in JSON blocks.
    - Include type field: user, plan, action, observation, or output
    - Be concise and structured.

    **Example:**

    Assistant:
    {
      "type": "plan",
      "plan": "Retrieve job details based on user input title"
    }


    ## Tools (Functions) You Can Use:

    Use these functions when responding to user intents:

    1. getCompanyAllWorkflow(companyId:number): when user wants to know their workflows
    2. createWorkflow(workflow: string[], title: string, companyId:number, userId:number): When user wants to create a workflow with specified rounds
    3. getJobsforAssignment(companyId:number): When user wants to assign a workflow to job, 
    4. assignWorkflowToJob(companyId:number, jobId:number, workflowId:number): When user confirms which workflow to assigned to which job

    ## Response Format:
    - Include clear, actionable output messages
    - Format lists and options for easy readability
    - Provide confirmation steps for critical actions
    - ALWAYS format observation data into human-readable text
    - Always respond in JSON blocks.
    - Include type field: user, plan, action, observation, or output
    - Be concise and structured.

    **Example:**

    Assistant:
    {
      "type": "plan",
      "plan": "Retrieve job details based on user input title"
    }

    ## Example Interaction:
    1. Get all workflows
    { "type": "user", "user": "all my workflows" }
    { "type": "plan", "plan": "I will call the getCompanyAllWorkflow function to display all the workflows." }
    { "type": "action", "function": "getCompanyAllWorkflow", "input": { "companyId":${company.id} } }
    { "type": "observation", "observation": { "success": true, "message":"1. Basic Rounds Only(78), 2. Advanced Rounds(123),etc" } }
    { "type": "output", "output": "Below are the workflows. 1. Basic Rounds Only , 2. Advanced Rounds Which workflow would you like to assign to your jobs?" }

    2. Create a workflow with assessment integration
    { "type": "user", "user": "I want to create a workflow" }
    { "type": "output", "output": "Sure! There are three categories through which you can add tasks to your position's workflow:\n1. Interview Tasks\n- HR Audio Video Interview\n- Senior HR Audio Video Interview\n- Technical Audio Video Interview\n2. Assessment Tasks\n- Functional/Domain Assessment\n- Live Task/Coding Assessment\n- Take Home/Coding Assessment\n3. Screening and Hiring Tasks\n- Conditional Offer Letter Generation\n- Background Screening\n- Drug Screening\n\nWhich ones would you like to include?" }

    { "type": "user", "user": "live task" }
    { "type": "output", "output": "Would you like to add the 'Live Task/Coding Assessment' to your workflow? If so, please confirm, or let me know if you'd like to add more tasks." }

    { "type": "user", "user": "no only this" }
    { "type": "output", "output": "Great! You've chosen to include the 'Live Task/Coding Assessment' in your workflow. Would you like to use an existing live coding assessment or create a new one?" }

    { "type": "user", "user": "existing" }
    { "type": "plan", "plan": "User wants to use existing live coding assessment, I'll get the list" }
    { "type": "action", "function": "getExistingLiveCodingAssessments", "input": { "companyId": ${company.id} } }
    { "type": "observation", "observation": { "success": true, "message": "Here are your existing live coding assessments:\n\n1. React Component Test (123)\n2. Algorithm Challenge (456)\n\nWhich assessment would you like to add?" } }
    { "type": "output", "output": "Here are your existing live coding assessments:\n\n1. React Component Test (123)\n2. Algorithm Challenge (456)\n\nWhich assessment would you like to add to your workflow? Just type the assessment name." }

    { "type": "user", "user": "React Component Test" }
    { "type": "output", "output": "Perfect! I've added 'React Component Test' live coding assessment to your workflow. What name would you like to give this workflow?" }

    { "type": "user", "user": "Technical Screening Workflow" }
    { "type": "plan", "plan": "Proceed to create the workflow using createWorkflow tool." }
    { "type": "action", "function": "createWorkflow", "input": { "workflow": ["Live Task/Coding Assessment"], "title": "Technical Screening Workflow",companyId:${company.id},userId:${userId} } }
    { "type": "observation", "observation": { "success": true, workflowId:234, "message": "Workflow created successfully." } }
    { "type": "output", "output": "The workflow 'Technical Screening Workflow' has been created successfully with the selected tasks. You can now use it in your job postings. Would you like me to assign it to your jobs?" }

    { "type": "user", "user": "Yes" }
    { "type": "plan", "plan": "I will call the getJobsforAssignment function to display all the jobs they can choose to assign it." }
    { "type": "action", "function": "getJobsforAssignment", "input": { companyId:${company.id}} }
    { "type": "observation", "observation": { "success": true, message:"1. Software Engineer(167), 2.Python developer(456),etc" } }
    { "type": "output", "output": "Below are the jobs. Which job would you like to assign the 'Basic Rounds Only' workflow to?" }

    { "type": "user", "user": "Software engineer" }
    { "type": "plan", "plan": "I will call the assignWorkflowToJob function to assign the workflow to the selected job." }
    { "type": "action", "function": "assignWorkflowToJob", "input": { companyId:${company.id},jobId:167,workflowId:234} }
    { "type": "observation", "observation": { "success": true, message:"Successfully assigned the workflow." } }
    { "type": "output", "output": "The 'Basic Rounds Only' workflow has been successfully assigned to the Software Engineer position." }

    ## CRITICAL: Assessment Integration Flow
    When user wants to add assessment steps (Domain Assessment, Take-Home Assessment, Live Coding Assessment):

    3. Assessment Integration - Existing Assessment
    { "type": "user", "user": "I want to add domain assessment" }
    { "type": "output", "output": "Would you like to use an existing domain assessment or create a new one?" }
    { "type": "user", "user": "existing" }
    { "type": "plan", "plan": "User wants to use existing domain assessment, I'll get the list" }
    { "type": "action", "function": "getExistingDomainAssessments", "input": { "companyId": ${company.id} } }
    { "type": "observation", "observation": { "success": true, "message": "Here are your existing domain assessments:\\n\\n1. Technical Knowledge Test (123)\\n2. JavaScript Fundamentals (456)\\n\\nWhich assessment would you like to add?" } }
    { "type": "output", "output": "Here are your existing domain assessments:\\n\\n1. Technical Knowledge Test (123)\\n2. JavaScript Fundamentals (456)\\n\\nWhich assessment would you like to add to your workflow? Just type the assessment name." }
    { "type": "user", "user": "Technical Knowledge Test" }
    { "type": "output", "output": "Perfect! I've added 'Technical Knowledge Test' domain assessment to your workflow. What other steps would you like to include?" }

    4. Assessment Integration - Create New Assessment
    { "type": "user", "user": "I want to add take-home assessment" }
    { "type": "output", "output": "Would you like to use an existing take-home assessment or create a new one?" }
    { "type": "user", "user": "create new" }
    { "type": "output", "output": "I'll help you create a new take-home assessment. You'll be transferred to the assessment creation system. Once created, you can return here to add it to your workflow. Please specify 'take-home assessment' to begin creation." }

    ## CRITICAL WORKFLOW CREATION RULES:

    **WORKFLOW CREATION START - MANDATORY DETAILED LIST:**
    When user asks to create a workflow, ALWAYS show the complete detailed list:
    "Sure! There are three categories through which you can add tasks to your position's workflow:
    1. Interview Tasks
    - HR Audio Video Interview
    - Senior HR Audio Video Interview
    - Technical Audio Video Interview
    2. Assessment Tasks
    - Functional/Domain Assessment
    - Live Task/Coding Assessment
    - Take Home/Coding Assessment
    3. Screening and Hiring Tasks
    - Conditional Offer Letter Generation
    - Background Screening
    - Drug Screening

    Which ones would you like to include?"

    **ASSESSMENT INTEGRATION - MANDATORY FLOW:**
    When user selects assessment types, follow these SPECIFIC flows:

    **DOMAIN ASSESSMENT FLOW:**
    1. User selects "Functional/Domain Assessment"
    2. Ask: "Would you like to use an existing domain assessment or create a new one?"
    3. For existing: Call getExistingDomainAssessments function
    4. For create new: Guide user to assessment creation system
    5. Only after assessment selection/creation: Ask for workflow name

    **LIVE TASK/CODING ASSESSMENT FLOW:**
    1. User selects "Live Task/Coding Assessment"
    2. **DIRECTLY** guide to create new assessment (skip existing option)
    3. Say: "I'll help you create a new live coding assessment. You'll be transferred to the assessment creation system. Please specify 'live coding assessment' to begin creation."
    4. Only after assessment creation: Ask for workflow name

    **TAKE HOME/CODING ASSESSMENT FLOW:**
    1. User selects "Take Home/Coding Assessment"
    2. **DIRECTLY** guide to create new assessment (skip existing option)
    3. Say: "I'll help you create a new take-home assessment. You'll be transferred to the assessment creation system. Please specify 'take-home assessment' to begin creation."
    4. Only after assessment creation: Ask for workflow name

    **CRITICAL RULES:**
    - Domain Assessment: Ask existing/new
    - Live Task/Coding: Always create new (no existing option)
    - Take Home/Coding: Always create new (no existing option)

    **ASSESSMENT ASSIGNMENT COMPLETION:**
    When returning from assessment assignment (contextHint: 'WORKFLOW_RETURN_FROM_ASSIGNMENT'):
    1. The assessment has been successfully assigned and assessment data is available
    2. Continue with workflow creation process
    3. Ask for workflow name if not provided
    4. Use the provided assessment data when creating the workflow
    5. Acknowledge the assessment assignment: "Great! The assessment has been assigned to your workflow."

    **NEVER SKIP THE ASSESSMENT INTEGRATION STEP!**

    ## MANDATORY ASSESSMENT INTEGRATION EXAMPLES:

    **Domain Assessment Example (CORRECT FLOW - ASK EXISTING/NEW):**
    {"type": "user","user": "domain assessment"}
    { "type": "output", "output": "Would you like to add the 'Functional/Domain Assessment' to your workflow? If so, please confirm, or let me know if you'd like to add more tasks." }
    {"type": "user","user": "yes"}
    { "type": "output", "output": "Great! You've chosen to include the 'Functional/Domain Assessment' in your workflow. Would you like to use an existing domain assessment or create a new one?" }
    {"type": "user","user": "existing"}
    { "type": "plan", "plan": "User wants to use existing domain assessment, I'll get the list" }
    { "type": "action", "function": "getExistingDomainAssessments", "input": { "companyId": ${company.id} } }

    **Live Coding Assessment Example (CORRECT FLOW - ALWAYS CREATE NEW):**
    {"type": "user","user": "live task"}
    { "type": "output", "output": "Would you like to add the 'Live Task/Coding Assessment' to your workflow? If so, please confirm, or let me know if you'd like to add more tasks." }
    {"type": "user","user": "no only this"}
    { "type": "output", "output": "Great! You've chosen to include the 'Live Task/Coding Assessment' in your workflow. I'll help you create a new live coding assessment. You'll be transferred to the assessment creation system. Please specify 'live coding assessment' to begin creation." }

    **Take-Home Assessment Example (CORRECT FLOW - ALWAYS CREATE NEW):**
    {"type": "user","user": "take home assessment"}
    { "type": "output", "output": "Would you like to add the 'Take Home/Coding Assessment' to your workflow? If so, please confirm, or let me know if you'd like to add more tasks." }
    {"type": "user","user": "only this"}
    { "type": "output", "output": "Great! You've chosen to include the 'Take Home/Coding Assessment' in your workflow. I'll help you create a new take-home assessment. You'll be transferred to the assessment creation system. Please specify 'take-home assessment' to begin creation." }

    **CRITICAL: The sequence MUST be:**

    **For Domain Assessment:**
    1. User selects "domain assessment"
    2. Ask for confirmation to add to workflow
    3. User confirms
    4. Ask: "existing or create new?"
    5. Handle existing/new selection
    6. ONLY THEN ask for workflow name

    **For Live Task/Coding & Take Home/Coding:**
    1. User selects assessment type
    2. Ask for confirmation to add to workflow
    3. User confirms
    4. DIRECTLY guide to create new assessment (skip existing option)
    5. Transfer to assessment creation system
    6. ONLY THEN ask for workflow name
    `;
  }

  // update job with updated workflowId
  async assignWorkflowToJob({ jobId, workflowId, companyId, authHeader }) {
    try {
      if (!jobId || !workflowId || !companyId) {
        console.error(`[assignWorkflowToJob] Missing parameters:`, { jobId, workflowId, companyId });
        return {
          success: false,
          message: "Missing required parameters: jobId, workflowId, or companyId."
        };
      }

      // Validate that the workflow exists and belongs to the same company
      const workflow = await this.workflowRepository.findOne({
        where: {
          id: workflowId,
          companyId: companyId
        }
      });

      if (!workflow) {
        return {
          success: false,
          message: `Workflow with ID ${workflowId} not found or doesn't belong to your company.`
        };
      }

      // Validate that the job exists and belongs to the same company
      const job = await this.jobRepository.findOne({
        where: {
          id: jobId,
          companyId: companyId
        }
      });

      if (!job) {
        return {
          success: false,
          message: `Job with ID ${jobId} not found or doesn't belong to your company.`
        };
      }

      // Make the API call to update the job
      const response = await this.httpService.patch(
        `${process.env.RECRUITMENT_API_URI}/api/job`,
        { id: jobId, workflowId },
        { headers: { "Authorization": authHeader } }
      ).toPromise();

      if (response?.status == 200) {
        return {
          success: true,
          message: `Successfully assigned workflow "${workflow.title}" to job "${job.title}".`
        };
      } else if (response.status == 401) {
        return {
          success: false,
          message: "You are unauthorized to assign the workflow to job."
        };
      } else if (response.status == 403) {
        return {
          success: false,
          message: "You don't have required permission to assign workflow to job."
        };
      } else {
        return {
          success: false,
          message: "Failed to assign the workflow."
        };
      }
    } catch (error) {
      console.error('[assignWorkflowToJob] Error:', error);

      // Handle specific database constraint errors
      if (error.message?.includes('foreign key constraint')) {
        return {
          success: false,
          message: "Invalid workflow or job ID. Please ensure both exist and belong to your company."
        };
      }

      return {
        success: false,
        message: `Failed to assign workflow: ${error.message || 'Unknown error'}`
      };
    }
  }

  // Get all the jobs which user can assign a workflow to
  async getJobsforAssignment({ companyId }) {
    if (!companyId) {
      console.error('[getJobsforAssignment] No companyId provided');
      return {
        success: false,
        formattedString: 'No company ID provided.',
        jobs: [],
      };
    }

    const jobs = await this.jobRepository.findAll({
      where: { companyId },
      attributes: ['title', 'id', 'createdAt'], // Include createdAt for sorting
      include: [{ model: Workflow, attributes: ['title', 'id'] }],
      order: [['createdAt', 'DESC']], // Sort by createdAt in descending order
    });

    if (jobs?.length) {
      const str = jobs
        .map((i) => `${i.title} (${i.id}), Assigned Workflow: ${i.workflowId ? i.workflow?.title + ` (${i.workflowId})` : 'None'}`)
        .join(',\n');
      return {
        success: true,
        formattedString: str,
        jobs: jobs.map((job) => ({
          id: job.id,
          title: job.title,
          workflowId: job.workflowId,
          workflowTitle: job.workflow?.title,
        })),
      };
    } else {
      return {
        success: true,
        formattedString: `You haven't created any jobs yet. You can create it through "https://app.urecruits.com/job/create".`,
        jobs: [],
      };
    }
  }
  
  //Get all workflows company has
  async getCompanyAllWorkflow({ companyId }) {
    if (companyId) {
      const workflows = await this.workflowRepository.findAll({ where: { companyId }, attributes: ["title", "id", "workflow"] })
      if (workflows.length) {
        const str = workflows?.map(i => `${i.title} (${i.id})`).join(", ");
        return str
      }
    }

  }

  // Store the workflow details in the database
  async createWorkflow(data: {
    workflow: string[],
    title: string,
    companyId: number,
    userId: number,
    assessmentData?: any
  }) {
    const { workflow, title, companyId, userId, assessmentData } = data

    const masterWorkflowRounds = [
      { id: 1, settings: false, name: "HR Audio Video Interview" },
      { id: 2, settings: true, name: "Functional/Domain Assessment" },
      { id: 3, settings: true, name: "Live Task/Coding Assessment" },
      { id: 4, settings: true, name: "Take Home/Coding Assessment" },
      { id: 5, settings: false, name: "Technical Audio Video Interview" },
      { id: 6, settings: false, name: "Senior HR Audio Video Interview" },
      { id: 7, settings: false, name: "Conditional Offer Letter Generation" },
      { id: 8, settings: false, name: "Background Screening" },
      { id: 9, settings: false, name: "Drug Screening" }
    ];

    const filteredWorkflows = masterWorkflowRounds.filter(round => workflow.includes(round.name));
    if (filteredWorkflows.length && title) {
      try {
        // Prepare the workflow payload with assessment data
        const workflowPayload: any = {
          companyId: companyId,
          authorId: userId,
          workflow: filteredWorkflows,
          title,
        };

        // Add assessment data if provided
        if (assessmentData) {
          Object.assign(workflowPayload, assessmentData);
        }

        const newWorkflow = await this.workflowRepository.create(workflowPayload)
        if (newWorkflow) {

          return {
            success: true,
            workflowId: newWorkflow.id,
            response: "Successfully created workflow for " + newWorkflow.title + "."
          }
        } else {
          return {
            success: false,
            response: "Failed to create workflow"
          }
        }
      } catch (err) {
        return {
          success: false,
          response: "Failed to create new workflow."
        }
      }
    } else {
      return {
        success: false,
        response: "Failed to create new workflow due to empty title or position workflow"
      }
    }
  }

  //Get existing domain assessments for the company
  async getExistingDomainAssessments({ companyId, authHeader }) {
    try {
      // Check if ASSESSMENT_API_URI is configured
      const assessmentApiUrl = process.env.ASSESSMENT_API_URI || 'http://localhost:5481/api';

      if (!process.env.ASSESSMENT_API_URI) {
        console.warn('[getExistingDomainAssessments] ASSESSMENT_API_URI not configured, using fallback');
      }

      // Prepare headers with authentication if available
      const headers: any = {
        'Content-Type': 'application/json'
      };

      if (authHeader) {
        headers['Authorization'] = authHeader;
      }

      // Call the correct endpoint that uses questionsService.findAllAssessment
      // Note: companyId is extracted from the JWT token by the controller, not from query params
      const response = await this.httpService.get(
        `${assessmentApiUrl}/domain-questions/all`,
        { headers }
      ).toPromise();

      // Check if response has data (could be array or object with data property)
      const assessments = response?.data?.data || response?.data;

      if (assessments && Array.isArray(assessments) && assessments.length > 0) {
        const assessmentList = assessments.map((assessment: any, index: number) =>
          `${index + 1}. ${assessment.name || assessment.title} (ID: ${assessment.id})`
        ).join('\n');

        return {
          success: true,
          message: `Here are your existing domain assessments:\n\n${assessmentList}\n\nWhich assessment would you like to add to your workflow? Just type the assessment name or ID.`
        };
      } else {
        return {
          success: false,
          message: "No existing domain assessments found. Would you like to create a new one?"
        };
      }
    } catch (error) {
      console.error('Error fetching domain assessments:', error);
      console.error('Error details:', error.response?.data || error.message);

      // Check if it's an authentication error
      if (error.response?.status === 401) {
        return {
          success: false,
          message: "Authentication required to fetch domain assessments. Would you like to create a new one instead?"
        };
      }

      // Check if it's a connection error
      if (error.code === 'ECONNREFUSED') {
        return {
          success: false,
          message: "Assessment service is currently unavailable. Would you like to create a new assessment instead?"
        };
      }

      return {
        success: false,
        message: "Error fetching domain assessments. Would you like to create a new one instead?"
      };
    }
  }

  //Get existing take-home assessments for the company
  async getExistingTakeHomeAssessments({ companyId, authHeader }) {
    try {
      // Check if ASSESSMENT_API_URI is configured
      const assessmentApiUrl = process.env.ASSESSMENT_API_URI || 'http://localhost:5481/api';

      if (!process.env.ASSESSMENT_API_URI) {
        console.warn('[getExistingTakeHomeAssessments] ASSESSMENT_API_URI not configured, using fallback');
      }

      // Prepare headers with authentication if available
      const headers: any = {
        'Content-Type': 'application/json'
      };

      if (authHeader) {
        headers['Authorization'] = authHeader;
      }

      const response = await this.httpService.get(
        `${assessmentApiUrl}/take-home-task?companyId=${companyId}`,
        { headers }
      ).toPromise();

      if (response?.data && response.data.length > 0) {
        const assessments = response.data.map((assessment: any, index: number) =>
          `${index + 1}. ${assessment.name} (${assessment.id})`
        ).join('\n');

        return {
          success: true,
          message: `Here are your existing take-home assessments:\n\n${assessments}\n\nWhich assessment would you like to add to your workflow? Just type the assessment name.`
        };
      } else {
        return {
          success: false,
          message: "No existing take-home assessments found. Would you like to create a new one?"
        };
      }
    } catch (error) {
      console.error('Error fetching take-home assessments:', error);
      return {
        success: false,
        message: "Error fetching take-home assessments. Would you like to create a new one instead?"
      };
    }
  }

  //Get existing live coding assessments for the company
  async getExistingLiveCodingAssessments({ companyId, authHeader }) {
    try {
      // Check if ASSESSMENT_API_URI is configured
      const assessmentApiUrl = process.env.ASSESSMENT_API_URI || 'http://localhost:5481/api';

      if (!process.env.ASSESSMENT_API_URI) {
        console.warn('[getExistingLiveCodingAssessments] ASSESSMENT_API_URI not configured, using fallback');
      }

      // Prepare headers with authentication if available
      const headers: any = {
        'Content-Type': 'application/json'
      };

      if (authHeader) {
        headers['Authorization'] = authHeader;
      }

      const response = await this.httpService.get(
        `${assessmentApiUrl}/live-coding?companyId=${companyId}`,
        { headers }
      ).toPromise();

      if (response?.data && response.data.length > 0) {
        const assessments = response.data.map((assessment: any, index: number) =>
          `${index + 1}. ${assessment.name} (${assessment.id})`
        ).join('\n');

        return {
          success: true,
          message: `Here are your existing live coding assessments:\n\n${assessments}\n\nWhich assessment would you like to add to your workflow? Just type the assessment name.`
        };
      } else {
        return {
          success: false,
          message: "No existing live coding assessments found. Would you like to create a new one?"
        };
      }
    } catch (error) {
      console.error('Error fetching live coding assessments:', error);
      return {
        success: false,
        message: "Error fetching live coding assessments. Would you like to create a new one instead?"
      };
    }
  }

  //Handles the conversation between user and workflow chatbot
  async startConversation(body, companyId, userId, authHeader) {
    if (companyId) {
      try {
        const { sessionId, userInput, assessmentData, contextHint } = body;
        const company = companyId && await this.companyRepository.findByPk(companyId);
        if (!company) return { chatResponse: "Company not found." };

        const conversationHistory = await this.getConversationHistory(sessionId);
        const messages = conversationHistory?.messages?.length ?
          conversationHistory.messages :
          [new ChatMessage({ role: "system", content: await this.getSystemPrompt(company, userId) })];

        // Handle special context hints for workflow continuation
        let userMessage = userInput;
        if (contextHint === 'WORKFLOW_CONTINUATION_AFTER_ASSESSMENT') {
          userMessage = 'continue with workflow creation';
        } else if (contextHint === 'WORKFLOW_RETURN_FROM_ASSIGNMENT') {
          userMessage = 'continue with workflow creation after assessment assignment. The assessment has been successfully assigned and assessment data is available for workflow creation.';
        }

        messages.push(new ChatMessage({ role: "user", content: JSON.stringify({ type: "user", user: userMessage }) }));

        while (true) {
          const chat = await this.chatModel.invoke(messages);
          const data = chat.content;

          // First, try to find any embedded JSON in the response
          const jsonPattern = /\{(?:[^{}]|\{(?:[^{}]|\{[^{}]*\})*\})*\}/g;
          const matches = typeof data === 'string' ? data.match(jsonPattern) : null;
          let call;
          let processedContent = data;

          if (matches?.length) {
            try {
              call = JSON.parse(matches[matches.length - 1]);
              processedContent = matches[matches.length - 1];
            } catch (parseError) {
              console.error("Error parsing extracted JSON:", parseError);
            }
          }

          try {
            if (!call) {
              call = typeof processedContent === 'string' ? JSON.parse(processedContent) : processedContent;
            }
            if (!call.type) {
              call = { type: "output", output: processedContent };
            }
          } catch (error) {
            call = { type: "output", output: processedContent };
          }

          messages.push(new ChatMessage({ role: "assistant", content: processedContent }));

          if (call.type === "plan" || call.type === "continue") continue;

          if (call.type === "action" && call.function && call.input) {
            if (call.function == 'assignWorkflowToJob') {
              call.input.authHeader = authHeader
            }
            // Pass authHeader to assessment-related functions that need authentication
            if (call.function === 'getExistingDomainAssessments' ||
                call.function === 'getExistingTakeHomeAssessments' ||
                call.function === 'getExistingLiveCodingAssessments') {
              call.input.authHeader = authHeader;
            }
            // Pass assessment data to createWorkflow function
            if (call.function === 'createWorkflow' && assessmentData) {
              call.input.assessmentData = assessmentData;
            }
            const observation = await this[call.function](call.input);
            messages.push(new ChatMessage({ role: "assistant", content: JSON.stringify({ role: "observation", observation }) }));
            continue;
          }

          if (call.type === "output") {
            await this.storeConversationInQdrant({ sessionId: sessionId, companyId, messages, userId, timestamp: new Date().toISOString() });
            return { chatResponse: call.output };
          }
          break;
        }

        return { chatResponse: "I'm sorry, I couldn't process your request. Please try again." };
      } catch (error) {
        console.error("Error in startConversation:", error);
        throw error;
      }
    }
  }

  //Stores conversation history
  private async storeConversationInQdrant(data: {
    sessionId: string;
    companyId: number;
    messages: ChatMessage[];
    timestamp: string;
    userId: number;
  }) {
    try {
      // Convert conversation to string for embedding
      const conversationStr = data.messages
        .map(msg => `${msg.role}: ${msg.content}`)
        .join('\n');

      // Get embedding
      const vector = await this.langchainService.getEmbeddings(conversationStr);

      // Prepare point for Qdrant
      const point = {
        id: data.sessionId,
        vector,
        payload: {
          sessionId: data.sessionId,
          companyId: data.companyId,
          userId: data.userId,
          flowType: "workflow",
          messages: data.messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          timestamp: data.timestamp
        }
      };

      // Upsert conversation in Qdrant
      await this.qdrantClient.upsert("conversations", {
        wait: true,
        points: [point]
      });

    } catch (error) {
      console.error("Error storing conversation in Qdrant:", error);
      throw error;
    }
  }

  //Retrieves Conversation history
  private async getConversationHistory(sessionId: string) {
    try {
      // First, check for reset markers
      const resetResponse = await this.qdrantClient.scroll("conversations", {
        filter: {
          must: [
            {
              key: "sessionId",
              match: {
                value: sessionId,
              },
            },
            {
              key: "flowType",
              match: {
                value: "chat_reset",
              },
            },
          ],
        },
        limit: 10,
      });

      let lastResetTimestamp: string | null = null;
      if (resetResponse.points && resetResponse.points.length > 0) {
        const resetMarkers = resetResponse.points
          .filter(point => point.payload?.isResetMarker === true)
          .sort((a, b) => new Date(b.payload?.resetTimestamp as string).getTime() - new Date(a.payload?.resetTimestamp as string).getTime());

        if (resetMarkers.length > 0) {
          lastResetTimestamp = resetMarkers[0].payload?.resetTimestamp as string;
        }
      }

      // Get workflow conversation history
      const response = await this.qdrantClient.scroll("conversations", {
        filter: {
          must: [
            {
              key: "sessionId",
              match: {
                value: sessionId,
              },
            },
            {
              key: "flowType",
              match: {
                value: "workflow", // 👈 filter for flowType
              },
            },
          ],
        },
        limit: 1,
      });

      const [conversation] = response.points || [];

      if (conversation) {
        // Check if conversation is after last reset
        if (lastResetTimestamp) {
          const conversationTimestamp = conversation.payload?.timestamp as string;
          if (conversationTimestamp && new Date(conversationTimestamp) <= new Date(lastResetTimestamp)) {
            // Conversation is before reset, return null to start fresh
            return null;
          }
        }

        return {
          messages: (conversation.payload.messages as any[]).map(msg =>
            new ChatMessage({
              role: msg.role,
              content: msg.content,
            })
          ),
        };
      }

      return null;
    } catch (error) {
      console.error("Error retrieving conversation from Qdrant:", error);
      return null;
    }
  }
}
