import { AuthUser, PermissionsGuard } from '@microservices/auth';
import { GmailService, OutlookService } from '@microservices/recruitment-db';
import {
  BadRequestException,
  Body,
  Controller, Get, Logger, Param,
  PayloadTooLargeException,
  Post, Query, UploadedFiles, UseGuards, UseInterceptors
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { AnyFilesInterceptor } from '@nestjs/platform-express';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

@ApiTags('Outlook')
@Controller('outlook')
export class OutlookController {
  constructor(
    private outlookService:OutlookService,
    ) {}

  @ApiOperation({ summary: 'Send Mail' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @UseInterceptors(AnyFilesInterceptor()) 
  @Post('/send-mail')
  async sendEmail(@Body() data,@UploadedFiles() files: Array<Express.Multer.File>,@AuthUser() user) {
    try{
   if(files){
    const maxAllowedFileSize = 25 * 1024 * 1024; 
    const totalFileSize = files.reduce((acc, file) => acc + file.size, 0);
    if (totalFileSize > maxAllowedFileSize) {
      throw new BadRequestException('Total file size exceeds the allowed limit.');
    }
   }
    return this.outlookService.sendEmail(data,files,user['https://urecruits.com/userId']);
  }catch(error){
    throw new PayloadTooLargeException('Error '+ error)
  }
  }

  @ApiOperation({ summary: 'Send Mail' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @UseInterceptors(AnyFilesInterceptor()) 
  @Post('draft/:id')
  async updateDraftEmail(@Param('id') id:any,@Body() data,@UploadedFiles() files: Array<Express.Multer.File>,@AuthUser() user) {
    try{
   if(files){
    const maxAllowedFileSize = 25 * 1024 * 1024;
    const totalFileSize = files.reduce((acc, file) => acc + file.size, 0);
    if (totalFileSize > maxAllowedFileSize) {
      throw new BadRequestException('Total file size exceeds the allowed limit.');
    }
   }
    return this.outlookService.updateDraftEmail(id,data,files,user['https://urecruits.com/userId']);
  
  }catch(error){
    Logger.log('Error '+ error)
  }
  }

  @ApiOperation({ summary: 'create the google auth url' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-mails')
  async getMail(@Query() query,@AuthUser() user) {
    return this.outlookService.listMessages(query,user['https://urecruits.com/userId']);
  }

  @ApiOperation({ summary: 'create the google auth url' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/getById/:id')
  async getMailById(@Param('id') id,@AuthUser() user) {
    return this.outlookService.getMailById(id,user['https://urecruits.com/userId']);
  }

  @ApiOperation({ summary: 'create the google auth url' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/delete-mail')
  async deleteMail(@Body() id:[]   ,@AuthUser() user) {
    return this.outlookService.deleteEmail(id,user['https://urecruits.com/userId']);
  }

  @ApiOperation({ summary: 'create the google auth url' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/mark-as-read')
  async markEmailAsRead(@Body() messageIds:[],@AuthUser() user) {
    return this.outlookService.markEmailAsRead(messageIds,user['https://urecruits.com/userId']);
  }  
}
