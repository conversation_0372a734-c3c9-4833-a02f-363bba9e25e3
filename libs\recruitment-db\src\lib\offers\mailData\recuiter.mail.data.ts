const recuiterMailData = (documentName, recipientName, jobTitle, redirectLink) => `<h2>Dear ${recipientName},</h2>
<p><PERSON><PERSON><PERSON> has signed on  the offer letter for ${jobTitle} job.</p>
<p> You need to sign ${documentName} </p>
<p>You can sign digitally via below link.</p>
<br/>
<button style="
font-size: 14px;
font-weight: 900;
line-height: 100%;
color: white;
padding: 16px 28px;
border: none;
cursor: pointer;
background: linear-gradient(125.2deg, #099C73 8.04%, #015462 127.26%);
border-radius: 4px;
">
  <a href="${redirectLink}" style="
      text-decoration: none;
      color: inherit;
      display: block;
      width: 100%;
      height: 100%;
  ">
      Sign document
  </a>
</button>
<br/>`
export default recuiterMailData