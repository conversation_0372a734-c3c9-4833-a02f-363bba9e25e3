import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CodeExecutorService } from './code-executor.service';
import { OpenAIExecutorService } from './openai-executor.service';

@Module({
  imports: [ConfigModule.forRoot(), HttpModule],
  providers: [CodeExecutorService, OpenAIExecutorService],
  exports: [CodeExecutorService, OpenAIExecutorService],
})
export class CodeExecutorModule {}
