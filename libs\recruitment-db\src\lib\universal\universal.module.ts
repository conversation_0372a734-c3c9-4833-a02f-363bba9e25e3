import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { DrugModule } from '../drug-screening/drug.module';
import {BackgroundModule} from '../background-screening/background.module';
import {UniversalService} from '../integrations/universal/universal.service';
import {User} from "../users/users.model";
import {UniversalIntegration} from "../universal/universal.model";
import { HttpService,HttpModule } from "@nestjs/axios";
import { NotifyPermissionsModule } from '../notify-permissions/notify-permissions.module';

@Module({
  imports: [BackgroundModule,DrugModule,HttpModule,NotifyPermissionsModule,SequelizeModule.forFeature([User,UniversalIntegration])],
  providers: [UniversalService],
  exports: [UniversalService],
})
export class UniversalModule {}
