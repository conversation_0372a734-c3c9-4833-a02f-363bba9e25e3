import { ApiProperty } from '@nestjs/swagger';
import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({ modelName: 'googleAuth' })
export class GoogleAuth extends Model<GoogleAuth> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id?: number;

  @ApiProperty({ description: 'Id of specific token' })
  @Column({ type: DataType.STRING })
    tokenId: string;

  @ApiProperty({ example: 'pig latin', description: 'Encoded access token' })
  @Column({ type: DataType.STRING })
    accessToken: string;

  @ApiProperty({ example: 'pig latin', description: 'Encoded refresh token' })
  @Column({ type: DataType.STRING })
    refreshToken: string;

  @ApiProperty({ example: '1', description: 'Id of related user' })
  @Column({ type: DataType.INTEGER })
    userId: number;
}
