import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { ExperienceDto } from "./experience.dto";

export class CreateExperienceDto extends ExperienceDto {
  @ApiPropertyOptional({ example: "1", description: "Recruiter ID" })
  @IsOptional()
  readonly recruiterId?: number;

  @ApiPropertyOptional({ example: "1", description: "Candidate ID" })
  @IsOptional()
  readonly candidateId?: number;
}
