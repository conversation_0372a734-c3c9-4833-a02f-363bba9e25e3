import { <PERSON>, <PERSON>, <PERSON>, Get, Param, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse} from "@nestjs/swagger";
import { UsersService, UpdateUserDto, User } from "@microservices/recruitment-db";
import { ChangePasswordDto } from "./dto/change-password.dto";
import { AuthGuard } from "@nestjs/passport";
import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";

@ApiTags('User')
@Controller('user')
export class UsersController {
  constructor (private readonly usersService: UsersService) {}

  @ApiOperation({ summary: 'Update user' })
  @ApiResponse({ status: 201, type: User })
  @UseGuards(AuthGuard('jwt'))
  @Patch()
  update (@Body() dto: UpdateUserDto, @AuthUser() user: any) {
    return this.usersService.updateUser({...dto, authId: user.sub})
  }

  @ApiOperation({ summary: 'Change password' })
  @ApiResponse({ status: 201, type: User })
  @UseGuards(AuthGuard('jwt'))
  @Patch('/change-password')
  changePassword (@Body() dto: ChangePasswordDto, @AuthUser() user: any) {
    return this.usersService.changePassword({...dto, authId: user.sub})
  }

  @ApiOperation({ summary: 'Get user by email' })
  @ApiResponse({ status: 200, type: User })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/:email')
  getByEmail(@Param('email') email: string) {
    return this.usersService.isExistUser(email);
  }

  @ApiOperation({ summary: 'Get user' })
  @ApiResponse({ status: 200, type: User })
  @UseGuards(AuthGuard('jwt'))
  @Get()
  getUserByUserId(@AuthUser() user: any) {
    return this.usersService.getUserByUserId(user['https://urecruits.com/userId']);
  }
}
