import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Certificate } from "./certificates.model";
import { UpdateCertificateDto } from "./dto/update-certificate.dto";
import { CertificateDto } from "./dto/certificate.dto";

@Injectable()
export class CertificatesService {
  constructor(
    @InjectModel(Certificate) private certificateRepository: typeof Certificate
  ) {}

  async create(candidateId: number, dto?: CertificateDto) {
    return await this.certificateRepository.create({
      ...dto,
      candidateId: candidateId,
    });
  }

  async update(dto: UpdateCertificateDto) {
    const data = await this.get(dto.id, dto.candidateId);
    Object.assign(data, dto);
    return await data.save();
  }

  async delete(dto: UpdateCertificateDto) {
    const data = await this.get(dto.id, dto.candidateId);
    await data.destroy();
    return true;
  }

  async get(id: number, candidateId: number) {
    const data = await this.certificateRepository.findOne({
      where: { id, candidateId },
    });
    if (!data) {
      throw new HttpException("Certificate not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }
}
