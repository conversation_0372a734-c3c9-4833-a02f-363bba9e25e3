import { ApiProperty } from "@nestjs/swagger";

export class CreatePriceDto{
  @ApiProperty({example:'price_xxx',description:"Price Id of the Stripe Product"})
  priceId: string;

  @ApiProperty({example:'prod_xxx',description:"Product Id of the Stripe Product"})
  productId: string;

  @ApiProperty({example:'500',description:"Price of the Stripe Price"})
  price: number;

  @ApiProperty({example:'5',description:"Free quanity incase of addons products"})
  freeQty: number;

  @ApiProperty({example:'price_xxx',description:"Active status of the Stripe price"})
  active: boolean;

  @ApiProperty({example:'price_xxx',description:"Period of the price created for the product"})
  monthlyYearly: string;
}