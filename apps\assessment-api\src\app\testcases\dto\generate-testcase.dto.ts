import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class GenerateTestCaseDto {
    @ApiProperty({
        description: 'Language ID for the programming language',
        example: 69, // Python
    })
    @IsNumber()
    @IsNotEmpty()
    languageId: number;

    @ApiProperty({
        description: 'Description of the coding question',
        example: 'Write a function to find an element in a sorted array and return its index. If the element is not found, return -1.',
    })
    @IsString()
    @IsNotEmpty()
    questionDescription: string;

    @ApiProperty({
        description: 'Name of the coding question',
        example: 'Find Element in Array',
    })
    @IsString()
    @IsNotEmpty()
    nameQuestion: string;
} 