import { ApiProperty } from "@nestjs/swagger";
import { UserDto } from "./user.dto";

export class CreateAuthCandidateDto extends UserDto {
  @ApiProperty({ example: '123456qQ@', description: 'Password' })
  readonly password: string;

  @ApiProperty({ example: '1', description: 'Candidate id' })
  readonly candidateId: number;

  @ApiProperty({ example: '1', description: 'User id' })
  readonly userId: number;
}
