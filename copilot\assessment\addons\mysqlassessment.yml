Parameters:
  App:
    Type: String
    Description: Your application's name.
  Env:
    Type: String
    Description: The environment name your service, job, or workflow is being deployed to.
  Name:
    Type: String
    Description: The name of the service, job, or workflow being deployed.
  # Customize your Aurora Serverless cluster by setting the default value of the following parameters.


Resources:
  mysqlassessmentDBSubnetGroup:
    Type: 'AWS::RDS::DBSubnetGroup'
    Properties:
      DBSubnetGroupDescription: Group of Copilot public subnets for RDS.
      SubnetIds:
        !Split [',', { 'Fn::ImportValue': !Sub '${App}-${Env}-PublicSubnets' }]
  mysqlassessmentSecurityGroup:
    Metadata:
      'aws:copilot:description': 'A security group for your workload to access the DB  mysqlassessment'
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: !Sub 'The Security Group for ${Name} to access DB  mysqlassessment.'
      VpcId:
        Fn::ImportValue:
          !Sub '${App}-${Env}-VpcId'
      Tags:
        - Key: Name
          Value: !Sub 'copilot-${App}-${Env}-${Name}-RDS'
  mysqlassessmentDBClusterSecurityGroup:
    Metadata:
      'aws:copilot:description': 'A security group for your DB  mysqlassessment'
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: The Security Group for the database.
      SecurityGroupIngress:
        - ToPort: 3306
          FromPort: 3306
          IpProtocol: tcp
          Description: !Sub 'From the Aurora Security Group of the workload ${Name}.'
          SourceSecurityGroupId: !Ref mysqlassessmentSecurityGroup
      VpcId:
        Fn::ImportValue:
          !Sub '${App}-${Env}-VpcId'
  mysqlassessmentAuroraSecret:
    Metadata:
      'aws:copilot:description': 'A Secrets Manager secret to store your DB credentials'
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub 'urecruits-${Env}-assessment-RDSMySQL'
      Description: !Sub Aurora main user secret for ${AWS::StackName}
      GenerateSecretString:
        SecretStringTemplate: '{"username": "admin"}'
        GenerateStringKey: "password"
        ExcludePunctuation: true
        IncludeSpace: false
        PasswordLength: 16
  mysqlassessmentDBCluster:
    Metadata:
      'aws:copilot:description': 'The mysqlassessment RDS Instance database'
    Type: 'AWS::RDS::DBInstance'
    Properties:
      MasterUsername:
        !Join [ "",  [ '{{resolve:secretsmanager:', !Ref mysqlassessmentAuroraSecret, ":SecretString:username}}" ]]
      MasterUserPassword:
        !Join [ "",  [ '{{resolve:secretsmanager:', !Ref mysqlassessmentAuroraSecret, ":SecretString:password}}" ]]
      DBInstanceIdentifier: !Sub '${App}-${Env}-${Name}-mysql-db'
      DBName: "mysqldb"
      Engine: MySQL
      EngineVersion: '8.0.23'
      DBInstanceClass: 'db.t3.small'
      AllocatedStorage: 20
      StorageType: gp2
      BackupRetentionPeriod: 7
      PubliclyAccessible: true
      DBSubnetGroupName: !Ref mysqlassessmentDBSubnetGroup
      VPCSecurityGroups:
        - !Ref mysqlassessmentDBClusterSecurityGroup
  mysqlassessmentSecretAuroraClusterAttachment:
    Type: AWS::SecretsManager::SecretTargetAttachment
    Properties:
      SecretId: !Ref mysqlassessmentAuroraSecret
      TargetId: !Ref mysqlassessmentDBCluster
      TargetType: AWS::RDS::DBInstance
Outputs:
  mysqlassessmentSecret: # injected as MYSQLASSESSMENT_SECRET environment variable by Copilot.
    Description: "The JSON secret that holds the database username and password. Fields are 'host', 'port', 'dbname', 'username', 'password', 'dbClusterIdentifier' and 'engine'"
    Value: !Ref mysqlassessmentAuroraSecret
  mysqlassessmentSecurityGroup:
    Description: "The security group to attach to the workload."
    Value: !Ref mysqlassessmentSecurityGroup
