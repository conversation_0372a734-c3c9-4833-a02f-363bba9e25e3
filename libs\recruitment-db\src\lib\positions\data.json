[{"value": "Aba Therapist", "label": "Aba Therapist"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Able Bodied Seaman", "label": "Able Bodied Seaman"}, {"value": "Academic Administrator", "label": "Academic Administrator"}, {"value": "Academic Advisor", "label": "Academic Advisor"}, {"value": "Academic Coach", "label": "Academic Coach"}, {"value": "Academic Coordinator", "label": "Academic Coordinator"}, {"value": "Academic Counselor", "label": "Academic Counselor"}, {"value": "Academic Dean", "label": "Academic Dean"}, {"value": "Academic Director", "label": "Academic Director"}, {"value": "Academic Tutor", "label": "Academic Tutor"}, {"value": "Accessioner", "label": "Accessioner"}, {"value": "Accompanist", "label": "Accompanist"}, {"value": "Account Administrator", "label": "Account Administrator"}, {"value": "Account <PERSON><PERSON><PERSON>", "label": "Account <PERSON><PERSON><PERSON>"}, {"value": "Account Assistant", "label": "Account Assistant"}, {"value": "Account Coordinator", "label": "Account Coordinator"}, {"value": "Account Director", "label": "Account Director"}, {"value": "Account Executive", "label": "Account Executive"}, {"value": "Account Manager", "label": "Account Manager"}, {"value": "Account Representative", "label": "Account Representative"}, {"value": "Account Specialist", "label": "Account Specialist"}, {"value": "Accountant", "label": "Accountant"}, {"value": "Accounting Administrator", "label": "Accounting Administrator"}, {"value": "Accounting Analyst", "label": "Accounting Analyst"}, {"value": "Accounting Assistant", "label": "Accounting Assistant"}, {"value": "Accounting Associate", "label": "Accounting Associate"}, {"value": "Accounting Auditor", "label": "Accounting Auditor"}, {"value": "Accounting Clerk", "label": "Accounting Clerk"}, {"value": "Accounting Consultant", "label": "Accounting Consultant"}, {"value": "Accounting Coordinator", "label": "Accounting Coordinator"}, {"value": "Accounting Director", "label": "Accounting Director"}, {"value": "Accounting Instructor", "label": "Accounting Instructor"}, {"value": "Accounting Manager", "label": "Accounting Manager"}, {"value": "Accounting Professor", "label": "Accounting Professor"}, {"value": "Accounting Specialist", "label": "Accounting Specialist"}, {"value": "Accounting Supervisor", "label": "Accounting Supervisor"}, {"value": "Accounting Technician", "label": "Accounting Technician"}, {"value": "Accounting Tutor", "label": "Accounting Tutor"}, {"value": "Accounts Payable", "label": "Accounts Payable"}, {"value": "Accounts Payable Analyst", "label": "Accounts Payable Analyst"}, {"value": "Accounts Payable Assistant", "label": "Accounts Payable Assistant"}, {"value": "Accounts Payable Clerk", "label": "Accounts Payable Clerk"}, {"value": "Accounts Payable Coordinator", "label": "Accounts Payable Coordinator"}, {"value": "Accounts Payable Manager", "label": "Accounts Payable Manager"}, {"value": "Accounts Payable Specialist", "label": "Accounts Payable Specialist"}, {"value": "Accounts Payable Supervisor", "label": "Accounts Payable Supervisor"}, {"value": "Accounts Receivable Clerk", "label": "Accounts Receivable Clerk"}, {"value": "Accounts Receivable Manager", "label": "Accounts Receivable Manager"}, {"value": "Accounts Receivable Specialist", "label": "Accounts Receivable Specialist"}, {"value": "Acquisition Specialist", "label": "Acquisition Specialist"}, {"value": "Activist", "label": "Activist"}, {"value": "Activities Assistant", "label": "Activities Assistant"}, {"value": "Activities Coordinator", "label": "Activities Coordinator"}, {"value": "Activities Director", "label": "Activities Director"}, {"value": "Activity Assistant", "label": "Activity Assistant"}, {"value": "Activity Coordinator", "label": "Activity Coordinator"}, {"value": "Actor", "label": "Actor"}, {"value": "Actuarial Analyst", "label": "Actuarial Analyst"}, {"value": "Actuary", "label": "Actuary"}, {"value": "Acupuncturist", "label": "Acupuncturist"}, {"value": "Acute Care Nurse Practitioner", "label": "Acute Care Nurse Practitioner"}, {"value": "Addiction Counselor", "label": "Addiction Counselor"}, {"value": "Adjudicator", "label": "Adjudicator"}, {"value": "Adjunct Faculty", "label": "Adjunct Faculty"}, {"value": "Adjunct Instructor", "label": "Adjunct Instructor"}, {"value": "Adjunct Nursing Faculty", "label": "Adjunct Nursing Faculty"}, {"value": "Adjunct Professor", "label": "Adjunct Professor"}, {"value": "Adjuster", "label": "Adjuster"}, {"value": "Admin", "label": "Admin"}, {"value": "Administration", "label": "Administration"}, {"value": "Administration Manager", "label": "Administration Manager"}, {"value": "Administrative Analyst", "label": "Administrative Analyst"}, {"value": "Administrative Assistant", "label": "Administrative Assistant"}, {"value": "Administrative Associate", "label": "Administrative Associate"}, {"value": "Administrative Clerk", "label": "Administrative Clerk"}, {"value": "Administrative Coordinator", "label": "Administrative Coordinator"}, {"value": "Administrative Director", "label": "Administrative Director"}, {"value": "Administrative Law Judge", "label": "Administrative Law Judge"}, {"value": "Administrative Manager", "label": "Administrative Manager"}, {"value": "Administrative Officer", "label": "Administrative Officer"}, {"value": "Administrative Secretary", "label": "Administrative Secretary"}, {"value": "Administrative Services Manager", "label": "Administrative Services Manager"}, {"value": "Administrative Specialist", "label": "Administrative Specialist"}, {"value": "Administrative Supervisor", "label": "Administrative Supervisor"}, {"value": "Administrator", "label": "Administrator"}, {"value": "Admissions Advisor", "label": "Admissions Advisor"}, {"value": "Admissions Assistant", "label": "Admissions Assistant"}, {"value": "Admissions Coordinator", "label": "Admissions Coordinator"}, {"value": "Admissions Counselor", "label": "Admissions Counselor"}, {"value": "Admissions Officer", "label": "Admissions Officer"}, {"value": "Admissions Representative", "label": "Admissions Representative"}, {"value": "Admitting Clerk", "label": "Admitting Clerk"}, {"value": "Adoption Social Worker", "label": "Adoption Social Worker"}, {"value": "Adoption Specialist", "label": "Adoption Specialist"}, {"value": "Advertising Account Executive", "label": "Advertising Account Executive"}, {"value": "Advertising Account Manager", "label": "Advertising Account Manager"}, {"value": "Advertising Assistant", "label": "Advertising Assistant"}, {"value": "Advertising Consultant", "label": "Advertising Consultant"}, {"value": "Advertising Coordinator", "label": "Advertising Coordinator"}, {"value": "Advertising Director", "label": "Advertising Director"}, {"value": "Advertising Executive", "label": "Advertising Executive"}, {"value": "Advertising Manager", "label": "Advertising Manager"}, {"value": "Advertising Sales", "label": "Advertising Sales"}, {"value": "Advertising Sales Agent", "label": "Advertising Sales Agent"}, {"value": "Advertising Sales Representative", "label": "Advertising Sales Representative"}, {"value": "Advocate", "label": "Advocate"}, {"value": "Aerial Lineman", "label": "Aerial Lineman"}, {"value": "Aerodynamics Engineer", "label": "Aerodynamics Engineer"}, {"value": "Aeronautical Engineer", "label": "Aeronautical Engineer"}, {"value": "Aerospace Engineer", "label": "Aerospace Engineer"}, {"value": "Aerospace Technician", "label": "Aerospace Technician"}, {"value": "Aesthetic Nurse", "label": "Aesthetic Nurse"}, {"value": "Aesthetician", "label": "Aesthetician"}, {"value": "Affiliate Manager", "label": "Affiliate Manager"}, {"value": "After School Teacher", "label": "After School Teacher"}, {"value": "Agency Nurse", "label": "Agency Nurse"}, {"value": "Agent", "label": "Agent"}, {"value": "Agile Coach", "label": "Agile Coach"}, {"value": "Agile Project Manager", "label": "Agile Project Manager"}, {"value": "Agricultural Engineer", "label": "Agricultural Engineer"}, {"value": "Agriculture Teacher", "label": "Agriculture Teacher"}, {"value": "Agronomist", "label": "Agronomist"}, {"value": "Aide", "label": "Aide"}, {"value": "Air Marshal", "label": "Air Marshal"}, {"value": "Air Traffic Controller", "label": "Air Traffic Controller"}, {"value": "Aircraft Assembler", "label": "Aircraft Assembler"}, {"value": "Aircraft Cleaner", "label": "Aircraft Cleaner"}, {"value": "Aircraft Dispatcher", "label": "Aircraft Dispatcher"}, {"value": "Aircraft Electrician", "label": "Aircraft Electrician"}, {"value": "Aircraft Engineer", "label": "Aircraft Engineer"}, {"value": "Aircraft Fueler", "label": "Aircraft Fueler"}, {"value": "Aircraft Inspector", "label": "Aircraft Inspector"}, {"value": "Aircraft Maintenance Technician", "label": "Aircraft Maintenance Technician"}, {"value": "Aircraft Mechanic", "label": "Aircraft Mechanic"}, {"value": "Aircraft Painter", "label": "Aircraft Painter"}, {"value": "Aircraft Technician", "label": "Aircraft Technician"}, {"value": "Airport", "label": "Airport"}, {"value": "Airport Attendant", "label": "Airport Attendant"}, {"value": "Airport Manager", "label": "Airport Manager"}, {"value": "Alarm Technician", "label": "Alarm Technician"}, {"value": "Allocation Analyst", "label": "Allocation Analyst"}, {"value": "Ambassador", "label": "Ambassador"}, {"value": "Ambulance Driver", "label": "Ambulance Driver"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Analog Design Engineer", "label": "Analog Design Engineer"}, {"value": "Analyst", "label": "Analyst"}, {"value": "Analytical Chemist", "label": "Analytical Chemist"}, {"value": "Analytical Scientist", "label": "Analytical Scientist"}, {"value": "Analytics Consultant", "label": "Analytics Consultant"}, {"value": "Analytics Manager", "label": "Analytics Manager"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Android Developer", "label": "Android Developer"}, {"value": "Anesthesia Technician", "label": "Anesthesia Technician"}, {"value": "Anesthesiologist", "label": "Anesthesiologist"}, {"value": "Anesthesiologist Assistant", "label": "Anesthesiologist Assistant"}, {"value": "Animal Care Technician", "label": "Animal Care Technician"}, {"value": "Animal Caretaker", "label": "Animal Caretaker"}, {"value": "Animal Control Officer", "label": "Animal Control Officer"}, {"value": "Animal Cruelty Investigator", "label": "Animal Cruelty Investigator"}, {"value": "Animal Keeper", "label": "Animal Keeper"}, {"value": "Animal Technician", "label": "Animal Technician"}, {"value": "Animator", "label": "Animator"}, {"value": "Announcer", "label": "Announcer"}, {"value": "An<PERSON>na Engineer", "label": "An<PERSON>na Engineer"}, {"value": "Apartment Leasing Agent", "label": "Apartment Leasing Agent"}, {"value": "Apartment Locator", "label": "Apartment Locator"}, {"value": "Apartment Manager", "label": "Apartment Manager"}, {"value": "Apartment Property Manager", "label": "Apartment Property Manager"}, {"value": "Apparel Designer", "label": "Apparel Designer"}, {"value": "Application Developer", "label": "Application Developer"}, {"value": "Applications Engineer", "label": "Applications Engineer"}, {"value": "Appointment Scheduler", "label": "Appointment Scheduler"}, {"value": "Appointment Setter", "label": "Appointment Setter"}, {"value": "Appraiser", "label": "Appraiser"}, {"value": "Apprentice", "label": "Apprentice"}, {"value": "Apprentice Carpenter", "label": "Apprentice Carpenter"}, {"value": "Apprentice Electrician", "label": "Apprentice Electrician"}, {"value": "Aquatics Director", "label": "Aquatics Director"}, {"value": "Arabic Linguist", "label": "Arabic Linguist"}, {"value": "Arabic Teacher", "label": "Arabic Teacher"}, {"value": "Arborist", "label": "Arborist"}, {"value": "Archaeologist", "label": "Archaeologist"}, {"value": "Architect", "label": "Architect"}, {"value": "Architectural Designer", "label": "Architectural Designer"}, {"value": "Architectural Drafter", "label": "Architectural Drafter"}, {"value": "Architectural Draftsman", "label": "Architectural Draftsman"}, {"value": "Architectural Engineer", "label": "Architectural Engineer"}, {"value": "Architectural Project Manager", "label": "Architectural Project Manager"}, {"value": "Archivist", "label": "Archivist"}, {"value": "Area Manager", "label": "Area Manager"}, {"value": "Area Supervisor", "label": "Area Supervisor"}, {"value": "Armed Guard", "label": "Armed Guard"}, {"value": "Armed Security", "label": "Armed Security"}, {"value": "Armed Security Officer", "label": "Armed Security Officer"}, {"value": "Armored Truck Driver", "label": "Armored Truck Driver"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Army Ranger", "label": "Army Ranger"}, {"value": "Arson Investigator", "label": "Arson Investigator"}, {"value": "Art Assistant", "label": "Art Assistant"}, {"value": "Art Consultant", "label": "Art Consultant"}, {"value": "Art Curator", "label": "Art Curator"}, {"value": "Art Director", "label": "Art Director"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Art Instructor", "label": "Art Instructor"}, {"value": "Art Professor", "label": "Art Professor"}, {"value": "Art Teacher", "label": "Art Teacher"}, {"value": "Art Therapist", "label": "Art Therapist"}, {"value": "Artist", "label": "Artist"}, {"value": "Artist Assistant", "label": "Artist Assistant"}, {"value": "Artist Manager", "label": "Artist Manager"}, {"value": "Artistic Director", "label": "Artistic Director"}, {"value": "Arts Manager", "label": "Arts Manager"}, {"value": "<PERSON>ic Design Engineer", "label": "<PERSON>ic Design Engineer"}, {"value": "Asp.Net Developer", "label": "Asp.Net Developer"}, {"value": "Assembler", "label": "Assembler"}, {"value": "Assembly Line Worker", "label": "Assembly Line Worker"}, {"value": "Assembly Operator", "label": "Assembly Operator"}, {"value": "Assembly Technician", "label": "Assembly Technician"}, {"value": "Assembly Worker", "label": "Assembly Worker"}, {"value": "Assessor", "label": "Assessor"}, {"value": "Asset Management Analyst", "label": "Asset Management Analyst"}, {"value": "Asset Manager", "label": "Asset Manager"}, {"value": "Asset Protection Manager", "label": "Asset Protection Manager"}, {"value": "Assignment Editor", "label": "Assignment Editor"}, {"value": "Assistant", "label": "Assistant"}, {"value": "Assistant Account Executive", "label": "Assistant Account Executive"}, {"value": "Assistant Administrator", "label": "Assistant Administrator"}, {"value": "Assistant Art Director", "label": "Assistant Art Director"}, {"value": "Assistant Athletic Director", "label": "Assistant Athletic Director"}, {"value": "Assistant Basketball Coach", "label": "Assistant Basketball Coach"}, {"value": "Assistant Bookkeeper", "label": "Assistant Bookkeeper"}, {"value": "Assistant Branch Manager", "label": "Assistant Branch Manager"}, {"value": "Assistant Brand Manager", "label": "Assistant Brand Manager"}, {"value": "Assistant Buyer", "label": "Assistant Buyer"}, {"value": "Assistant Chief Engineer", "label": "Assistant Chief Engineer"}, {"value": "Assistant Coach", "label": "Assistant Coach"}, {"value": "Assistant Community Manager", "label": "Assistant Community Manager"}, {"value": "Assistant Construction Manager", "label": "Assistant Construction Manager"}, {"value": "Assistant Controller", "label": "Assistant Controller"}, {"value": "Assistant Cook", "label": "Assistant Cook"}, {"value": "Assistant <PERSON><PERSON>tor", "label": "Assistant <PERSON><PERSON>tor"}, {"value": "Assistant Dean", "label": "Assistant Dean"}, {"value": "Assistant Designer", "label": "Assistant Designer"}, {"value": "Assistant Director", "label": "Assistant Director"}, {"value": "Assistant Director Of Nursing", "label": "Assistant Director Of Nursing"}, {"value": "Assistant District Attorney", "label": "Assistant District Attorney"}, {"value": "Assistant Editor", "label": "Assistant Editor"}, {"value": "Assistant Engineer", "label": "Assistant Engineer"}, {"value": "Assistant Facility Manager", "label": "Assistant Facility Manager"}, {"value": "Assistant Fashion Designer", "label": "Assistant Fashion Designer"}, {"value": "Assistant Football Coach", "label": "Assistant Football Coach"}, {"value": "Assistant Front Office Manager", "label": "Assistant Front Office Manager"}, {"value": "Assistant General Manager", "label": "Assistant General Manager"}, {"value": "Assistant Golf Professional", "label": "Assistant Golf Professional"}, {"value": "Assistant Hotel Manager", "label": "Assistant Hotel Manager"}, {"value": "Assistant <PERSON><PERSON><PERSON>", "label": "Assistant <PERSON><PERSON><PERSON>"}, {"value": "Assistant Manager", "label": "Assistant Manager"}, {"value": "Assistant Marketing Manager", "label": "Assistant Marketing Manager"}, {"value": "Assistant Media Planner", "label": "Assistant Media Planner"}, {"value": "Assistant Nurse Manager", "label": "Assistant Nurse Manager"}, {"value": "Assistant Office Manager", "label": "Assistant Office Manager"}, {"value": "Assistant Operations Manager", "label": "Assistant Operations Manager"}, {"value": "Assistant Portfolio Manager", "label": "Assistant Portfolio Manager"}, {"value": "Assistant Principal", "label": "Assistant Principal"}, {"value": "Assistant Producer", "label": "Assistant Producer"}, {"value": "Assistant Product Manager", "label": "Assistant Product Manager"}, {"value": "Assistant Professor", "label": "Assistant Professor"}, {"value": "Assistant Project Manager", "label": "Assistant Project Manager"}, {"value": "Assistant Property Manager", "label": "Assistant Property Manager"}, {"value": "Assistant Registrar", "label": "Assistant Registrar"}, {"value": "Assistant Restaurant Manager", "label": "Assistant Restaurant Manager"}, {"value": "Assistant Retail Manager", "label": "Assistant Retail Manager"}, {"value": "Assistant Sales Manager", "label": "Assistant Sales Manager"}, {"value": "Assistant Soccer Coach", "label": "Assistant Soccer Coach"}, {"value": "Assistant Store Manager", "label": "Assistant Store Manager"}, {"value": "Assistant Superintendent", "label": "Assistant Superintendent"}, {"value": "Assistant Teacher", "label": "Assistant Teacher"}, {"value": "Assistant Video Editor", "label": "Assistant Video Editor"}, {"value": "Assisted Living Administrator", "label": "Assisted Living Administrator"}, {"value": "Associate", "label": "Associate"}, {"value": "Associate Accountant", "label": "Associate Accountant"}, {"value": "Associate Attorney", "label": "Associate Attorney"}, {"value": "Associate Brand Manager", "label": "Associate Brand Manager"}, {"value": "Associate Buyer", "label": "Associate Buyer"}, {"value": "Associate Consultant", "label": "Associate Consultant"}, {"value": "Associate Creative Director", "label": "Associate Creative Director"}, {"value": "Associate Dean", "label": "Associate Dean"}, {"value": "Associate Dentist", "label": "Associate Dentist"}, {"value": "Associate Editor", "label": "Associate Editor"}, {"value": "Associate Marketing Manager", "label": "Associate Marketing Manager"}, {"value": "Associate Pastor", "label": "Associate Pastor"}, {"value": "Associate Producer", "label": "Associate Producer"}, {"value": "Associate Product Manager", "label": "Associate Product Manager"}, {"value": "Associate Professor", "label": "Associate Professor"}, {"value": "Associate Project Manager", "label": "Associate Project Manager"}, {"value": "Associate Scientist", "label": "Associate Scientist"}, {"value": "Associate Software Engineer", "label": "Associate Software Engineer"}, {"value": "Associate Veterinarian", "label": "Associate Veterinarian"}, {"value": "Astronomer", "label": "Astronomer"}, {"value": "Athlete", "label": "Athlete"}, {"value": "Athletic Coordinator", "label": "Athletic Coordinator"}, {"value": "Athletic Director", "label": "Athletic Director"}, {"value": "Athletic Trainer", "label": "Athletic Trainer"}, {"value": "Atm Technician", "label": "Atm Technician"}, {"value": "Atmospheric Scientist", "label": "Atmospheric Scientist"}, {"value": "Attorney", "label": "Attorney"}, {"value": "Attorney General", "label": "Attorney General"}, {"value": "Au Pair", "label": "Au Pair"}, {"value": "Auctioneer", "label": "Auctioneer"}, {"value": "Audio Engineer", "label": "Audio Engineer"}, {"value": "Audio Technician", "label": "Audio Technician"}, {"value": "Audio Visual Technician", "label": "Audio Visual Technician"}, {"value": "Audiologist", "label": "Audiologist"}, {"value": "Audiology Assistant", "label": "Audiology Assistant"}, {"value": "Audit Associate", "label": "Audit Associate"}, {"value": "Audit Director", "label": "Audit Director"}, {"value": "Audit Manager", "label": "Audit Manager"}, {"value": "Audit Supervisor", "label": "Audit Supervisor"}, {"value": "Auditor", "label": "Auditor"}, {"value": "Author", "label": "Author"}, {"value": "Auto Appraiser", "label": "Auto Appraiser"}, {"value": "Auto Body Painter", "label": "Auto Body Painter"}, {"value": "Auto Body Technician", "label": "Auto Body Technician"}, {"value": "Auto Claims Adjuster", "label": "Auto Claims Adjuster"}, {"value": "Auto Damage Appraiser", "label": "Auto Damage Appraiser"}, {"value": "Auto Detailer", "label": "Auto Detailer"}, {"value": "Auto Glass Technician", "label": "Auto Glass Technician"}, {"value": "Auto Mechanic", "label": "Auto Mechanic"}, {"value": "Auto Sales", "label": "Auto Sales"}, {"value": "Auto Technician", "label": "Auto Technician"}, {"value": "Autocad Designer", "label": "Autocad Designer"}, {"value": "Autocad Drafter", "label": "Autocad Drafter"}, {"value": "Autocad Draftsman", "label": "Autocad Draftsman"}, {"value": "Automation Engineer", "label": "Automation Engineer"}, {"value": "Automation Specialist", "label": "Automation Specialist"}, {"value": "Automation Technician", "label": "Automation Technician"}, {"value": "Automation Test Engineer", "label": "Automation Test Engineer"}, {"value": "Automation Tester", "label": "Automation Tester"}, {"value": "Automotive Detailer", "label": "Automotive Detailer"}, {"value": "Automotive Engineer", "label": "Automotive Engineer"}, {"value": "Automotive Instructor", "label": "Automotive Instructor"}, {"value": "Automotive Manager", "label": "Automotive Manager"}, {"value": "Automotive Mechanic", "label": "Automotive Mechanic"}, {"value": "Automotive Painter", "label": "Automotive Painter"}, {"value": "Automotive Sales Manager", "label": "Automotive Sales Manager"}, {"value": "Automotive Service Advisor", "label": "Automotive Service Advisor"}, {"value": "Automotive Service Manager", "label": "Automotive Service Manager"}, {"value": "Automotive Service Technician", "label": "Automotive Service Technician"}, {"value": "Automotive Technician", "label": "Automotive Technician"}, {"value": "Aviation Maintenance Technician", "label": "Aviation Maintenance Technician"}, {"value": "Aviation Manager", "label": "Aviation Manager"}, {"value": "Aviation Mechanic", "label": "Aviation Mechanic"}, {"value": "Aviation Technician", "label": "Aviation Technician"}, {"value": "Avionics Engineer", "label": "Avionics Engineer"}, {"value": "Avionics Technician", "label": "Avionics Technician"}, {"value": "Babysitter", "label": "Babysitter"}, {"value": "Backend Developer", "label": "Backend Developer"}, {"value": "Background Investigator", "label": "Background Investigator"}, {"value": "Backup Administrator", "label": "Backup Administrator"}, {"value": "Baggage Handler", "label": "Baggage Handler"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON> Assistant", "label": "<PERSON><PERSON> Assistant"}, {"value": "<PERSON><PERSON> Clerk", "label": "<PERSON><PERSON> Clerk"}, {"value": "Bakery Manager", "label": "Bakery Manager"}, {"value": "Ballet Teacher", "label": "Ballet Teacher"}, {"value": "Band Director", "label": "Band Director"}, {"value": "Bank Examiner", "label": "Bank Examiner"}, {"value": "Bank Manager", "label": "Bank Manager"}, {"value": "Bank Teller", "label": "Bank Teller"}, {"value": "Banker", "label": "Banker"}, {"value": "Banking Analyst", "label": "Banking Analyst"}, {"value": "Banking Consultant", "label": "Banking Consultant"}, {"value": "Banking Manager", "label": "Banking Manager"}, {"value": "Banking Officer", "label": "Banking Officer"}, {"value": "Bankruptcy Paralegal", "label": "Bankruptcy Paralegal"}, {"value": "Bankruptcy Specialist", "label": "Bankruptcy Specialist"}, {"value": "Banquet <PERSON>ender", "label": "Banquet <PERSON>ender"}, {"value": "Banquet Captain", "label": "Banquet Captain"}, {"value": "Banquet Chef", "label": "Banquet Chef"}, {"value": "Banquet Manager", "label": "Banquet Manager"}, {"value": "Banquet Server", "label": "Banquet Server"}, {"value": "Bar Manager", "label": "Bar Manager"}, {"value": "Bar Server", "label": "Bar Server"}, {"value": "<PERSON>back", "label": "<PERSON>back"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Barista", "label": "Barista"}, {"value": "Bartender", "label": "Bartender"}, {"value": "Baseball Coach", "label": "Baseball Coach"}, {"value": "Basketball Coach", "label": "Basketball Coach"}, {"value": "Basketball Referee", "label": "Basketball Referee"}, {"value": "Beach Attendant", "label": "Beach Attendant"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Beauty Advisor", "label": "Beauty Advisor"}, {"value": "Beauty Consultant", "label": "Beauty Consultant"}, {"value": "Behavior Analyst", "label": "Behavior Analyst"}, {"value": "Behavior Interventionist", "label": "Behavior Interventionist"}, {"value": "Behavior Specialist", "label": "Behavior Specialist"}, {"value": "Behavior Technician", "label": "Behavior Technician"}, {"value": "Behavioral Health Technician", "label": "Behavioral Health Technician"}, {"value": "Behavioral Interventionist", "label": "Behavioral Interventionist"}, {"value": "Behavioral Specialist", "label": "Behavioral Specialist"}, {"value": "Behavioral Technician", "label": "Behavioral Technician"}, {"value": "Behavioral Therapist", "label": "Behavioral Therapist"}, {"value": "Bellhop", "label": "Bellhop"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Bench Jeweler", "label": "Bench Jeweler"}, {"value": "Bench Technician", "label": "Bench Technician"}, {"value": "Benefits Administrator", "label": "Benefits Administrator"}, {"value": "Benefits Analyst", "label": "Benefits Analyst"}, {"value": "Benefits Consultant", "label": "Benefits Consultant"}, {"value": "Benefits Coordinator", "label": "Benefits Coordinator"}, {"value": "Benefits Manager", "label": "Benefits Manager"}, {"value": "Benefits Specialist", "label": "Benefits Specialist"}, {"value": "Beta Tester", "label": "Beta Tester"}, {"value": "Beverage Cart Attendant", "label": "Beverage Cart Attendant"}, {"value": "Beverage Manager", "label": "Beverage Manager"}, {"value": "Beverage Server", "label": "Beverage Server"}, {"value": "<PERSON><PERSON>ly<PERSON>", "label": "<PERSON><PERSON>ly<PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Bid Manager", "label": "Bid Manager"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Bilingual", "label": "Bilingual"}, {"value": "Bilingual Customer Service", "label": "Bilingual Customer Service"}, {"value": "Bilingual Teacher", "label": "Bilingual Teacher"}, {"value": "Billing Analyst", "label": "Billing Analyst"}, {"value": "Billing Assistant", "label": "Billing Assistant"}, {"value": "Billing Clerk", "label": "Billing Clerk"}, {"value": "Billing Coordinator", "label": "Billing Coordinator"}, {"value": "Billing Manager", "label": "Billing Manager"}, {"value": "Billing Representative", "label": "Billing Representative"}, {"value": "Billing Specialist", "label": "Billing Specialist"}, {"value": "Billing Supervisor", "label": "Billing Supervisor"}, {"value": "Bindery Operator", "label": "Bindery Operator"}, {"value": "Biochemical Engineer", "label": "Biochemical Engineer"}, {"value": "Biochemist", "label": "Biochemist"}, {"value": "Bioinformatician", "label": "Bioinformatician"}, {"value": "Bioinformatics", "label": "Bioinformatics"}, {"value": "Biological Scientist", "label": "Biological Scientist"}, {"value": "Biological Technician", "label": "Biological Technician"}, {"value": "Biologist", "label": "Biologist"}, {"value": "Biology Instructor", "label": "Biology Instructor"}, {"value": "Biology Teacher", "label": "Biology Teacher"}, {"value": "Biology Tutor", "label": "Biology Tutor"}, {"value": "Biomedical Engineer", "label": "Biomedical Engineer"}, {"value": "Biomedical Equipment Technician", "label": "Biomedical Equipment Technician"}, {"value": "Biomedical Scientist", "label": "Biomedical Scientist"}, {"value": "Biomedical Technician", "label": "Biomedical Technician"}, {"value": "Biostatistician", "label": "Biostatistician"}, {"value": "Biotechnology", "label": "Biotechnology"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Biztalk Developer", "label": "Biztalk Developer"}, {"value": "Blackjack Dealer", "label": "Blackjack Dealer"}, {"value": "Blog Writer", "label": "Blog Writer"}, {"value": "Blogger", "label": "Blogger"}, {"value": "Board Operator", "label": "Board Operator"}, {"value": "Boat Captain", "label": "Boat Captain"}, {"value": "Body Shop Estimator", "label": "Body Shop Estimator"}, {"value": "Body Shop Manager", "label": "Body Shop Manager"}, {"value": "Bodyguard", "label": "Bodyguard"}, {"value": "Boiler Operator", "label": "Boiler Operator"}, {"value": "Boilermaker", "label": "Boilermaker"}, {"value": "Book Editor", "label": "Book Editor"}, {"value": "Book Reviewer", "label": "Book Reviewer"}, {"value": "Booking Agent", "label": "Booking Agent"}, {"value": "Bookkeeper", "label": "Bookkeeper"}, {"value": "Bookkeeping", "label": "Bookkeeping"}, {"value": "Bookseller", "label": "Bookseller"}, {"value": "Bookstore", "label": "Bookstore"}, {"value": "Bookstore Manager", "label": "Bookstore Manager"}, {"value": "Border Patrol Agent", "label": "Border Patrol Agent"}, {"value": "Botani<PERSON>", "label": "Botani<PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Box Office Manager", "label": "Box Office Manager"}, {"value": "Box Truck Driver", "label": "Box Truck Driver"}, {"value": "Branch Manager", "label": "Branch Manager"}, {"value": "Branch Office Administrator", "label": "Branch Office Administrator"}, {"value": "Brand Ambassador", "label": "Brand Ambassador"}, {"value": "Brand Consultant", "label": "Brand Consultant"}, {"value": "Brand Director", "label": "Brand Director"}, {"value": "Brand Manager", "label": "Brand Manager"}, {"value": "Brand Marketing Manager", "label": "Brand Marketing Manager"}, {"value": "Brand Representative", "label": "Brand Representative"}, {"value": "Brand Specialist", "label": "Brand Specialist"}, {"value": "Brand Strategist", "label": "Brand Strategist"}, {"value": "Breakfast Attendant", "label": "Breakfast Attendant"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Brick Mason", "label": "Brick Mason"}, {"value": "Bricklayer", "label": "Bricklayer"}, {"value": "Bridal Consultant", "label": "Bridal Consultant"}, {"value": "Bridge Engineer", "label": "Bridge Engineer"}, {"value": "Bridge Inspector", "label": "Bridge Inspector"}, {"value": "Broadband Technician", "label": "Broadband Technician"}, {"value": "Broadcast Engineer", "label": "Broadcast Engineer"}, {"value": "Broadcast Journalist", "label": "Broadcast Journalist"}, {"value": "Broadcast Producer", "label": "Broadcast Producer"}, {"value": "Broadcast Technician", "label": "Broadcast Technician"}, {"value": "Broker", "label": "Broker"}, {"value": "Broker Assistant", "label": "Broker Assistant"}, {"value": "Budget Analyst", "label": "Budget Analyst"}, {"value": "Budget Manager", "label": "Budget Manager"}, {"value": "Budget Officer", "label": "Budget Officer"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Build Engineer", "label": "Build Engineer"}, {"value": "Builder", "label": "Builder"}, {"value": "Building Engineer", "label": "Building Engineer"}, {"value": "Building Inspector", "label": "Building Inspector"}, {"value": "Building Maintenance", "label": "Building Maintenance"}, {"value": "Building Manager", "label": "Building Manager"}, {"value": "Building Official", "label": "Building Official"}, {"value": "Building Superintendent", "label": "Building Superintendent"}, {"value": "Bursar", "label": "Bursar"}, {"value": "Bus Aide", "label": "Bus Aide"}, {"value": "Bus Driver", "label": "Bus Driver"}, {"value": "Bus Monitor", "label": "Bus Monitor"}, {"value": "Bus Operator", "label": "Bus Operator"}, {"value": "Busboy", "label": "Busboy"}, {"value": "Business Administrator", "label": "Business Administrator"}, {"value": "Business Advisor", "label": "Business Advisor"}, {"value": "Business Analyst", "label": "Business Analyst"}, {"value": "Business Analyst Manager", "label": "Business Analyst Manager"}, {"value": "Business Architect", "label": "Business Architect"}, {"value": "Business Assistant", "label": "Business Assistant"}, {"value": "Business Associate", "label": "Business Associate"}, {"value": "Business Banker", "label": "Business Banker"}, {"value": "Business Banking Relationship Manager", "label": "Business Banking Relationship Manager"}, {"value": "Business Consultant", "label": "Business Consultant"}, {"value": "Business Continuity Manager", "label": "Business Continuity Manager"}, {"value": "Business Controller", "label": "Business Controller"}, {"value": "Business Coordinator", "label": "Business Coordinator"}, {"value": "Business Data Analyst", "label": "Business Data Analyst"}, {"value": "Business Developer", "label": "Business Developer"}, {"value": "Business Development Analyst", "label": "Business Development Analyst"}, {"value": "Business Development Associate", "label": "Business Development Associate"}, {"value": "Business Development Consultant", "label": "Business Development Consultant"}, {"value": "Business Development Coordinator", "label": "Business Development Coordinator"}, {"value": "Business Development Director", "label": "Business Development Director"}, {"value": "Business Development Executive", "label": "Business Development Executive"}, {"value": "Business Development Manager", "label": "Business Development Manager"}, {"value": "Business Development Officer", "label": "Business Development Officer"}, {"value": "Business Development Representative", "label": "Business Development Representative"}, {"value": "Business Development Specialist", "label": "Business Development Specialist"}, {"value": "Business Director", "label": "Business Director"}, {"value": "Business Instructor", "label": "Business Instructor"}, {"value": "Business Intelligence Analyst", "label": "Business Intelligence Analyst"}, {"value": "Business Intelligence Consultant", "label": "Business Intelligence Consultant"}, {"value": "Business Intelligence Developer", "label": "Business Intelligence Developer"}, {"value": "Business Intelligence Manager", "label": "Business Intelligence Manager"}, {"value": "Business Leader", "label": "Business Leader"}, {"value": "Business Management Consultant", "label": "Business Management Consultant"}, {"value": "Business Manager", "label": "Business Manager"}, {"value": "Business Objects Developer", "label": "Business Objects Developer"}, {"value": "Business Office Manager", "label": "Business Office Manager"}, {"value": "Business Operations Analyst", "label": "Business Operations Analyst"}, {"value": "Business Operations Manager", "label": "Business Operations Manager"}, {"value": "Business Operations Specialist", "label": "Business Operations Specialist"}, {"value": "Business Partner", "label": "Business Partner"}, {"value": "Business Process Analyst", "label": "Business Process Analyst"}, {"value": "Business Process Consultant", "label": "Business Process Consultant"}, {"value": "Business Process Manager", "label": "Business Process Manager"}, {"value": "Business Project Manager", "label": "Business Project Manager"}, {"value": "Business Relationship Manager", "label": "Business Relationship Manager"}, {"value": "Business Specialist", "label": "Business Specialist"}, {"value": "Business Strategist", "label": "Business Strategist"}, {"value": "Business Support", "label": "Business Support"}, {"value": "Business Systems Analyst", "label": "Business Systems Analyst"}, {"value": "Business Teacher", "label": "Business Teacher"}, {"value": "Business Unit Manager", "label": "Business Unit Manager"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Buyer", "label": "Buyer"}, {"value": "Buyer Planner", "label": "Buyer Planner"}, {"value": "ci-developer", "label": "C Developer"}, {"value": "sharp-developer", "label": "C# Developer"}, {"value": "c-plus-plus-developer", "label": "C++ Developer"}, {"value": "<PERSON><PERSON> <PERSON>", "label": "<PERSON><PERSON> <PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Cabinet Installer", "label": "Cabinet Installer"}, {"value": "Cabinet Maker", "label": "Cabinet Maker"}, {"value": "Cable Installation Technician", "label": "Cable Installation Technician"}, {"value": "Cable Technician", "label": "Cable Technician"}, {"value": "Cad Designer", "label": "Cad Designer"}, {"value": "<PERSON>ad Drafter", "label": "<PERSON>ad Drafter"}, {"value": "Cad Engineer", "label": "Cad Engineer"}, {"value": "Cad Operator", "label": "Cad Operator"}, {"value": "<PERSON>ad Technician", "label": "<PERSON>ad Technician"}, {"value": "Cafe Manager", "label": "Cafe Manager"}, {"value": "Cake Decorator", "label": "Cake Decorator"}, {"value": "Calibration Technician", "label": "Calibration Technician"}, {"value": "Call Center", "label": "Call Center"}, {"value": "Call Center Agent", "label": "Call Center Agent"}, {"value": "Call Center Director", "label": "Call Center Director"}, {"value": "Call Center Manager", "label": "Call Center Manager"}, {"value": "Call Center Operator", "label": "Call Center Operator"}, {"value": "Call Center Representative", "label": "Call Center Representative"}, {"value": "Call Center Specialist", "label": "Call Center Specialist"}, {"value": "Call Center Supervisor", "label": "Call Center Supervisor"}, {"value": "Caller", "label": "Caller"}, {"value": "Camera Operator", "label": "Camera Operator"}, {"value": "Camp Counselor", "label": "Camp Counselor"}, {"value": "Camp Director", "label": "Camp Director"}, {"value": "Campaign Coordinator", "label": "Campaign Coordinator"}, {"value": "Campaign Manager", "label": "Campaign Manager"}, {"value": "Campus Recruiter", "label": "Campus Recruiter"}, {"value": "Cancer Registrar", "label": "Cancer Registrar"}, {"value": "Cannabis", "label": "Cannabis"}, {"value": "Canvasser", "label": "Canvasser"}, {"value": "Captain", "label": "Captain"}, {"value": "Car Dealer", "label": "Car Dealer"}, {"value": "Car Detailer", "label": "Car Detailer"}, {"value": "Car Driver", "label": "Car Driver"}, {"value": "Car Mechanic", "label": "Car Mechanic"}, {"value": "<PERSON> Porter", "label": "<PERSON> Porter"}, {"value": "Car Sales", "label": "Car Sales"}, {"value": "Cardiac Sonographer", "label": "Cardiac Sonographer"}, {"value": "Cardiologist", "label": "Cardiologist"}, {"value": "Cardiology Nurse Practitioner", "label": "Cardiology Nurse Practitioner"}, {"value": "Cardiothoracic Surgeon", "label": "Cardiothoracic Surgeon"}, {"value": "Cardiovascular Technologist", "label": "Cardiovascular Technologist"}, {"value": "Care Assistant", "label": "Care Assistant"}, {"value": "Care Manager", "label": "Care Manager"}, {"value": "Care Provider", "label": "Care Provider"}, {"value": "Care Taker", "label": "Care Taker"}, {"value": "Career Advisor", "label": "Career Advisor"}, {"value": "Career Coach", "label": "Career Coach"}, {"value": "Career Consultant", "label": "Career Consultant"}, {"value": "Career Counselor", "label": "Career Counselor"}, {"value": "Career Specialist", "label": "Career Specialist"}, {"value": "Caregiver", "label": "Caregiver"}, {"value": "Caretaker", "label": "Caretaker"}, {"value": "Cargo Agent", "label": "Cargo Agent"}, {"value": "<PERSON><PERSON> Handler", "label": "<PERSON><PERSON> Handler"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "<PERSON> Helper", "label": "<PERSON> Helper"}, {"value": "Carpentry", "label": "Carpentry"}, {"value": "Carpet Cleaner", "label": "Carpet Cleaner"}, {"value": "Carrier", "label": "Carrier"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Cartographer", "label": "Cartographer"}, {"value": "Case Aide", "label": "Case Aide"}, {"value": "Case Investigator", "label": "Case Investigator"}, {"value": "Case Manager", "label": "Case Manager"}, {"value": "Case Worker", "label": "Case Worker"}, {"value": "Cash Manager", "label": "Cash Manager"}, {"value": "Cashier", "label": "Cashier"}, {"value": "Casino", "label": "Casino"}, {"value": "Casino Dealer", "label": "Casino Dealer"}, {"value": "Casino Host", "label": "Casino Host"}, {"value": "Casino Manager", "label": "Casino Manager"}, {"value": "Casting Assistant", "label": "Casting Assistant"}, {"value": "Category Manager", "label": "Category Manager"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Catering Assistant", "label": "Catering Assistant"}, {"value": "Catering Chef", "label": "Catering Chef"}, {"value": "Catering Coordinator", "label": "Catering Coordinator"}, {"value": "Catering Manager", "label": "Catering Manager"}, {"value": "Catering Sales Manager", "label": "Catering Sales Manager"}, {"value": "Cdl Driver", "label": "Cdl Driver"}, {"value": "Cell Tower Climber", "label": "Cell Tower Climber"}, {"value": "Center Manager", "label": "Center Manager"}, {"value": "Central Office Technician", "label": "Central Office Technician"}, {"value": "Central Service Technician", "label": "Central Service Technician"}, {"value": "CEO", "label": "CEO"}, {"value": "Certified Athletic Trainer", "label": "Certified Athletic Trainer"}, {"value": "Certified Dental Assistant", "label": "Certified Dental Assistant"}, {"value": "Certified Diabetes Educator", "label": "Certified Diabetes Educator"}, {"value": "Certified Dietary Manager", "label": "Certified Dietary Manager"}, {"value": "Certified Home Health Aide", "label": "Certified Home Health Aide"}, {"value": "Certified Medical Assistant", "label": "Certified Medical Assistant"}, {"value": "Certified Medication Aide", "label": "Certified Medication Aide"}, {"value": "Certified Nurse Aide", "label": "Certified Nurse Aide"}, {"value": "Certified Nurse Assistant", "label": "Certified Nurse Assistant"}, {"value": "Certified Nurse Midwife", "label": "Certified Nurse Midwife"}, {"value": "Certified Nursing Assistant", "label": "Certified Nursing Assistant"}, {"value": "Certified Occupational Therapy Assistant", "label": "Certified Occupational Therapy Assistant"}, {"value": "Certified Ophthalmic Assistant", "label": "Certified Ophthalmic Assistant"}, {"value": "Certified Personal Trainer", "label": "Certified Personal Trainer"}, {"value": "Certified Pharmacy Technician", "label": "Certified Pharmacy Technician"}, {"value": "Certified Phlebotomy Technician", "label": "Certified Phlebotomy Technician"}, {"value": "Certified Professional Coder", "label": "Certified Professional Coder"}, {"value": "Certified Public Accountant", "label": "Certified Public Accountant"}, {"value": "Certified Surgical Technologist", "label": "Certified Surgical Technologist"}, {"value": "Certified Welding Inspector", "label": "Certified Welding Inspector"}, {"value": "Cfd Engineer", "label": "Cfd Engineer"}, {"value": "CFO", "label": "CFO"}, {"value": "Chairman", "label": "Chairman"}, {"value": "Change Management Consultant", "label": "Change Management Consultant"}, {"value": "Change Manager", "label": "Change Manager"}, {"value": "Channel Manager", "label": "Channel Manager"}, {"value": "Channel Marketing Manager", "label": "Channel Marketing Manager"}, {"value": "Channel Sales Manager", "label": "Channel Sales Manager"}, {"value": "Chaplain", "label": "Chaplain"}, {"value": "Character Artist", "label": "Character Artist"}, {"value": "Charge Nurse", "label": "Charge Nurse"}, {"value": "Charter Pilot", "label": "Charter Pilot"}, {"value": "Chartered Accountant", "label": "Chartered Accountant"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Cheerleading Coach", "label": "Cheerleading Coach"}, {"value": "Chef", "label": "Chef"}, {"value": "Chef Assistant", "label": "Chef Assistant"}, {"value": "Chef <PERSON>", "label": "Chef <PERSON>"}, {"value": "Chef <PERSON>", "label": "Chef <PERSON>"}, {"value": "Chef Manager", "label": "Chef Manager"}, {"value": "Chemical Analyst", "label": "Chemical Analyst"}, {"value": "Chemical Dependency Counselor", "label": "Chemical Dependency Counselor"}, {"value": "Chemical Engineer", "label": "Chemical Engineer"}, {"value": "Chemical Lab Technician", "label": "Chemical Lab Technician"}, {"value": "Chemical Operator", "label": "Chemical Operator"}, {"value": "Chemical Plant Operator", "label": "Chemical Plant Operator"}, {"value": "Chemical Process Engineer", "label": "Chemical Process Engineer"}, {"value": "Chemical Technician", "label": "Chemical Technician"}, {"value": "Chemist", "label": "Chemist"}, {"value": "Chemistry Professor", "label": "Chemistry Professor"}, {"value": "Chemistry Teacher", "label": "Chemistry Teacher"}, {"value": "Chemistry Tutor", "label": "Chemistry Tutor"}, {"value": "Chief Administrative Officer", "label": "Chief Administrative Officer"}, {"value": "Chief Communications Officer", "label": "Chief Communications Officer"}, {"value": "Chief Compliance Officer", "label": "Chief Compliance Officer"}, {"value": "Chief Development Officer", "label": "Chief Development Officer"}, {"value": "Chief Diversity Officer", "label": "Chief Diversity Officer"}, {"value": "Chief Engineer", "label": "Chief Engineer"}, {"value": "Chief Estimator", "label": "Chief Estimator"}, {"value": "Chief Executive Officer", "label": "Chief Executive Officer"}, {"value": "Chief Financial Officer", "label": "Chief Financial Officer"}, {"value": "Chief Human Resources Officer", "label": "Chief Human Resources Officer"}, {"value": "Chief Information Officer", "label": "Chief Information Officer"}, {"value": "Chief Information Security Officer", "label": "Chief Information Security Officer"}, {"value": "Chief Investment Officer", "label": "Chief Investment Officer"}, {"value": "Chief Learning Officer", "label": "Chief Learning Officer"}, {"value": "Chief Marketing Officer", "label": "Chief Marketing Officer"}, {"value": "Chief Medical Officer", "label": "Chief Medical Officer"}, {"value": "Chief Nursing Officer", "label": "Chief Nursing Officer"}, {"value": "Chief Of Police", "label": "Chief Of Police"}, {"value": "Chief Of Staff", "label": "Chief Of Staff"}, {"value": "Chief Operating Officer", "label": "Chief Operating Officer"}, {"value": "Chief Risk Officer", "label": "Chief Risk Officer"}, {"value": "Chief Security Officer", "label": "Chief Security Officer"}, {"value": "Chief Strategy Officer", "label": "Chief Strategy Officer"}, {"value": "Chief Technology Officer", "label": "Chief Technology Officer"}, {"value": "Child Advocate", "label": "Child Advocate"}, {"value": "Child Care Assistant", "label": "Child Care Assistant"}, {"value": "Child Care Worker", "label": "Child Care Worker"}, {"value": "Child Life Specialist", "label": "Child Life Specialist"}, {"value": "Child Psychiatrist", "label": "Child Psychiatrist"}, {"value": "Child Psychologist", "label": "Child Psychologist"}, {"value": "Childcare Teacher", "label": "Childcare Teacher"}, {"value": "Chinese Teacher", "label": "Chinese Teacher"}, {"value": "Chiropractic Assistant", "label": "Chiropractic Assistant"}, {"value": "Chiropractor", "label": "Chiropractor"}, {"value": "Choir Director", "label": "Choir Director"}, {"value": "Choreographer", "label": "Choreographer"}, {"value": "Church Administrator", "label": "Church Administrator"}, {"value": "Church Musician", "label": "Church Musician"}, {"value": "Church Secretary", "label": "Church Secretary"}, {"value": "Cinematographer", "label": "Cinematographer"}, {"value": "Circuit Design Engineer", "label": "Circuit Design Engineer"}, {"value": "Circulation Manager", "label": "Circulation Manager"}, {"value": "Cisco Engineer", "label": "Cisco Engineer"}, {"value": "Cisco Network Engineer", "label": "Cisco Network Engineer"}, {"value": "Cisco Voice Engineer", "label": "Cisco Voice Engineer"}, {"value": "Citrix Administrator", "label": "Citrix Administrator"}, {"value": "Citrix Engineer", "label": "Citrix Engineer"}, {"value": "City Administrator", "label": "City Administrator"}, {"value": "City Attorney", "label": "City Attorney"}, {"value": "City Clerk", "label": "City Clerk"}, {"value": "City Driver", "label": "City Driver"}, {"value": "City Engineer", "label": "City Engineer"}, {"value": "City Manager", "label": "City Manager"}, {"value": "City Planner", "label": "City Planner"}, {"value": "Civil Designer", "label": "Civil Designer"}, {"value": "Civil Drafter", "label": "Civil Drafter"}, {"value": "Civil Engineer", "label": "Civil Engineer"}, {"value": "Civil Engineering Technician", "label": "Civil Engineering Technician"}, {"value": "Civil Project Engineer", "label": "Civil Project Engineer"}, {"value": "Civil Structural Engineer", "label": "Civil Structural Engineer"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>aminer", "label": "<PERSON><PERSON><PERSON>aminer"}, {"value": "Claim Representative", "label": "Claim Representative"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Clai<PERSON> Adjuster", "label": "Clai<PERSON> Adjuster"}, {"value": "<PERSON><PERSON><PERSON> Analyst", "label": "<PERSON><PERSON><PERSON> Analyst"}, {"value": "<PERSON><PERSON>ms Assistant", "label": "<PERSON><PERSON>ms Assistant"}, {"value": "Claims Auditor", "label": "Claims Auditor"}, {"value": "Claims Clerk", "label": "Claims Clerk"}, {"value": "Claims Examiner", "label": "Claims Examiner"}, {"value": "Claims Investigator", "label": "Claims Investigator"}, {"value": "Claims Manager", "label": "Claims Manager"}, {"value": "Claims Processor", "label": "Claims Processor"}, {"value": "Claims Representative", "label": "Claims Representative"}, {"value": "Claims Supervisor", "label": "Claims Supervisor"}, {"value": "Class A CDL", "label": "Class A CDL"}, {"value": "Class A Driver", "label": "Class A Driver"}, {"value": "Class B CDL", "label": "Class B CDL"}, {"value": "Class B Driver", "label": "Class B Driver"}, {"value": "Classroom Aide", "label": "Classroom Aide"}, {"value": "Classroom Assistant", "label": "Classroom Assistant"}, {"value": "Classroom Teacher", "label": "Classroom Teacher"}, {"value": "Cleaner", "label": "Cleaner"}, {"value": "Clerical", "label": "Clerical"}, {"value": "Clerical Assistant", "label": "Clerical Assistant"}, {"value": "Clerk", "label": "Clerk"}, {"value": "Clerk <PERSON><PERSON>t", "label": "Clerk <PERSON><PERSON>t"}, {"value": "Client Advisor", "label": "Client Advisor"}, {"value": "Client Associate", "label": "Client Associate"}, {"value": "Client Coordinator", "label": "Client Coordinator"}, {"value": "Client Manager", "label": "Client Manager"}, {"value": "Client Relations Manager", "label": "Client Relations Manager"}, {"value": "Client Relationship Manager", "label": "Client Relationship Manager"}, {"value": "Client Service Associate", "label": "Client Service Associate"}, {"value": "Client Services Manager", "label": "Client Services Manager"}, {"value": "Client Success Manager", "label": "Client Success Manager"}, {"value": "Clinic Assistant", "label": "Clinic Assistant"}, {"value": "Clinic Manager", "label": "Clinic Manager"}, {"value": "Clinic Receptionist", "label": "Clinic Receptionist"}, {"value": "Clinical Analyst", "label": "Clinical Analyst"}, {"value": "Clinical Assistant", "label": "Clinical Assistant"}, {"value": "Clinical Associate", "label": "Clinical Associate"}, {"value": "Clinical Consultant", "label": "Clinical Consultant"}, {"value": "Clinical Coordinator", "label": "Clinical Coordinator"}, {"value": "Clinical Data Analyst", "label": "Clinical Data Analyst"}, {"value": "Clinical Data Associate", "label": "Clinical Data Associate"}, {"value": "Clinical Data Coordinator", "label": "Clinical Data Coordinator"}, {"value": "Clinical Data Manager", "label": "Clinical Data Manager"}, {"value": "Clinical Dietitian", "label": "Clinical Dietitian"}, {"value": "Clinical Director", "label": "Clinical Director"}, {"value": "Clinical Documentation Specialist", "label": "Clinical Documentation Specialist"}, {"value": "Clinical Educator", "label": "Clinical Educator"}, {"value": "Clinical Engineer", "label": "Clinical Engineer"}, {"value": "Clinical Exercise Physiologist", "label": "Clinical Exercise Physiologist"}, {"value": "Clinical Informatics Specialist", "label": "Clinical Informatics Specialist"}, {"value": "Clinical Instructor", "label": "Clinical Instructor"}, {"value": "Clinical Laboratory Scientist", "label": "Clinical Laboratory Scientist"}, {"value": "Clinical Laboratory Technician", "label": "Clinical Laboratory Technician"}, {"value": "Clinical Laboratory Technologist", "label": "Clinical Laboratory Technologist"}, {"value": "Clinical Liaison", "label": "Clinical Liaison"}, {"value": "Clinical Manager", "label": "Clinical Manager"}, {"value": "Clinical Medical Assistant", "label": "Clinical Medical Assistant"}, {"value": "Clinical Nurse", "label": "Clinical Nurse"}, {"value": "Clinical Nurse Educator", "label": "Clinical Nurse Educator"}, {"value": "Clinical Nurse Manager", "label": "Clinical Nurse Manager"}, {"value": "Clinical Nurse Specialist", "label": "Clinical Nurse Specialist"}, {"value": "Clinical Nursing Instructor", "label": "Clinical Nursing Instructor"}, {"value": "Clinical Operations Manager", "label": "Clinical Operations Manager"}, {"value": "Clinical Pharmacist", "label": "Clinical Pharmacist"}, {"value": "Clinical Project Manager", "label": "Clinical Project Manager"}, {"value": "Clinical Psychologist", "label": "Clinical Psychologist"}, {"value": "Clinical Research", "label": "Clinical Research"}, {"value": "Clinical Research Assistant", "label": "Clinical Research Assistant"}, {"value": "Clinical Research Associate", "label": "Clinical Research Associate"}, {"value": "Clinical Research Coordinator", "label": "Clinical Research Coordinator"}, {"value": "Clinical Research Manager", "label": "Clinical Research Manager"}, {"value": "Clinical Research Nurse", "label": "Clinical Research Nurse"}, {"value": "Clinical Researcher", "label": "Clinical Researcher"}, {"value": "Clinical Scientist", "label": "Clinical Scientist"}, {"value": "Clinical Social Worker", "label": "Clinical Social Worker"}, {"value": "Clinical Specialist", "label": "Clinical Specialist"}, {"value": "Clinical Supervisor", "label": "Clinical Supervisor"}, {"value": "Clinical Support Specialist", "label": "Clinical Support Specialist"}, {"value": "Clinical Systems Analyst", "label": "Clinical Systems Analyst"}, {"value": "Clinical Technician", "label": "Clinical Technician"}, {"value": "Clinical Therapist", "label": "Clinical Therapist"}, {"value": "Clinical Trial Assistant", "label": "Clinical Trial Assistant"}, {"value": "Clinical Trial Associate", "label": "Clinical Trial Associate"}, {"value": "Clinical Trial Manager", "label": "Clinical Trial Manager"}, {"value": "Clinician", "label": "Clinician"}, {"value": "Cloud Architect", "label": "Cloud Architect"}, {"value": "Cloud Consultant", "label": "Cloud Consultant"}, {"value": "Cloud Engineer", "label": "Cloud Engineer"}, {"value": "Clown", "label": "Clown"}, {"value": "Club Manager", "label": "Club Manager"}, {"value": "Cmm Operator", "label": "Cmm Operator"}, {"value": "Cmm Programmer", "label": "Cmm Programmer"}, {"value": "CNA", "label": "CNA"}, {"value": "Cnc Machine Operator", "label": "Cnc Machine Operator"}, {"value": "Cnc Machinist", "label": "Cnc Machinist"}, {"value": "Cnc Operator", "label": "Cnc Operator"}, {"value": "Cnc Programmer", "label": "Cnc Programmer"}, {"value": "Coach", "label": "Coach"}, {"value": "Coal Miner", "label": "Coal Miner"}, {"value": "Coating Inspector", "label": "Coating Inspector"}, {"value": "Cobol Developer", "label": "Cobol Developer"}, {"value": "Cobol Programmer", "label": "Cobol Programmer"}, {"value": "Cocktail Server", "label": "Cocktail Server"}, {"value": "<PERSON><PERSON><PERSON>ress", "label": "<PERSON><PERSON><PERSON>ress"}, {"value": "Code Enforcement Officer", "label": "Code Enforcement Officer"}, {"value": "Coding Specialist", "label": "Coding Specialist"}, {"value": "Coffee Roaster", "label": "Coffee Roaster"}, {"value": "Coffee Shop Manager", "label": "Coffee Shop Manager"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Coldfusion Developer", "label": "Coldfusion Developer"}, {"value": "Collection Agent", "label": "Collection Agent"}, {"value": "Collection Analyst", "label": "Collection Analyst"}, {"value": "Collection Manager", "label": "Collection Manager"}, {"value": "Collection Representative", "label": "Collection Representative"}, {"value": "Collection Specialist", "label": "Collection Specialist"}, {"value": "Collection Supervisor", "label": "Collection Supervisor"}, {"value": "Collections Agent", "label": "Collections Agent"}, {"value": "Collections Specialist", "label": "Collections Specialist"}, {"value": "College Advisor", "label": "College Advisor"}, {"value": "College Counselor", "label": "College Counselor"}, {"value": "College Instructor", "label": "College Instructor"}, {"value": "Colorist", "label": "Colorist"}, {"value": "Columnist", "label": "Columnist"}, {"value": "Combat Engineer", "label": "Combat Engineer"}, {"value": "Combo Welder", "label": "Combo Welder"}, {"value": "Comic", "label": "Comic"}, {"value": "Commercial Account Manager", "label": "Commercial Account Manager"}, {"value": "Commercial Analyst", "label": "Commercial Analyst"}, {"value": "Commercial Banker", "label": "Commercial Banker"}, {"value": "Commercial Director", "label": "Commercial Director"}, {"value": "Commercial Diver", "label": "Commercial Diver"}, {"value": "Commercial Driver", "label": "Commercial Driver"}, {"value": "Commercial Lender", "label": "Commercial Lender"}, {"value": "Commercial Loan Officer", "label": "Commercial Loan Officer"}, {"value": "Commercial Manager", "label": "Commercial Manager"}, {"value": "Commercial Project Manager", "label": "Commercial Project Manager"}, {"value": "Commercial Property Manager", "label": "Commercial Property Manager"}, {"value": "Commercial Real Estate Appraiser", "label": "Commercial Real Estate Appraiser"}, {"value": "Commercial Real Estate Broker", "label": "Commercial Real Estate Broker"}, {"value": "Commercial Sales Manager", "label": "Commercial Sales Manager"}, {"value": "Commercial Underwriter", "label": "Commercial Underwriter"}, {"value": "Commission Analyst", "label": "Commission Analyst"}, {"value": "Commissioning Engineer", "label": "Commissioning Engineer"}, {"value": "Commodity Buyer", "label": "Commodity Buyer"}, {"value": "Commodity Trader", "label": "Commodity Trader"}, {"value": "Communication Technician", "label": "Communication Technician"}, {"value": "Communications", "label": "Communications"}, {"value": "Communications Associate", "label": "Communications Associate"}, {"value": "Communications Consultant", "label": "Communications Consultant"}, {"value": "Communications Coordinator", "label": "Communications Coordinator"}, {"value": "Communications Director", "label": "Communications Director"}, {"value": "Communications Manager", "label": "Communications Manager"}, {"value": "Communications Officer", "label": "Communications Officer"}, {"value": "Communications Specialist", "label": "Communications Specialist"}, {"value": "Communications Technician", "label": "Communications Technician"}, {"value": "Communicator", "label": "Communicator"}, {"value": "Community Assistant", "label": "Community Assistant"}, {"value": "Community Association Manager", "label": "Community Association Manager"}, {"value": "Community Health Educator", "label": "Community Health Educator"}, {"value": "Community Health Nurse", "label": "Community Health Nurse"}, {"value": "Community Health Worker", "label": "Community Health Worker"}, {"value": "Community Liaison", "label": "Community Liaison"}, {"value": "Community Manager", "label": "Community Manager"}, {"value": "Community Organizer", "label": "Community Organizer"}, {"value": "Community Outreach Coordinator", "label": "Community Outreach Coordinator"}, {"value": "Community Outreach Specialist", "label": "Community Outreach Specialist"}, {"value": "Community Planner", "label": "Community Planner"}, {"value": "Community Service Officer", "label": "Community Service Officer"}, {"value": "Community Worker", "label": "Community Worker"}, {"value": "Companion", "label": "Companion"}, {"value": "Company Driver", "label": "Company Driver"}, {"value": "Compensation Analyst", "label": "Compensation Analyst"}, {"value": "Compensation Manager", "label": "Compensation Manager"}, {"value": "Compensation Specialist", "label": "Compensation Specialist"}, {"value": "Compliance Analyst", "label": "Compliance Analyst"}, {"value": "Compliance Auditor", "label": "Compliance Auditor"}, {"value": "Compliance Manager", "label": "Compliance Manager"}, {"value": "Compliance Officer", "label": "Compliance Officer"}, {"value": "Compliance Specialist", "label": "Compliance Specialist"}, {"value": "Component Engineer", "label": "Component Engineer"}, {"value": "Composite Technician", "label": "Composite Technician"}, {"value": "Compositor", "label": "Compositor"}, {"value": "Compounder", "label": "Compounder"}, {"value": "Computational Biologist", "label": "Computational Biologist"}, {"value": "Computational Linguist", "label": "Computational Linguist"}, {"value": "Computer Analyst", "label": "Computer Analyst"}, {"value": "Computer Engineer", "label": "Computer Engineer"}, {"value": "Computer Hardware Engineer", "label": "Computer Hardware Engineer"}, {"value": "Computer Operator", "label": "Computer Operator"}, {"value": "Computer Programmer", "label": "Computer Programmer"}, {"value": "Computer Repair Technician", "label": "Computer Repair Technician"}, {"value": "Computer Science Teacher", "label": "Computer Science Teacher"}, {"value": "Computer Science Tutor", "label": "Computer Science Tutor"}, {"value": "Computer Scientist", "label": "Computer Scientist"}, {"value": "Computer Support Specialist", "label": "Computer Support Specialist"}, {"value": "Computer Systems Analyst", "label": "Computer Systems Analyst"}, {"value": "Computer Teacher", "label": "Computer Teacher"}, {"value": "Computer Technician", "label": "Computer Technician"}, {"value": "Concept Artist", "label": "Concept Artist"}, {"value": "Concierge", "label": "Concierge"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Concrete Pump Operator", "label": "Concrete Pump Operator"}, {"value": "Concrete Truck Driver", "label": "Concrete Truck Driver"}, {"value": "Concrete Worker", "label": "Concrete Worker"}, {"value": "Conductor", "label": "Conductor"}, {"value": "Conference Coordinator", "label": "Conference Coordinator"}, {"value": "Configuration Analyst", "label": "Configuration Analyst"}, {"value": "Configuration Manager", "label": "Configuration Manager"}, {"value": "Conservation Officer", "label": "Conservation Officer"}, {"value": "Conservationist", "label": "Conservationist"}, {"value": "Conservator", "label": "Conservator"}, {"value": "Console Operator", "label": "Console Operator"}, {"value": "Constable", "label": "Constable"}, {"value": "Construction Administrator", "label": "Construction Administrator"}, {"value": "Construction Apprentice", "label": "Construction Apprentice"}, {"value": "Construction Assistant", "label": "Construction Assistant"}, {"value": "Construction Consultant", "label": "Construction Consultant"}, {"value": "Construction Coordinator", "label": "Construction Coordinator"}, {"value": "Construction Driver", "label": "Construction Driver"}, {"value": "Construction Engineer", "label": "Construction Engineer"}, {"value": "Construction Estimator", "label": "Construction Estimator"}, {"value": "Construction Flagger", "label": "Construction Flagger"}, {"value": "Construction Foreman", "label": "Construction Foreman"}, {"value": "Construction Helper", "label": "Construction Helper"}, {"value": "Construction Inspector", "label": "Construction Inspector"}, {"value": "Construction Laborer", "label": "Construction Laborer"}, {"value": "Construction Manager", "label": "Construction Manager"}, {"value": "Construction Project Coordinator", "label": "Construction Project Coordinator"}, {"value": "Construction Project Engineer", "label": "Construction Project Engineer"}, {"value": "Construction Project Manager", "label": "Construction Project Manager"}, {"value": "Construction Superintendent", "label": "Construction Superintendent"}, {"value": "Construction Supervisor", "label": "Construction Supervisor"}, {"value": "Construction Technician", "label": "Construction Technician"}, {"value": "Construction Worker", "label": "Construction Worker"}, {"value": "Consultant", "label": "Consultant"}, {"value": "Consulting Engineer", "label": "Consulting Engineer"}, {"value": "Contact Center Manager", "label": "Contact Center Manager"}, {"value": "Contact Tracer", "label": "Contact Tracer"}, {"value": "Contact Tracing", "label": "Contact Tracing"}, {"value": "Content Creator", "label": "Content Creator"}, {"value": "Content Developer", "label": "Content Developer"}, {"value": "Content Editor", "label": "Content Editor"}, {"value": "Content Manager", "label": "Content Manager"}, {"value": "Content Producer", "label": "Content Producer"}, {"value": "Content Specialist", "label": "Content Specialist"}, {"value": "Content Strategist", "label": "Content Strategist"}, {"value": "Content Writer", "label": "Content Writer"}, {"value": "Contract Administrator", "label": "Contract Administrator"}, {"value": "Contract Analyst", "label": "Contract Analyst"}, {"value": "Contract Attorney", "label": "Contract Attorney"}, {"value": "Contract Driver", "label": "Contract Driver"}, {"value": "Contract Manager", "label": "Contract Manager"}, {"value": "Contract Negotiator", "label": "Contract Negotiator"}, {"value": "Contract Specialist", "label": "Contract Specialist"}, {"value": "Contracts Manager", "label": "Contracts Manager"}, {"value": "Control Engineer", "label": "Control Engineer"}, {"value": "Control Room Operator", "label": "Control Room Operator"}, {"value": "Control Systems Engineer", "label": "Control Systems Engineer"}, {"value": "Controller", "label": "Controller"}, {"value": "Controls Engineer", "label": "Controls Engineer"}, {"value": "Controls Technician", "label": "Controls Technician"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Coordinator", "label": "Coordinator"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>nician", "label": "<PERSON><PERSON><PERSON>nician"}, {"value": "Copy Editor", "label": "Copy Editor"}, {"value": "Copy Writer", "label": "Copy Writer"}, {"value": "Copy<PERSON>", "label": "Copy<PERSON>"}, {"value": "Corporate Accountant", "label": "Corporate Accountant"}, {"value": "Corporate Attorney", "label": "Corporate Attorney"}, {"value": "Corporate Controller", "label": "Corporate Controller"}, {"value": "Corporate Counsel", "label": "Corporate Counsel"}, {"value": "Corporate Event Planner", "label": "Corporate Event Planner"}, {"value": "Corporate Financial Analyst", "label": "Corporate Financial Analyst"}, {"value": "Corporate Lawyer", "label": "Corporate Lawyer"}, {"value": "Corporate Paralegal", "label": "Corporate Paralegal"}, {"value": "Corporate Pilot", "label": "Corporate Pilot"}, {"value": "Corporate Receptionist", "label": "Corporate Receptionist"}, {"value": "Corporate Recruiter", "label": "Corporate Recruiter"}, {"value": "Corporate Trainer", "label": "Corporate Trainer"}, {"value": "Corporate Travel Agent", "label": "Corporate Travel Agent"}, {"value": "Corporate Travel Consultant", "label": "Corporate Travel Consultant"}, {"value": "Correction Officer", "label": "Correction Officer"}, {"value": "Correctional Officer", "label": "Correctional Officer"}, {"value": "Corrosion Engineer", "label": "Corrosion Engineer"}, {"value": "Corrosion Technician", "label": "Corrosion Technician"}, {"value": "Cosmetic Chemist", "label": "Cosmetic Chemist"}, {"value": "Cosmetologist", "label": "Cosmetologist"}, {"value": "Cosmetology Educator", "label": "Cosmetology Educator"}, {"value": "Cosmetology Instructor", "label": "Cosmetology Instructor"}, {"value": "Cost Accountant", "label": "Cost Accountant"}, {"value": "Cost Accounting Manager", "label": "Cost Accounting Manager"}, {"value": "Cost Analyst", "label": "Cost Analyst"}, {"value": "Cost Controller", "label": "Cost Controller"}, {"value": "Cost Engineer", "label": "Cost Engineer"}, {"value": "Cost Estimator", "label": "Cost Estimator"}, {"value": "Costume Designer", "label": "Costume Designer"}, {"value": "Counseling Psychologist", "label": "Counseling Psychologist"}, {"value": "Counselor", "label": "Counselor"}, {"value": "Counter Manager", "label": "Counter Manager"}, {"value": "Courier", "label": "Courier"}, {"value": "Courier Driver", "label": "Courier Driver"}, {"value": "Court Administrator", "label": "Court Administrator"}, {"value": "Court Clerk", "label": "Court Clerk"}, {"value": "Court Interpreter", "label": "Court Interpreter"}, {"value": "Court Officer", "label": "Court Officer"}, {"value": "Court Reporter", "label": "Court Reporter"}, {"value": "Court Security Officer", "label": "Court Security Officer"}, {"value": "Courtesy Clerk", "label": "Courtesy Clerk"}, {"value": "Cowboy", "label": "Cowboy"}, {"value": "CPA", "label": "CPA"}, {"value": "CPR Instructor", "label": "CPR Instructor"}, {"value": "Craftsman", "label": "Craftsman"}, {"value": "Crane Operator", "label": "Crane Operator"}, {"value": "Creative Assistant", "label": "Creative Assistant"}, {"value": "Creative Designer", "label": "Creative Designer"}, {"value": "Creative Director", "label": "Creative Director"}, {"value": "Creative Producer", "label": "Creative Producer"}, {"value": "Creative Project Manager", "label": "Creative Project Manager"}, {"value": "Creative Strategist", "label": "Creative Strategist"}, {"value": "Creative Writer", "label": "Creative Writer"}, {"value": "Creative Writing Professor", "label": "Creative Writing Professor"}, {"value": "Credentialing Coordinator", "label": "Credentialing Coordinator"}, {"value": "Credentialing Specialist", "label": "Credentialing Specialist"}, {"value": "Credit Analyst", "label": "Credit Analyst"}, {"value": "Credit Controller", "label": "Credit Controller"}, {"value": "Credit Counselor", "label": "Credit Counselor"}, {"value": "Credit Manager", "label": "Credit Manager"}, {"value": "Credit Officer", "label": "Credit Officer"}, {"value": "Credit Risk Analyst", "label": "Credit Risk Analyst"}, {"value": "Credit Specialist", "label": "Credit Specialist"}, {"value": "Crematory Operator", "label": "Crematory Operator"}, {"value": "Crew Chief", "label": "Crew Chief"}, {"value": "Crew Member", "label": "Crew Member"}, {"value": "Crew Scheduler", "label": "Crew Scheduler"}, {"value": "Crime Analyst", "label": "Crime Analyst"}, {"value": "Crime Scene Technician", "label": "Crime Scene Technician"}, {"value": "Criminal Intelligence Analyst", "label": "Criminal Intelligence Analyst"}, {"value": "Criminal Investigator", "label": "Criminal Investigator"}, {"value": "Criminal Justice <PERSON><PERSON><PERSON>", "label": "Criminal Justice <PERSON><PERSON><PERSON>"}, {"value": "Criminalist", "label": "Criminalist"}, {"value": "Crisis Counselor", "label": "Crisis Counselor"}, {"value": "Critical Care Nurse", "label": "Critical Care Nurse"}, {"value": "Crm Administrator", "label": "Crm Administrator"}, {"value": "Crm Analyst", "label": "Crm Analyst"}, {"value": "Crm Developer", "label": "Crm Developer"}, {"value": "Crm Manager", "label": "Crm Manager"}, {"value": "Crna", "label": "Crna"}, {"value": "Crossing Guard", "label": "Crossing Guard"}, {"value": "Ct Technologist", "label": "Ct Technologist"}, {"value": "CTO", "label": "CTO"}, {"value": "Culinary Instructor", "label": "Culinary Instructor"}, {"value": "Cultivator", "label": "Cultivator"}, {"value": "Curator", "label": "Curator"}, {"value": "Curatorial Assistant", "label": "Curatorial Assistant"}, {"value": "Curriculum Coordinator", "label": "Curriculum Coordinator"}, {"value": "Curriculum Designer", "label": "Curriculum Designer"}, {"value": "Curriculum Developer", "label": "Curriculum Developer"}, {"value": "Curriculum Director", "label": "Curriculum Director"}, {"value": "Curriculum Specialist", "label": "Curriculum Specialist"}, {"value": "Curriculum Writer", "label": "Curriculum Writer"}, {"value": "Custodial Supervisor", "label": "Custodial Supervisor"}, {"value": "Custodial Worker", "label": "Custodial Worker"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "Customer Account Manager", "label": "Customer Account Manager"}, {"value": "Customer Advocate", "label": "Customer Advocate"}, {"value": "Customer Assistant", "label": "Customer Assistant"}, {"value": "Customer Care Representative", "label": "Customer Care Representative"}, {"value": "Customer Experience Manager", "label": "Customer Experience Manager"}, {"value": "Customer Representative", "label": "Customer Representative"}, {"value": "Customer Service", "label": "Customer Service"}, {"value": "Customer Service Advisor", "label": "Customer Service Advisor"}, {"value": "Customer Service Agent", "label": "Customer Service Agent"}, {"value": "Customer Service Assistant", "label": "Customer Service Assistant"}, {"value": "Customer Service Associate", "label": "Customer Service Associate"}, {"value": "Customer Service Consultant", "label": "Customer Service Consultant"}, {"value": "Customer Service Coordinator", "label": "Customer Service Coordinator"}, {"value": "Customer Service Director", "label": "Customer Service Director"}, {"value": "Customer Service Manager", "label": "Customer Service Manager"}, {"value": "Customer Service Operator", "label": "Customer Service Operator"}, {"value": "Customer Service Receptionist", "label": "Customer Service Receptionist"}, {"value": "Customer Service Representative", "label": "Customer Service Representative"}, {"value": "Customer Service Specialist", "label": "Customer Service Specialist"}, {"value": "Customer Service Supervisor", "label": "Customer Service Supervisor"}, {"value": "Customer Service Trainer", "label": "Customer Service Trainer"}, {"value": "Customer Success Manager", "label": "Customer Success Manager"}, {"value": "Customer Support Manager", "label": "Customer Support Manager"}, {"value": "Customer Support Representative", "label": "Customer Support Representative"}, {"value": "Customer Support Specialist", "label": "Customer Support Specialist"}, {"value": "Customs Broker", "label": "Customs Broker"}, {"value": "Cyber Security", "label": "Cyber Security"}, {"value": "Cyber Security Analyst", "label": "Cyber Security Analyst"}, {"value": "Cyber Security Engineer", "label": "Cyber Security Engineer"}, {"value": "Cyber Security Specialist", "label": "Cyber Security Specialist"}, {"value": "Cytogenetic Technologist", "label": "Cytogenetic Technologist"}, {"value": "Cytotechnologist", "label": "Cytotechnologist"}, {"value": "Dairy Manager", "label": "Dairy Manager"}, {"value": "Dance Instructor", "label": "Dance Instructor"}, {"value": "Dance Teacher", "label": "Dance Teacher"}, {"value": "Dancer", "label": "Dancer"}, {"value": "Data Analyst", "label": "Data Analyst"}, {"value": "Data Architect", "label": "Data Architect"}, {"value": "Data Center Manager", "label": "Data Center Manager"}, {"value": "Data Center Technician", "label": "Data Center Technician"}, {"value": "Data Clerk", "label": "Data Clerk"}, {"value": "Data Collector", "label": "Data Collector"}, {"value": "Data Engineer", "label": "Data Engineer"}, {"value": "Data Entry", "label": "Data Entry"}, {"value": "Data Entry Clerk", "label": "Data Entry Clerk"}, {"value": "Data Entry Operator", "label": "Data Entry Operator"}, {"value": "Data Entry Specialist", "label": "Data Entry Specialist"}, {"value": "Data Manager", "label": "Data Manager"}, {"value": "Data Modeler", "label": "Data Modeler"}, {"value": "Data Scientist", "label": "Data Scientist"}, {"value": "Data Specialist", "label": "Data Specialist"}, {"value": "Data Technician", "label": "Data Technician"}, {"value": "Database Administrator", "label": "Database Administrator"}, {"value": "Database Analyst", "label": "Database Analyst"}, {"value": "Database Architect", "label": "Database Architect"}, {"value": "Database Designer", "label": "Database Designer"}, {"value": "Database Developer", "label": "Database Developer"}, {"value": "Database Engineer", "label": "Database Engineer"}, {"value": "Database Manager", "label": "Database Manager"}, {"value": "Database Programmer", "label": "Database Programmer"}, {"value": "Database Specialist", "label": "Database Specialist"}, {"value": "Day Camp Counselor", "label": "Day Camp Counselor"}, {"value": "Day Care Teacher", "label": "Day Care Teacher"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Day Trader", "label": "Day Trader"}, {"value": "DBA", "label": "DBA"}, {"value": "Deaconess", "label": "Deaconess"}, {"value": "Dealer", "label": "Dealer"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Debt Collector", "label": "Debt Collector"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Decorator", "label": "Decorator"}, {"value": "Defense Attorney", "label": "Defense Attorney"}, {"value": "Deli Clerk", "label": "Deli Clerk"}, {"value": "Deli Manager", "label": "Deli Manager"}, {"value": "Delivery", "label": "Delivery"}, {"value": "Delivery Driver", "label": "Delivery Driver"}, {"value": "Delivery Manager", "label": "Delivery Manager"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Demonstrator", "label": "Demonstrator"}, {"value": "Dental", "label": "Dental"}, {"value": "Dental Assistant", "label": "Dental Assistant"}, {"value": "Dental Ceramist", "label": "Dental Ceramist"}, {"value": "Dental Hygienist", "label": "Dental Hygienist"}, {"value": "Dental Lab Technician", "label": "Dental Lab Technician"}, {"value": "Dental Office Manager", "label": "Dental Office Manager"}, {"value": "Dental Receptionist", "label": "Dental Receptionist"}, {"value": "Dental Technician", "label": "Dental Technician"}, {"value": "Dentist", "label": "Dentist"}, {"value": "Department Assistant", "label": "Department Assistant"}, {"value": "Department Manager", "label": "Department Manager"}, {"value": "Deployment Engineer", "label": "Deployment Engineer"}, {"value": "Deployment Manager", "label": "Deployment Manager"}, {"value": "Deployment Technician", "label": "Deployment Technician"}, {"value": "Deputy Clerk", "label": "Deputy Clerk"}, {"value": "Deputy Court Clerk", "label": "Deputy Court Clerk"}, {"value": "Deputy Director", "label": "Deputy Director"}, {"value": "Deputy Sheriff", "label": "Deputy Sheriff"}, {"value": "Derivatives Analyst", "label": "Derivatives Analyst"}, {"value": "Dermatologist", "label": "Dermatologist"}, {"value": "Dermatology Nurse Practitioner", "label": "Dermatology Nurse Practitioner"}, {"value": "Design Consultant", "label": "Design Consultant"}, {"value": "Design Director", "label": "Design Director"}, {"value": "Design Engineer", "label": "Design Engineer"}, {"value": "Design Manager", "label": "Design Manager"}, {"value": "Designer", "label": "Designer"}, {"value": "Desk Clerk", "label": "Desk Clerk"}, {"value": "Desk Receptionist", "label": "Desk Receptionist"}, {"value": "Desktop Administrator", "label": "Desktop Administrator"}, {"value": "Desktop Engineer", "label": "Desktop Engineer"}, {"value": "Desktop Publisher", "label": "Desktop Publisher"}, {"value": "Desktop Support", "label": "Desktop Support"}, {"value": "Desktop Support Analyst", "label": "Desktop Support Analyst"}, {"value": "Desktop Support Engineer", "label": "Desktop Support Engineer"}, {"value": "Desktop Support Manager", "label": "Desktop Support Manager"}, {"value": "Desktop Support Specialist", "label": "Desktop Support Specialist"}, {"value": "Desktop Support Technician", "label": "Desktop Support Technician"}, {"value": "Detailer", "label": "Detailer"}, {"value": "Detective", "label": "Detective"}, {"value": "Detention Officer", "label": "Detention Officer"}, {"value": "Developer", "label": "Developer"}, {"value": "Development Assistant", "label": "Development Assistant"}, {"value": "Development Associate", "label": "Development Associate"}, {"value": "Development Coordinator", "label": "Development Coordinator"}, {"value": "Development Director", "label": "Development Director"}, {"value": "Development Engineer", "label": "Development Engineer"}, {"value": "Development Manager", "label": "Development Manager"}, {"value": "Development Officer", "label": "Development Officer"}, {"value": "Devops", "label": "Devops"}, {"value": "Devops Engineer", "label": "Devops Engineer"}, {"value": "Diabetes Educator", "label": "Diabetes Educator"}, {"value": "Diagnostic Medical Sonographer", "label": "Diagnostic Medical Sonographer"}, {"value": "Dialysis Nurse", "label": "Dialysis Nurse"}, {"value": "Dialysis Rn", "label": "Dialysis Rn"}, {"value": "Dialysis Technician", "label": "Dialysis Technician"}, {"value": "Die Maker", "label": "Die Maker"}, {"value": "Diesel Mechanic", "label": "Diesel Mechanic"}, {"value": "Diesel Technician", "label": "Diesel Technician"}, {"value": "Diet Clerk", "label": "Diet Clerk"}, {"value": "Diet Technician", "label": "Diet Technician"}, {"value": "Dietary Aide", "label": "Dietary Aide"}, {"value": "Dietary Cook", "label": "Dietary Cook"}, {"value": "Dietary Manager", "label": "Dietary Manager"}, {"value": "Dietary Supervisor", "label": "Dietary Supervisor"}, {"value": "Dietetic Technician", "label": "Dietetic Technician"}, {"value": "Dietitian", "label": "Dietitian"}, {"value": "Digital Account Executive", "label": "Digital Account Executive"}, {"value": "Digital Account Manager", "label": "Digital Account Manager"}, {"value": "Digital Archivist", "label": "Digital Archivist"}, {"value": "Digital Artist", "label": "Digital Artist"}, {"value": "Digital Court Reporter", "label": "Digital Court Reporter"}, {"value": "Digital Designer", "label": "Digital Designer"}, {"value": "Digital Marketing", "label": "Digital Marketing"}, {"value": "Digital Marketing Analyst", "label": "Digital Marketing Analyst"}, {"value": "Digital Marketing Consultant", "label": "Digital Marketing Consultant"}, {"value": "Digital Marketing Coordinator", "label": "Digital Marketing Coordinator"}, {"value": "Digital Marketing Manager", "label": "Digital Marketing Manager"}, {"value": "Digital Marketing Specialist", "label": "Digital Marketing Specialist"}, {"value": "Digital Marketing Strategist", "label": "Digital Marketing Strategist"}, {"value": "Digital Media Specialist", "label": "Digital Media Specialist"}, {"value": "Digital Producer", "label": "Digital Producer"}, {"value": "Digital Project Manager", "label": "Digital Project Manager"}, {"value": "Digital Strategist", "label": "Digital Strategist"}, {"value": "Dining Room Manager", "label": "Dining Room Manager"}, {"value": "Direct Care Professional", "label": "Direct Care Professional"}, {"value": "Direct Care Staff", "label": "Direct Care Staff"}, {"value": "Direct Care Worker", "label": "Direct Care Worker"}, {"value": "Direct Support Professional", "label": "Direct Support Professional"}, {"value": "Directional Driller", "label": "Directional Driller"}, {"value": "Director", "label": "Director"}, {"value": "Director Of Accounting", "label": "Director Of Accounting"}, {"value": "Director Of Admissions", "label": "Director Of Admissions"}, {"value": "Director Of Analytics", "label": "Director Of Analytics"}, {"value": "Director Of Business Development", "label": "Director Of Business Development"}, {"value": "Director Of Communications", "label": "Director Of Communications"}, {"value": "Director Of Construction", "label": "Director Of Construction"}, {"value": "Director Of Customer Service", "label": "Director Of Customer Service"}, {"value": "Director Of Development", "label": "Director Of Development"}, {"value": "Director Of Digital Marketing", "label": "Director Of Digital Marketing"}, {"value": "Director Of Ecommerce", "label": "Director Of Ecommerce"}, {"value": "Director Of Education", "label": "Director Of Education"}, {"value": "Director Of Engineering", "label": "Director Of Engineering"}, {"value": "Director Of Environmental Services", "label": "Director Of Environmental Services"}, {"value": "Director Of Events", "label": "Director Of Events"}, {"value": "Director Of Facilities", "label": "Director Of Facilities"}, {"value": "Director Of Finance", "label": "Director Of Finance"}, {"value": "Director Of Food And Beverage", "label": "Director Of Food And Beverage"}, {"value": "Director Of Human Resources", "label": "Director Of Human Resources"}, {"value": "Director Of Logistics", "label": "Director Of Logistics"}, {"value": "Director Of Manufacturing", "label": "Director Of Manufacturing"}, {"value": "Director Of Marketing", "label": "Director Of Marketing"}, {"value": "Director Of Nursing", "label": "Director Of Nursing"}, {"value": "Director Of Operations", "label": "Director Of Operations"}, {"value": "Director Of Photography", "label": "Director Of Photography"}, {"value": "Director Of Product Management", "label": "Director Of Product Management"}, {"value": "Director Of Public Safety", "label": "Director Of Public Safety"}, {"value": "Director Of Purchasing", "label": "Director Of Purchasing"}, {"value": "Director Of Quality", "label": "Director Of Quality"}, {"value": "Director Of Radiology", "label": "Director Of Radiology"}, {"value": "Director Of Rehabilitation", "label": "Director Of Rehabilitation"}, {"value": "Director Of Sales", "label": "Director Of Sales"}, {"value": "Director Of Security", "label": "Director Of Security"}, {"value": "Director Of Social Services", "label": "Director Of Social Services"}, {"value": "Director Of Software Engineering", "label": "Director Of Software Engineering"}, {"value": "Director Of Special Education", "label": "Director Of Special Education"}, {"value": "Director Of Special Events", "label": "Director Of Special Events"}, {"value": "Director Of Sustainability", "label": "Director Of Sustainability"}, {"value": "Director Of Training", "label": "Director Of Training"}, {"value": "Disability Case Manager", "label": "Disability Case Manager"}, {"value": "Disc Jockey", "label": "Disc Jockey"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Dishwasher", "label": "Dishwasher"}, {"value": "Dispatch Manager", "label": "Dispatch Manager"}, {"value": "Di<PERSON>atcher", "label": "Di<PERSON>atcher"}, {"value": "Distribution Center Supervisor", "label": "Distribution Center Supervisor"}, {"value": "Distribution Clerk", "label": "Distribution Clerk"}, {"value": "Distribution Manager", "label": "Distribution Manager"}, {"value": "Distribution Supervisor", "label": "Distribution Supervisor"}, {"value": "Distributor", "label": "Distributor"}, {"value": "District Attorney", "label": "District Attorney"}, {"value": "District Manager", "label": "District Manager"}, {"value": "District Sales Manager", "label": "District Sales Manager"}, {"value": "Divemaster", "label": "Divemaster"}, {"value": "Diver", "label": "Diver"}, {"value": "Diversity Manager", "label": "Diversity Manager"}, {"value": "Diving Coach", "label": "Diving Coach"}, {"value": "Division Order Analyst", "label": "Division Order Analyst"}, {"value": "DJ", "label": "DJ"}, {"value": "<PERSON>na Analyst", "label": "<PERSON>na Analyst"}, {"value": "Dock Supervisor", "label": "Dock Supervisor"}, {"value": "Dock Worker", "label": "Dock Worker"}, {"value": "Doctor", "label": "Doctor"}, {"value": "Doctor Assistant", "label": "Doctor Assistant"}, {"value": "Document Control Manager", "label": "Document Control Manager"}, {"value": "Document Control Specialist", "label": "Document Control Specialist"}, {"value": "Document Controller", "label": "Document Controller"}, {"value": "Document Processor", "label": "Document Processor"}, {"value": "Document Review Attorney", "label": "Document Review Attorney"}, {"value": "Document Specialist", "label": "Document Specialist"}, {"value": "Documentation Specialist", "label": "Documentation Specialist"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Dog Daycare", "label": "Dog Daycare"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Dog Sitter", "label": "Dog Sitter"}, {"value": "<PERSON> Trainer", "label": "<PERSON> Trainer"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Domestic Violence Advocate", "label": "Domestic Violence Advocate"}, {"value": "<PERSON>man", "label": "<PERSON>man"}, {"value": "Dosimetrist", "label": "Dosimetrist"}, {"value": "Dot Net Developer", "label": "Dot Net Developer"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Dozer Operator", "label": "Dozer Operator"}, {"value": "Drafter", "label": "Drafter"}, {"value": "Draftsman", "label": "Draftsman"}, {"value": "Drama Teacher", "label": "Drama Teacher"}, {"value": "Driller", "label": "Driller"}, {"value": "Drilling Engineer", "label": "Drilling Engineer"}, {"value": "Drilling Fluids Engineer", "label": "Drilling Fluids Engineer"}, {"value": "Driver", "label": "Driver"}, {"value": "Driver Helper", "label": "Driver Helper"}, {"value": "Driver Recruiter", "label": "Driver Recruiter"}, {"value": "Driving Instructor", "label": "Driving Instructor"}, {"value": "Drone Pilot", "label": "Drone Pilot"}, {"value": "Drug And Alcohol Counselor", "label": "Drug And Alcohol Counselor"}, {"value": "Drug Safety Associate", "label": "Drug Safety Associate"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Drywall Finisher", "label": "Drywall Finisher"}, {"value": "Drywall Installer", "label": "Drywall Installer"}, {"value": "Dsp Engineer", "label": "Dsp Engineer"}, {"value": "Dump Truck Driver", "label": "Dump Truck Driver"}, {"value": "Early Childhood Education", "label": "Early Childhood Education"}, {"value": "Early Childhood Teacher", "label": "Early Childhood Teacher"}, {"value": "Early Intervention Specialist", "label": "Early Intervention Specialist"}, {"value": "Echocardiographer", "label": "Echocardiographer"}, {"value": "Ecologist", "label": "Ecologist"}, {"value": "Ecommerce", "label": "Ecommerce"}, {"value": "Ecommerce Manager", "label": "Ecommerce Manager"}, {"value": "Economic Analyst", "label": "Economic Analyst"}, {"value": "Economic Consultant", "label": "Economic Consultant"}, {"value": "Economics Teacher", "label": "Economics Teacher"}, {"value": "Economist", "label": "Economist"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Editor", "label": "Editor"}, {"value": "Editor In Chief", "label": "Editor In Chief"}, {"value": "Editorial Assistant", "label": "Editorial Assistant"}, {"value": "Education Administrator", "label": "Education Administrator"}, {"value": "Education Consultant", "label": "Education Consultant"}, {"value": "Education Coordinator", "label": "Education Coordinator"}, {"value": "Education Counselor", "label": "Education Counselor"}, {"value": "Education Specialist", "label": "Education Specialist"}, {"value": "Education Teacher", "label": "Education Teacher"}, {"value": "Educational Assistant", "label": "Educational Assistant"}, {"value": "Educational Consultant", "label": "Educational Consultant"}, {"value": "Educational Diagnostician", "label": "Educational Diagnostician"}, {"value": "Educator", "label": "Educator"}, {"value": "<PERSON><PERSON>ian", "label": "<PERSON><PERSON>ian"}, {"value": "Ehs Manager", "label": "Ehs Manager"}, {"value": "Ehs Specialist", "label": "Ehs Specialist"}, {"value": "Ekg Technician", "label": "Ekg Technician"}, {"value": "Elderly Caregiver", "label": "Elderly Caregiver"}, {"value": "Electric Lineman", "label": "Electric Lineman"}, {"value": "Electrical Apprentice", "label": "Electrical Apprentice"}, {"value": "Electrical Assistant", "label": "Electrical Assistant"}, {"value": "Electrical Design Engineer", "label": "Electrical Design Engineer"}, {"value": "Electrical Designer", "label": "Electrical Designer"}, {"value": "Electrical Drafter", "label": "Electrical Drafter"}, {"value": "Electrical Engineer", "label": "Electrical Engineer"}, {"value": "Electrical Engineering", "label": "Electrical Engineering"}, {"value": "Electrical Engineering Technician", "label": "Electrical Engineering Technician"}, {"value": "Electrical Estimator", "label": "Electrical Estimator"}, {"value": "Electrical Helper", "label": "Electrical Helper"}, {"value": "Electrical Inspector", "label": "Electrical Inspector"}, {"value": "Electrical Project Manager", "label": "Electrical Project Manager"}, {"value": "Electrical Superintendent", "label": "Electrical Superintendent"}, {"value": "Electrical Technician", "label": "Electrical Technician"}, {"value": "Electrician", "label": "Electrician"}, {"value": "Electrician Apprentice", "label": "Electrician Apprentice"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Electromechanical Technician", "label": "Electromechanical Technician"}, {"value": "Electronic Assembler", "label": "Electronic Assembler"}, {"value": "Electronic Engineer", "label": "Electronic Engineer"}, {"value": "Electronic Technician", "label": "Electronic Technician"}, {"value": "Electronics Engineer", "label": "Electronics Engineer"}, {"value": "Electronics Technician", "label": "Electronics Technician"}, {"value": "Elementary Art Teacher", "label": "Elementary Art Teacher"}, {"value": "Elementary Music Teacher", "label": "Elementary Music Teacher"}, {"value": "Elementary School Principal", "label": "Elementary School Principal"}, {"value": "Elementary School Teacher", "label": "Elementary School Teacher"}, {"value": "Elevator Mechanic", "label": "Elevator Mechanic"}, {"value": "Elevator Technician", "label": "Elevator Technician"}, {"value": "Eligibility Specialist", "label": "Eligibility Specialist"}, {"value": "Eligibility Worker", "label": "Eligibility Worker"}, {"value": "Email Marketing Specialist", "label": "Email Marketing Specialist"}, {"value": "Embalmer", "label": "Embalmer"}, {"value": "Embedded Engineer", "label": "Embedded Engineer"}, {"value": "Embedded Software Engineer", "label": "Embedded Software Engineer"}, {"value": "Embedded Systems Engineer", "label": "Embedded Systems Engineer"}, {"value": "Embroidery Machine Operator", "label": "Embroidery Machine Operator"}, {"value": "Embryologist", "label": "Embryologist"}, {"value": "Emergency Department Technician", "label": "Emergency Department Technician"}, {"value": "Emergency Dispatcher", "label": "Emergency Dispatcher"}, {"value": "Emergency Management", "label": "Emergency Management"}, {"value": "Emergency Management Director", "label": "Emergency Management Director"}, {"value": "Emergency Management Specialist", "label": "Emergency Management Specialist"}, {"value": "Emergency Medical Technician", "label": "Emergency Medical Technician"}, {"value": "Emergency Medicine Physician", "label": "Emergency Medicine Physician"}, {"value": "Emergency Medicine Physician Assistant", "label": "Emergency Medicine Physician Assistant"}, {"value": "Emergency Room Nurse", "label": "Emergency Room Nurse"}, {"value": "Emergency Room Technician", "label": "Emergency Room Technician"}, {"value": "Employee Relations Manager", "label": "Employee Relations Manager"}, {"value": "Employee Relations Specialist", "label": "Employee Relations Specialist"}, {"value": "Employment Consultant", "label": "Employment Consultant"}, {"value": "Employment Specialist", "label": "Employment Specialist"}, {"value": "EMT", "label": "EMT"}, {"value": "Endocrinologist", "label": "Endocrinologist"}, {"value": "Endodontist", "label": "Endodontist"}, {"value": "Endoscopy Technician", "label": "Endoscopy Technician"}, {"value": "Energy Analyst", "label": "Energy Analyst"}, {"value": "Energy Auditor", "label": "Energy Auditor"}, {"value": "Energy Consultant", "label": "Energy Consultant"}, {"value": "Energy Engineer", "label": "Energy Engineer"}, {"value": "Energy Manager", "label": "Energy Manager"}, {"value": "Engagement Manager", "label": "Engagement Manager"}, {"value": "Engineer", "label": "Engineer"}, {"value": "Engineering Assistant", "label": "Engineering Assistant"}, {"value": "Engineering Consultant", "label": "Engineering Consultant"}, {"value": "Engineering Director", "label": "Engineering Director"}, {"value": "Engineering Manager", "label": "Engineering Manager"}, {"value": "Engineering Program Manager", "label": "Engineering Program Manager"}, {"value": "Engineering Project Manager", "label": "Engineering Project Manager"}, {"value": "Engineering Teacher", "label": "Engineering Teacher"}, {"value": "Engineering Technician", "label": "Engineering Technician"}, {"value": "English Instructor", "label": "English Instructor"}, {"value": "English Teacher", "label": "English Teacher"}, {"value": "English Tutor", "label": "English Tutor"}, {"value": "Enologist", "label": "Enologist"}, {"value": "Enrolled Agent", "label": "Enrolled Agent"}, {"value": "Enrollment Specialist", "label": "Enrollment Specialist"}, {"value": "Enterprise Architect", "label": "Enterprise Architect"}, {"value": "Enterprise Data Architect", "label": "Enterprise Data Architect"}, {"value": "Entertainer", "label": "Entertainer"}, {"value": "Entertainment Writer", "label": "Entertainment Writer"}, {"value": "Entomologist", "label": "Entomologist"}, {"value": "Entry Level Accountant", "label": "Entry Level Accountant"}, {"value": "Entry Level Chemical Engineer", "label": "Entry Level Chemical Engineer"}, {"value": "Entry Level Electrical Engineer", "label": "Entry Level Electrical Engineer"}, {"value": "Entry Level Mechanical Engineer", "label": "Entry Level Mechanical Engineer"}, {"value": "Entry Level Programmer", "label": "Entry Level Programmer"}, {"value": "Environment Artist", "label": "Environment Artist"}, {"value": "Environmental Analyst", "label": "Environmental Analyst"}, {"value": "Environmental Attorney", "label": "Environmental Attorney"}, {"value": "Environmental Chemist", "label": "Environmental Chemist"}, {"value": "Environmental Consultant", "label": "Environmental Consultant"}, {"value": "Environmental Coordinator", "label": "Environmental Coordinator"}, {"value": "Environmental Educator", "label": "Environmental Educator"}, {"value": "Environmental Engineer", "label": "Environmental Engineer"}, {"value": "Environmental Health Specialist", "label": "Environmental Health Specialist"}, {"value": "Environmental Manager", "label": "Environmental Manager"}, {"value": "Environmental Planner", "label": "Environmental Planner"}, {"value": "Environmental Project Manager", "label": "Environmental Project Manager"}, {"value": "Environmental Scientist", "label": "Environmental Scientist"}, {"value": "Environmental Services Manager", "label": "Environmental Services Manager"}, {"value": "Environmental Specialist", "label": "Environmental Specialist"}, {"value": "Environmental Technician", "label": "Environmental Technician"}, {"value": "Environmentalist", "label": "Environmentalist"}, {"value": "Epic Analyst", "label": "Epic Analyst"}, {"value": "Epic Trainer", "label": "Epic Trainer"}, {"value": "Epidemiologist", "label": "Epidemiologist"}, {"value": "Equestrian", "label": "Equestrian"}, {"value": "Equipment Engineer", "label": "Equipment Engineer"}, {"value": "Equipment Manager", "label": "Equipment Manager"}, {"value": "Equipment Operator", "label": "Equipment Operator"}, {"value": "Equipment Specialist", "label": "Equipment Specialist"}, {"value": "Equipment Technician", "label": "Equipment Technician"}, {"value": "Equity Analyst", "label": "Equity Analyst"}, {"value": "Equity Research Analyst", "label": "Equity Research Analyst"}, {"value": "Equity Research Associate", "label": "Equity Research Associate"}, {"value": "Equity Trader", "label": "Equity Trader"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Er <PERSON>n", "label": "Er <PERSON>n"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON> Analyst", "label": "<PERSON><PERSON> Analyst"}, {"value": "Erp Consultant", "label": "Erp Consultant"}, {"value": "Erp Manager", "label": "Erp Manager"}, {"value": "Erp Project Manager", "label": "Erp Project Manager"}, {"value": "Escrow Assistant", "label": "Escrow Assistant"}, {"value": "Escrow Officer", "label": "Escrow Officer"}, {"value": "Esl Instructor", "label": "Esl Instructor"}, {"value": "Esl Teacher", "label": "Esl Teacher"}, {"value": "<PERSON><PERSON><PERSON> Tu<PERSON>", "label": "<PERSON><PERSON><PERSON> Tu<PERSON>"}, {"value": "Estate Manager", "label": "Estate Manager"}, {"value": "Estate Planning Attorney", "label": "Estate Planning Attorney"}, {"value": "Esthetician", "label": "Esthetician"}, {"value": "Estimator", "label": "Estimator"}, {"value": "Etl Developer", "label": "Etl Developer"}, {"value": "Etl Informatica Developer", "label": "Etl Informatica Developer"}, {"value": "<PERSON><PERSON> Tester", "label": "<PERSON><PERSON> Tester"}, {"value": "Event Coordinator", "label": "Event Coordinator"}, {"value": "Event Designer", "label": "Event Designer"}, {"value": "Event Director", "label": "Event Director"}, {"value": "Event Manager", "label": "Event Manager"}, {"value": "Event Planner", "label": "Event Planner"}, {"value": "Event Planning", "label": "Event Planning"}, {"value": "Event Planning Assistant", "label": "Event Planning Assistant"}, {"value": "Event Producer", "label": "Event Producer"}, {"value": "Event Specialist", "label": "Event Specialist"}, {"value": "Events Assistant", "label": "Events Assistant"}, {"value": "Evidence Technician", "label": "Evidence Technician"}, {"value": "Excavator", "label": "Excavator"}, {"value": "Excavator Operator", "label": "Excavator Operator"}, {"value": "Exchange Administrator", "label": "Exchange Administrator"}, {"value": "Exchange Engineer", "label": "Exchange Engineer"}, {"value": "Executive", "label": "Executive"}, {"value": "Executive Administrative Assistant", "label": "Executive Administrative Assistant"}, {"value": "Executive Administrator", "label": "Executive Administrator"}, {"value": "Executive Assistant", "label": "Executive Assistant"}, {"value": "Executive Chef", "label": "Executive Chef"}, {"value": "Executive Creative Director", "label": "Executive Creative Director"}, {"value": "Executive Director", "label": "Executive Director"}, {"value": "Executive Housekeeper", "label": "Executive Housekeeper"}, {"value": "Executive Manager", "label": "Executive Manager"}, {"value": "Executive <PERSON>ry Chef", "label": "Executive <PERSON>ry Chef"}, {"value": "Executive Personal Assistant", "label": "Executive Personal Assistant"}, {"value": "Executive Producer", "label": "Executive Producer"}, {"value": "Executive Recruiter", "label": "Executive Recruiter"}, {"value": "Executive Secretary", "label": "Executive Secretary"}, {"value": "Executive Sous Chef", "label": "Executive Sous Chef"}, {"value": "Executive Steward", "label": "Executive Steward"}, {"value": "Exercise Physiologist", "label": "Exercise Physiologist"}, {"value": "Exercise Specialist", "label": "Exercise Specialist"}, {"value": "Expeditor", "label": "Expeditor"}, {"value": "Export Coordinator", "label": "Export Coordinator"}, {"value": "Export Specialist", "label": "Export Specialist"}, {"value": "Exterminator", "label": "Exterminator"}, {"value": "External Auditor", "label": "External Auditor"}, {"value": "External Wholesaler", "label": "External Wholesaler"}, {"value": "Fabrication Manager", "label": "Fabrication Manager"}, {"value": "Fabricator", "label": "Fabricator"}, {"value": "Facilitator", "label": "Facilitator"}, {"value": "Facilities Administrator", "label": "Facilities Administrator"}, {"value": "Facilities Assistant", "label": "Facilities Assistant"}, {"value": "Facilities Coordinator", "label": "Facilities Coordinator"}, {"value": "Facilities Director", "label": "Facilities Director"}, {"value": "Facilities Engineer", "label": "Facilities Engineer"}, {"value": "Facilities Maintenance", "label": "Facilities Maintenance"}, {"value": "Facilities Management", "label": "Facilities Management"}, {"value": "Facilities Manager", "label": "Facilities Manager"}, {"value": "Facility Coordinator", "label": "Facility Coordinator"}, {"value": "Facility Engineer", "label": "Facility Engineer"}, {"value": "Facility Maintenance Manager", "label": "Facility Maintenance Manager"}, {"value": "Facility Manager", "label": "Facility Manager"}, {"value": "Facility Security Officer", "label": "Facility Security Officer"}, {"value": "Facility Supervisor", "label": "Facility Supervisor"}, {"value": "Facility Technician", "label": "Facility Technician"}, {"value": "Fact Checker", "label": "Fact Checker"}, {"value": "Factory Worker", "label": "Factory Worker"}, {"value": "Faculty", "label": "Faculty"}, {"value": "Failure Analysis Engineer", "label": "Failure Analysis Engineer"}, {"value": "Family Advocate", "label": "Family Advocate"}, {"value": "Family Consumer Science Teacher", "label": "Family Consumer Science Teacher"}, {"value": "Family Counselor", "label": "Family Counselor"}, {"value": "Family Medicine Physician", "label": "Family Medicine Physician"}, {"value": "Family Nurse Practitioner", "label": "Family Nurse Practitioner"}, {"value": "Family Physician", "label": "Family Physician"}, {"value": "Family Support Specialist", "label": "Family Support Specialist"}, {"value": "Family Support Worker", "label": "Family Support Worker"}, {"value": "Family Therapist", "label": "Family Therapist"}, {"value": "Farm", "label": "Farm"}, {"value": "Farm Hand", "label": "Farm Hand"}, {"value": "Farm Manager", "label": "Farm Manager"}, {"value": "Farm Worker", "label": "Farm Worker"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Fashion Buyer", "label": "Fashion Buyer"}, {"value": "Fashion Consultant", "label": "Fashion Consultant"}, {"value": "Fashion Designer", "label": "Fashion Designer"}, {"value": "Fashion Stylist", "label": "Fashion Stylist"}, {"value": "<PERSON><PERSON> Engineer", "label": "<PERSON><PERSON> Engineer"}, {"value": "Federal Air Marshal", "label": "Federal Air Marshal"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Fiber Optic Splicer", "label": "Fiber Optic Splicer"}, {"value": "Fiber Optic Technician", "label": "Fiber Optic Technician"}, {"value": "Field Application Engineer", "label": "Field Application Engineer"}, {"value": "Field Engineer", "label": "Field Engineer"}, {"value": "Field Inspector", "label": "Field Inspector"}, {"value": "Field Interviewer", "label": "Field Interviewer"}, {"value": "Field Investigator", "label": "Field Investigator"}, {"value": "Field Manager", "label": "Field Manager"}, {"value": "Field Marketing Manager", "label": "Field Marketing Manager"}, {"value": "Field Marketing Representative", "label": "Field Marketing Representative"}, {"value": "Field Operator", "label": "Field Operator"}, {"value": "Field Organizer", "label": "Field Organizer"}, {"value": "Field Representative", "label": "Field Representative"}, {"value": "Field Sales Manager", "label": "Field Sales Manager"}, {"value": "Field Sales Representative", "label": "Field Sales Representative"}, {"value": "Field Service Engineer", "label": "Field Service Engineer"}, {"value": "Field Service Representative", "label": "Field Service Representative"}, {"value": "Field Service Technician", "label": "Field Service Technician"}, {"value": "Field Technician", "label": "Field Technician"}, {"value": "Field Worker", "label": "Field Worker"}, {"value": "Figure Model", "label": "Figure Model"}, {"value": "File Clerk", "label": "File Clerk"}, {"value": "Film", "label": "Film"}, {"value": "Film Crew", "label": "Film Crew"}, {"value": "Film Producer", "label": "Film Producer"}, {"value": "Finance Administrator", "label": "Finance Administrator"}, {"value": "Finance Analyst", "label": "Finance Analyst"}, {"value": "Finance Assistant", "label": "Finance Assistant"}, {"value": "Finance Associate", "label": "Finance Associate"}, {"value": "Finance Consultant", "label": "Finance Consultant"}, {"value": "Finance Controller", "label": "Finance Controller"}, {"value": "Finance Director", "label": "Finance Director"}, {"value": "Finance Executive", "label": "Finance Executive"}, {"value": "Finance Manager", "label": "Finance Manager"}, {"value": "Finance Officer", "label": "Finance Officer"}, {"value": "Finance Professor", "label": "Finance Professor"}, {"value": "Finance Specialist", "label": "Finance Specialist"}, {"value": "Financial Accountant", "label": "Financial Accountant"}, {"value": "Financial Advisor", "label": "Financial Advisor"}, {"value": "Financial Advisor Trainee", "label": "Financial Advisor Trainee"}, {"value": "Financial Aid Advisor", "label": "Financial Aid Advisor"}, {"value": "Financial Analyst", "label": "Financial Analyst"}, {"value": "Financial Assistant", "label": "Financial Assistant"}, {"value": "Financial Associate", "label": "Financial Associate"}, {"value": "Financial Auditor", "label": "Financial Auditor"}, {"value": "Financial Business Analyst", "label": "Financial Business Analyst"}, {"value": "Financial Consultant", "label": "Financial Consultant"}, {"value": "Financial Controller", "label": "Financial Controller"}, {"value": "Financial Coordinator", "label": "Financial Coordinator"}, {"value": "Financial Counselor", "label": "Financial Counselor"}, {"value": "Financial Data Analyst", "label": "Financial Data Analyst"}, {"value": "Financial Engineer", "label": "Financial Engineer"}, {"value": "Financial Examiner", "label": "Financial Examiner"}, {"value": "Financial Manager", "label": "Financial Manager"}, {"value": "Financial Officer", "label": "Financial Officer"}, {"value": "Financial Planner", "label": "Financial Planner"}, {"value": "Financial Planning Analyst", "label": "Financial Planning Analyst"}, {"value": "Financial Reporting Analyst", "label": "Financial Reporting Analyst"}, {"value": "Financial Reporting Manager", "label": "Financial Reporting Manager"}, {"value": "Financial Representative", "label": "Financial Representative"}, {"value": "Financial Risk Analyst", "label": "Financial Risk Analyst"}, {"value": "Financial Services Representative", "label": "Financial Services Representative"}, {"value": "Financial Specialist", "label": "Financial Specialist"}, {"value": "Financial Systems Analyst", "label": "Financial Systems Analyst"}, {"value": "Fine Dining Server", "label": "Fine Dining Server"}, {"value": "Fingerprint Technician", "label": "Fingerprint Technician"}, {"value": "Finish Carpenter", "label": "Finish Carpenter"}, {"value": "Finisher", "label": "Finisher"}, {"value": "Fire Alarm Technician", "label": "Fire Alarm Technician"}, {"value": "Fire Captain", "label": "Fire Captain"}, {"value": "Fire Chief", "label": "Fire Chief"}, {"value": "Fire Inspector", "label": "Fire Inspector"}, {"value": "Fire Investigator", "label": "Fire Investigator"}, {"value": "Fire Marshal", "label": "Fire Marshal"}, {"value": "Fire Protection Engineer", "label": "Fire Protection Engineer"}, {"value": "Fire Safety Director", "label": "Fire Safety Director"}, {"value": "Fire Watch", "label": "Fire Watch"}, {"value": "Firearms Instructor", "label": "Firearms Instructor"}, {"value": "Firefighter", "label": "Firefighter"}, {"value": "Firefighter Paramedic", "label": "Firefighter Paramedic"}, {"value": "Fireman", "label": "Fireman"}, {"value": "Firmware Engineer", "label": "Firmware Engineer"}, {"value": "First Officer", "label": "First Officer"}, {"value": "Fisheries Biologist", "label": "Fisheries Biologist"}, {"value": "Fisheries Technician", "label": "Fisheries Technician"}, {"value": "Fitness Coach", "label": "Fitness Coach"}, {"value": "Fitness Consultant", "label": "Fitness Consultant"}, {"value": "Fitness Coordinator", "label": "Fitness Coordinator"}, {"value": "Fitness Director", "label": "Fitness Director"}, {"value": "Fitness Instructor", "label": "Fitness Instructor"}, {"value": "Fitness Manager", "label": "Fitness Manager"}, {"value": "Fitness Specialist", "label": "Fitness Specialist"}, {"value": "Fitness Trainer", "label": "Fitness Trainer"}, {"value": "Fitter", "label": "Fitter"}, {"value": "Fixed Income Analyst", "label": "Fixed Income Analyst"}, {"value": "Flagger", "label": "Flagger"}, {"value": "Flatbed Driver", "label": "Flatbed Driver"}, {"value": "Fleet Administrator", "label": "Fleet Administrator"}, {"value": "Fleet Coordinator", "label": "Fleet Coordinator"}, {"value": "Fleet Manager", "label": "Fleet Manager"}, {"value": "Fleet Mechanic", "label": "Fleet Mechanic"}, {"value": "Flight Attendant", "label": "Flight Attendant"}, {"value": "Flight Coordinator", "label": "Flight Coordinator"}, {"value": "Flight Dispatcher", "label": "Flight Dispatcher"}, {"value": "Flight Engineer", "label": "Flight Engineer"}, {"value": "Flight Instructor", "label": "Flight Instructor"}, {"value": "Flight Nurse", "label": "Flight Nurse"}, {"value": "Flight Paramedic", "label": "Flight Paramedic"}, {"value": "Flight Test Engineer", "label": "Flight Test Engineer"}, {"value": "Floor Manager", "label": "Floor Manager"}, {"value": "Floor Supervisor", "label": "Floor Supervisor"}, {"value": "Floor Technician", "label": "Floor Technician"}, {"value": "Flooring In<PERSON>ler", "label": "Flooring In<PERSON>ler"}, {"value": "Floral Assistant", "label": "Floral Assistant"}, {"value": "Floral Designer", "label": "Floral Designer"}, {"value": "Florist", "label": "Florist"}, {"value": "Food And Beverage Manager", "label": "Food And Beverage Manager"}, {"value": "Food Broker", "label": "Food Broker"}, {"value": "Food Editor", "label": "Food Editor"}, {"value": "Food Handler", "label": "Food Handler"}, {"value": "Food Inspector", "label": "Food Inspector"}, {"value": "Food Manager", "label": "Food Manager"}, {"value": "Food Prep", "label": "Food Prep"}, {"value": "Food Production Manager", "label": "Food Production Manager"}, {"value": "Food Production Worker", "label": "Food Production Worker"}, {"value": "Food Runner", "label": "Food Runner"}, {"value": "Food Safety Specialist", "label": "Food Safety Specialist"}, {"value": "Food Scientist", "label": "Food Scientist"}, {"value": "Food Server", "label": "Food Server"}, {"value": "Food Service Director", "label": "Food Service Director"}, {"value": "Food Service Manager", "label": "Food Service Manager"}, {"value": "Food Service Specialist", "label": "Food Service Specialist"}, {"value": "Food Service Supervisor", "label": "Food Service Supervisor"}, {"value": "Food Service Worker", "label": "Food Service Worker"}, {"value": "Food Services Manager", "label": "Food Services Manager"}, {"value": "Food Stylist", "label": "Food Stylist"}, {"value": "Food Technologist", "label": "Food Technologist"}, {"value": "Food Worker", "label": "Food Worker"}, {"value": "Football Coach", "label": "Football Coach"}, {"value": "Footwear Designer", "label": "Footwear Designer"}, {"value": "Foreclosure Specialist", "label": "Foreclosure Specialist"}, {"value": "Foreign Language Teacher", "label": "Foreign Language Teacher"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Forensic Accountant", "label": "Forensic Accountant"}, {"value": "Forensic Chemist", "label": "Forensic Chemist"}, {"value": "Forensic Engineer", "label": "Forensic Engineer"}, {"value": "Forensic Examiner", "label": "Forensic Examiner"}, {"value": "Forensic Investigator", "label": "Forensic Investigator"}, {"value": "Forensic Nurse", "label": "Forensic Nurse"}, {"value": "Forensic Pathologist", "label": "Forensic Pathologist"}, {"value": "Forensic Psychologist", "label": "Forensic Psychologist"}, {"value": "Forensic Scientist", "label": "Forensic Scientist"}, {"value": "Forensic Social Worker", "label": "Forensic Social Worker"}, {"value": "Forensic Specialist", "label": "Forensic Specialist"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Forestry Technician", "label": "Forestry Technician"}, {"value": "Fork Lift Operator", "label": "Fork Lift Operator"}, {"value": "Forklift", "label": "Forklift"}, {"value": "Forklift Driver", "label": "Forklift Driver"}, {"value": "Forklift Mechanic", "label": "Forklift Mechanic"}, {"value": "Forklift Operator", "label": "Forklift Operator"}, {"value": "Form Carpenter", "label": "Form Carpenter"}, {"value": "Formulation Chemist", "label": "Formulation Chemist"}, {"value": "Formulation Scientist", "label": "Formulation Scientist"}, {"value": "Foster Care Case Manager", "label": "Foster Care Case Manager"}, {"value": "Founder", "label": "Founder"}, {"value": "Fpga Design Engineer", "label": "Fpga Design Engineer"}, {"value": "Fpga Engineer", "label": "Fpga Engineer"}, {"value": "Framer", "label": "Framer"}, {"value": "Franchise Business Consultant", "label": "Franchise Business Consultant"}, {"value": "Franchise Manager", "label": "Franchise Manager"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Freelance", "label": "Freelance"}, {"value": "Freelance Artist", "label": "Freelance Artist"}, {"value": "Freelance Designer", "label": "Freelance Designer"}, {"value": "Freelance Editor", "label": "Freelance Editor"}, {"value": "Freelance Graphic Designer", "label": "Freelance Graphic Designer"}, {"value": "Freelance Makeup Artist", "label": "Freelance Makeup Artist"}, {"value": "Freelance Photographer", "label": "Freelance Photographer"}, {"value": "Freelance Producer", "label": "Freelance Producer"}, {"value": "Freelance Project Manager", "label": "Freelance Project Manager"}, {"value": "Freelance Translator", "label": "Freelance Translator"}, {"value": "Freelance Video Editor", "label": "Freelance Video Editor"}, {"value": "Freelance Writer", "label": "Freelance Writer"}, {"value": "Freelancer", "label": "Freelancer"}, {"value": "Freight Agent", "label": "Freight Agent"}, {"value": "Freight Associate", "label": "Freight Associate"}, {"value": "Freight Broker", "label": "Freight Broker"}, {"value": "Freight Coordinator", "label": "Freight Coordinator"}, {"value": "Freight Dispatcher", "label": "Freight Dispatcher"}, {"value": "<PERSON><PERSON><PERSON> Handler", "label": "<PERSON><PERSON><PERSON> Handler"}, {"value": "French Teacher", "label": "French Teacher"}, {"value": "Front Desk", "label": "Front Desk"}, {"value": "Front Desk Agent", "label": "Front Desk Agent"}, {"value": "Front Desk Assistant", "label": "Front Desk Assistant"}, {"value": "Front Desk Associate", "label": "Front Desk Associate"}, {"value": "Front Desk Attendant", "label": "Front Desk Attendant"}, {"value": "Front Desk Clerk", "label": "Front Desk Clerk"}, {"value": "Front Desk Coordinator", "label": "Front Desk Coordinator"}, {"value": "Front Desk Manager", "label": "Front Desk Manager"}, {"value": "Front Desk Receptionist", "label": "Front Desk Receptionist"}, {"value": "Front Desk Supervisor", "label": "Front Desk Supervisor"}, {"value": "Front End Developer", "label": "Front End Developer"}, {"value": "Front End Engineer", "label": "Front End Engineer"}, {"value": "Front End Web Developer", "label": "Front End Web Developer"}, {"value": "Front Of House Manager", "label": "Front Of House Manager"}, {"value": "Front Office Assistant", "label": "Front Office Assistant"}, {"value": "Front Office Clerk", "label": "Front Office Clerk"}, {"value": "Front Office Coordinator", "label": "Front Office Coordinator"}, {"value": "Front Office Manager", "label": "Front Office Manager"}, {"value": "Front Office Receptionist", "label": "Front Office Receptionist"}, {"value": "Front Office Supervisor", "label": "Front Office Supervisor"}, {"value": "Fuel Truck Driver", "label": "Fuel Truck Driver"}, {"value": "Full Charge Bookkeeper", "label": "Full Charge Bookkeeper"}, {"value": "Full Stack Developer", "label": "Full Stack Developer"}, {"value": "Functional Consultant", "label": "Functional Consultant"}, {"value": "Fund Accountant", "label": "Fund Accountant"}, {"value": "Fund Accounting Manager", "label": "Fund Accounting Manager"}, {"value": "Fund Manager", "label": "Fund Manager"}, {"value": "Fundraiser", "label": "Fundraiser"}, {"value": "Fundraising Manager", "label": "Fundraising Manager"}, {"value": "<PERSON>eral <PERSON>", "label": "<PERSON>eral <PERSON>"}, {"value": "Funeral Assistant", "label": "Funeral Assistant"}, {"value": "Funeral Attendant", "label": "Funeral Attendant"}, {"value": "Funeral Director", "label": "Funeral Director"}, {"value": "Furniture Designer", "label": "Furniture Designer"}, {"value": "Fx Artist", "label": "Fx Artist"}, {"value": "Gallery Assistant", "label": "Gallery Assistant"}, {"value": "Game Artist", "label": "Game Artist"}, {"value": "Game Designer", "label": "Game Designer"}, {"value": "Game Developer", "label": "Game Developer"}, {"value": "Game Master", "label": "Game Master"}, {"value": "Game Programmer", "label": "Game Programmer"}, {"value": "Game Tester", "label": "Game Tester"}, {"value": "Gamer", "label": "Gamer"}, {"value": "Garbage Truck Driver", "label": "Garbage Truck Driver"}, {"value": "Gardener", "label": "Gardener"}, {"value": "Gas Controller", "label": "Gas Controller"}, {"value": "Gas Plant Operator", "label": "Gas Plant Operator"}, {"value": "Gas Station Attendant", "label": "Gas Station Attendant"}, {"value": "Gas Station Cashier", "label": "Gas Station Cashier"}, {"value": "Gas Technician", "label": "Gas Technician"}, {"value": "Gastroenterologist", "label": "Gastroenterologist"}, {"value": "Gate Agent", "label": "Gate Agent"}, {"value": "Gate Guard", "label": "Gate Guard"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Gemologist", "label": "Gemologist"}, {"value": "General Accountant", "label": "General Accountant"}, {"value": "General Clerk", "label": "General Clerk"}, {"value": "General Contractor", "label": "General Contractor"}, {"value": "General Counsel", "label": "General Counsel"}, {"value": "General Dentist", "label": "General Dentist"}, {"value": "General Engineer", "label": "General Engineer"}, {"value": "General Labor", "label": "General Labor"}, {"value": "General Laborer", "label": "General Laborer"}, {"value": "General Ledger Accountant", "label": "General Ledger Accountant"}, {"value": "General Maintenance Technician", "label": "General Maintenance Technician"}, {"value": "General Manager", "label": "General Manager"}, {"value": "General Office Clerk", "label": "General Office Clerk"}, {"value": "General Practitioner", "label": "General Practitioner"}, {"value": "General Service Technician", "label": "General Service Technician"}, {"value": "General Surgeon", "label": "General Surgeon"}, {"value": "General Worker", "label": "General Worker"}, {"value": "Generalist", "label": "Generalist"}, {"value": "Generator Technician", "label": "Generator Technician"}, {"value": "Genetic Counselor", "label": "Genetic Counselor"}, {"value": "Geneticist", "label": "Geneticist"}, {"value": "Geographer", "label": "Geographer"}, {"value": "Geologist", "label": "Geologist"}, {"value": "Geophysicist", "label": "Geophysicist"}, {"value": "Geoscientist", "label": "Geoscientist"}, {"value": "Geospatial Analyst", "label": "Geospatial Analyst"}, {"value": "Geotechnical Engineer", "label": "Geotechnical Engineer"}, {"value": "Geriatric Nursing Assistant", "label": "Geriatric Nursing Assistant"}, {"value": "German Teacher", "label": "German Teacher"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Gis Manager", "label": "Gis Manager"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "G<PERSON>zier", "label": "G<PERSON>zier"}, {"value": "Global Account Manager", "label": "Global Account Manager"}, {"value": "Golf Cart Attendant", "label": "Golf Cart Attendant"}, {"value": "Golf Coach", "label": "Golf Coach"}, {"value": "Golf Course Superintendent", "label": "Golf Course Superintendent"}, {"value": "Golf Professional", "label": "Golf Professional"}, {"value": "Government Contractor", "label": "Government Contractor"}, {"value": "Graduate Assistant", "label": "Graduate Assistant"}, {"value": "Graduate Nurse", "label": "Graduate Nurse"}, {"value": "Grant Accountant", "label": "Grant Accountant"}, {"value": "Grant Coordinator", "label": "Grant Coordinator"}, {"value": "<PERSON> Writer", "label": "<PERSON> Writer"}, {"value": "Grants Administrator", "label": "Grants Administrator"}, {"value": "Grants Manager", "label": "Grants Manager"}, {"value": "Graphic Artist", "label": "Graphic Artist"}, {"value": "Graphic Design Assistant", "label": "Graphic Design Assistant"}, {"value": "Graphic Designer", "label": "Graphic Designer"}, {"value": "Graphic Web Designer", "label": "Graphic Web Designer"}, {"value": "Graphics Designer", "label": "Graphics Designer"}, {"value": "Greenhouse Manager", "label": "Greenhouse Manager"}, {"value": "Greeter", "label": "Greeter"}, {"value": "Grill Cook", "label": "Grill Cook"}, {"value": "Grocer", "label": "Grocer"}, {"value": "Grocer<PERSON> Bagger", "label": "Grocer<PERSON> Bagger"}, {"value": "Grocery Clerk", "label": "Grocery Clerk"}, {"value": "Grocery Manager", "label": "Grocery Manager"}, {"value": "Grocery Stocker", "label": "Grocery Stocker"}, {"value": "Grocery Store Manager", "label": "Grocery Store Manager"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Groundman", "label": "Groundman"}, {"value": "Grounds Manager", "label": "Grounds Manager"}, {"value": "Groundskeeper", "label": "Groundskeeper"}, {"value": "Groundsman", "label": "Groundsman"}, {"value": "Group Exercise Instructor", "label": "Group Exercise Instructor"}, {"value": "Group Fitness Instructor", "label": "Group Fitness Instructor"}, {"value": "Group Home Manager", "label": "Group Home Manager"}, {"value": "Group Sales Manager", "label": "Group Sales Manager"}, {"value": "Gse Mechanic", "label": "Gse Mechanic"}, {"value": "Guard", "label": "Guard"}, {"value": "Guest Relations Manager", "label": "Guest Relations Manager"}, {"value": "Guest Services", "label": "Guest Services"}, {"value": "Guest Services Manager", "label": "Guest Services Manager"}, {"value": "Guidance Counselor", "label": "Guidance Counselor"}, {"value": "Guide", "label": "Guide"}, {"value": "Guitar Teacher", "label": "Guitar Teacher"}, {"value": "Gunsmith", "label": "Gunsmith"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Gym", "label": "Gym"}, {"value": "Gymnastics Coach", "label": "Gymnastics Coach"}, {"value": "Gymnastics Instructor", "label": "Gymnastics Instructor"}, {"value": "Gynecologist", "label": "Gynecologist"}, {"value": "Hacker", "label": "Hacker"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Hair Assistant", "label": "Hair Assistant"}, {"value": "Hair Stylist", "label": "Hair Stylist"}, {"value": "Hairdresser", "label": "Hairdresser"}, {"value": "Hairstylist", "label": "Hairstylist"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Hardware Engineer", "label": "Hardware Engineer"}, {"value": "Hardware Technician", "label": "Hardware Technician"}, {"value": "Head Basketball Coach", "label": "Head Basketball Coach"}, {"value": "Head Cashier", "label": "Head Cashier"}, {"value": "Head Chef", "label": "Head Chef"}, {"value": "Head Coach", "label": "Head Coach"}, {"value": "Head Football Coach", "label": "Head Football Coach"}, {"value": "Head Of Security", "label": "Head Of Security"}, {"value": "Head Start Teacher", "label": "Head Start Teacher"}, {"value": "Head Teller", "label": "Head Teller"}, {"value": "Headhunter", "label": "Headhunter"}, {"value": "Health Aide", "label": "Health Aide"}, {"value": "Health And Safety Coordinator", "label": "Health And Safety Coordinator"}, {"value": "Health And Safety Manager", "label": "Health And Safety Manager"}, {"value": "Health Assistant", "label": "Health Assistant"}, {"value": "Health Care Administrator", "label": "Health Care Administrator"}, {"value": "Health Care Assistant", "label": "Health Care Assistant"}, {"value": "Health Coach", "label": "Health Coach"}, {"value": "Health Coordinator", "label": "Health Coordinator"}, {"value": "Health Data Analyst", "label": "Health Data Analyst"}, {"value": "Health Educator", "label": "Health Educator"}, {"value": "Health Information Manager", "label": "Health Information Manager"}, {"value": "Health Information Technician", "label": "Health Information Technician"}, {"value": "Health Inspector", "label": "Health Inspector"}, {"value": "Health Insurance Agent", "label": "Health Insurance Agent"}, {"value": "Health Insurance Specialist", "label": "Health Insurance Specialist"}, {"value": "Health Physicist", "label": "Health Physicist"}, {"value": "Health Policy Analyst", "label": "Health Policy Analyst"}, {"value": "Health Professional", "label": "Health Professional"}, {"value": "Health Psychologist", "label": "Health Psychologist"}, {"value": "Health Services Manager", "label": "Health Services Manager"}, {"value": "Health Specialist", "label": "Health Specialist"}, {"value": "Health Teacher", "label": "Health Teacher"}, {"value": "Health Technician", "label": "Health Technician"}, {"value": "Health Unit Coordinator", "label": "Health Unit Coordinator"}, {"value": "Healthcare Analyst", "label": "Healthcare Analyst"}, {"value": "Healthcare Business Analyst", "label": "Healthcare Business Analyst"}, {"value": "Healthcare Consultant", "label": "Healthcare Consultant"}, {"value": "Healthcare Data Analyst", "label": "Healthcare Data Analyst"}, {"value": "Healthcare Management", "label": "Healthcare Management"}, {"value": "Healthcare Manager", "label": "Healthcare Manager"}, {"value": "Healthcare Project Manager", "label": "Healthcare Project Manager"}, {"value": "Healthcare Recruiter", "label": "Healthcare Recruiter"}, {"value": "Hearing Aid Specialist", "label": "Hearing Aid Specialist"}, {"value": "Hearing Instrument Specialist", "label": "Hearing Instrument Specialist"}, {"value": "Hearing Officer", "label": "Hearing Officer"}, {"value": "Heavy Construction", "label": "Heavy Construction"}, {"value": "Heavy Equipment Mechanic", "label": "Heavy Equipment Mechanic"}, {"value": "Heavy Equipment Operator", "label": "Heavy Equipment Operator"}, {"value": "Heavy Equipment Operators", "label": "Heavy Equipment Operators"}, {"value": "Heavy Truck Driver", "label": "Heavy Truck Driver"}, {"value": "Hebrew Teacher", "label": "Hebrew Teacher"}, {"value": "Hedge Fund Accountant", "label": "Hedge Fund Accountant"}, {"value": "Hedge Fund Analyst", "label": "Hedge Fund Analyst"}, {"value": "Hedge Fund Manager", "label": "Hedge Fund Manager"}, {"value": "Helicopter Mechanic", "label": "Helicopter Mechanic"}, {"value": "Helicopter Pilot", "label": "Helicopter Pilot"}, {"value": "Help Desk", "label": "Help Desk"}, {"value": "Help Desk Administrator", "label": "Help Desk Administrator"}, {"value": "Help Desk Analyst", "label": "Help Desk Analyst"}, {"value": "Help Desk Manager", "label": "Help Desk Manager"}, {"value": "Help Desk Specialist", "label": "Help Desk Specialist"}, {"value": "Help Desk Technician", "label": "Help Desk Technician"}, {"value": "Helper", "label": "Helper"}, {"value": "Hemodialysis Technician", "label": "Hemodialysis Technician"}, {"value": "High School Biology Teacher", "label": "High School Biology Teacher"}, {"value": "High School Counselor", "label": "High School Counselor"}, {"value": "High School English Teacher", "label": "High School English Teacher"}, {"value": "High School History Teacher", "label": "High School History Teacher"}, {"value": "High School Math Teacher", "label": "High School Math Teacher"}, {"value": "High School Principal", "label": "High School Principal"}, {"value": "High School Science Teacher", "label": "High School Science Teacher"}, {"value": "High School Spanish Teacher", "label": "High School Spanish Teacher"}, {"value": "High School Teacher", "label": "High School Teacher"}, {"value": "Highway Engineer", "label": "Highway Engineer"}, {"value": "Highway Maintenance Worker", "label": "Highway Maintenance Worker"}, {"value": "Histologist", "label": "Histologist"}, {"value": "Histology Technician", "label": "Histology Technician"}, {"value": "Historian", "label": "Historian"}, {"value": "History Teacher", "label": "History Teacher"}, {"value": "History Tutor", "label": "History Tutor"}, {"value": "Histotechnician", "label": "Histotechnician"}, {"value": "Histotechnologist", "label": "Histotechnologist"}, {"value": "Home Attendant", "label": "Home Attendant"}, {"value": "Home Builder", "label": "Home Builder"}, {"value": "Home Care Aide", "label": "Home Care Aide"}, {"value": "Home Care Provider", "label": "Home Care Provider"}, {"value": "Home Caregiver", "label": "Home Caregiver"}, {"value": "Home Health Aide", "label": "Home Health Aide"}, {"value": "Home Health Care", "label": "Home Health Care"}, {"value": "Home Health Nurse", "label": "Home Health Nurse"}, {"value": "Home Inspector", "label": "Home Inspector"}, {"value": "Home Stager", "label": "Home Stager"}, {"value": "Homemaker", "label": "Homemaker"}, {"value": "Horse Trainer", "label": "Horse Trainer"}, {"value": "Horticultural Therapist", "label": "Horticultural Therapist"}, {"value": "Horticulturist", "label": "Horticulturist"}, {"value": "Hospice Aide", "label": "Hospice Aide"}, {"value": "Hospice Chaplain", "label": "Hospice Chaplain"}, {"value": "Hospice Nurse", "label": "Hospice Nurse"}, {"value": "Hospice Social Worker", "label": "Hospice Social Worker"}, {"value": "Hospital Administrator", "label": "Hospital Administrator"}, {"value": "Hospital Cleaner", "label": "Hospital Cleaner"}, {"value": "Hospital Director", "label": "Hospital Director"}, {"value": "Hospital Housekeeping", "label": "Hospital Housekeeping"}, {"value": "Hospital Medical Assistant", "label": "Hospital Medical Assistant"}, {"value": "Hospital Pharmacist", "label": "Hospital Pharmacist"}, {"value": "Hospital Receptionist", "label": "Hospital Receptionist"}, {"value": "Hospital Security Officer", "label": "Hospital Security Officer"}, {"value": "Hospital Social Worker", "label": "Hospital Social Worker"}, {"value": "Hospital Technician", "label": "Hospital Technician"}, {"value": "Hospitalist", "label": "Hospitalist"}, {"value": "Hospitality Aide", "label": "Hospitality Aide"}, {"value": "Hospitality Manager", "label": "Hospitality Manager"}, {"value": "Hostess", "label": "Hostess"}, {"value": "Hostler", "label": "Hostler"}, {"value": "Hot Shot", "label": "Hot Shot"}, {"value": "Hotel Assistant Manager", "label": "Hotel Assistant Manager"}, {"value": "Hotel Clerk", "label": "Hotel Clerk"}, {"value": "Hotel Concierge", "label": "Hotel Concierge"}, {"value": "Hotel Front Desk", "label": "Hotel Front Desk"}, {"value": "Hotel Front Office Manager", "label": "Hotel Front Office Manager"}, {"value": "Hotel General Manager", "label": "Hotel General Manager"}, {"value": "Hotel Housekeeping", "label": "Hotel Housekeeping"}, {"value": "Hotel Manager", "label": "Hotel Manager"}, {"value": "Hotel Night Auditor", "label": "Hotel Night Auditor"}, {"value": "Hotel Operations Manager", "label": "Hotel Operations Manager"}, {"value": "Hotel Receptionist", "label": "Hotel Receptionist"}, {"value": "Hotel Sales Manager", "label": "Hotel Sales Manager"}, {"value": "House Cleaner", "label": "House Cleaner"}, {"value": "House Manager", "label": "House Manager"}, {"value": "House Painter", "label": "House Painter"}, {"value": "House Sitter", "label": "House Sitter"}, {"value": "Housekeeper", "label": "Housekeeper"}, {"value": "Housekeeping", "label": "Housekeeping"}, {"value": "Housekeeping Attendant", "label": "Housekeeping Attendant"}, {"value": "Housekeeping Manager", "label": "Housekeeping Manager"}, {"value": "Housekeeping Supervisor", "label": "Housekeeping Supervisor"}, {"value": "Houseman", "label": "Houseman"}, {"value": "Houseperson", "label": "Houseperson"}, {"value": "Housing Coordinator", "label": "Housing Coordinator"}, {"value": "Housing Counselor", "label": "Housing Counselor"}, {"value": "Housing Specialist", "label": "Housing Specialist"}, {"value": "Hr Administrative Assistant", "label": "Hr Administrative Assistant"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "Hr Assistant", "label": "Hr Assistant"}, {"value": "Hr Consultant", "label": "Hr Consultant"}, {"value": "Hr Coordinator", "label": "Hr Coordinator"}, {"value": "Hr Director", "label": "Hr Director"}, {"value": "Hr Generalist", "label": "Hr Generalist"}, {"value": "Hr Manager", "label": "Hr Manager"}, {"value": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}, {"value": "Hr Representative", "label": "Hr Representative"}, {"value": "Hr Specialist", "label": "Hr Specialist"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Hris <PERSON>", "label": "Hris <PERSON>"}, {"value": "Hse Manager", "label": "Hse Manager"}, {"value": "Html Developer", "label": "Html Developer"}, {"value": "Human Factors Engineer", "label": "Human Factors Engineer"}, {"value": "Human Resource Assistant", "label": "Human Resource Assistant"}, {"value": "Human Resource Consultant", "label": "Human Resource Consultant"}, {"value": "Human Resources Administrator", "label": "Human Resources Administrator"}, {"value": "Human Resources Analyst", "label": "Human Resources Analyst"}, {"value": "Human Resources Assistant", "label": "Human Resources Assistant"}, {"value": "Human Resources Associate", "label": "Human Resources Associate"}, {"value": "Human Resources Business Partner", "label": "Human Resources Business Partner"}, {"value": "Human Resources Clerk", "label": "Human Resources Clerk"}, {"value": "Human Resources Coordinator", "label": "Human Resources Coordinator"}, {"value": "Human Resources Director", "label": "Human Resources Director"}, {"value": "Human Resources Executive", "label": "Human Resources Executive"}, {"value": "Human Resources Generalist", "label": "Human Resources Generalist"}, {"value": "Human Resources Manager", "label": "Human Resources Manager"}, {"value": "Human Resources Officer", "label": "Human Resources Officer"}, {"value": "Human Resources Recruiter", "label": "Human Resources Recruiter"}, {"value": "Human Resources Representative", "label": "Human Resources Representative"}, {"value": "Human Resources Specialist", "label": "Human Resources Specialist"}, {"value": "Hunting Guide", "label": "Hunting Guide"}, {"value": "Hvac", "label": "Hvac"}, {"value": "Hvac Apprentice", "label": "Hvac Apprentice"}, {"value": "Hvac Design Engineer", "label": "Hvac Design Engineer"}, {"value": "Hvac Engineer", "label": "Hvac Engineer"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Hvac Installer", "label": "Hvac Installer"}, {"value": "Hvac Instructor", "label": "Hvac Instructor"}, {"value": "Hvac Mechanic", "label": "Hvac Mechanic"}, {"value": "Hvac Sales", "label": "Hvac Sales"}, {"value": "Hvac Service Technician", "label": "Hvac Service Technician"}, {"value": "Hvac Technician", "label": "Hvac Technician"}, {"value": "Hydraulic Engineer", "label": "Hydraulic Engineer"}, {"value": "Hydrogeologist", "label": "Hydrogeologist"}, {"value": "Hydrologist", "label": "Hydrologist"}, {"value": "Hygienist", "label": "Hygienist"}, {"value": "Ic Design Engineer", "label": "Ic Design Engineer"}, {"value": "Icu Nurse", "label": "Icu Nurse"}, {"value": "Illustrator", "label": "Illustrator"}, {"value": "Imagery Analyst", "label": "Imagery Analyst"}, {"value": "Immigration Attorney", "label": "Immigration Attorney"}, {"value": "Immigration Paralegal", "label": "Immigration Paralegal"}, {"value": "Immigration Specialist", "label": "Immigration Specialist"}, {"value": "Immunologist", "label": "Immunologist"}, {"value": "Implementation Analyst", "label": "Implementation Analyst"}, {"value": "Implementation Consultant", "label": "Implementation Consultant"}, {"value": "Implementation Coordinator", "label": "Implementation Coordinator"}, {"value": "Implementation Manager", "label": "Implementation Manager"}, {"value": "Implementation Project Manager", "label": "Implementation Project Manager"}, {"value": "Implementation Specialist", "label": "Implementation Specialist"}, {"value": "Import Coordinator", "label": "Import Coordinator"}, {"value": "Import Specialist", "label": "Import Specialist"}, {"value": "In House Counsel", "label": "In House Counsel"}, {"value": "Incident Manager", "label": "Incident Manager"}, {"value": "Independent Adjuster", "label": "Independent Adjuster"}, {"value": "Independent Consultant", "label": "Independent Consultant"}, {"value": "Independent Contractor", "label": "Independent Contractor"}, {"value": "Independent Sales Representative", "label": "Independent Sales Representative"}, {"value": "Industrial Cleaner", "label": "Industrial Cleaner"}, {"value": "Industrial Designer", "label": "Industrial Designer"}, {"value": "Industrial Electrician", "label": "Industrial Electrician"}, {"value": "Industrial Engineer", "label": "Industrial Engineer"}, {"value": "Industrial Hygienist", "label": "Industrial Hygienist"}, {"value": "Industrial Maintenance Mechanic", "label": "Industrial Maintenance Mechanic"}, {"value": "Industrial Maintenance Technician", "label": "Industrial Maintenance Technician"}, {"value": "Industrial Mechanic", "label": "Industrial Mechanic"}, {"value": "Industrial Painter", "label": "Industrial Painter"}, {"value": "Industrial Production Manager", "label": "Industrial Production Manager"}, {"value": "Infant Teacher", "label": "Infant Teacher"}, {"value": "Infantry", "label": "Infantry"}, {"value": "Infantryman", "label": "Infantryman"}, {"value": "Informatica Developer", "label": "Informatica Developer"}, {"value": "Informatics Nurse", "label": "Informatics Nurse"}, {"value": "Information Analyst", "label": "Information Analyst"}, {"value": "Information Architect", "label": "Information Architect"}, {"value": "Information Assurance", "label": "Information Assurance"}, {"value": "Information Manager", "label": "Information Manager"}, {"value": "Information Officer", "label": "Information Officer"}, {"value": "Information Security", "label": "Information Security"}, {"value": "Information Security Analyst", "label": "Information Security Analyst"}, {"value": "Information Security Consultant", "label": "Information Security Consultant"}, {"value": "Information Security Engineer", "label": "Information Security Engineer"}, {"value": "Information Security Manager", "label": "Information Security Manager"}, {"value": "Information Security Officer", "label": "Information Security Officer"}, {"value": "Information Security Specialist", "label": "Information Security Specialist"}, {"value": "Information Specialist", "label": "Information Specialist"}, {"value": "Information Systems Manager", "label": "Information Systems Manager"}, {"value": "Information Technology", "label": "Information Technology"}, {"value": "Information Technology Director", "label": "Information Technology Director"}, {"value": "Information Technology Manager", "label": "Information Technology Manager"}, {"value": "Information Technology Technician", "label": "Information Technology Technician"}, {"value": "Infrastructure Analyst", "label": "Infrastructure Analyst"}, {"value": "Infrastructure Architect", "label": "Infrastructure Architect"}, {"value": "Infrastructure Engineer", "label": "Infrastructure Engineer"}, {"value": "Infrastructure Manager", "label": "Infrastructure Manager"}, {"value": "Infrastructure Project Manager", "label": "Infrastructure Project Manager"}, {"value": "Infusion Nurse", "label": "Infusion Nurse"}, {"value": "Innkeeper", "label": "Innkeeper"}, {"value": "Innovation Manager", "label": "Innovation Manager"}, {"value": "Inpatient Coder", "label": "Inpatient Coder"}, {"value": "Inside Sales Associate", "label": "Inside Sales Associate"}, {"value": "Inside Sales Executive", "label": "Inside Sales Executive"}, {"value": "Inside Sales Manager", "label": "Inside Sales Manager"}, {"value": "Inside Sales Representative", "label": "Inside Sales Representative"}, {"value": "Inspector", "label": "Inspector"}, {"value": "Inspector General", "label": "Inspector General"}, {"value": "Installation Coordinator", "label": "Installation Coordinator"}, {"value": "Installation Manager", "label": "Installation Manager"}, {"value": "Installation Technician", "label": "Installation Technician"}, {"value": "Installer", "label": "Installer"}, {"value": "Instructional Aide", "label": "Instructional Aide"}, {"value": "Instructional Assistant", "label": "Instructional Assistant"}, {"value": "Instructional Coach", "label": "Instructional Coach"}, {"value": "Instructional Coordinator", "label": "Instructional Coordinator"}, {"value": "Instructional Designer", "label": "Instructional Designer"}, {"value": "Instructional Technology Specialist", "label": "Instructional Technology Specialist"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "Instrument Engineer", "label": "Instrument Engineer"}, {"value": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"value": "Instrument Technician", "label": "Instrument Technician"}, {"value": "Instrumentation Technician", "label": "Instrumentation Technician"}, {"value": "Insulator", "label": "Insulator"}, {"value": "Insurance", "label": "Insurance"}, {"value": "Insurance Adjuster", "label": "Insurance Adjuster"}, {"value": "Insurance Agent", "label": "Insurance Agent"}, {"value": "Insurance Analyst", "label": "Insurance Analyst"}, {"value": "Insurance Assistant", "label": "Insurance Assistant"}, {"value": "Insurance Broker", "label": "Insurance Broker"}, {"value": "Insurance Clerk", "label": "Insurance Clerk"}, {"value": "Insurance Consultant", "label": "Insurance Consultant"}, {"value": "Insurance Coordinator", "label": "Insurance Coordinator"}, {"value": "Insurance Fraud Investigator", "label": "Insurance Fraud Investigator"}, {"value": "Insurance Investigator", "label": "Insurance Investigator"}, {"value": "Insurance Manager", "label": "Insurance Manager"}, {"value": "Insurance Producer", "label": "Insurance Producer"}, {"value": "Insurance Sales", "label": "Insurance Sales"}, {"value": "Insurance Sales Agent", "label": "Insurance Sales Agent"}, {"value": "Insurance Sales Manager", "label": "Insurance Sales Manager"}, {"value": "Insurance Sales Representative", "label": "Insurance Sales Representative"}, {"value": "Insurance Specialist", "label": "Insurance Specialist"}, {"value": "Insurance Underwriter", "label": "Insurance Underwriter"}, {"value": "Insurance Verification Specialist", "label": "Insurance Verification Specialist"}, {"value": "Intake Coordinator", "label": "Intake Coordinator"}, {"value": "Intake Specialist", "label": "Intake Specialist"}, {"value": "Integration Analyst", "label": "Integration Analyst"}, {"value": "Integration Architect", "label": "Integration Architect"}, {"value": "Integration Consultant", "label": "Integration Consultant"}, {"value": "Integration Developer", "label": "Integration Developer"}, {"value": "Integration Engineer", "label": "Integration Engineer"}, {"value": "Integration Specialist", "label": "Integration Specialist"}, {"value": "Intellectual Property Attorney", "label": "Intellectual Property Attorney"}, {"value": "Intellectual Property Paralegal", "label": "Intellectual Property Paralegal"}, {"value": "Intelligence", "label": "Intelligence"}, {"value": "Intelligence Analyst", "label": "Intelligence Analyst"}, {"value": "Intelligence Officer", "label": "Intelligence Officer"}, {"value": "Intelligence Specialist", "label": "Intelligence Specialist"}, {"value": "Interaction Designer", "label": "Interaction Designer"}, {"value": "Interactive Designer", "label": "Interactive Designer"}, {"value": "Interior Decorator", "label": "Interior Decorator"}, {"value": "Interior Design", "label": "Interior Design"}, {"value": "Interior Design Assistant", "label": "Interior Design Assistant"}, {"value": "Interior Design Consultant", "label": "Interior Design Consultant"}, {"value": "Interior Designer", "label": "Interior Designer"}, {"value": "Interior Painter", "label": "Interior Painter"}, {"value": "Interpreter", "label": "Interpreter"}, {"value": "Intervention Specialist", "label": "Intervention Specialist"}, {"value": "Interventional Radiologist", "label": "Interventional Radiologist"}, {"value": "Interviewer", "label": "Interviewer"}, {"value": "Inventor", "label": "Inventor"}, {"value": "Inventory", "label": "Inventory"}, {"value": "Inventory Accountant", "label": "Inventory Accountant"}, {"value": "Inventory Analyst", "label": "Inventory Analyst"}, {"value": "Inventory Associate", "label": "Inventory Associate"}, {"value": "Inventory Auditor", "label": "Inventory Auditor"}, {"value": "Inventory Clerk", "label": "Inventory Clerk"}, {"value": "Inventory Control Clerk", "label": "Inventory Control Clerk"}, {"value": "Inventory Control Manager", "label": "Inventory Control Manager"}, {"value": "Inventory Control Specialist", "label": "Inventory Control Specialist"}, {"value": "Inventory Control Supervisor", "label": "Inventory Control Supervisor"}, {"value": "Inventory Controller", "label": "Inventory Controller"}, {"value": "Inventory Coordinator", "label": "Inventory Coordinator"}, {"value": "Inventory Manager", "label": "Inventory Manager"}, {"value": "Inventory Planner", "label": "Inventory Planner"}, {"value": "Inventory Specialist", "label": "Inventory Specialist"}, {"value": "Inventory Supervisor", "label": "Inventory Supervisor"}, {"value": "Investigative Analyst", "label": "Investigative Analyst"}, {"value": "Investigator", "label": "Investigator"}, {"value": "Investment Accountant", "label": "Investment Accountant"}, {"value": "Investment Advisor", "label": "Investment Advisor"}, {"value": "Investment Analyst", "label": "Investment Analyst"}, {"value": "Investment Associate", "label": "Investment Associate"}, {"value": "Investment Banker", "label": "Investment Banker"}, {"value": "Investment Banking Analyst", "label": "Investment Banking Analyst"}, {"value": "Investment Banking Associate", "label": "Investment Banking Associate"}, {"value": "Investment Consultant", "label": "Investment Consultant"}, {"value": "Investment Manager", "label": "Investment Manager"}, {"value": "Investment Officer", "label": "Investment Officer"}, {"value": "Investment Specialist", "label": "Investment Specialist"}, {"value": "Investor", "label": "Investor"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Iphone Developer", "label": "Iphone Developer"}, {"value": "Ironworker", "label": "Ironworker"}, {"value": "Irrigation Technician", "label": "Irrigation Technician"}, {"value": "It Analyst", "label": "It Analyst"}, {"value": "It Architect", "label": "It Architect"}, {"value": "It Auditor", "label": "It Auditor"}, {"value": "It Business Analyst", "label": "It Business Analyst"}, {"value": "It Consultant", "label": "It Consultant"}, {"value": "It Director", "label": "It Director"}, {"value": "It Engineer", "label": "It Engineer"}, {"value": "It Manager", "label": "It Manager"}, {"value": "It Operations Manager", "label": "It Operations Manager"}, {"value": "It Program Manager", "label": "It Program Manager"}, {"value": "It Project Coordinator", "label": "It Project Coordinator"}, {"value": "It Project Manager", "label": "It Project Manager"}, {"value": "It Recruiter", "label": "It Recruiter"}, {"value": "It Security Engineer", "label": "It Security Engineer"}, {"value": "It Specialist", "label": "It Specialist"}, {"value": "It Support", "label": "It Support"}, {"value": "It Support Analyst", "label": "It Support Analyst"}, {"value": "It Support Specialist", "label": "It Support Specialist"}, {"value": "J2Ee Developer", "label": "J2Ee Developer"}, {"value": "Jailer", "label": "Jailer"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Janitorial Manager", "label": "Janitorial Manager"}, {"value": "Janitorial Supervisor", "label": "Janitorial Supervisor"}, {"value": "Java", "label": "Java"}, {"value": "Java Architect", "label": "Java Architect"}, {"value": "Java Consultant", "label": "Java Consultant"}, {"value": "Java Developer", "label": "Java Developer"}, {"value": "Java Engineer", "label": "Java Engineer"}, {"value": "Java Programmer", "label": "Java Programmer"}, {"value": "Java Software Developer", "label": "Java Software Developer"}, {"value": "Java Software Engineer", "label": "Java Software Engineer"}, {"value": "Java Web Developer", "label": "Java Web Developer"}, {"value": "Javascript Developer", "label": "Javascript Developer"}, {"value": "Jeweler", "label": "Jeweler"}, {"value": "Jewelry Consultant", "label": "Jewelry Consultant"}, {"value": "Jewelry Designer", "label": "Jewelry Designer"}, {"value": "Job Coach", "label": "Job Coach"}, {"value": "Journalism", "label": "Journalism"}, {"value": "Journalist", "label": "Journalist"}, {"value": "Journeyman", "label": "Journeyman"}, {"value": "Journeyman Electrician", "label": "Journeyman Electrician"}, {"value": "Journeyman Lineman", "label": "Journeyman Lineman"}, {"value": "Journeyman Plumber", "label": "Journeyman Plumber"}, {"value": "Judge", "label": "Judge"}, {"value": "Judicial Law Clerk", "label": "Judicial Law Clerk"}, {"value": "Junior Accountant", "label": "Junior Accountant"}, {"value": "Junior Analyst", "label": "Junior Analyst"}, {"value": "Junior Architect", "label": "Junior Architect"}, {"value": "Junior Art Director", "label": "Junior Art Director"}, {"value": "Junior Business Analyst", "label": "Junior Business Analyst"}, {"value": "Junior Buyer", "label": "Junior Buyer"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Junior Data Analyst", "label": "Junior Data Analyst"}, {"value": "Junior Designer", "label": "Junior Designer"}, {"value": "Junior Developer", "label": "Junior Developer"}, {"value": "Junior Engineer", "label": "Junior Engineer"}, {"value": "Junior Financial Analyst", "label": "Junior Financial Analyst"}, {"value": "Junior Graphic Designer", "label": "Junior Graphic Designer"}, {"value": "Junior Java Developer", "label": "Junior Java Developer"}, {"value": "Junior Loan <PERSON>or", "label": "Junior Loan <PERSON>or"}, {"value": "Junior Mechanical Engineer", "label": "Junior Mechanical Engineer"}, {"value": "Junior Network Engineer", "label": "Junior Network Engineer"}, {"value": "Junior Programmer", "label": "Junior Programmer"}, {"value": "Junior Project Manager", "label": "Junior Project Manager"}, {"value": "Junior Recruiter", "label": "Junior Recruiter"}, {"value": "Junior Software Developer", "label": "Junior Software Developer"}, {"value": "Junior Software Engineer", "label": "Junior Software Engineer"}, {"value": "Junior Systems Administrator", "label": "Junior Systems Administrator"}, {"value": "Junior Underwriter", "label": "Junior Underwriter"}, {"value": "Junior Web Developer", "label": "Junior Web Developer"}, {"value": "Jury Consultant", "label": "Jury Consultant"}, {"value": "Justice", "label": "Justice"}, {"value": "Juvenile Detention Officer", "label": "Juvenile Detention Officer"}, {"value": "Juvenile Probation Officer", "label": "Juvenile Probation Officer"}, {"value": "K9 Handler", "label": "K9 Handler"}, {"value": "<PERSON>nel Assistant", "label": "<PERSON>nel Assistant"}, {"value": "<PERSON><PERSON>ant", "label": "<PERSON><PERSON>ant"}, {"value": "Kennel Technician", "label": "Kennel Technician"}, {"value": "Key Account Manager", "label": "Key Account Manager"}, {"value": "Key Holder", "label": "Key Holder"}, {"value": "Kindergarten Teacher", "label": "Kindergarten Teacher"}, {"value": "Kitchen Assistant", "label": "Kitchen Assistant"}, {"value": "Kitchen Designer", "label": "Kitchen Designer"}, {"value": "Kitchen Hand", "label": "Kitchen Hand"}, {"value": "Kitchen Helper", "label": "Kitchen Helper"}, {"value": "Kitchen Manager", "label": "Kitchen Manager"}, {"value": "Kitchen Staff", "label": "Kitchen Staff"}, {"value": "Kitchen Steward", "label": "Kitchen Steward"}, {"value": "Kitchen Supervisor", "label": "Kitchen Supervisor"}, {"value": "Kitchen Worker", "label": "Kitchen Worker"}, {"value": "Knowledge Management Specialist", "label": "Knowledge Management Specialist"}, {"value": "Knowledge Manager", "label": "Knowledge Manager"}, {"value": "Kyc Analyst", "label": "Kyc Analyst"}, {"value": "Lab Aide", "label": "Lab Aide"}, {"value": "Lab Analyst", "label": "Lab Analyst"}, {"value": "Lab Assistant", "label": "Lab Assistant"}, {"value": "Lab Technician", "label": "Lab Technician"}, {"value": "Lab Technologist", "label": "Lab Technologist"}, {"value": "Labor Relations Manager", "label": "Labor Relations Manager"}, {"value": "Labor Relations Specialist", "label": "Labor Relations Specialist"}, {"value": "Laboratory Analyst", "label": "Laboratory Analyst"}, {"value": "Laboratory Assistant", "label": "Laboratory Assistant"}, {"value": "Laboratory Director", "label": "Laboratory Director"}, {"value": "Laboratory Manager", "label": "Laboratory Manager"}, {"value": "Laboratory Supervisor", "label": "Laboratory Supervisor"}, {"value": "Laboratory Technician", "label": "Laboratory Technician"}, {"value": "Laboratory Technologist", "label": "Laboratory Technologist"}, {"value": "Laborer", "label": "Laborer"}, {"value": "Lacrosse Coach", "label": "Lacrosse Coach"}, {"value": "Lactation Consultant", "label": "Lactation Consultant"}, {"value": "Land Surveyor", "label": "Land Surveyor"}, {"value": "Landman", "label": "Landman"}, {"value": "Landscape Architect", "label": "Landscape Architect"}, {"value": "Landscape Designer", "label": "Landscape Designer"}, {"value": "Landscape Foreman", "label": "Landscape Foreman"}, {"value": "Landscape Laborer", "label": "Landscape Laborer"}, {"value": "Landscape Manager", "label": "Landscape Manager"}, {"value": "Landscape Technician", "label": "Landscape Technician"}, {"value": "Landscaper", "label": "Landscaper"}, {"value": "Landscaping", "label": "Landscaping"}, {"value": "Language Arts Teacher", "label": "Language Arts Teacher"}, {"value": "Language Specialist", "label": "Language Specialist"}, {"value": "Laser Operator", "label": "Laser Operator"}, {"value": "Laser Technician", "label": "Laser Technician"}, {"value": "Lathe Machinist", "label": "Lathe Machinist"}, {"value": "Lathe Operator", "label": "Lathe Operator"}, {"value": "Latin Teacher", "label": "Latin Teacher"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Law Clerk", "label": "Law Clerk"}, {"value": "Law Enforcement Officer", "label": "Law Enforcement Officer"}, {"value": "Law Librarian", "label": "Law Librarian"}, {"value": "Law Office Assistant", "label": "Law Office Assistant"}, {"value": "Law Professor", "label": "Law Professor"}, {"value": "Lawyer", "label": "Lawyer"}, {"value": "Lead Auditor", "label": "Lead Auditor"}, {"value": "Lead Carpenter", "label": "Lead Carpenter"}, {"value": "Lead Developer", "label": "Lead Developer"}, {"value": "Lead Engineer", "label": "Lead Engineer"}, {"value": "Lead Generation Specialist", "label": "Lead Generation Specialist"}, {"value": "Lead Generator", "label": "Lead Generator"}, {"value": "Lead Mechanical Engineer", "label": "Lead Mechanical Engineer"}, {"value": "Lead Medical Assistant", "label": "Lead Medical Assistant"}, {"value": "Lead Pastor", "label": "Lead Pastor"}, {"value": "Lead Pharmacy Technician", "label": "Lead Pharmacy Technician"}, {"value": "Lead Software Engineer", "label": "Lead Software Engineer"}, {"value": "Lead Teacher", "label": "Lead Teacher"}, {"value": "Lead Technician", "label": "Lead Technician"}, {"value": "Leadership Development Program", "label": "Leadership Development Program"}, {"value": "Lean Consultant", "label": "Lean Consultant"}, {"value": "Learning And Development Manager", "label": "Learning And Development Manager"}, {"value": "Learning And Development Specialist", "label": "Learning And Development Specialist"}, {"value": "Learning Consultant", "label": "Learning Consultant"}, {"value": "Learning Specialist", "label": "Learning Specialist"}, {"value": "Lease Administrator", "label": "Lease Administrator"}, {"value": "Lease Operator", "label": "Lease Operator"}, {"value": "Leasing Agent", "label": "Leasing Agent"}, {"value": "Leasing Consultant", "label": "Leasing Consultant"}, {"value": "Leasing Manager", "label": "Leasing Manager"}, {"value": "Leasing Specialist", "label": "Leasing Specialist"}, {"value": "Lecturer", "label": "Lecturer"}, {"value": "Legal Administrative Assistant", "label": "Legal Administrative Assistant"}, {"value": "Legal Administrator", "label": "Legal Administrator"}, {"value": "Legal Analyst", "label": "Legal Analyst"}, {"value": "Legal Assistant", "label": "Legal Assistant"}, {"value": "Legal Clerk", "label": "Legal Clerk"}, {"value": "Legal Consultant", "label": "Legal Consultant"}, {"value": "Legal Counsel", "label": "Legal Counsel"}, {"value": "Legal Editor", "label": "Legal Editor"}, {"value": "Legal Nurse Consultant", "label": "Legal Nurse Consultant"}, {"value": "Legal Officer", "label": "Legal Officer"}, {"value": "Legal Receptionist", "label": "Legal Receptionist"}, {"value": "Legal Secretary", "label": "Legal Secretary"}, {"value": "Legislative Assistant", "label": "Legislative Assistant"}, {"value": "Lending Manager", "label": "Lending Manager"}, {"value": "Letter Carrier", "label": "Letter Carrier"}, {"value": "<PERSON>ison", "label": "<PERSON>ison"}, {"value": "Librarian", "label": "Librarian"}, {"value": "Library Aide", "label": "Library Aide"}, {"value": "Library Assistant", "label": "Library Assistant"}, {"value": "Library Associate", "label": "Library Associate"}, {"value": "Library Clerk", "label": "Library Clerk"}, {"value": "Library Director", "label": "Library Director"}, {"value": "Library Media Specialist", "label": "Library Media Specialist"}, {"value": "Library Page", "label": "Library Page"}, {"value": "Library Technician", "label": "Library Technician"}, {"value": "Licensed Customs Broker", "label": "Licensed Customs Broker"}, {"value": "Licensed Insurance Agent", "label": "Licensed Insurance Agent"}, {"value": "Licensed Massage Therapist", "label": "Licensed Massage Therapist"}, {"value": "Licensed Practical Nurse", "label": "Licensed Practical Nurse"}, {"value": "Licensed Practical Nurse Lpn", "label": "Licensed Practical Nurse Lpn"}, {"value": "Licensed Professional Counselor", "label": "Licensed Professional Counselor"}, {"value": "Life Coach", "label": "Life Coach"}, {"value": "Life Insurance Agent", "label": "Life Insurance Agent"}, {"value": "Lifeguard", "label": "Lifeguard"}, {"value": "Lift Operator", "label": "Lift Operator"}, {"value": "Lighting Designer", "label": "Lighting Designer"}, {"value": "Lighting Technician", "label": "Lighting Technician"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Line Cook", "label": "Line Cook"}, {"value": "Line Leader", "label": "Line Leader"}, {"value": "Line Operator", "label": "Line Operator"}, {"value": "Line Producer", "label": "Line Producer"}, {"value": "Line Service Technician", "label": "Line Service Technician"}, {"value": "Lineman", "label": "Lineman"}, {"value": "Lineman Apprentice", "label": "Lineman Apprentice"}, {"value": "Linguist", "label": "Linguist"}, {"value": "Linux Administrator", "label": "Linux Administrator"}, {"value": "Linux Engineer", "label": "Linux Engineer"}, {"value": "Linux System Administrator", "label": "Linux System Administrator"}, {"value": "Linux System Engineer", "label": "Linux System Engineer"}, {"value": "Literacy Specialist", "label": "Literacy Specialist"}, {"value": "Litigation Associate", "label": "Litigation Associate"}, {"value": "Litigation Attorney", "label": "Litigation Attorney"}, {"value": "Litigation Legal Assistant", "label": "Litigation Legal Assistant"}, {"value": "Litigation Paralegal", "label": "Litigation Paralegal"}, {"value": "Live In Caregiver", "label": "Live In Caregiver"}, {"value": "Live In Nanny", "label": "Live In Nanny"}, {"value": "Loader", "label": "Loader"}, {"value": "Loader Operator", "label": "Loader Operator"}, {"value": "Loadmaster", "label": "Loadmaster"}, {"value": "Loan Administrator", "label": "Loan Administrator"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Loan Assistant", "label": "Loan Assistant"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Loan Consultant", "label": "Loan Consultant"}, {"value": "Loan Coordinator", "label": "Loan Coordinator"}, {"value": "Loan Manager", "label": "Loan Manager"}, {"value": "Loan Officer", "label": "Loan Officer"}, {"value": "Loan Officer Assistant", "label": "Loan Officer Assistant"}, {"value": "Loan Originator", "label": "Loan Originator"}, {"value": "Loan Processor", "label": "Loan Processor"}, {"value": "Loan Servicing Specialist", "label": "Loan Servicing Specialist"}, {"value": "Loan Specialist", "label": "Loan Specialist"}, {"value": "Loan Underwriter", "label": "Loan Underwriter"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Lobbyist", "label": "Lobbyist"}, {"value": "Local Cdl", "label": "Local Cdl"}, {"value": "Local Delivery Driver", "label": "Local Delivery Driver"}, {"value": "Local Truck Driver", "label": "Local Truck Driver"}, {"value": "Locker Room Attendant", "label": "Locker Room Attendant"}, {"value": "Locksmith", "label": "Locksmith"}, {"value": "Locomotive Engineer", "label": "Locomotive Engineer"}, {"value": "Log Truck Driver", "label": "Log Truck Driver"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Logistician", "label": "Logistician"}, {"value": "Logistics Administrator", "label": "Logistics Administrator"}, {"value": "Logistics Analyst", "label": "Logistics Analyst"}, {"value": "Logistics Assistant", "label": "Logistics Assistant"}, {"value": "Logistics Associate", "label": "Logistics Associate"}, {"value": "Logistics Clerk", "label": "Logistics Clerk"}, {"value": "Logistics Consultant", "label": "Logistics Consultant"}, {"value": "Logistics Coordinator", "label": "Logistics Coordinator"}, {"value": "Logistics Engineer", "label": "Logistics Engineer"}, {"value": "Logistics Manager", "label": "Logistics Manager"}, {"value": "Logistics Planner", "label": "Logistics Planner"}, {"value": "Logistics Specialist", "label": "Logistics Specialist"}, {"value": "Logistics Supervisor", "label": "Logistics Supervisor"}, {"value": "Long Term Substitute Teacher", "label": "Long Term Substitute Teacher"}, {"value": "Longshoreman", "label": "Longshoreman"}, {"value": "Loss Mitigation Specialist", "label": "Loss Mitigation Specialist"}, {"value": "Loss Prevention", "label": "Loss Prevention"}, {"value": "Loss Prevention Detective", "label": "Loss Prevention Detective"}, {"value": "Loss Prevention Investigator", "label": "Loss Prevention Investigator"}, {"value": "Loss Prevention Manager", "label": "Loss Prevention Manager"}, {"value": "Loss Prevention Officer", "label": "Loss Prevention Officer"}, {"value": "Loss Prevention Specialist", "label": "Loss Prevention Specialist"}, {"value": "Lot Attendant", "label": "Lot Attendant"}, {"value": "Low Voltage Electrician", "label": "Low Voltage Electrician"}, {"value": "Low Voltage Technician", "label": "Low Voltage Technician"}, {"value": "Lpn", "label": "Lpn"}, {"value": "Lpn Nurse", "label": "Lpn Nurse"}, {"value": "Lube Technician", "label": "Lube Technician"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Lvn", "label": "Lvn"}, {"value": "Machine Builder", "label": "Machine Builder"}, {"value": "Machine Learning Engineer", "label": "Machine Learning Engineer"}, {"value": "Machine Operator", "label": "Machine Operator"}, {"value": "Machinist", "label": "Machinist"}, {"value": "Magazine Editor", "label": "Magazine Editor"}, {"value": "Maid", "label": "Maid"}, {"value": "Mail Carrier", "label": "Mail Carrier"}, {"value": "Mail Clerk", "label": "Mail Clerk"}, {"value": "Mail Handler", "label": "Mail Handler"}, {"value": "Mail Processing", "label": "Mail Processing"}, {"value": "Mail Processing Clerk", "label": "Mail Processing Clerk"}, {"value": "Mail Sorter", "label": "Mail Sorter"}, {"value": "Mailroom Clerk", "label": "Mailroom Clerk"}, {"value": "Mainframe Developer", "label": "Mainframe Developer"}, {"value": "Mainframe Programmer", "label": "Mainframe Programmer"}, {"value": "Maintenance", "label": "Maintenance"}, {"value": "Maintenance Assistant", "label": "Maintenance Assistant"}, {"value": "Maintenance Coordinator", "label": "Maintenance Coordinator"}, {"value": "Maintenance Director", "label": "Maintenance Director"}, {"value": "Maintenance Electrician", "label": "Maintenance Electrician"}, {"value": "Maintenance Engineer", "label": "Maintenance Engineer"}, {"value": "Maintenance Manager", "label": "Maintenance Manager"}, {"value": "Maintenance Mechanic", "label": "Maintenance Mechanic"}, {"value": "Maintenance Planner", "label": "Maintenance Planner"}, {"value": "Maintenance Specialist", "label": "Maintenance Specialist"}, {"value": "Maintenance Superintendent", "label": "Maintenance Superintendent"}, {"value": "Maintenance Supervisor", "label": "Maintenance Supervisor"}, {"value": "Maintenance Technician", "label": "Maintenance Technician"}, {"value": "Maintenance Worker", "label": "Maintenance Worker"}, {"value": "Major Gifts Officer", "label": "Major Gifts Officer"}, {"value": "Make Up Artist", "label": "Make Up Artist"}, {"value": "Makeup Artist", "label": "Makeup Artist"}, {"value": "Male Model", "label": "Male Model"}, {"value": "Mall", "label": "Mall"}, {"value": "Mamm<PERSON>", "label": "Mamm<PERSON>"}, {"value": "Mammography Technologist", "label": "Mammography Technologist"}, {"value": "Management Accountant", "label": "Management Accountant"}, {"value": "Management Analyst", "label": "Management Analyst"}, {"value": "Management Assistant", "label": "Management Assistant"}, {"value": "Management Consultant", "label": "Management Consultant"}, {"value": "Management Consulting", "label": "Management Consulting"}, {"value": "Management Engineer", "label": "Management Engineer"}, {"value": "Management Trainee", "label": "Management Trainee"}, {"value": "Manager", "label": "Manager"}, {"value": "Manager <PERSON>ee", "label": "Manager <PERSON>ee"}, {"value": "Managing Consultant", "label": "Managing Consultant"}, {"value": "Managing Director", "label": "Managing Director"}, {"value": "Managing Editor", "label": "Managing Editor"}, {"value": "Managing Partner", "label": "Managing Partner"}, {"value": "Mandarin Teacher", "label": "Mandarin Teacher"}, {"value": "Manicurist", "label": "Manicurist"}, {"value": "Manual Machinist", "label": "Manual Machinist"}, {"value": "Manual Qa Tester", "label": "Manual Qa Tester"}, {"value": "Manual Tester", "label": "Manual Tester"}, {"value": "Manufacturing", "label": "Manufacturing"}, {"value": "Manufacturing Analyst", "label": "Manufacturing Analyst"}, {"value": "Manufacturing Associate", "label": "Manufacturing Associate"}, {"value": "Manufacturing Engineer", "label": "Manufacturing Engineer"}, {"value": "Manufacturing Manager", "label": "Manufacturing Manager"}, {"value": "Manufacturing Operations Manager", "label": "Manufacturing Operations Manager"}, {"value": "Manufacturing Operator", "label": "Manufacturing Operator"}, {"value": "Manufacturing Planner", "label": "Manufacturing Planner"}, {"value": "Manufacturing Process Engineer", "label": "Manufacturing Process Engineer"}, {"value": "Manufacturing Specialist", "label": "Manufacturing Specialist"}, {"value": "Manufacturing Supervisor", "label": "Manufacturing Supervisor"}, {"value": "Manufacturing Technician", "label": "Manufacturing Technician"}, {"value": "Marine", "label": "Marine"}, {"value": "Marine Biologist", "label": "Marine Biologist"}, {"value": "Marine Electrician", "label": "Marine Electrician"}, {"value": "Marine Engineer", "label": "Marine Engineer"}, {"value": "Marine Mechanic", "label": "Marine Mechanic"}, {"value": "Marine Surveyor", "label": "Marine Surveyor"}, {"value": "Marine Technician", "label": "Marine Technician"}, {"value": "Market Analyst", "label": "Market Analyst"}, {"value": "Market Manager", "label": "Market Manager"}, {"value": "Market Research Analyst", "label": "Market Research Analyst"}, {"value": "Market Research Interviewer", "label": "Market Research Interviewer"}, {"value": "Market Research Manager", "label": "Market Research Manager"}, {"value": "Market Researcher", "label": "Market Researcher"}, {"value": "Marketer", "label": "Marketer"}, {"value": "Marketing", "label": "Marketing"}, {"value": "Marketing Account Executive", "label": "Marketing Account Executive"}, {"value": "Marketing Account Manager", "label": "Marketing Account Manager"}, {"value": "Marketing Administrator", "label": "Marketing Administrator"}, {"value": "Marketing Analyst", "label": "Marketing Analyst"}, {"value": "Marketing Assistant", "label": "Marketing Assistant"}, {"value": "Marketing Associate", "label": "Marketing Associate"}, {"value": "Marketing Automation Specialist", "label": "Marketing Automation Specialist"}, {"value": "Marketing Communications", "label": "Marketing Communications"}, {"value": "Marketing Communications Manager", "label": "Marketing Communications Manager"}, {"value": "Marketing Communications Specialist", "label": "Marketing Communications Specialist"}, {"value": "Marketing Consultant", "label": "Marketing Consultant"}, {"value": "Marketing Coordinator", "label": "Marketing Coordinator"}, {"value": "Marketing Copywriter", "label": "Marketing Copywriter"}, {"value": "Marketing Data Analyst", "label": "Marketing Data Analyst"}, {"value": "Marketing Director", "label": "Marketing Director"}, {"value": "Marketing Engineer", "label": "Marketing Engineer"}, {"value": "Marketing Executive", "label": "Marketing Executive"}, {"value": "Marketing Manager", "label": "Marketing Manager"}, {"value": "Marketing Officer", "label": "Marketing Officer"}, {"value": "Marketing Operations Manager", "label": "Marketing Operations Manager"}, {"value": "Marketing Professional", "label": "Marketing Professional"}, {"value": "Marketing Project Manager", "label": "Marketing Project Manager"}, {"value": "Marketing Representative", "label": "Marketing Representative"}, {"value": "Marketing Research Analyst", "label": "Marketing Research Analyst"}, {"value": "Marketing Specialist", "label": "Marketing Specialist"}, {"value": "Marketing Strategist", "label": "Marketing Strategist"}, {"value": "Marriage And Family Therapist", "label": "Marriage And Family Therapist"}, {"value": "Mascot", "label": "Mascot"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Massage Therapist", "label": "Massage Therapist"}, {"value": "Master Control Operator", "label": "Master Control Operator"}, {"value": "Master Electrician", "label": "Master Electrician"}, {"value": "Master Esthetician", "label": "Master Esthetician"}, {"value": "Master Plumber", "label": "Master Plumber"}, {"value": "Master Scheduler", "label": "Master Scheduler"}, {"value": "Material Controller", "label": "Material Controller"}, {"value": "Material Engineer", "label": "Material Engineer"}, {"value": "Material Handler", "label": "Material Handler"}, {"value": "Materials Coordinator", "label": "Materials Coordinator"}, {"value": "Materials Engineer", "label": "Materials Engineer"}, {"value": "Materials Manager", "label": "Materials Manager"}, {"value": "Materials Planner", "label": "Materials Planner"}, {"value": "Materials Scientist", "label": "Materials Scientist"}, {"value": "Math Instructor", "label": "Math Instructor"}, {"value": "Math Teacher", "label": "Math Teacher"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Mathematician", "label": "Mathematician"}, {"value": "Mds Coordinator", "label": "Mds Coordinator"}, {"value": "Mds Nurse", "label": "Mds Nurse"}, {"value": "Meat Cutter", "label": "Meat Cutter"}, {"value": "Meat Manager", "label": "Meat Manager"}, {"value": "Mechanic", "label": "Mechanic"}, {"value": "Mechanic Helper", "label": "Mechanic Helper"}, {"value": "Mechanical Assembler", "label": "Mechanical Assembler"}, {"value": "Mechanical Design Engineer", "label": "Mechanical Design Engineer"}, {"value": "Mechanical Designer", "label": "Mechanical Designer"}, {"value": "Mechanical Drafter", "label": "Mechanical Drafter"}, {"value": "Mechanical Engineer", "label": "Mechanical Engineer"}, {"value": "Mechanical Engineering Technician", "label": "Mechanical Engineering Technician"}, {"value": "Mechanical Inspector", "label": "Mechanical Inspector"}, {"value": "Mechanical Project Engineer", "label": "Mechanical Project Engineer"}, {"value": "Mechanical Technician", "label": "Mechanical Technician"}, {"value": "Mechatronics Engineer", "label": "Mechatronics Engineer"}, {"value": "Media Analyst", "label": "Media Analyst"}, {"value": "Media Assistant", "label": "Media Assistant"}, {"value": "Media Buyer", "label": "Media Buyer"}, {"value": "Media Consultant", "label": "Media Consultant"}, {"value": "Media Coordinator", "label": "Media Coordinator"}, {"value": "Media Director", "label": "Media Director"}, {"value": "Media Executive", "label": "Media Executive"}, {"value": "Media Planner", "label": "Media Planner"}, {"value": "Media Producer", "label": "Media Producer"}, {"value": "Media Relations Manager", "label": "Media Relations Manager"}, {"value": "Media Specialist", "label": "Media Specialist"}, {"value": "Media Strategist", "label": "Media Strategist"}, {"value": "Mediator", "label": "Mediator"}, {"value": "Medical Administrative Assistant", "label": "Medical Administrative Assistant"}, {"value": "Medical Administrative Specialist", "label": "Medical Administrative Specialist"}, {"value": "Medical Administrator", "label": "Medical Administrator"}, {"value": "Medical Assembler", "label": "Medical Assembler"}, {"value": "Medical Assistant", "label": "Medical Assistant"}, {"value": "Medical Assistant In<PERSON><PERSON>ctor", "label": "Medical Assistant In<PERSON><PERSON>ctor"}, {"value": "<PERSON> Bill<PERSON>", "label": "<PERSON> Bill<PERSON>"}, {"value": "Medical Billing Clerk", "label": "Medical Billing Clerk"}, {"value": "Medical Billing Specialist", "label": "Medical Billing Specialist"}, {"value": "Medical Case Manager", "label": "Medical Case Manager"}, {"value": "Medical Claims Processor", "label": "Medical Claims Processor"}, {"value": "Medical Clerk", "label": "Medical Clerk"}, {"value": "Medical Coder", "label": "Medical Coder"}, {"value": "Medical Coding Specialist", "label": "Medical Coding Specialist"}, {"value": "Medical Collector", "label": "Medical Collector"}, {"value": "Medical Consultant", "label": "Medical Consultant"}, {"value": "Medical Courier", "label": "Medical Courier"}, {"value": "Medical Director", "label": "Medical Director"}, {"value": "Medical Doctor", "label": "Medical Doctor"}, {"value": "Medical Dosimetrist", "label": "Medical Dosimetrist"}, {"value": "Medical Editor", "label": "Medical Editor"}, {"value": "Medical Engineer", "label": "Medical Engineer"}, {"value": "Medical Esthetician", "label": "Medical Esthetician"}, {"value": "Medical Examiner", "label": "Medical Examiner"}, {"value": "Medical Illustrator", "label": "Medical Illustrator"}, {"value": "Medical Information Specialist", "label": "Medical Information Specialist"}, {"value": "Medical Instructor", "label": "Medical Instructor"}, {"value": "Medical Interpreter", "label": "Medical Interpreter"}, {"value": "Medical Lab Assistant", "label": "Medical Lab Assistant"}, {"value": "Medical Laboratory        Technologist", "label": "Medical Laboratory        Technologist"}, {"value": "Medical Laboratory Scientist", "label": "Medical Laboratory Scientist"}, {"value": "Medical Laboratory Technician", "label": "Medical Laboratory Technician"}, {"value": "Medical Liaison", "label": "Medical Liaison"}, {"value": "Medical Librarian", "label": "Medical Librarian"}, {"value": "Medical Manager", "label": "Medical Manager"}, {"value": "Medical Office Administrator", "label": "Medical Office Administrator"}, {"value": "Medical Office Assistant", "label": "Medical Office Assistant"}, {"value": "Medical Office Manager", "label": "Medical Office Manager"}, {"value": "Medical Office Receptionist", "label": "Medical Office Receptionist"}, {"value": "Medical Office Specialist", "label": "Medical Office Specialist"}, {"value": "Medical Officer", "label": "Medical Officer"}, {"value": "Medical Physicist", "label": "Medical Physicist"}, {"value": "Medical Practice Manager", "label": "Medical Practice Manager"}, {"value": "Medical Receptionist", "label": "Medical Receptionist"}, {"value": "Medical Records Clerk", "label": "Medical Records Clerk"}, {"value": "Medical Records Specialist", "label": "Medical Records Specialist"}, {"value": "Medical Records Technician", "label": "Medical Records Technician"}, {"value": "Medical Recruiter", "label": "Medical Recruiter"}, {"value": "Medical Representative", "label": "Medical Representative"}, {"value": "Medical Researcher", "label": "Medical Researcher"}, {"value": "Medical Sales", "label": "Medical Sales"}, {"value": "Medical Sales Representative", "label": "Medical Sales Representative"}, {"value": "Medical Scheduler", "label": "Medical Scheduler"}, {"value": "Medical Science Liaison", "label": "Medical Science Liaison"}, {"value": "Medical Scientist", "label": "Medical Scientist"}, {"value": "Medical Scribe", "label": "Medical Scribe"}, {"value": "Medical Secretary", "label": "Medical Secretary"}, {"value": "Medical Social Worker", "label": "Medical Social Worker"}, {"value": "Medical Sonographer", "label": "Medical Sonographer"}, {"value": "Medical Staff Coordinator", "label": "Medical Staff Coordinator"}, {"value": "Medical Support Assistant", "label": "Medical Support Assistant"}, {"value": "Medical Technician", "label": "Medical Technician"}, {"value": "Medical Technologist", "label": "Medical Technologist"}, {"value": "Medical Transcription", "label": "Medical Transcription"}, {"value": "Medical Transcriptionist", "label": "Medical Transcriptionist"}, {"value": "Medical Writer", "label": "Medical Writer"}, {"value": "Medication Aide", "label": "Medication Aide"}, {"value": "Medication Technician", "label": "Medication Technician"}, {"value": "Meeting Planner", "label": "Meeting Planner"}, {"value": "Membership Director", "label": "Membership Director"}, {"value": "Mental Health Assistant", "label": "Mental Health Assistant"}, {"value": "Mental Health Associate", "label": "Mental Health Associate"}, {"value": "Mental Health Case Manager", "label": "Mental Health Case Manager"}, {"value": "Mental Health Clinician", "label": "Mental Health Clinician"}, {"value": "Mental Health Counselor", "label": "Mental Health Counselor"}, {"value": "Mental Health Nurse", "label": "Mental Health Nurse"}, {"value": "Mental Health Professional", "label": "Mental Health Professional"}, {"value": "Mental Health Specialist", "label": "Mental Health Specialist"}, {"value": "Mental Health Technician", "label": "Mental Health Technician"}, {"value": "Mental Health Therapist", "label": "Mental Health Therapist"}, {"value": "Mental Health Worker", "label": "Mental Health Worker"}, {"value": "Mentor", "label": "Mentor"}, {"value": "Merchandise Buyer", "label": "Merchandise Buyer"}, {"value": "Merchandise Coordinator", "label": "Merchandise Coordinator"}, {"value": "Merchandise Planner", "label": "Merchandise Planner"}, {"value": "Merchandiser", "label": "Merchandiser"}, {"value": "Merchandising", "label": "Merchandising"}, {"value": "Merchandising Assistant", "label": "Merchandising Assistant"}, {"value": "Merchandising Coordinator", "label": "Merchandising Coordinator"}, {"value": "Merchandising Manager", "label": "Merchandising Manager"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Metallurgical Engineer", "label": "Metallurgical Engineer"}, {"value": "Metallurgist", "label": "Metallurgist"}, {"value": "Meteorologist", "label": "Meteorologist"}, {"value": "Meter Reader", "label": "Meter Reader"}, {"value": "Microbiologist", "label": "Microbiologist"}, {"value": "Microbiology", "label": "Microbiology"}, {"value": "Microbiology Technician", "label": "Microbiology Technician"}, {"value": "Microbiology Technologist", "label": "Microbiology Technologist"}, {"value": "Microstrategy Developer", "label": "Microstrategy Developer"}, {"value": "Middle School Principal", "label": "Middle School Principal"}, {"value": "Middle School Science Teacher", "label": "Middle School Science Teacher"}, {"value": "Middle School Teacher", "label": "Middle School Teacher"}, {"value": "Middleware Engineer", "label": "Middleware Engineer"}, {"value": "Midwife", "label": "Midwife"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Military", "label": "Military"}, {"value": "Military Analyst", "label": "Military Analyst"}, {"value": "Military Officer", "label": "Military Officer"}, {"value": "Military Police", "label": "Military Police"}, {"value": "Military Police Officer", "label": "Military Police Officer"}, {"value": "Millwright", "label": "Millwright"}, {"value": "Miner", "label": "Miner"}, {"value": "Mining Engineer", "label": "Mining Engineer"}, {"value": "Minister", "label": "Minister"}, {"value": "Missions Pastor", "label": "Missions Pastor"}, {"value": "Mobile Application Developer", "label": "Mobile Application Developer"}, {"value": "Mobile Developer", "label": "Mobile Developer"}, {"value": "Mobile Engineer", "label": "Mobile Engineer"}, {"value": "Mobile Phlebotomist", "label": "Mobile Phlebotomist"}, {"value": "Model", "label": "Model"}, {"value": "Model Maker", "label": "Model Maker"}, {"value": "Modeler", "label": "Modeler"}, {"value": "Mold Maker", "label": "Mold Maker"}, {"value": "Molecular Biologist", "label": "Molecular Biologist"}, {"value": "Molecular Technologist", "label": "Molecular Technologist"}, {"value": "Money Manager", "label": "Money Manager"}, {"value": "Monitor", "label": "Monitor"}, {"value": "Monitor Technician", "label": "Monitor Technician"}, {"value": "Montessori Teacher", "label": "Montessori Teacher"}, {"value": "Mortgage Advisor", "label": "Mortgage Advisor"}, {"value": "Mortgage Assistant", "label": "Mortgage Assistant"}, {"value": "Mortgage Banker", "label": "Mortgage Banker"}, {"value": "Mortgage Broker", "label": "Mortgage Broker"}, {"value": "Mortgage Closer", "label": "Mortgage Closer"}, {"value": "Mortgage Consultant", "label": "Mortgage Consultant"}, {"value": "Mortgage Loan Closer", "label": "Mortgage Loan Closer"}, {"value": "Mortgage Loan Officer", "label": "Mortgage Loan Officer"}, {"value": "Mortgage Loan Processor", "label": "Mortgage Loan Processor"}, {"value": "Mortgage Originator", "label": "Mortgage Originator"}, {"value": "Mortgage Processor", "label": "Mortgage Processor"}, {"value": "Mortgage Specialist", "label": "Mortgage Specialist"}, {"value": "Mortgage Underwriter", "label": "Mortgage Underwriter"}, {"value": "Mortician", "label": "Mortician"}, {"value": "Motion Graphics Designer", "label": "Motion Graphics Designer"}, {"value": "Motorcycle Mechanic", "label": "Motorcycle Mechanic"}, {"value": "Motorcycle Technician", "label": "Motorcycle Technician"}, {"value": "Mover", "label": "Mover"}, {"value": "Mover Helper", "label": "Mover Helper"}, {"value": "<PERSON><PERSON> Technician", "label": "<PERSON><PERSON> Technician"}, {"value": "Mri Technologist", "label": "Mri Technologist"}, {"value": "Mud Engineer", "label": "Mud Engineer"}, {"value": "Multimedia Artist", "label": "Multimedia Artist"}, {"value": "Multimedia Designer", "label": "Multimedia Designer"}, {"value": "Multimedia Journalist", "label": "Multimedia Journalist"}, {"value": "Multimedia Specialist", "label": "Multimedia Specialist"}, {"value": "Museum Curator", "label": "Museum Curator"}, {"value": "Museum Director", "label": "Museum Director"}, {"value": "Museum Educator", "label": "Museum Educator"}, {"value": "Museum Registrar", "label": "Museum Registrar"}, {"value": "Music Director", "label": "Music Director"}, {"value": "Music Librarian", "label": "Music Librarian"}, {"value": "Music Producer", "label": "Music Producer"}, {"value": "Music Teacher", "label": "Music Teacher"}, {"value": "Music Therapist", "label": "Music Therapist"}, {"value": "<PERSON>ian", "label": "<PERSON>ian"}, {"value": "Mystery Shopper", "label": "Mystery Shopper"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "National Account Executive", "label": "National Account Executive"}, {"value": "National Account Manager", "label": "National Account Manager"}, {"value": "National Guard", "label": "National Guard"}, {"value": "National Sales Director", "label": "National Sales Director"}, {"value": "National Sales Manager", "label": "National Sales Manager"}, {"value": "Naturalist", "label": "Naturalist"}, {"value": "Naturopathic Doctor", "label": "Naturopathic Doctor"}, {"value": "Naval Architect", "label": "Naval Architect"}, {"value": "Naval Engineer", "label": "Naval Engineer"}, {"value": "Ndt Technician", "label": "Ndt Technician"}, {"value": "Negotiator", "label": "Negotiator"}, {"value": "Neonatal Nurse", "label": "Neonatal Nurse"}, {"value": "Neonatal Nurse Practitioner", "label": "Neonatal Nurse Practitioner"}, {"value": "Neonatologist", "label": "Neonatologist"}, {"value": "Nephrologist", "label": "Nephrologist"}, {"value": "Net Developer", "label": "Net Developer"}, {"value": "Netsuite Administrator", "label": "Netsuite Administrator"}, {"value": "Netsuite Consultant", "label": "Netsuite Consultant"}, {"value": "Network Administrator", "label": "Network Administrator"}, {"value": "Network Analyst", "label": "Network Analyst"}, {"value": "Network Architect", "label": "Network Architect"}, {"value": "Network Consultant", "label": "Network Consultant"}, {"value": "Network Designer", "label": "Network Designer"}, {"value": "Network Engineer", "label": "Network Engineer"}, {"value": "Network Manager", "label": "Network Manager"}, {"value": "Network Operations Manager", "label": "Network Operations Manager"}, {"value": "Network Security Administrator", "label": "Network Security Administrator"}, {"value": "Network Security Analyst", "label": "Network Security Analyst"}, {"value": "Network Security Engineer", "label": "Network Security Engineer"}, {"value": "Network Specialist", "label": "Network Specialist"}, {"value": "Network Support Engineer", "label": "Network Support Engineer"}, {"value": "Network Support Specialist", "label": "Network Support Specialist"}, {"value": "Network Support Technician", "label": "Network Support Technician"}, {"value": "Network Systems Administrator", "label": "Network Systems Administrator"}, {"value": "Network Systems Engineer", "label": "Network Systems Engineer"}, {"value": "Network Technician", "label": "Network Technician"}, {"value": "Neurodiagnostic Technologist", "label": "Neurodiagnostic Technologist"}, {"value": "Neurologist", "label": "Neurologist"}, {"value": "Neuropsychologist", "label": "Neuropsychologist"}, {"value": "Neuroscientist", "label": "Neuroscientist"}, {"value": "Neurosurgeon", "label": "Neurosurgeon"}, {"value": "New Home Sales Consultant", "label": "New Home Sales Consultant"}, {"value": "News Anchor", "label": "News Anchor"}, {"value": "News Director", "label": "News Director"}, {"value": "News Editor", "label": "News Editor"}, {"value": "News Photographer", "label": "News Photographer"}, {"value": "News Producer", "label": "News Producer"}, {"value": "News Reporter", "label": "News Reporter"}, {"value": "Newspaper Carrier", "label": "Newspaper Carrier"}, {"value": "Newspaper Delivery", "label": "Newspaper Delivery"}, {"value": "Night", "label": "Night"}, {"value": "Night Auditor", "label": "Night Auditor"}, {"value": "Night Manager", "label": "Night Manager"}, {"value": "Night Stocker", "label": "Night Stocker"}, {"value": "Noc Engineer", "label": "Noc Engineer"}, {"value": "Noc Manager", "label": "Noc Manager"}, {"value": "Noc Technician", "label": "Noc Technician"}, {"value": "Non Cdl Driver", "label": "Non Cdl Driver"}, {"value": "Notary Public", "label": "Notary Public"}, {"value": "Nuclear Engineer", "label": "Nuclear Engineer"}, {"value": "Nuclear Medicine Technologist", "label": "Nuclear Medicine Technologist"}, {"value": "Nuclear Pharmacist", "label": "Nuclear Pharmacist"}, {"value": "Nuclear Physicist", "label": "Nuclear Physicist"}, {"value": "Nurse", "label": "Nurse"}, {"value": "Nurse Administrator", "label": "Nurse Administrator"}, {"value": "Nurse Aide", "label": "Nurse Aide"}, {"value": "Nurse Anesthetist", "label": "Nurse Anesthetist"}, {"value": "Nurse Assistant", "label": "Nurse Assistant"}, {"value": "Nurse Case Manager", "label": "Nurse Case Manager"}, {"value": "Nurse Clinician", "label": "Nurse Clinician"}, {"value": "Nurse Consultant", "label": "Nurse Consultant"}, {"value": "Nurse Coordinator", "label": "Nurse Coordinator"}, {"value": "Nurse Educator", "label": "Nurse Educator"}, {"value": "Nurse Extern", "label": "Nurse Extern"}, {"value": "Nurse <PERSON><PERSON>", "label": "Nurse <PERSON><PERSON>"}, {"value": "Nurse Manager", "label": "Nurse Manager"}, {"value": "Nurse Midwife", "label": "Nurse Midwife"}, {"value": "Nurse Practitioner", "label": "Nurse Practitioner"}, {"value": "Nurse Recruiter", "label": "Nurse Recruiter"}, {"value": "Nurse Rn", "label": "Nurse Rn"}, {"value": "Nurse Specialist", "label": "Nurse Specialist"}, {"value": "Nurse Supervisor", "label": "Nurse Supervisor"}, {"value": "Nurse Technician", "label": "Nurse Technician"}, {"value": "Nursery Worker", "label": "Nursery Worker"}, {"value": "Nursing", "label": "Nursing"}, {"value": "Nursing Assistant", "label": "Nursing Assistant"}, {"value": "Nursing Attendant", "label": "Nursing Attendant"}, {"value": "Nursing Faculty", "label": "Nursing Faculty"}, {"value": "Nursing Home", "label": "Nursing Home"}, {"value": "Nursing Home Administrator", "label": "Nursing Home Administrator"}, {"value": "Nursing Instructor", "label": "Nursing Instructor"}, {"value": "Nursing Supervisor", "label": "Nursing Supervisor"}, {"value": "Nutrition Assistant", "label": "Nutrition Assistant"}, {"value": "Nutrition Director", "label": "Nutrition Director"}, {"value": "Nutrition Educator", "label": "Nutrition Educator"}, {"value": "Nutrition Manager", "label": "Nutrition Manager"}, {"value": "Nutritionist", "label": "Nutritionist"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Obstetrician", "label": "Obstetrician"}, {"value": "Occupational Health Nurse", "label": "Occupational Health Nurse"}, {"value": "Occupational Medicine Physician", "label": "Occupational Medicine Physician"}, {"value": "Occupational Nurse", "label": "Occupational Nurse"}, {"value": "Occupational Therapist", "label": "Occupational Therapist"}, {"value": "Occupational Therapy Aide", "label": "Occupational Therapy Aide"}, {"value": "Occupational Therapy Assistant", "label": "Occupational Therapy Assistant"}, {"value": "Office", "label": "Office"}, {"value": "Office Administrative Assistant", "label": "Office Administrative Assistant"}, {"value": "Office Administrator", "label": "Office Administrator"}, {"value": "Office Assistant", "label": "Office Assistant"}, {"value": "Office Associate", "label": "Office Associate"}, {"value": "Office Cleaner", "label": "Office Cleaner"}, {"value": "Office Cleaning", "label": "Office Cleaning"}, {"value": "Office Clerk", "label": "Office Clerk"}, {"value": "Office Coordinator", "label": "Office Coordinator"}, {"value": "Office Engineer", "label": "Office Engineer"}, {"value": "Office Manager", "label": "Office Manager"}, {"value": "Office Nurse", "label": "Office Nurse"}, {"value": "Office Receptionist", "label": "Office Receptionist"}, {"value": "Office Secretary", "label": "Office Secretary"}, {"value": "Office Support", "label": "Office Support"}, {"value": "Office Technician", "label": "Office Technician"}, {"value": "Office Worker", "label": "Office Worker"}, {"value": "Officer", "label": "Officer"}, {"value": "Oil Field", "label": "Oil Field"}, {"value": "Ombudsman", "label": "Ombudsman"}, {"value": "Onboarding Specialist", "label": "Onboarding Specialist"}, {"value": "Oncologist", "label": "Oncologist"}, {"value": "Oncology Nurse", "label": "Oncology Nurse"}, {"value": "Oncology Pharmacist", "label": "Oncology Pharmacist"}, {"value": "Online Editor", "label": "Online Editor"}, {"value": "Online English Teacher", "label": "Online English Teacher"}, {"value": "Online Instructor", "label": "Online Instructor"}, {"value": "Online Marketing Manager", "label": "Online Marketing Manager"}, {"value": "Online Teacher", "label": "Online Teacher"}, {"value": "Online Tutor", "label": "Online Tutor"}, {"value": "Operating Engineer", "label": "Operating Engineer"}, {"value": "Operating Room Assistant", "label": "Operating Room Assistant"}, {"value": "Operating Room Nurse", "label": "Operating Room Nurse"}, {"value": "Operation Specialist", "label": "Operation Specialist"}, {"value": "Operations Analyst", "label": "Operations Analyst"}, {"value": "Operations Assistant", "label": "Operations Assistant"}, {"value": "Operations Associate", "label": "Operations Associate"}, {"value": "Operations Clerk", "label": "Operations Clerk"}, {"value": "Operations Coordinator", "label": "Operations Coordinator"}, {"value": "Operations Engineer", "label": "Operations Engineer"}, {"value": "Operations Manager", "label": "Operations Manager"}, {"value": "Operations Research Analyst", "label": "Operations Research Analyst"}, {"value": "Operations Specialist", "label": "Operations Specialist"}, {"value": "Operations Supervisor", "label": "Operations Supervisor"}, {"value": "Operations Team Leader", "label": "Operations Team Leader"}, {"value": "Operations Technician", "label": "Operations Technician"}, {"value": "Operator", "label": "Operator"}, {"value": "Ophthalmic Assistant", "label": "Ophthalmic Assistant"}, {"value": "Ophthalmic Technician", "label": "Ophthalmic Technician"}, {"value": "Ophthalmologist", "label": "Ophthalmologist"}, {"value": "Optical Assistant", "label": "Optical Assistant"}, {"value": "Optical Engineer", "label": "Optical Engineer"}, {"value": "Optical Manager", "label": "Optical Manager"}, {"value": "Optical Technician", "label": "Optical Technician"}, {"value": "Optician", "label": "Optician"}, {"value": "Options Trader", "label": "Options Trader"}, {"value": "Optometric Assistant", "label": "Optometric Assistant"}, {"value": "Optometric Technician", "label": "Optometric Technician"}, {"value": "Optometrist", "label": "Optometrist"}, {"value": "Optometrist Assistant", "label": "Optometrist Assistant"}, {"value": "Oracle Consultant", "label": "Oracle Consultant"}, {"value": "Oracle Database Administrator", "label": "Oracle Database Administrator"}, {"value": "Oracle Database Developer", "label": "Oracle Database Developer"}, {"value": "Oracle Dba", "label": "Oracle Dba"}, {"value": "Oracle Developer", "label": "Oracle Developer"}, {"value": "Oracle Pl Sql Developer", "label": "Oracle Pl Sql Developer"}, {"value": "Oral Surgeon", "label": "Oral Surgeon"}, {"value": "Oral Surgery Assistant", "label": "Oral Surgery Assistant"}, {"value": "Orchestra Teacher", "label": "Orchestra Teacher"}, {"value": "Order Builder", "label": "Order Builder"}, {"value": "Order Clerk", "label": "Order Clerk"}, {"value": "Order Filler", "label": "Order Filler"}, {"value": "Order Picker", "label": "Order Picker"}, {"value": "Order Processor", "label": "Order Processor"}, {"value": "Order Puller", "label": "Order Puller"}, {"value": "Order Selector", "label": "Order Selector"}, {"value": "Orderly", "label": "Orderly"}, {"value": "Ordinary Seaman", "label": "Ordinary Seaman"}, {"value": "Organic Chemist", "label": "Organic Chemist"}, {"value": "Organist", "label": "Organist"}, {"value": "Organizational Development Consultant", "label": "Organizational Development Consultant"}, {"value": "Organizational Psychologist", "label": "Organizational Psychologist"}, {"value": "Organizer", "label": "Organizer"}, {"value": "Orthodontic Assistant", "label": "Orthodontic Assistant"}, {"value": "Orthodontist", "label": "Orthodontist"}, {"value": "Orthodontist Assistant", "label": "Orthodontist Assistant"}, {"value": "Orthopaedic Surgeon", "label": "Orthopaedic Surgeon"}, {"value": "Orthopaedic Technician", "label": "Orthopaedic Technician"}, {"value": "Orthopedic Nurse Practitioner", "label": "Orthopedic Nurse Practitioner"}, {"value": "Orthopedic Surgeon", "label": "Orthopedic Surgeon"}, {"value": "Orthopedic Technician", "label": "Orthopedic Technician"}, {"value": "Orthotist", "label": "Orthotist"}, {"value": "Osp Engineer", "label": "Osp Engineer"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Otr Truck Driver", "label": "Otr Truck Driver"}, {"value": "Outpatient Therapist", "label": "Outpatient Therapist"}, {"value": "Outreach Coordinator", "label": "Outreach Coordinator"}, {"value": "Outreach Specialist", "label": "Outreach Specialist"}, {"value": "Outreach Worker", "label": "Outreach Worker"}, {"value": "Outside Machinist", "label": "Outside Machinist"}, {"value": "Outside Sales", "label": "Outside Sales"}, {"value": "Outside Sales Rep", "label": "Outside Sales Rep"}, {"value": "Outside Sales Representative", "label": "Outside Sales Representative"}, {"value": "Overnight Stocker", "label": "Overnight Stocker"}, {"value": "Owner Operator", "label": "Owner Operator"}, {"value": "Package Designer", "label": "Package Designer"}, {"value": "Package Handler", "label": "Package Handler"}, {"value": "Packager", "label": "Packager"}, {"value": "Packaging", "label": "Packaging"}, {"value": "Packaging Designer", "label": "Packaging Designer"}, {"value": "Packaging Engineer", "label": "Packaging Engineer"}, {"value": "Packaging Machine Operator", "label": "Packaging Machine Operator"}, {"value": "Packaging Manager", "label": "Packaging Manager"}, {"value": "Packaging Operator", "label": "Packaging Operator"}, {"value": "Packaging Specialist", "label": "Packaging Specialist"}, {"value": "Packaging Supervisor", "label": "Packaging Supervisor"}, {"value": "Packaging Technician", "label": "Packaging Technician"}, {"value": "<PERSON>er", "label": "<PERSON>er"}, {"value": "Pacs Administrator", "label": "Pacs Administrator"}, {"value": "Pacu Rn", "label": "Pacu Rn"}, {"value": "Paint Mixer", "label": "Paint Mixer"}, {"value": "Paint Technician", "label": "Paint Technician"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Paleontologist", "label": "Paleontologist"}, {"value": "Palliative Care Nurse Practitioner", "label": "Palliative Care Nurse Practitioner"}, {"value": "Para Educator", "label": "Para Educator"}, {"value": "Paraeducator", "label": "Paraeducator"}, {"value": "Paralegal", "label": "Paralegal"}, {"value": "Paralegal Assistant", "label": "Paralegal Assistant"}, {"value": "Paramedic", "label": "Paramedic"}, {"value": "Paraprofessional", "label": "Paraprofessional"}, {"value": "Parent Educator", "label": "Parent Educator"}, {"value": "Park Manager", "label": "Park Manager"}, {"value": "Park Ranger", "label": "Park Ranger"}, {"value": "Parking Attendant", "label": "Parking Attendant"}, {"value": "Parking Enforcement Officer", "label": "Parking Enforcement Officer"}, {"value": "Parking Lot Attendant", "label": "Parking Lot Attendant"}, {"value": "Parking Manager", "label": "Parking Manager"}, {"value": "Parole Officer", "label": "Parole Officer"}, {"value": "Part Time Sales Associate", "label": "Part Time Sales Associate"}, {"value": "Partner", "label": "Partner"}, {"value": "Parts Advisor", "label": "Parts Advisor"}, {"value": "Parts Clerk", "label": "Parts Clerk"}, {"value": "Parts Delivery Driver", "label": "Parts Delivery Driver"}, {"value": "Parts Driver", "label": "Parts Driver"}, {"value": "Parts Manager", "label": "Parts Manager"}, {"value": "Parts Runner", "label": "Parts Runner"}, {"value": "Parts Specialist", "label": "Parts Specialist"}, {"value": "Party Chief", "label": "Party Chief"}, {"value": "Party Planner", "label": "Party Planner"}, {"value": "Passenger Service Agent", "label": "Passenger Service Agent"}, {"value": "Pastor", "label": "Pastor"}, {"value": "Pastry Chef", "label": "Pastry Chef"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Patent Agent", "label": "Patent Agent"}, {"value": "Patent Analyst", "label": "Patent Analyst"}, {"value": "Patent Attorney", "label": "Patent Attorney"}, {"value": "Patent Engineer", "label": "Patent Engineer"}, {"value": "Patent Paralegal", "label": "Patent Paralegal"}, {"value": "Pathologist", "label": "Pathologist"}, {"value": "Pathologist Assistant", "label": "Pathologist Assistant"}, {"value": "Patient Access Manager", "label": "Patient Access Manager"}, {"value": "Patient Access Representative", "label": "Patient Access Representative"}, {"value": "Patient Access Specialist", "label": "Patient Access Specialist"}, {"value": "Patient Account Representative", "label": "Patient Account Representative"}, {"value": "Patient Advocate", "label": "Patient Advocate"}, {"value": "Patient Care Assistant", "label": "Patient Care Assistant"}, {"value": "Patient Care Associate", "label": "Patient Care Associate"}, {"value": "Patient Care Coordinator", "label": "Patient Care Coordinator"}, {"value": "Patient Care Manager", "label": "Patient Care Manager"}, {"value": "Patient Care Specialist", "label": "Patient Care Specialist"}, {"value": "Patient Care Technician", "label": "Patient Care Technician"}, {"value": "Patient Coordinator", "label": "Patient Coordinator"}, {"value": "Patient Escort", "label": "Patient Escort"}, {"value": "Patient Financial Counselor", "label": "Patient Financial Counselor"}, {"value": "Patient <PERSON>ison", "label": "Patient <PERSON>ison"}, {"value": "Patient Navigator", "label": "Patient Navigator"}, {"value": "Patient Registrar", "label": "Patient Registrar"}, {"value": "Patient Representative", "label": "Patient Representative"}, {"value": "Patient Service Representative", "label": "Patient Service Representative"}, {"value": "Patient Service Specialist", "label": "Patient Service Specialist"}, {"value": "Patient Services Coordinator", "label": "Patient Services Coordinator"}, {"value": "Patient Services Representative", "label": "Patient Services Representative"}, {"value": "Patient Sitter", "label": "Patient Sitter"}, {"value": "Patient Transport", "label": "Patient Transport"}, {"value": "Patient Transporter", "label": "Patient Transporter"}, {"value": "Patrol Officer", "label": "Patrol Officer"}, {"value": "Pattern Maker", "label": "Pattern Maker"}, {"value": "Paver", "label": "Paver"}, {"value": "Payment Processor", "label": "Payment Processor"}, {"value": "Payroll", "label": "Payroll"}, {"value": "Payroll Accountant", "label": "Payroll Accountant"}, {"value": "Payroll Administrator", "label": "Payroll Administrator"}, {"value": "Payroll Analyst", "label": "Payroll Analyst"}, {"value": "Payroll Assistant", "label": "Payroll Assistant"}, {"value": "Payroll Clerk", "label": "Payroll Clerk"}, {"value": "Payroll Consultant", "label": "Payroll Consultant"}, {"value": "Payroll Coordinator", "label": "Payroll Coordinator"}, {"value": "Payroll Director", "label": "Payroll Director"}, {"value": "Payroll Manager", "label": "Payroll Manager"}, {"value": "Payroll Processor", "label": "Payroll Processor"}, {"value": "Payroll Specialist", "label": "Payroll Specialist"}, {"value": "Payroll Supervisor", "label": "Payroll Supervisor"}, {"value": "Pbx Operator", "label": "Pbx Operator"}, {"value": "Pc Specialist", "label": "Pc Specialist"}, {"value": "Pc Technician", "label": "Pc Technician"}, {"value": "Pcb Design Engineer", "label": "Pcb Design Engineer"}, {"value": "Pcb Designer", "label": "Pcb Designer"}, {"value": "Peace Officer", "label": "Peace Officer"}, {"value": "Pediatric Cardiologist", "label": "Pediatric Cardiologist"}, {"value": "Pediatric Dentist", "label": "Pediatric Dentist"}, {"value": "Pediatric Hospitalist", "label": "Pediatric Hospitalist"}, {"value": "Pediatric Nurse", "label": "Pediatric Nurse"}, {"value": "Pediatric Nurse Practitioner", "label": "Pediatric Nurse Practitioner"}, {"value": "Pediatric Occupational Therapist", "label": "Pediatric Occupational Therapist"}, {"value": "Pediatric Oncology Nurse", "label": "Pediatric Oncology Nurse"}, {"value": "Pediatric Physical Therapist", "label": "Pediatric Physical Therapist"}, {"value": "Pediatric Physician Assistant", "label": "Pediatric Physician Assistant"}, {"value": "Pediatrician", "label": "Pediatrician"}, {"value": "Peer Counselor", "label": "Peer Counselor"}, {"value": "<PERSON><PERSON> Mentor", "label": "<PERSON><PERSON> Mentor"}, {"value": "Peer Support Specialist", "label": "Peer Support Specialist"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Penetration Tester", "label": "Penetration Tester"}, {"value": "Peoplesoft Administrator", "label": "Peoplesoft Administrator"}, {"value": "Peoplesoft Consultant", "label": "Peoplesoft Consultant"}, {"value": "Peoplesoft Developer", "label": "Peoplesoft Developer"}, {"value": "Performance Analyst", "label": "Performance Analyst"}, {"value": "Performance Engineer", "label": "Performance Engineer"}, {"value": "Performance Test Engineer", "label": "Performance Test Engineer"}, {"value": "Performance Tester", "label": "Performance Tester"}, {"value": "Performer", "label": "Performer"}, {"value": "Perfusionist", "label": "Perfusionist"}, {"value": "Periodontist", "label": "Periodontist"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Personal Assistant", "label": "Personal Assistant"}, {"value": "Personal Banker", "label": "Personal Banker"}, {"value": "Personal Care Aide", "label": "Personal Care Aide"}, {"value": "Personal Care Assistant", "label": "Personal Care Assistant"}, {"value": "Personal Care Attendant", "label": "Personal Care Attendant"}, {"value": "Personal Care Worker", "label": "Personal Care Worker"}, {"value": "Personal Caregiver", "label": "Personal Caregiver"}, {"value": "Personal Chef", "label": "Personal Chef"}, {"value": "Personal Driver", "label": "Personal Driver"}, {"value": "Personal Fitness Trainer", "label": "Personal Fitness Trainer"}, {"value": "Personal Injury Attorney", "label": "Personal Injury Attorney"}, {"value": "Personal Injury Paralegal", "label": "Personal Injury Paralegal"}, {"value": "Personal Shopper", "label": "Personal Shopper"}, {"value": "Personal Stylist", "label": "Personal Stylist"}, {"value": "Personal Trainer", "label": "Personal Trainer"}, {"value": "Personnel Security Specialist", "label": "Personnel Security Specialist"}, {"value": "Pest Control Technician", "label": "Pest Control Technician"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "<PERSON> Sitter", "label": "<PERSON> Sitter"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Petroleum Engineer", "label": "Petroleum Engineer"}, {"value": "Petroleum Inspector", "label": "Petroleum Inspector"}, {"value": "Pharmaceutical", "label": "Pharmaceutical"}, {"value": "Pharmaceutical Sales", "label": "Pharmaceutical Sales"}, {"value": "Pharmaceutical Sales Representative", "label": "Pharmaceutical Sales Representative"}, {"value": "Pharmaceutical Scientist", "label": "Pharmaceutical Scientist"}, {"value": "Pharmacist", "label": "Pharmacist"}, {"value": "Pharmacist Manager", "label": "Pharmacist Manager"}, {"value": "Pharmacist <PERSON><PERSON>ian", "label": "Pharmacist <PERSON><PERSON>ian"}, {"value": "Pharmacy Analyst", "label": "Pharmacy Analyst"}, {"value": "Pharmacy Assistant", "label": "Pharmacy Assistant"}, {"value": "Pharmacy Cashier", "label": "Pharmacy Cashier"}, {"value": "Pharmacy Clerk", "label": "Pharmacy Clerk"}, {"value": "Pharmacy Manager", "label": "Pharmacy Manager"}, {"value": "Pharmacy Supervisor", "label": "Pharmacy Supervisor"}, {"value": "Pharmacy Technician", "label": "Pharmacy Technician"}, {"value": "Pharmacy Technician Instru<PERSON>", "label": "Pharmacy Technician Instru<PERSON>"}, {"value": "Pharmacy Technician Trainee", "label": "Pharmacy Technician Trainee"}, {"value": "Phlebotomist", "label": "Phlebotomist"}, {"value": "Phlebotomy", "label": "Phlebotomy"}, {"value": "Phlebotomy Supervisor", "label": "Phlebotomy Supervisor"}, {"value": "Phlebotomy Technician", "label": "Phlebotomy Technician"}, {"value": "Phone Banker", "label": "Phone Banker"}, {"value": "Phone Operator", "label": "Phone Operator"}, {"value": "Phone Sales", "label": "Phone Sales"}, {"value": "Photo Editor", "label": "Photo Editor"}, {"value": "Photo Lab Technician", "label": "Photo Lab Technician"}, {"value": "Photo Retoucher", "label": "Photo Retoucher"}, {"value": "Photographer", "label": "Photographer"}, {"value": "Photography", "label": "Photography"}, {"value": "Photography Assistant", "label": "Photography Assistant"}, {"value": "Photojournalist", "label": "Photojournalist"}, {"value": "Php Developer", "label": "Php Developer"}, {"value": "Php Programmer", "label": "Php Programmer"}, {"value": "Php Web Developer", "label": "Php Web Developer"}, {"value": "Physical Design Engineer", "label": "Physical Design Engineer"}, {"value": "Physical Education Teacher", "label": "Physical Education Teacher"}, {"value": "Physical Scientist", "label": "Physical Scientist"}, {"value": "Physical Therapist", "label": "Physical Therapist"}, {"value": "Physical Therapist <PERSON><PERSON>", "label": "Physical Therapist <PERSON><PERSON>"}, {"value": "Physical Therapist Assistant", "label": "Physical Therapist Assistant"}, {"value": "Physical Therapist Technician", "label": "Physical Therapist Technician"}, {"value": "Physical Therapy Aide", "label": "Physical Therapy Aide"}, {"value": "Physical Therapy Assistant", "label": "Physical Therapy Assistant"}, {"value": "Physical Therapy Technician", "label": "Physical Therapy Technician"}, {"value": "Physician", "label": "Physician"}, {"value": "Physician Assistant", "label": "Physician Assistant"}, {"value": "Physician <PERSON>", "label": "Physician <PERSON>"}, {"value": "Physician <PERSON><PERSON><PERSON><PERSON>", "label": "Physician <PERSON><PERSON><PERSON><PERSON>"}, {"value": "Physicist", "label": "Physicist"}, {"value": "Physics Professor", "label": "Physics Professor"}, {"value": "Physics Teacher", "label": "Physics Teacher"}, {"value": "Physiologist", "label": "Physiologist"}, {"value": "Pianist", "label": "Pianist"}, {"value": "Piano Teacher", "label": "Piano Teacher"}, {"value": "Picker", "label": "Picker"}, {"value": "<PERSON><PERSON> Instru<PERSON>", "label": "<PERSON><PERSON> Instru<PERSON>"}, {"value": "Pilot", "label": "Pilot"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Pipefitter", "label": "Pipefitter"}, {"value": "Pipeline", "label": "Pipeline"}, {"value": "Pipeline Controller", "label": "Pipeline Controller"}, {"value": "Pipeline Engineer", "label": "Pipeline Engineer"}, {"value": "Pipeline Operator", "label": "Pipeline Operator"}, {"value": "Pipeline Technician", "label": "Pipeline Technician"}, {"value": "Piping Designer", "label": "Piping Designer"}, {"value": "Piping Engineer", "label": "Piping Engineer"}, {"value": "Pizza Cook", "label": "Pizza Cook"}, {"value": "Pizza Delivery Driver", "label": "Pizza Delivery Driver"}, {"value": "Pizza Maker", "label": "Pizza Maker"}, {"value": "Pl Sql Developer", "label": "Pl Sql Developer"}, {"value": "Placement Coordinator", "label": "Placement Coordinator"}, {"value": "Planner", "label": "Planner"}, {"value": "Planning Analyst", "label": "Planning Analyst"}, {"value": "Planning Engineer", "label": "Planning Engineer"}, {"value": "Plans Examiner", "label": "Plans Examiner"}, {"value": "Plant Controller", "label": "Plant Controller"}, {"value": "Plant Engineer", "label": "Plant Engineer"}, {"value": "Plant Manager", "label": "Plant Manager"}, {"value": "Plant Operator", "label": "Plant Operator"}, {"value": "Plant Supervisor", "label": "Plant Supervisor"}, {"value": "Plant Technician", "label": "Plant Technician"}, {"value": "Plant Worker", "label": "Plant Worker"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Plastic Surgeon", "label": "Plastic Surgeon"}, {"value": "Plastics Engineer", "label": "Plastics Engineer"}, {"value": "Platform Engineer", "label": "Platform Engineer"}, {"value": "Plumber", "label": "Plumber"}, {"value": "Plumber Helper", "label": "Plumber Helper"}, {"value": "Plumbing Engineer", "label": "Plumbing Engineer"}, {"value": "Plumbing Inspector", "label": "Plumbing Inspector"}, {"value": "Pmo Analyst", "label": "Pmo Analyst"}, {"value": "Pmo Manager", "label": "Pmo Manager"}, {"value": "Podiatrist", "label": "Podiatrist"}, {"value": "Poker Dealer", "label": "Poker Dealer"}, {"value": "Police Cadet", "label": "Police Cadet"}, {"value": "Police Chief", "label": "Police Chief"}, {"value": "Police Detective", "label": "Police Detective"}, {"value": "Police Dispatcher", "label": "Police Dispatcher"}, {"value": "Police Lieutenant", "label": "Police Lieutenant"}, {"value": "Police Officer", "label": "Police Officer"}, {"value": "Police Records Clerk", "label": "Police Records Clerk"}, {"value": "Police Sergeant", "label": "Police Sergeant"}, {"value": "Policy Advisor", "label": "Policy Advisor"}, {"value": "Policy Analyst", "label": "Policy Analyst"}, {"value": "Polygraph Examiner", "label": "Polygraph Examiner"}, {"value": "Pool Attendant", "label": "Pool Attendant"}, {"value": "Pool Manager", "label": "Pool Manager"}, {"value": "Pool Technician", "label": "Pool Technician"}, {"value": "Port Engineer", "label": "Port Engineer"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Portfolio Analyst", "label": "Portfolio Analyst"}, {"value": "Portfolio Manager", "label": "Portfolio Manager"}, {"value": "Post Office", "label": "Post Office"}, {"value": "Postal Clerk", "label": "Postal Clerk"}, {"value": "Postal Worker", "label": "Postal Worker"}, {"value": "Postdoc", "label": "Postdoc"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Power Engineer", "label": "Power Engineer"}, {"value": "Power Plant Engineer", "label": "Power Plant Engineer"}, {"value": "Power Plant Operator", "label": "Power Plant Operator"}, {"value": "Practical Nurse", "label": "Practical Nurse"}, {"value": "Practice Administrator", "label": "Practice Administrator"}, {"value": "Practice Manager", "label": "Practice Manager"}, {"value": "Pre K Teacher", "label": "Pre K Teacher"}, {"value": "Pre Sales Consultant", "label": "Pre Sales Consultant"}, {"value": "Premium Auditor", "label": "Premium Auditor"}, {"value": "Prep Cook", "label": "Prep Cook"}, {"value": "Prepress", "label": "Prepress"}, {"value": "Prepress Technician", "label": "Prepress Technician"}, {"value": "Preschool Assistant", "label": "Preschool Assistant"}, {"value": "Preschool Director", "label": "Preschool Director"}, {"value": "Preschool Teacher", "label": "Preschool Teacher"}, {"value": "Presenter", "label": "Presenter"}, {"value": "President", "label": "President"}, {"value": "Press Brake Operator", "label": "Press Brake Operator"}, {"value": "Press Operator", "label": "Press Operator"}, {"value": "Press Secretary", "label": "Press Secretary"}, {"value": "Pressman", "label": "Pressman"}, {"value": "Pressure Washer", "label": "Pressure Washer"}, {"value": "Pricing Analyst", "label": "Pricing Analyst"}, {"value": "Pricing Specialist", "label": "Pricing Specialist"}, {"value": "Priest", "label": "Priest"}, {"value": "Primary Care Physician", "label": "Primary Care Physician"}, {"value": "Primary Teacher", "label": "Primary Teacher"}, {"value": "Principal", "label": "Principal"}, {"value": "Principal Consultant", "label": "Principal Consultant"}, {"value": "Principal Electrical Engineer", "label": "Principal Electrical Engineer"}, {"value": "Principal Engineer", "label": "Principal Engineer"}, {"value": "Principal Mechanical Engineer", "label": "Principal Mechanical Engineer"}, {"value": "Principal Software Engineer", "label": "Principal Software Engineer"}, {"value": "Print Designer", "label": "Print Designer"}, {"value": "Print Production Manager", "label": "Print Production Manager"}, {"value": "Printer Technician", "label": "Printer Technician"}, {"value": "Printing Press Operator", "label": "Printing Press Operator"}, {"value": "Privacy Officer", "label": "Privacy Officer"}, {"value": "Private Banker", "label": "Private Banker"}, {"value": "Private Caregiver", "label": "Private Caregiver"}, {"value": "Private Chef", "label": "Private Chef"}, {"value": "Private Duty Nurse", "label": "Private Duty Nurse"}, {"value": "Private Equity", "label": "Private Equity"}, {"value": "Private Equity Analyst", "label": "Private Equity Analyst"}, {"value": "Private Equity Associate", "label": "Private Equity Associate"}, {"value": "Private Investigator", "label": "Private Investigator"}, {"value": "Private Tutor", "label": "Private Tutor"}, {"value": "Prn", "label": "Prn"}, {"value": "Probation Officer", "label": "Probation Officer"}, {"value": "Process Analyst", "label": "Process Analyst"}, {"value": "Process Consultant", "label": "Process Consultant"}, {"value": "Process Control Engineer", "label": "Process Control Engineer"}, {"value": "Process Development Engineer", "label": "Process Development Engineer"}, {"value": "Process Engineer", "label": "Process Engineer"}, {"value": "Process Improvement Engineer", "label": "Process Improvement Engineer"}, {"value": "Process Improvement Manager", "label": "Process Improvement Manager"}, {"value": "Process Improvement Specialist", "label": "Process Improvement Specialist"}, {"value": "Process Manager", "label": "Process Manager"}, {"value": "Process Operator", "label": "Process Operator"}, {"value": "Process Safety Engineer", "label": "Process Safety Engineer"}, {"value": "Process Server", "label": "Process Server"}, {"value": "Process Technician", "label": "Process Technician"}, {"value": "Processor", "label": "Processor"}, {"value": "Proctor", "label": "Proctor"}, {"value": "Procurement Administrator", "label": "Procurement Administrator"}, {"value": "Procurement Agent", "label": "Procurement Agent"}, {"value": "Procurement Analyst", "label": "Procurement Analyst"}, {"value": "Procurement Assistant", "label": "Procurement Assistant"}, {"value": "Procurement Clerk", "label": "Procurement Clerk"}, {"value": "Procurement Coordinator", "label": "Procurement Coordinator"}, {"value": "Procurement Director", "label": "Procurement Director"}, {"value": "Procurement Engineer", "label": "Procurement Engineer"}, {"value": "Procurement Manager", "label": "Procurement Manager"}, {"value": "Procurement Officer", "label": "Procurement Officer"}, {"value": "Procurement Specialist", "label": "Procurement Specialist"}, {"value": "Produce Clerk", "label": "Produce Clerk"}, {"value": "Produce Manager", "label": "Produce Manager"}, {"value": "Producer", "label": "Producer"}, {"value": "Product Analyst", "label": "Product Analyst"}, {"value": "Product Assistant", "label": "Product Assistant"}, {"value": "Product Coordinator", "label": "Product Coordinator"}, {"value": "Product Demonstrator", "label": "Product Demonstrator"}, {"value": "Product Design Engineer", "label": "Product Design Engineer"}, {"value": "Product Designer", "label": "Product Designer"}, {"value": "Product Developer", "label": "Product Developer"}, {"value": "Product Development Engineer", "label": "Product Development Engineer"}, {"value": "Product Development Manager", "label": "Product Development Manager"}, {"value": "Product Director", "label": "Product Director"}, {"value": "Product Engineer", "label": "Product Engineer"}, {"value": "Product Manager", "label": "Product Manager"}, {"value": "Product Marketing Manager", "label": "Product Marketing Manager"}, {"value": "Product Owner", "label": "Product Owner"}, {"value": "Product Photographer", "label": "Product Photographer"}, {"value": "Product Safety Engineer", "label": "Product Safety Engineer"}, {"value": "Product Specialist", "label": "Product Specialist"}, {"value": "Product Support Specialist", "label": "Product Support Specialist"}, {"value": "Product Tester", "label": "Product Tester"}, {"value": "Product Trainer", "label": "Product Trainer"}, {"value": "Production Accountant", "label": "Production Accountant"}, {"value": "Production Artist", "label": "Production Artist"}, {"value": "Production Assistant", "label": "Production Assistant"}, {"value": "Production Associate", "label": "Production Associate"}, {"value": "Production Chemist", "label": "Production Chemist"}, {"value": "Production Clerk", "label": "Production Clerk"}, {"value": "Production Control Manager", "label": "Production Control Manager"}, {"value": "Production Controller", "label": "Production Controller"}, {"value": "Production Coordinator", "label": "Production Coordinator"}, {"value": "Production Designer", "label": "Production Designer"}, {"value": "Production Editor", "label": "Production Editor"}, {"value": "Production Engineer", "label": "Production Engineer"}, {"value": "Production Manager", "label": "Production Manager"}, {"value": "Production Operator", "label": "Production Operator"}, {"value": "Production Planner", "label": "Production Planner"}, {"value": "Production Scheduler", "label": "Production Scheduler"}, {"value": "Production Specialist", "label": "Production Specialist"}, {"value": "Production Supervisor", "label": "Production Supervisor"}, {"value": "Production Support", "label": "Production Support"}, {"value": "Production Team Leader", "label": "Production Team Leader"}, {"value": "Production Technician", "label": "Production Technician"}, {"value": "Production Worker", "label": "Production Worker"}, {"value": "Professional Driver", "label": "Professional Driver"}, {"value": "Professional Engineer", "label": "Professional Engineer"}, {"value": "Professional Organizer", "label": "Professional Organizer"}, {"value": "Professional Services Consultant", "label": "Professional Services Consultant"}, {"value": "Professional Tutor", "label": "Professional Tutor"}, {"value": "Professor", "label": "Professor"}, {"value": "Program Administrator", "label": "Program Administrator"}, {"value": "Program Analyst", "label": "Program Analyst"}, {"value": "Program Assistant", "label": "Program Assistant"}, {"value": "Program Associate", "label": "Program Associate"}, {"value": "Program Coordinator", "label": "Program Coordinator"}, {"value": "Program Director", "label": "Program Director"}, {"value": "Program Evaluator", "label": "Program Evaluator"}, {"value": "Program Manager", "label": "Program Manager"}, {"value": "Program Officer", "label": "Program Officer"}, {"value": "Program Specialist", "label": "Program Specialist"}, {"value": "Program Supervisor", "label": "Program Supervisor"}, {"value": "Programmer", "label": "Programmer"}, {"value": "Programmer Analyst", "label": "Programmer Analyst"}, {"value": "Programming", "label": "Programming"}, {"value": "Project Accountant", "label": "Project Accountant"}, {"value": "Project Administrator", "label": "Project Administrator"}, {"value": "Project Analyst", "label": "Project Analyst"}, {"value": "Project Architect", "label": "Project Architect"}, {"value": "Project Assistant", "label": "Project Assistant"}, {"value": "Project Consultant", "label": "Project Consultant"}, {"value": "Project Control Analyst", "label": "Project Control Analyst"}, {"value": "Project Controller", "label": "Project Controller"}, {"value": "Project Coordinator", "label": "Project Coordinator"}, {"value": "Project Director", "label": "Project Director"}, {"value": "Project Engineer", "label": "Project Engineer"}, {"value": "Project Engineering Manager", "label": "Project Engineering Manager"}, {"value": "Project Estimator", "label": "Project Estimator"}, {"value": "Project Executive", "label": "Project Executive"}, {"value": "Project Lead", "label": "Project Lead"}, {"value": "Project Leader", "label": "Project Leader"}, {"value": "Project Management Assistant", "label": "Project Management Assistant"}, {"value": "Project Management Consultant", "label": "Project Management Consultant"}, {"value": "Project Management Specialist", "label": "Project Management Specialist"}, {"value": "Project Manager", "label": "Project Manager"}, {"value": "Project Officer", "label": "Project Officer"}, {"value": "Project Scheduler", "label": "Project Scheduler"}, {"value": "Project Specialist", "label": "Project Specialist"}, {"value": "Project Superintendent", "label": "Project Superintendent"}, {"value": "Project Supervisor", "label": "Project Supervisor"}, {"value": "Promoter", "label": "Promoter"}, {"value": "Promotional Model", "label": "Promotional Model"}, {"value": "Promotions Assistant", "label": "Promotions Assistant"}, {"value": "Promotions Coordinator", "label": "Promotions Coordinator"}, {"value": "Promotions Manager", "label": "Promotions Manager"}, {"value": "Proofreader", "label": "Proofreader"}, {"value": "Property Accountant", "label": "Property Accountant"}, {"value": "Property Administrator", "label": "Property Administrator"}, {"value": "Property Caretaker", "label": "Property Caretaker"}, {"value": "Property Claims Adjuster", "label": "Property Claims Adjuster"}, {"value": "Property Management", "label": "Property Management"}, {"value": "Property Management Assistant", "label": "Property Management Assistant"}, {"value": "Property Manager", "label": "Property Manager"}, {"value": "Proposal Coordinator", "label": "Proposal Coordinator"}, {"value": "Proposal Manager", "label": "Proposal Manager"}, {"value": "Proposal Specialist", "label": "Proposal Specialist"}, {"value": "Proposal Writer", "label": "Proposal Writer"}, {"value": "Proprietary Trader", "label": "Proprietary Trader"}, {"value": "Propulsion Engineer", "label": "Propulsion Engineer"}, {"value": "Prosecutor", "label": "Prosecutor"}, {"value": "Prosthetist", "label": "Prosthetist"}, {"value": "Prosthodontist", "label": "Prosthodontist"}, {"value": "Provider", "label": "Provider"}, {"value": "Provider Relations Representative", "label": "Provider Relations Representative"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Psychiatric Aide", "label": "Psychiatric Aide"}, {"value": "Psychiatric Nurse", "label": "Psychiatric Nurse"}, {"value": "Psychiatric Nurse Practitioner", "label": "Psychiatric Nurse Practitioner"}, {"value": "Psychiatric Social Worker", "label": "Psychiatric Social Worker"}, {"value": "Psychiatric Technician", "label": "Psychiatric Technician"}, {"value": "Psychiatrist", "label": "Psychiatrist"}, {"value": "Psychological Assistant", "label": "Psychological Assistant"}, {"value": "Psychologist", "label": "Psychologist"}, {"value": "Psychology Instructor", "label": "Psychology Instructor"}, {"value": "Psychology Professor", "label": "Psychology Professor"}, {"value": "Psychometrician", "label": "Psychometrician"}, {"value": "Psychometrist", "label": "Psychometrist"}, {"value": "Psychotherapist", "label": "Psychotherapist"}, {"value": "Public Address Announcer", "label": "Public Address Announcer"}, {"value": "Public Affairs Specialist", "label": "Public Affairs Specialist"}, {"value": "Public Defender", "label": "Public Defender"}, {"value": "Public Health Advisor", "label": "Public Health Advisor"}, {"value": "Public Health Analyst", "label": "Public Health Analyst"}, {"value": "Public Health Dentist", "label": "Public Health Dentist"}, {"value": "Public Health Educator", "label": "Public Health Educator"}, {"value": "Public Health Nurse", "label": "Public Health Nurse"}, {"value": "Public Health Physician", "label": "Public Health Physician"}, {"value": "Public Information Officer", "label": "Public Information Officer"}, {"value": "Public Relations", "label": "Public Relations"}, {"value": "Public Relations Assistant", "label": "Public Relations Assistant"}, {"value": "Public Relations Coordinator", "label": "Public Relations Coordinator"}, {"value": "Public Relations Director", "label": "Public Relations Director"}, {"value": "Public Relations Manager", "label": "Public Relations Manager"}, {"value": "Public Relations Specialist", "label": "Public Relations Specialist"}, {"value": "Public Safety Dispatcher", "label": "Public Safety Dispatcher"}, {"value": "Public Safety Officer", "label": "Public Safety Officer"}, {"value": "Public Works Director", "label": "Public Works Director"}, {"value": "Publicist", "label": "Publicist"}, {"value": "Publisher", "label": "Publisher"}, {"value": "Publishing Assistant", "label": "Publishing Assistant"}, {"value": "Pump Operator", "label": "Pump Operator"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Purchaser", "label": "Purchaser"}, {"value": "Purchasing Agent", "label": "Purchasing Agent"}, {"value": "Purchasing Analyst", "label": "Purchasing Analyst"}, {"value": "Purchasing Assistant", "label": "Purchasing Assistant"}, {"value": "Purchasing Buyer", "label": "Purchasing Buyer"}, {"value": "Purchasing Clerk", "label": "Purchasing Clerk"}, {"value": "Purchasing Coordinator", "label": "Purchasing Coordinator"}, {"value": "Purchasing Director", "label": "Purchasing Director"}, {"value": "Purchasing Manager", "label": "Purchasing Manager"}, {"value": "Purchasing Specialist", "label": "Purchasing Specialist"}, {"value": "Python Developer", "label": "Python Developer"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Qa Automation Engineer", "label": "Qa Automation Engineer"}, {"value": "Qa Consultant", "label": "Qa Consultant"}, {"value": "Qa Director", "label": "Qa Director"}, {"value": "Qa Engineer", "label": "Qa Engineer"}, {"value": "Qa Inspector", "label": "Qa Inspector"}, {"value": "Qa Lead", "label": "Qa Lead"}, {"value": "Qa Manager", "label": "Qa Manager"}, {"value": "Qa Software Tester", "label": "Qa Software Tester"}, {"value": "Qa Specialist", "label": "Qa Specialist"}, {"value": "Qa Supervisor", "label": "Qa Supervisor"}, {"value": "Qa Test Lead", "label": "Qa Test Lead"}, {"value": "<PERSON><PERSON> Tester", "label": "<PERSON><PERSON> Tester"}, {"value": "Quality Analyst", "label": "Quality Analyst"}, {"value": "Quality Assurance", "label": "Quality Assurance"}, {"value": "Quality Assurance Analyst", "label": "Quality Assurance Analyst"}, {"value": "Quality Assurance Associate", "label": "Quality Assurance Associate"}, {"value": "Quality Assurance Auditor", "label": "Quality Assurance Auditor"}, {"value": "Quality Assurance Director", "label": "Quality Assurance Director"}, {"value": "Quality Assurance Engineer", "label": "Quality Assurance Engineer"}, {"value": "Quality Assurance Inspector", "label": "Quality Assurance Inspector"}, {"value": "Quality Assurance Manager", "label": "Quality Assurance Manager"}, {"value": "Quality Assurance Specialist", "label": "Quality Assurance Specialist"}, {"value": "Quality Assurance Technician", "label": "Quality Assurance Technician"}, {"value": "Quality Assurance Tester", "label": "Quality Assurance Tester"}, {"value": "Quality Auditor", "label": "Quality Auditor"}, {"value": "Quality Consultant", "label": "Quality Consultant"}, {"value": "Quality Control", "label": "Quality Control"}, {"value": "Quality Control Analyst", "label": "Quality Control Analyst"}, {"value": "Quality Control Assistant", "label": "Quality Control Assistant"}, {"value": "Quality Control Associate", "label": "Quality Control Associate"}, {"value": "Quality Control Chemist", "label": "Quality Control Chemist"}, {"value": "Quality Control Engineer", "label": "Quality Control Engineer"}, {"value": "Quality Control Inspector", "label": "Quality Control Inspector"}, {"value": "Quality Control Manager", "label": "Quality Control Manager"}, {"value": "Quality Control Specialist", "label": "Quality Control Specialist"}, {"value": "Quality Control Supervisor", "label": "Quality Control Supervisor"}, {"value": "Quality Control Technician", "label": "Quality Control Technician"}, {"value": "Quality Coordinator", "label": "Quality Coordinator"}, {"value": "Quality Engineer", "label": "Quality Engineer"}, {"value": "Quality Improvement Specialist", "label": "Quality Improvement Specialist"}, {"value": "Quality Inspector", "label": "Quality Inspector"}, {"value": "Quality Manager", "label": "Quality Manager"}, {"value": "Quality Specialist", "label": "Quality Specialist"}, {"value": "Quality Supervisor", "label": "Quality Supervisor"}, {"value": "Quality Technician", "label": "Quality Technician"}, {"value": "Quantitative Analyst", "label": "Quantitative Analyst"}, {"value": "Quantitative Developer", "label": "Quantitative Developer"}, {"value": "Quantitative Research Analyst", "label": "Quantitative Research Analyst"}, {"value": "Quantity Surveyor", "label": "Quantity Surveyor"}, {"value": "R&D Engineer", "label": "R&D Engineer"}, {"value": "Rabbi", "label": "Rabbi"}, {"value": "Radar Technician", "label": "Radar Technician"}, {"value": "Radiation Safety Officer", "label": "Radiation Safety Officer"}, {"value": "Radiation Therapist", "label": "Radiation Therapist"}, {"value": "Radio", "label": "Radio"}, {"value": "Radio Host", "label": "Radio Host"}, {"value": "Radio Operator", "label": "Radio Operator"}, {"value": "Radio Producer", "label": "Radio Producer"}, {"value": "Radiographer", "label": "Radiographer"}, {"value": "Radiologic Technologist", "label": "Radiologic Technologist"}, {"value": "Radiologist", "label": "Radiologist"}, {"value": "Radiology", "label": "Radiology"}, {"value": "Radiology Assistant", "label": "Radiology Assistant"}, {"value": "Radiology Manager", "label": "Radiology Manager"}, {"value": "Radiology Technician", "label": "Radiology Technician"}, {"value": "Radiology Technologist", "label": "Radiology Technologist"}, {"value": "Railroad Conductor", "label": "Railroad Conductor"}, {"value": "Rails Developer", "label": "Rails Developer"}, {"value": "Ramp Agent", "label": "Ramp Agent"}, {"value": "Ranch Hand", "label": "Ranch Hand"}, {"value": "Ranch Manager", "label": "Ranch Manager"}, {"value": "Rancher", "label": "Rancher"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Reactor Operator", "label": "Reactor Operator"}, {"value": "Reader", "label": "Reader"}, {"value": "Reading Specialist", "label": "Reading Specialist"}, {"value": "Reading Teacher", "label": "Reading Teacher"}, {"value": "Reading Tutor", "label": "Reading Tutor"}, {"value": "Real Estate Accountant", "label": "Real Estate Accountant"}, {"value": "Real Estate Administrative Assistant", "label": "Real Estate Administrative Assistant"}, {"value": "Real Estate Agent", "label": "Real Estate Agent"}, {"value": "Real Estate Analyst", "label": "Real Estate Analyst"}, {"value": "Real Estate Appraiser", "label": "Real Estate Appraiser"}, {"value": "Real Estate Assistant", "label": "Real Estate Assistant"}, {"value": "Real Estate Associate", "label": "Real Estate Associate"}, {"value": "Real Estate Attorney", "label": "Real Estate Attorney"}, {"value": "Real Estate Broker", "label": "Real Estate Broker"}, {"value": "Real Estate Consultant", "label": "Real Estate Consultant"}, {"value": "Real Estate Developer", "label": "Real Estate Developer"}, {"value": "Real Estate Investor", "label": "Real Estate Investor"}, {"value": "Real Estate Lawyer", "label": "Real Estate Lawyer"}, {"value": "Real Estate Manager", "label": "Real Estate Manager"}, {"value": "Real Estate Paralegal", "label": "Real Estate Paralegal"}, {"value": "Real Estate Photographer", "label": "Real Estate Photographer"}, {"value": "Real Estate Sales Agent", "label": "Real Estate Sales Agent"}, {"value": "Real Estate Salesperson", "label": "Real Estate Salesperson"}, {"value": "Realtor", "label": "Realtor"}, {"value": "Realtor Assistant", "label": "Realtor Assistant"}, {"value": "Receiver", "label": "Receiver"}, {"value": "Receiving Associate", "label": "Receiving Associate"}, {"value": "Receiving Clerk", "label": "Receiving Clerk"}, {"value": "Receiving Manager", "label": "Receiving Manager"}, {"value": "Receptionist", "label": "Receptionist"}, {"value": "Reconciliation Analyst", "label": "Reconciliation Analyst"}, {"value": "Reconciliation Specialist", "label": "Reconciliation Specialist"}, {"value": "Records Clerk", "label": "Records Clerk"}, {"value": "Records Manager", "label": "Records Manager"}, {"value": "Recovery Specialist", "label": "Recovery Specialist"}, {"value": "Recreation Aide", "label": "Recreation Aide"}, {"value": "Recreation Assistant", "label": "Recreation Assistant"}, {"value": "Recreation Coordinator", "label": "Recreation Coordinator"}, {"value": "Recreation Director", "label": "Recreation Director"}, {"value": "Recreation Leader", "label": "Recreation Leader"}, {"value": "Recreation Specialist", "label": "Recreation Specialist"}, {"value": "Recreation Supervisor", "label": "Recreation Supervisor"}, {"value": "Recreation Therapist", "label": "Recreation Therapist"}, {"value": "Recreational Therapist", "label": "Recreational Therapist"}, {"value": "Rec<PERSON>er", "label": "Rec<PERSON>er"}, {"value": "Recruiting Assistant", "label": "Recruiting Assistant"}, {"value": "Recruiting Coordinator", "label": "Recruiting Coordinator"}, {"value": "Recruiting Manager", "label": "Recruiting Manager"}, {"value": "Recruitment Consultant", "label": "Recruitment Consultant"}, {"value": "Recruitment Manager", "label": "Recruitment Manager"}, {"value": "Recruitment Specialist", "label": "Recruitment Specialist"}, {"value": "Referee", "label": "Referee"}, {"value": "Reference Librarian", "label": "Reference Librarian"}, {"value": "Referral Coordinator", "label": "Referral Coordinator"}, {"value": "Referral Specialist", "label": "Referral Specialist"}, {"value": "Refinery", "label": "Refinery"}, {"value": "Refinery Operator", "label": "Refinery Operator"}, {"value": "Refrigeration Engineer", "label": "Refrigeration Engineer"}, {"value": "Refrigeration Mechanic", "label": "Refrigeration Mechanic"}, {"value": "Refrigeration Technician", "label": "Refrigeration Technician"}, {"value": "Regional Account Manager", "label": "Regional Account Manager"}, {"value": "Regional Director", "label": "Regional Director"}, {"value": "Regional Manager", "label": "Regional Manager"}, {"value": "Regional Operations Manager", "label": "Regional Operations Manager"}, {"value": "Regional Property Manager", "label": "Regional Property Manager"}, {"value": "Regional Sales Director", "label": "Regional Sales Director"}, {"value": "Regional Sales Executive", "label": "Regional Sales Executive"}, {"value": "Regional Sales Manager", "label": "Regional Sales Manager"}, {"value": "Regional Sales Representative", "label": "Regional Sales Representative"}, {"value": "Registered Behavior Technician", "label": "Registered Behavior Technician"}, {"value": "Registered Dental Assistant", "label": "Registered Dental Assistant"}, {"value": "Registered Dietitian", "label": "Registered Dietitian"}, {"value": "Registered Medical Assistant", "label": "Registered Medical Assistant"}, {"value": "Registered Nurse", "label": "Registered Nurse"}, {"value": "Registered Respiratory Therapist", "label": "Registered Respiratory Therapist"}, {"value": "Registrar", "label": "Registrar"}, {"value": "Registration Clerk", "label": "Registration Clerk"}, {"value": "Registration Specialist", "label": "Registration Specialist"}, {"value": "Regulatory Affairs Associate", "label": "Regulatory Affairs Associate"}, {"value": "Regulatory Affairs Manager", "label": "Regulatory Affairs Manager"}, {"value": "Regulatory Affairs Specialist", "label": "Regulatory Affairs Specialist"}, {"value": "Regulatory Analyst", "label": "Regulatory Analyst"}, {"value": "Regulatory Specialist", "label": "Regulatory Specialist"}, {"value": "<PERSON><PERSON>e", "label": "<PERSON><PERSON>e"}, {"value": "Rehab Director", "label": "Rehab Director"}, {"value": "<PERSON><PERSON> Technician", "label": "<PERSON><PERSON> Technician"}, {"value": "Rehabilitation Aide", "label": "Rehabilitation Aide"}, {"value": "Rehabilitation Counselor", "label": "Rehabilitation Counselor"}, {"value": "Rehabilitation Nurse", "label": "Rehabilitation Nurse"}, {"value": "Rehabilitation Specialist", "label": "Rehabilitation Specialist"}, {"value": "Rehabilitation Technician", "label": "Rehabilitation Technician"}, {"value": "Rehabilitation Therapist", "label": "Rehabilitation Therapist"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Reimbursement Manager", "label": "Reimbursement Manager"}, {"value": "Reimbursement Specialist", "label": "Reimbursement Specialist"}, {"value": "Relationship Banker", "label": "Relationship Banker"}, {"value": "Relationship Manager", "label": "Relationship Manager"}, {"value": "<PERSON><PERSON> Technician", "label": "<PERSON><PERSON> Technician"}, {"value": "Release Engineer", "label": "Release Engineer"}, {"value": "Release Manager", "label": "Release Manager"}, {"value": "Reliability Engineer", "label": "Reliability Engineer"}, {"value": "Religion Teacher", "label": "Religion Teacher"}, {"value": "Relocation Consultant", "label": "Relocation Consultant"}, {"value": "Renal Dietitian", "label": "Renal Dietitian"}, {"value": "Renewable Energy", "label": "Renewable Energy"}, {"value": "Repair Technician", "label": "Repair Technician"}, {"value": "Report Analyst", "label": "Report Analyst"}, {"value": "Report Developer", "label": "Report Developer"}, {"value": "Report Writer", "label": "Report Writer"}, {"value": "Reporter", "label": "Reporter"}, {"value": "Reporting Analyst", "label": "Reporting Analyst"}, {"value": "Representative", "label": "Representative"}, {"value": "Requirements Analyst", "label": "Requirements Analyst"}, {"value": "Research Administrator", "label": "Research Administrator"}, {"value": "Research Analyst", "label": "Research Analyst"}, {"value": "Research And Development Engineer", "label": "Research And Development Engineer"}, {"value": "Research Assistant", "label": "Research Assistant"}, {"value": "Research Associate", "label": "Research Associate"}, {"value": "Research Chemist", "label": "Research Chemist"}, {"value": "Research Consultant", "label": "Research Consultant"}, {"value": "Research Coordinator", "label": "Research Coordinator"}, {"value": "Research Director", "label": "Research Director"}, {"value": "Research Engineer", "label": "Research Engineer"}, {"value": "Research Manager", "label": "Research Manager"}, {"value": "Research Nurse", "label": "Research Nurse"}, {"value": "Research Psychologist", "label": "Research Psychologist"}, {"value": "Research Scientist", "label": "Research Scientist"}, {"value": "Research Specialist", "label": "Research Specialist"}, {"value": "Research Technician", "label": "Research Technician"}, {"value": "Researcher", "label": "Researcher"}, {"value": "Reservation Agent", "label": "Reservation Agent"}, {"value": "Reservation Specialist", "label": "Reservation Specialist"}, {"value": "Reservationist", "label": "Reservationist"}, {"value": "Reservations Agent", "label": "Reservations Agent"}, {"value": "Reservoir Engineer", "label": "Reservoir Engineer"}, {"value": "Residence Counselor", "label": "Residence Counselor"}, {"value": "Residence Hall Director", "label": "Residence Hall Director"}, {"value": "Residence Manager", "label": "Residence Manager"}, {"value": "Resident Assistant", "label": "Resident Assistant"}, {"value": "Resident Director", "label": "Resident Director"}, {"value": "Resident Engineer", "label": "Resident Engineer"}, {"value": "Resident Manager", "label": "Resident Manager"}, {"value": "Residential Appraiser", "label": "Residential Appraiser"}, {"value": "Residential Director", "label": "Residential Director"}, {"value": "Residential Electrician", "label": "Residential Electrician"}, {"value": "Residential Property Manager", "label": "Residential Property Manager"}, {"value": "Resort Manager", "label": "Resort Manager"}, {"value": "Resource Specialist", "label": "Resource Specialist"}, {"value": "Respiratory Care Practitioner", "label": "Respiratory Care Practitioner"}, {"value": "Respiratory Therapist", "label": "Respiratory Therapist"}, {"value": "Restaurant", "label": "Restaurant"}, {"value": "Restaurant Assistant Manager", "label": "Restaurant Assistant Manager"}, {"value": "Restaurant Cashier", "label": "Restaurant Cashier"}, {"value": "Restaurant Consultant", "label": "Restaurant Consultant"}, {"value": "Restaurant General Manager", "label": "Restaurant General Manager"}, {"value": "Restaurant Host", "label": "Restaurant Host"}, {"value": "Restaurant Manager", "label": "Restaurant Manager"}, {"value": "Restaurant Operations Manager", "label": "Restaurant Operations Manager"}, {"value": "Restaurant Server", "label": "Restaurant Server"}, {"value": "Restaurant Supervisor", "label": "Restaurant Supervisor"}, {"value": "Restaurant Worker", "label": "Restaurant Worker"}, {"value": "Retail Assistant", "label": "Retail Assistant"}, {"value": "Retail Assistant Manager", "label": "Retail Assistant Manager"}, {"value": "Retail Associate", "label": "Retail Associate"}, {"value": "Retail Banker", "label": "Retail Banker"}, {"value": "Retail Buyer", "label": "Retail Buyer"}, {"value": "Retail Cashier", "label": "Retail Cashier"}, {"value": "Retail Clerk", "label": "Retail Clerk"}, {"value": "Retail Consultant", "label": "Retail Consultant"}, {"value": "Retail Director", "label": "Retail Director"}, {"value": "Retail District Manager", "label": "Retail District Manager"}, {"value": "Retail Management", "label": "Retail Management"}, {"value": "Retail Manager", "label": "Retail Manager"}, {"value": "Retail Marketing Manager", "label": "Retail Marketing Manager"}, {"value": "Retail Merchandiser", "label": "Retail Merchandiser"}, {"value": "Retail Operations Manager", "label": "Retail Operations Manager"}, {"value": "Retail Pharmacist", "label": "Retail Pharmacist"}, {"value": "Retail Sales", "label": "Retail Sales"}, {"value": "Retail Sales Associate", "label": "Retail Sales Associate"}, {"value": "Retail Sales Consultant", "label": "Retail Sales Consultant"}, {"value": "Retail Sales Manager", "label": "Retail Sales Manager"}, {"value": "Retail Sales Representative", "label": "Retail Sales Representative"}, {"value": "Retail Salesperson", "label": "Retail Salesperson"}, {"value": "Retail Specialist", "label": "Retail Specialist"}, {"value": "Retail Store Manager", "label": "Retail Store Manager"}, {"value": "Retail Supervisor", "label": "Retail Supervisor"}, {"value": "Retention Specialist", "label": "Retention Specialist"}, {"value": "Retirement Specialist", "label": "Retirement Specialist"}, {"value": "Retoucher", "label": "Retoucher"}, {"value": "Revenue Accountant", "label": "Revenue Accountant"}, {"value": "Revenue Analyst", "label": "Revenue Analyst"}, {"value": "Revenue Cycle Analyst", "label": "Revenue Cycle Analyst"}, {"value": "Revenue Cycle Manager", "label": "Revenue Cycle Manager"}, {"value": "Revenue Manager", "label": "Revenue Manager"}, {"value": "Revenue Officer", "label": "Revenue Officer"}, {"value": "Reviewer", "label": "Reviewer"}, {"value": "Rf Design Engineer", "label": "Rf Design Engineer"}, {"value": "Rf Engineer", "label": "Rf Engineer"}, {"value": "Rheumatologist", "label": "Rheumatologist"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Right Of Way Agent", "label": "Right Of Way Agent"}, {"value": "Risk Analyst", "label": "Risk Analyst"}, {"value": "Risk Management Analyst", "label": "Risk Management Analyst"}, {"value": "Risk Management Specialist", "label": "Risk Management Specialist"}, {"value": "Risk Manager", "label": "Risk Manager"}, {"value": "Rn Case Manager", "label": "Rn Case Manager"}, {"value": "Rn Supervisor", "label": "Rn Supervisor"}, {"value": "Robotics Engineer", "label": "Robotics Engineer"}, {"value": "Roll Off Driver", "label": "Roll Off Driver"}, {"value": "Roofer", "label": "Roofer"}, {"value": "Room Attendant", "label": "Room Attendant"}, {"value": "Room Service Server", "label": "Room Service Server"}, {"value": "Roughneck", "label": "Roughneck"}, {"value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"value": "Route Driver", "label": "Route Driver"}, {"value": "Route Sales Representative", "label": "Route Sales Representative"}, {"value": "Rpg Programmer", "label": "Rpg Programmer"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Runner", "label": "Runner"}, {"value": "Safety Advisor", "label": "Safety Advisor"}, {"value": "Safety Assistant", "label": "Safety Assistant"}, {"value": "Safety Consultant", "label": "Safety Consultant"}, {"value": "Safety Coordinator", "label": "Safety Coordinator"}, {"value": "Safety Director", "label": "Safety Director"}, {"value": "Safety Engineer", "label": "Safety Engineer"}, {"value": "Safety Inspector", "label": "Safety Inspector"}, {"value": "Safety Manager", "label": "Safety Manager"}, {"value": "Safety Officer", "label": "Safety Officer"}, {"value": "Safety Professional", "label": "Safety Professional"}, {"value": "Safety Specialist", "label": "Safety Specialist"}, {"value": "Safety Supervisor", "label": "Safety Supervisor"}, {"value": "Safety Technician", "label": "Safety Technician"}, {"value": "Sailing Instructor", "label": "Sailing Instructor"}, {"value": "Sales", "label": "Sales"}, {"value": "Sales Account Executive", "label": "Sales Account Executive"}, {"value": "Sales Account Manager", "label": "Sales Account Manager"}, {"value": "Sales Administrator", "label": "Sales Administrator"}, {"value": "Sales Advisor", "label": "Sales Advisor"}, {"value": "Sales Agent", "label": "Sales Agent"}, {"value": "Sales Analyst", "label": "Sales Analyst"}, {"value": "Sales Assistant", "label": "Sales Assistant"}, {"value": "Sales Associate", "label": "Sales Associate"}, {"value": "Sales Clerk", "label": "Sales Clerk"}, {"value": "Sales Coach", "label": "Sales Coach"}, {"value": "Sales Consultant", "label": "Sales Consultant"}, {"value": "Sales Contractor", "label": "Sales Contractor"}, {"value": "Sales Coordinator", "label": "Sales Coordinator"}, {"value": "Sales Development Representative", "label": "Sales Development Representative"}, {"value": "Sales Director", "label": "Sales Director"}, {"value": "Sales Engineer", "label": "Sales Engineer"}, {"value": "Sales Executive", "label": "Sales Executive"}, {"value": "Sales Lead", "label": "Sales Lead"}, {"value": "Sales Manager", "label": "Sales Manager"}, {"value": "Sales Marketing", "label": "Sales Marketing"}, {"value": "Sales Merchandiser", "label": "Sales Merchandiser"}, {"value": "Sales Operations Analyst", "label": "Sales Operations Analyst"}, {"value": "Sales Operations Manager", "label": "Sales Operations Manager"}, {"value": "Sales Professional", "label": "Sales Professional"}, {"value": "Sales Recruiter", "label": "Sales Recruiter"}, {"value": "Sales Representative", "label": "Sales Representative"}, {"value": "Sales Specialist", "label": "Sales Specialist"}, {"value": "Sales Supervisor", "label": "Sales Supervisor"}, {"value": "Sales Support Specialist", "label": "Sales Support Specialist"}, {"value": "Sales Team Leader", "label": "Sales Team Leader"}, {"value": "Sales Trader", "label": "Sales Trader"}, {"value": "Sales Trainer", "label": "Sales Trainer"}, {"value": "Salesforce Administrator", "label": "Salesforce Administrator"}, {"value": "Salesforce Business Analyst", "label": "Salesforce Business Analyst"}, {"value": "Salesforce Consultant", "label": "Salesforce Consultant"}, {"value": "Salesforce Developer", "label": "Salesforce Developer"}, {"value": "Salesman", "label": "Salesman"}, {"value": "Salesperson", "label": "Salesperson"}, {"value": "Salon Assistant", "label": "Salon Assistant"}, {"value": "Salon Manager", "label": "Salon Manager"}, {"value": "Salon Receptionist", "label": "Salon Receptionist"}, {"value": "Sandwich Maker", "label": "Sandwich Maker"}, {"value": "Sanitation Worker", "label": "Sanitation Worker"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Sap Administrator", "label": "Sap Administrator"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON> Administrator", "label": "<PERSON><PERSON> Administrator"}, {"value": "Sap Business Analyst", "label": "Sap Business Analyst"}, {"value": "Sap Consultant", "label": "Sap Consultant"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Sap Functional Consultant", "label": "Sap Functional Consultant"}, {"value": "Sap Manager", "label": "Sap Manager"}, {"value": "Sap Project Manager", "label": "Sap Project Manager"}, {"value": "Sap Security Consultant", "label": "Sap Security Consultant"}, {"value": "<PERSON><PERSON> Tester", "label": "<PERSON><PERSON> Tester"}, {"value": "Sas Analyst", "label": "Sas Analyst"}, {"value": "Sas Developer", "label": "Sas Developer"}, {"value": "Sas Programmer", "label": "Sas Programmer"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Satellite Engineer", "label": "Satellite Engineer"}, {"value": "Satellite Technician", "label": "Satellite Technician"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Scaffold Builder", "label": "Scaffold Builder"}, {"value": "Scale Operator", "label": "Scale Operator"}, {"value": "Scanner", "label": "Scanner"}, {"value": "Scenic Artist", "label": "Scenic Artist"}, {"value": "Scheduler", "label": "Scheduler"}, {"value": "Scheduling Coordinator", "label": "Scheduling Coordinator"}, {"value": "Scheduling Manager", "label": "Scheduling Manager"}, {"value": "School Administrator", "label": "School Administrator"}, {"value": "School Aide", "label": "School Aide"}, {"value": "School Bus Driver", "label": "School Bus Driver"}, {"value": "School Counselor", "label": "School Counselor"}, {"value": "School Custodian", "label": "School Custodian"}, {"value": "School Director", "label": "School Director"}, {"value": "School Librarian", "label": "School Librarian"}, {"value": "School Nurse", "label": "School Nurse"}, {"value": "School Principal", "label": "School Principal"}, {"value": "School Psychologist", "label": "School Psychologist"}, {"value": "School Secretary", "label": "School Secretary"}, {"value": "School Social Worker", "label": "School Social Worker"}, {"value": "School Teacher", "label": "School Teacher"}, {"value": "Science Teacher", "label": "Science Teacher"}, {"value": "Science Writer", "label": "Science Writer"}, {"value": "Scientific Writer", "label": "Scientific Writer"}, {"value": "Scientist", "label": "Scientist"}, {"value": "Scout", "label": "Scout"}, {"value": "Screen Printer", "label": "Screen Printer"}, {"value": "Screener", "label": "Screener"}, {"value": "Scribe", "label": "Scribe"}, {"value": "<PERSON><PERSON><PERSON> Writer", "label": "<PERSON><PERSON><PERSON> Writer"}, {"value": "Scrum Master", "label": "Scrum Master"}, {"value": "Sculp<PERSON>", "label": "Sculp<PERSON>"}, {"value": "Seamstress", "label": "Seamstress"}, {"value": "Search Engine Evaluator", "label": "Search Engine Evaluator"}, {"value": "Secondary School Teacher", "label": "Secondary School Teacher"}, {"value": "Secondary Teacher", "label": "Secondary Teacher"}, {"value": "Secretary", "label": "Secretary"}, {"value": "Security", "label": "Security"}, {"value": "Security Administrator", "label": "Security Administrator"}, {"value": "Security Agent", "label": "Security Agent"}, {"value": "Security Analyst", "label": "Security Analyst"}, {"value": "Security Architect", "label": "Security Architect"}, {"value": "Security Assistant", "label": "Security Assistant"}, {"value": "Security Consultant", "label": "Security Consultant"}, {"value": "Security Coordinator", "label": "Security Coordinator"}, {"value": "Security Director", "label": "Security Director"}, {"value": "Security Dispatcher", "label": "Security Dispatcher"}, {"value": "Security Engineer", "label": "Security Engineer"}, {"value": "Security Guard", "label": "Security Guard"}, {"value": "Security Manager", "label": "Security Manager"}, {"value": "Security Officer", "label": "Security Officer"}, {"value": "Security Receptionist", "label": "Security Receptionist"}, {"value": "Security Specialist", "label": "Security Specialist"}, {"value": "Security Supervisor", "label": "Security Supervisor"}, {"value": "Security Technician", "label": "Security Technician"}, {"value": "Segment Producer", "label": "Segment Producer"}, {"value": "Senior Account Executive", "label": "Senior Account Executive"}, {"value": "Senior Account Manager", "label": "Senior Account Manager"}, {"value": "Senior Accountant", "label": "Senior Accountant"}, {"value": "Senior Administrative Assistant", "label": "Senior Administrative Assistant"}, {"value": "Senior Analyst", "label": "Senior Analyst"}, {"value": "Senior Architect", "label": "Senior Architect"}, {"value": "Senior Art Director", "label": "Senior Art Director"}, {"value": "Senior Auditor", "label": "Senior Auditor"}, {"value": "Senior Business Analyst", "label": "Senior Business Analyst"}, {"value": "Senior Buyer", "label": "Senior Buyer"}, {"value": "Senior Caregiver", "label": "Senior Caregiver"}, {"value": "Senior Civil Engineer", "label": "Senior Civil Engineer"}, {"value": "Senior Consultant", "label": "Senior Consultant"}, {"value": "Senior Copywriter", "label": "Senior Copywriter"}, {"value": "Senior Credit Analyst", "label": "Senior Credit Analyst"}, {"value": "Senior Data Analyst", "label": "Senior Data Analyst"}, {"value": "Senior Database Administrator", "label": "Senior Database Administrator"}, {"value": "Senior Design Engineer", "label": "Senior Design Engineer"}, {"value": "Senior Designer", "label": "Senior Designer"}, {"value": "Senior Editor", "label": "Senior Editor"}, {"value": "Senior Electrical Engineer", "label": "Senior Electrical Engineer"}, {"value": "Senior Engineer", "label": "Senior Engineer"}, {"value": "Senior Executive Assistant", "label": "Senior Executive Assistant"}, {"value": "Senior Finance Manager", "label": "Senior Finance Manager"}, {"value": "Senior Financial Analyst", "label": "Senior Financial Analyst"}, {"value": "Senior Graphic Designer", "label": "Senior Graphic Designer"}, {"value": "Senior Instructional Designer", "label": "Senior Instructional Designer"}, {"value": "Senior Interior Designer", "label": "Senior Interior Designer"}, {"value": "Senior Manager", "label": "Senior Manager"}, {"value": "Senior Manufacturing Engineer", "label": "Senior Manufacturing Engineer"}, {"value": "Senior Marketing Manager", "label": "Senior Marketing Manager"}, {"value": "Senior Mechanical Design Engineer", "label": "Senior Mechanical Design Engineer"}, {"value": "Senior Mechanical Engineer", "label": "Senior Mechanical Engineer"}, {"value": "Senior Merchandiser", "label": "Senior Merchandiser"}, {"value": "Senior Network Engineer", "label": "Senior Network Engineer"}, {"value": "Senior Operations Manager", "label": "Senior Operations Manager"}, {"value": "Senior Paralegal", "label": "Senior Paralegal"}, {"value": "Senior Pastor", "label": "Senior Pastor"}, {"value": "Senior Pharmacy Technician", "label": "Senior Pharmacy Technician"}, {"value": "Senior Planner", "label": "Senior Planner"}, {"value": "Senior Process Engineer", "label": "Senior Process Engineer"}, {"value": "Senior Product Manager", "label": "Senior Product Manager"}, {"value": "Senior Program Manager", "label": "Senior Program Manager"}, {"value": "Senior Programmer", "label": "Senior Programmer"}, {"value": "Senior Project Engineer", "label": "Senior Project Engineer"}, {"value": "Senior Project Manager", "label": "Senior Project Manager"}, {"value": "Senior Quality Engineer", "label": "Senior Quality Engineer"}, {"value": "Senior Recruiter", "label": "Senior Recruiter"}, {"value": "Senior Sales Executive", "label": "Senior Sales Executive"}, {"value": "Senior Sales Manager", "label": "Senior Sales Manager"}, {"value": "Senior Scientist", "label": "Senior Scientist"}, {"value": "Senior Software Developer", "label": "Senior Software Developer"}, {"value": "Senior Software Engineer", "label": "Senior Software Engineer"}, {"value": "Senior Staff Accountant", "label": "Senior Staff Accountant"}, {"value": "Senior Structural Engineer", "label": "Senior Structural Engineer"}, {"value": "Senior Systems Administrator", "label": "Senior Systems Administrator"}, {"value": "Senior Systems Analyst", "label": "Senior Systems Analyst"}, {"value": "Senior Systems Engineer", "label": "Senior Systems Engineer"}, {"value": "Senior Tax Accountant", "label": "Senior Tax Accountant"}, {"value": "Senior Technical Writer", "label": "Senior Technical Writer"}, {"value": "Senior Treasury Analyst", "label": "Senior Treasury Analyst"}, {"value": "Senior Vice President", "label": "Senior Vice President"}, {"value": "Senior Web Developer", "label": "Senior Web Developer"}, {"value": "Sensor Operator", "label": "Sensor Operator"}, {"value": "Seo Manager", "label": "Seo Manager"}, {"value": "Seo Specialist", "label": "Seo Specialist"}, {"value": "Server", "label": "Server"}, {"value": "Server Administrator", "label": "Server Administrator"}, {"value": "Server Assistant", "label": "Server Assistant"}, {"value": "Server Engineer", "label": "Server Engineer"}, {"value": "Service Advisor", "label": "Service Advisor"}, {"value": "Service Assistant", "label": "Service Assistant"}, {"value": "Service Cashier", "label": "Service Cashier"}, {"value": "Service Consultant", "label": "Service Consultant"}, {"value": "Service Coordinator", "label": "Service Coordinator"}, {"value": "Service Delivery Manager", "label": "Service Delivery Manager"}, {"value": "Service Desk Analyst", "label": "Service Desk Analyst"}, {"value": "Service Desk Manager", "label": "Service Desk Manager"}, {"value": "Service Desk Technician", "label": "Service Desk Technician"}, {"value": "Service Director", "label": "Service Director"}, {"value": "Service Engineer", "label": "Service Engineer"}, {"value": "Service Manager", "label": "Service Manager"}, {"value": "Service Representative", "label": "Service Representative"}, {"value": "Service Technician", "label": "Service Technician"}, {"value": "Service Worker", "label": "Service Worker"}, {"value": "Service Writer", "label": "Service Writer"}, {"value": "Set Designer", "label": "Set Designer"}, {"value": "Sewing", "label": "Sewing"}, {"value": "Sewing Machine Operator", "label": "Sewing Machine Operator"}, {"value": "Sharepoint Administrator", "label": "Sharepoint Administrator"}, {"value": "Sharepoint Consultant", "label": "Sharepoint Consultant"}, {"value": "Sharepoint Developer", "label": "Sharepoint Developer"}, {"value": "Sheet Metal Mechanic", "label": "Sheet Metal Mechanic"}, {"value": "Sheet Metal Worker", "label": "Sheet Metal Worker"}, {"value": "Sheriff", "label": "Sheriff"}, {"value": "Shift Engineer", "label": "Shift Engineer"}, {"value": "Shift Leader", "label": "Shift Leader"}, {"value": "Shift Manager", "label": "Shift Manager"}, {"value": "Shift Supervisor", "label": "Shift Supervisor"}, {"value": "Ship Fitter", "label": "Ship Fitter"}, {"value": "Shipping", "label": "Shipping"}, {"value": "Shipping Clerk", "label": "Shipping Clerk"}, {"value": "Shipping Coordinator", "label": "Shipping Coordinator"}, {"value": "Shipping Manager", "label": "Shipping Manager"}, {"value": "Shipping Supervisor", "label": "Shipping Supervisor"}, {"value": "Shop Foreman", "label": "Shop Foreman"}, {"value": "Shop Manager", "label": "Shop Manager"}, {"value": "Shopper", "label": "Shopper"}, {"value": "Short Order Cook", "label": "Short Order Cook"}, {"value": "Showroom Manager", "label": "Showroom Manager"}, {"value": "Shuttle Bus Driver", "label": "Shuttle Bus Driver"}, {"value": "Shuttle Driver", "label": "Shuttle Driver"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Sign Language Interpreter", "label": "Sign Language Interpreter"}, {"value": "Signal Maintainer", "label": "Signal Maintainer"}, {"value": "Signing Agent", "label": "Signing Agent"}, {"value": "Simulation Engineer", "label": "Simulation Engineer"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Site Engineer", "label": "Site Engineer"}, {"value": "Ski Instructor", "label": "Ski Instructor"}, {"value": "Ski Patrol", "label": "Ski Patrol"}, {"value": "Skin Care Specialist", "label": "Skin Care Specialist"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Sleep Technician", "label": "Sleep Technician"}, {"value": "Slot Attendant", "label": "Slot Attendant"}, {"value": "Slot Technician", "label": "Slot Technician"}, {"value": "Small Business Consultant", "label": "Small Business Consultant"}, {"value": "Small Engine Mechanic", "label": "Small Engine Mechanic"}, {"value": "Smt Operator", "label": "Smt Operator"}, {"value": "Soccer Coach", "label": "Soccer Coach"}, {"value": "Social Media Analyst", "label": "Social Media Analyst"}, {"value": "Social Media Assistant", "label": "Social Media Assistant"}, {"value": "Social Media Consultant", "label": "Social Media Consultant"}, {"value": "Social Media Coordinator", "label": "Social Media Coordinator"}, {"value": "Social Media Director", "label": "Social Media Director"}, {"value": "Social Media Manager", "label": "Social Media Manager"}, {"value": "Social Media Marketing", "label": "Social Media Marketing"}, {"value": "Social Media Marketing Manager", "label": "Social Media Marketing Manager"}, {"value": "Social Media Specialist", "label": "Social Media Specialist"}, {"value": "Social Media Strategist", "label": "Social Media Strategist"}, {"value": "Social Science Teacher", "label": "Social Science Teacher"}, {"value": "Social Scientist", "label": "Social Scientist"}, {"value": "Social Service", "label": "Social Service"}, {"value": "Social Service Worker", "label": "Social Service Worker"}, {"value": "Social Services Assistant", "label": "Social Services Assistant"}, {"value": "Social Services Specialist", "label": "Social Services Specialist"}, {"value": "Social Studies Teacher", "label": "Social Studies Teacher"}, {"value": "Social Work", "label": "Social Work"}, {"value": "Social Work Assistant", "label": "Social Work Assistant"}, {"value": "Social Work Case Manager", "label": "Social Work Case Manager"}, {"value": "Social Worker", "label": "Social Worker"}, {"value": "Social Worker Assistant", "label": "Social Worker Assistant"}, {"value": "Software Analyst", "label": "Software Analyst"}, {"value": "Software Architect", "label": "Software Architect"}, {"value": "Software Consultant", "label": "Software Consultant"}, {"value": "Software Developer", "label": "Software Developer"}, {"value": "Software Development Engineer", "label": "Software Development Engineer"}, {"value": "Software Development Manager", "label": "Software Development Manager"}, {"value": "Software Engineer", "label": "Software Engineer"}, {"value": "Software Engineering Manager", "label": "Software Engineering Manager"}, {"value": "Software Manager", "label": "Software Manager"}, {"value": "Software Product Manager", "label": "Software Product Manager"}, {"value": "Software Programmer", "label": "Software Programmer"}, {"value": "Software Project Manager", "label": "Software Project Manager"}, {"value": "Software Qa Engineer", "label": "Software Qa Engineer"}, {"value": "Software Quality Assurance", "label": "Software Quality Assurance"}, {"value": "Software Quality Assurance Engineer", "label": "Software Quality Assurance Engineer"}, {"value": "Software Support Engineer", "label": "Software Support Engineer"}, {"value": "Software Test Engineer", "label": "Software Test Engineer"}, {"value": "Software Tester", "label": "Software Tester"}, {"value": "Software Trainer", "label": "Software Trainer"}, {"value": "Soil Scientist", "label": "Soil Scientist"}, {"value": "Solar Engineer", "label": "Solar Engineer"}, {"value": "Solar Installer", "label": "Solar Installer"}, {"value": "Solar Panel Installer", "label": "Solar Panel Installer"}, {"value": "Solar Sales", "label": "Solar Sales"}, {"value": "Solar Technician", "label": "Solar Technician"}, {"value": "Solution Architect", "label": "Solution Architect"}, {"value": "Solutions Architect", "label": "Solutions Architect"}, {"value": "Solutions Consultant", "label": "Solutions Consultant"}, {"value": "Solutions Engineer", "label": "Solutions Engineer"}, {"value": "Sommelier", "label": "Sommelier"}, {"value": "Sonographer", "label": "Sonographer"}, {"value": "Sound Designer", "label": "Sound Designer"}, {"value": "Sound Engineer", "label": "Sound Engineer"}, {"value": "Sound Technician", "label": "Sound Technician"}, {"value": "Sourcing Analyst", "label": "Sourcing Analyst"}, {"value": "Sourcing Manager", "label": "Sourcing Manager"}, {"value": "Sourcing Specialist", "label": "Sourcing Specialist"}, {"value": "Sous Chef", "label": "Sous Chef"}, {"value": "Spa Attendant", "label": "Spa Attendant"}, {"value": "Spa Coordinator", "label": "Spa Coordinator"}, {"value": "Spa Director", "label": "Spa Director"}, {"value": "Spa Manager", "label": "Spa Manager"}, {"value": "Spa Receptionist", "label": "Spa Receptionist"}, {"value": "Space Planner", "label": "Space Planner"}, {"value": "Spanish Interpreter", "label": "Spanish Interpreter"}, {"value": "Spanish Teacher", "label": "Spanish Teacher"}, {"value": "Spanish Tutor", "label": "Spanish Tutor"}, {"value": "Speaker", "label": "Speaker"}, {"value": "Special Agent", "label": "Special Agent"}, {"value": "Special Education Aide", "label": "Special Education Aide"}, {"value": "Special Education Assistant", "label": "Special Education Assistant"}, {"value": "Special Education Coordinator", "label": "Special Education Coordinator"}, {"value": "Special Education Paraprofessional", "label": "Special Education Paraprofessional"}, {"value": "Special Education Teacher", "label": "Special Education Teacher"}, {"value": "Special Educator", "label": "Special Educator"}, {"value": "Special Events Coordinator", "label": "Special Events Coordinator"}, {"value": "Special Events Manager", "label": "Special Events Manager"}, {"value": "Special Needs Teacher", "label": "Special Needs Teacher"}, {"value": "Special Police Officer", "label": "Special Police Officer"}, {"value": "Specialist", "label": "Specialist"}, {"value": "Specimen Processor", "label": "Specimen Processor"}, {"value": "Speech Language Pathologist", "label": "Speech Language Pathologist"}, {"value": "Speech Language Pathology Assistant", "label": "Speech Language Pathology Assistant"}, {"value": "Speech Pathologist", "label": "Speech Pathologist"}, {"value": "Speech Pathologist Assistant", "label": "Speech Pathologist Assistant"}, {"value": "Speech Therapist", "label": "Speech Therapist"}, {"value": "Speech Therapy Assistant", "label": "Speech Therapy Assistant"}, {"value": "Speech Writer", "label": "Speech Writer"}, {"value": "Sponsor", "label": "Sponsor"}, {"value": "Sports", "label": "Sports"}, {"value": "Sports Agent", "label": "Sports Agent"}, {"value": "Sports Anchor", "label": "Sports Anchor"}, {"value": "Sports Coordinator", "label": "Sports Coordinator"}, {"value": "Sports Director", "label": "Sports Director"}, {"value": "Sports Information Director", "label": "Sports Information Director"}, {"value": "Sports Medicine Physician", "label": "Sports Medicine Physician"}, {"value": "Sports Photographer", "label": "Sports Photographer"}, {"value": "Sports Physical Therapist", "label": "Sports Physical Therapist"}, {"value": "Sports Producer", "label": "Sports Producer"}, {"value": "Sports Psychologist", "label": "Sports Psychologist"}, {"value": "Sports Reporter", "label": "Sports Reporter"}, {"value": "Sports Statistician", "label": "Sports Statistician"}, {"value": "Sports Writer", "label": "Sports Writer"}, {"value": "Spotter", "label": "Spotter"}, {"value": "Sprinkler Fitter", "label": "Sprinkler Fitter"}, {"value": "Sql Analyst", "label": "Sql Analyst"}, {"value": "Sql Data Analyst", "label": "Sql Data Analyst"}, {"value": "Sql Database Administrator", "label": "Sql Database Administrator"}, {"value": "Sql Database Developer", "label": "Sql Database Developer"}, {"value": "Sql Dba", "label": "Sql Dba"}, {"value": "Sql Developer", "label": "Sql Developer"}, {"value": "Sql Programmer", "label": "Sql Programmer"}, {"value": "Sql Server Dba", "label": "Sql Server Dba"}, {"value": "Sql Server Developer", "label": "Sql Server Developer"}, {"value": "Staff Accountant", "label": "Staff Accountant"}, {"value": "Staff Assistant", "label": "Staff Assistant"}, {"value": "Staff Attorney", "label": "Staff Attorney"}, {"value": "Staff Auditor", "label": "Staff Auditor"}, {"value": "Staff Engineer", "label": "Staff Engineer"}, {"value": "Staff Nurse", "label": "Staff Nurse"}, {"value": "Staff Pharmacist", "label": "Staff Pharmacist"}, {"value": "Staff Writer", "label": "Staff Writer"}, {"value": "Staffing Consultant", "label": "Staffing Consultant"}, {"value": "Staffing Coordinator", "label": "Staffing Coordinator"}, {"value": "Staffing Manager", "label": "Staffing Manager"}, {"value": "Staffing Recruiter", "label": "Staffing Recruiter"}, {"value": "Staffing Specialist", "label": "Staffing Specialist"}, {"value": "Stage Hand", "label": "Stage Hand"}, {"value": "Stage Manager", "label": "Stage Manager"}, {"value": "Stage Technician", "label": "Stage Technician"}, {"value": "Starter", "label": "Starter"}, {"value": "State Trooper", "label": "State Trooper"}, {"value": "Stationary Engineer", "label": "Stationary Engineer"}, {"value": "Statistical Analyst", "label": "Statistical Analyst"}, {"value": "Statistical Programmer", "label": "Statistical Programmer"}, {"value": "Statistician", "label": "Statistician"}, {"value": "Steamfitter", "label": "Steamfitter"}, {"value": "Steel Worker", "label": "Steel Worker"}, {"value": "Stenographer", "label": "Stenographer"}, {"value": "Sterile Processing Technician", "label": "Sterile Processing Technician"}, {"value": "Steward", "label": "Steward"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Stock Analyst", "label": "Stock Analyst"}, {"value": "Stock Broker", "label": "Stock Broker"}, {"value": "Stock Clerk", "label": "Stock Clerk"}, {"value": "Stock Manager", "label": "Stock Manager"}, {"value": "Stock Trader", "label": "Stock Trader"}, {"value": "Stocker", "label": "Stocker"}, {"value": "Stockroom Associate", "label": "Stockroom Associate"}, {"value": "Stockroom Clerk", "label": "Stockroom Clerk"}, {"value": "Storage Administrator", "label": "Storage Administrator"}, {"value": "Storage Engineer", "label": "Storage Engineer"}, {"value": "Storage Manager", "label": "Storage Manager"}, {"value": "Store Assistant", "label": "Store Assistant"}, {"value": "Store Associate", "label": "Store Associate"}, {"value": "Store Cashier", "label": "Store Cashier"}, {"value": "Store Clerk", "label": "Store Clerk"}, {"value": "Store Detective", "label": "Store Detective"}, {"value": "Store Director", "label": "Store Director"}, {"value": "Store Manager", "label": "Store Manager"}, {"value": "Store Shopper", "label": "Store Shopper"}, {"value": "Store Supervisor", "label": "Store Supervisor"}, {"value": "Storyboard Artist", "label": "Storyboard Artist"}, {"value": "Strategic Account Manager", "label": "Strategic Account Manager"}, {"value": "Strategic Consultant", "label": "Strategic Consultant"}, {"value": "Strategic Planner", "label": "Strategic Planner"}, {"value": "Strategic Planning Manager", "label": "Strategic Planning Manager"}, {"value": "Strategic Sourcing Manager", "label": "Strategic Sourcing Manager"}, {"value": "Strategist", "label": "Strategist"}, {"value": "Strategy Analyst", "label": "Strategy Analyst"}, {"value": "Strategy Consultant", "label": "Strategy Consultant"}, {"value": "Strategy Manager", "label": "Strategy Manager"}, {"value": "Street Sweeper", "label": "Street Sweeper"}, {"value": "Strength And Conditioning Coach", "label": "Strength And Conditioning Coach"}, {"value": "Stress Engineer", "label": "Stress Engineer"}, {"value": "Structural Design Engineer", "label": "Structural Design Engineer"}, {"value": "Structural Designer", "label": "Structural Designer"}, {"value": "Structural Engineer", "label": "Structural Engineer"}, {"value": "Structural Welder", "label": "Structural Welder"}, {"value": "Student Advisor", "label": "Student Advisor"}, {"value": "Student Assistant", "label": "Student Assistant"}, {"value": "Student Counselor", "label": "Student Counselor"}, {"value": "Student Mentor", "label": "Student Mentor"}, {"value": "Student Nurse", "label": "Student Nurse"}, {"value": "Student Tutor", "label": "Student Tutor"}, {"value": "Student Worker", "label": "Student Worker"}, {"value": "Studio Assistant", "label": "Studio Assistant"}, {"value": "Studio Engineer", "label": "Studio Engineer"}, {"value": "Studio Manager", "label": "Studio Manager"}, {"value": "Studio Photographer", "label": "Studio Photographer"}, {"value": "Study Abroad Advisor", "label": "Study Abroad Advisor"}, {"value": "Study Coordinator", "label": "Study Coordinator"}, {"value": "Stylist", "label": "Stylist"}, {"value": "Stylist Assistant", "label": "Stylist Assistant"}, {"value": "Subject Matter Expert", "label": "Subject Matter Expert"}, {"value": "Substance Abuse Counselor", "label": "Substance Abuse Counselor"}, {"value": "Substation Electrician", "label": "Substation Electrician"}, {"value": "Substation Technician", "label": "Substation Technician"}, {"value": "Substitute Teacher", "label": "Substitute Teacher"}, {"value": "Summer Associate", "label": "Summer Associate"}, {"value": "Summer Camp Counselor", "label": "Summer Camp Counselor"}, {"value": "Summer Nanny", "label": "Summer Nanny"}, {"value": "Superintendent", "label": "Superintendent"}, {"value": "Supervisor", "label": "Supervisor"}, {"value": "Supplier Quality Engineer", "label": "Supplier Quality Engineer"}, {"value": "Supplier Quality Manager", "label": "Supplier Quality Manager"}, {"value": "Supply Chain", "label": "Supply Chain"}, {"value": "Supply Chain Analyst", "label": "Supply Chain Analyst"}, {"value": "Supply Chain Consultant", "label": "Supply Chain Consultant"}, {"value": "Supply Chain Coordinator", "label": "Supply Chain Coordinator"}, {"value": "Supply Chain Director", "label": "Supply Chain Director"}, {"value": "Supply Chain Management", "label": "Supply Chain Management"}, {"value": "Supply Chain Manager", "label": "Supply Chain Manager"}, {"value": "Supply Chain Planner", "label": "Supply Chain Planner"}, {"value": "Supply Chain Specialist", "label": "Supply Chain Specialist"}, {"value": "Supply Clerk", "label": "Supply Clerk"}, {"value": "Supply Coordinator", "label": "Supply Coordinator"}, {"value": "Supply Manager", "label": "Supply Manager"}, {"value": "Supply Planner", "label": "Supply Planner"}, {"value": "Supply Technician", "label": "Supply Technician"}, {"value": "Support Analyst", "label": "Support Analyst"}, {"value": "Support Coordinator", "label": "Support Coordinator"}, {"value": "Support Engineer", "label": "Support Engineer"}, {"value": "Support Manager", "label": "Support Manager"}, {"value": "Support Specialist", "label": "Support Specialist"}, {"value": "Support Technician", "label": "Support Technician"}, {"value": "Support Worker", "label": "Support Worker"}, {"value": "Surgeon", "label": "Surgeon"}, {"value": "Surgery Scheduler", "label": "Surgery Scheduler"}, {"value": "Surgical Assistant", "label": "Surgical Assistant"}, {"value": "Surgical Coordinator", "label": "Surgical Coordinator"}, {"value": "Surgical First Assistant", "label": "Surgical First Assistant"}, {"value": "Surgical Nurse", "label": "Surgical Nurse"}, {"value": "Surgical Nurse Practitioner", "label": "Surgical Nurse Practitioner"}, {"value": "Surgical Physician Assistant", "label": "Surgical Physician Assistant"}, {"value": "Surgical Technician", "label": "Surgical Technician"}, {"value": "Surgical Technologist", "label": "Surgical Technologist"}, {"value": "Surveillance Investigator", "label": "Surveillance Investigator"}, {"value": "Surveillance Operator", "label": "Surveillance Operator"}, {"value": "Survey Researcher", "label": "Survey Researcher"}, {"value": "Survey Technician", "label": "Survey Technician"}, {"value": "Surveyor", "label": "Surveyor"}, {"value": "<PERSON>shi Chef", "label": "<PERSON>shi Chef"}, {"value": "Sustainability Consultant", "label": "Sustainability Consultant"}, {"value": "Sustainability Coordinator", "label": "Sustainability Coordinator"}, {"value": "Sustainability Manager", "label": "Sustainability Manager"}, {"value": "Swim Coach", "label": "Swim Coach"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Switchboard Operator", "label": "Switchboard Operator"}, {"value": "System Administrator", "label": "System Administrator"}, {"value": "System Engineer", "label": "System Engineer"}, {"value": "Systems Administrator", "label": "Systems Administrator"}, {"value": "Systems Analyst", "label": "Systems Analyst"}, {"value": "Systems Architect", "label": "Systems Architect"}, {"value": "Systems Engineer", "label": "Systems Engineer"}, {"value": "Systems Manager", "label": "Systems Manager"}, {"value": "Systems Programmer", "label": "Systems Programmer"}, {"value": "Table Games Dealer", "label": "Table Games Dealer"}, {"value": "Tableau Developer", "label": "Tableau Developer"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Talent Acquisition", "label": "Talent Acquisition"}, {"value": "Talent Acquisition Manager", "label": "Talent Acquisition Manager"}, {"value": "Talent Acquisition Specialist", "label": "Talent Acquisition Specialist"}, {"value": "Talent Agent", "label": "Talent Agent"}, {"value": "Talent Coordinator", "label": "Talent Coordinator"}, {"value": "Talent Director", "label": "Talent Director"}, {"value": "Talent Manager", "label": "Talent Manager"}, {"value": "Tanker", "label": "Tanker"}, {"value": "Tanker Driver", "label": "Tanker Driver"}, {"value": "Tankerman", "label": "Tankerman"}, {"value": "Tattoo Artist", "label": "Tattoo Artist"}, {"value": "Tax Accountant", "label": "Tax Accountant"}, {"value": "Tax Advisor", "label": "Tax Advisor"}, {"value": "Tax Analyst", "label": "Tax Analyst"}, {"value": "Tax Assistant", "label": "Tax Assistant"}, {"value": "Tax Associate", "label": "Tax Associate"}, {"value": "Tax Attorney", "label": "Tax Attorney"}, {"value": "Tax Auditor", "label": "Tax Auditor"}, {"value": "Tax Consultant", "label": "Tax Consultant"}, {"value": "Tax Director", "label": "Tax Director"}, {"value": "Tax Examiner", "label": "Tax Examiner"}, {"value": "Tax Manager", "label": "Tax Manager"}, {"value": "Tax Preparer", "label": "Tax Preparer"}, {"value": "Tax Professional", "label": "Tax Professional"}, {"value": "Tax Specialist", "label": "Tax Specialist"}, {"value": "Taxi Driver", "label": "Taxi Driver"}, {"value": "Taxonomist", "label": "Taxonomist"}, {"value": "Teacher", "label": "Teacher"}, {"value": "Teacher Aide", "label": "Teacher Aide"}, {"value": "Teacher Assistant", "label": "Teacher Assistant"}, {"value": "Teachers Aide", "label": "Teachers Aide"}, {"value": "Teaching Artist", "label": "Teaching Artist"}, {"value": "Teaching Assistant", "label": "Teaching Assistant"}, {"value": "Team Coordinator", "label": "Team Coordinator"}, {"value": "Team Lead", "label": "Team Lead"}, {"value": "Team Leader", "label": "Team Leader"}, {"value": "Team Manager", "label": "Team Manager"}, {"value": "Team Member", "label": "Team Member"}, {"value": "Teamster", "label": "Teamster"}, {"value": "Tech Support", "label": "Tech Support"}, {"value": "Technical Account Manager", "label": "Technical Account Manager"}, {"value": "Technical Analyst", "label": "Technical Analyst"}, {"value": "Technical Architect", "label": "Technical Architect"}, {"value": "Technical Artist", "label": "Technical Artist"}, {"value": "Technical Assistant", "label": "Technical Assistant"}, {"value": "Technical Business Analyst", "label": "Technical Business Analyst"}, {"value": "Technical Consultant", "label": "Technical Consultant"}, {"value": "Technical Coordinator", "label": "Technical Coordinator"}, {"value": "Technical Designer", "label": "Technical Designer"}, {"value": "Technical Director", "label": "Technical Director"}, {"value": "Technical Editor", "label": "Technical Editor"}, {"value": "Technical Engineer", "label": "Technical Engineer"}, {"value": "Technical Illustrator", "label": "Technical Illustrator"}, {"value": "Technical Lead", "label": "Technical Lead"}, {"value": "Technical Manager", "label": "Technical Manager"}, {"value": "Technical Marketing Engineer", "label": "Technical Marketing Engineer"}, {"value": "Technical Product Manager", "label": "Technical Product Manager"}, {"value": "Technical Program Manager", "label": "Technical Program Manager"}, {"value": "Technical Project Manager", "label": "Technical Project Manager"}, {"value": "Technical Recruiter", "label": "Technical Recruiter"}, {"value": "Technical Sales", "label": "Technical Sales"}, {"value": "Technical Sales Engineer", "label": "Technical Sales Engineer"}, {"value": "Technical Sales Manager", "label": "Technical Sales Manager"}, {"value": "Technical Sales Representative", "label": "Technical Sales Representative"}, {"value": "Technical Services Manager", "label": "Technical Services Manager"}, {"value": "Technical Specialist", "label": "Technical Specialist"}, {"value": "Technical Support", "label": "Technical Support"}, {"value": "Technical Support Agent", "label": "Technical Support Agent"}, {"value": "Technical Support Analyst", "label": "Technical Support Analyst"}, {"value": "Technical Support Engineer", "label": "Technical Support Engineer"}, {"value": "Technical Support Manager", "label": "Technical Support Manager"}, {"value": "Technical Support Representative", "label": "Technical Support Representative"}, {"value": "Technical Support Specialist", "label": "Technical Support Specialist"}, {"value": "Technical Trainer", "label": "Technical Trainer"}, {"value": "Technical Writer", "label": "Technical Writer"}, {"value": "Technician", "label": "Technician"}, {"value": "Technologist", "label": "Technologist"}, {"value": "Technology", "label": "Technology"}, {"value": "Technology Analyst", "label": "Technology Analyst"}, {"value": "Technology Consultant", "label": "Technology Consultant"}, {"value": "Technology Coordinator", "label": "Technology Coordinator"}, {"value": "Technology Director", "label": "Technology Director"}, {"value": "Technology Manager", "label": "Technology Manager"}, {"value": "Technology Project Manager", "label": "Technology Project Manager"}, {"value": "Technology Sales", "label": "Technology Sales"}, {"value": "Technology Specialist", "label": "Technology Specialist"}, {"value": "Technology Teacher", "label": "Technology Teacher"}, {"value": "Technology Trainer", "label": "Technology Trainer"}, {"value": "Telecom Engineer", "label": "Telecom Engineer"}, {"value": "Telecom Sales", "label": "Telecom Sales"}, {"value": "Telecom Technician", "label": "Telecom Technician"}, {"value": "Telecommunications", "label": "Telecommunications"}, {"value": "Telecommunications Analyst", "label": "Telecommunications Analyst"}, {"value": "Telecommunications Engineer", "label": "Telecommunications Engineer"}, {"value": "Telecommunications Manager", "label": "Telecommunications Manager"}, {"value": "Telecommunications Specialist", "label": "Telecommunications Specialist"}, {"value": "Telecommunications Technician", "label": "Telecommunications Technician"}, {"value": "Telemarketer", "label": "Telemarketer"}, {"value": "Telemetry Technician", "label": "Telemetry Technician"}, {"value": "Telephone Interviewer", "label": "Telephone Interviewer"}, {"value": "Telephone Operator", "label": "Telephone Operator"}, {"value": "Telephone Triage Nurse", "label": "Telephone Triage Nurse"}, {"value": "Telesales Representative", "label": "Telesales Representative"}, {"value": "Television Producer", "label": "Television Producer"}, {"value": "Teller", "label": "Teller"}, {"value": "Tennis Coach", "label": "Tennis Coach"}, {"value": "Tennis Instructor", "label": "Tennis Instructor"}, {"value": "Teradata Developer", "label": "Teradata Developer"}, {"value": "Terminal Manager", "label": "Terminal Manager"}, {"value": "Terminal Operator", "label": "Terminal Operator"}, {"value": "Territory Account Manager", "label": "Territory Account Manager"}, {"value": "Territory Manager", "label": "Territory Manager"}, {"value": "Territory Sales Manager", "label": "Territory Sales Manager"}, {"value": "Territory Sales Representative", "label": "Territory Sales Representative"}, {"value": "Test Analyst", "label": "Test Analyst"}, {"value": "Test Architect", "label": "Test Architect"}, {"value": "Test Coordinator", "label": "Test Coordinator"}, {"value": "Test Driver", "label": "Test Driver"}, {"value": "Test Engineer", "label": "Test Engineer"}, {"value": "Test Manager", "label": "Test Manager"}, {"value": "Test Pilot", "label": "Test Pilot"}, {"value": "Test Proctor", "label": "Test Proctor"}, {"value": "Test Technician", "label": "Test Technician"}, {"value": "Tester", "label": "Tester"}, {"value": "Testing Coordinator", "label": "Testing Coordinator"}, {"value": "Textile Designer", "label": "Textile Designer"}, {"value": "Theater Manager", "label": "Theater Manager"}, {"value": "Theater Teacher", "label": "Theater Teacher"}, {"value": "Theatre", "label": "Theatre"}, {"value": "Theology Teacher", "label": "Theology Teacher"}, {"value": "Therapist", "label": "Therapist"}, {"value": "Therapy Aide", "label": "Therapy Aide"}, {"value": "Ticket Agent", "label": "Ticket Agent"}, {"value": "Ticket Taker", "label": "Ticket Taker"}, {"value": "Ticketing Agent", "label": "Ticketing Agent"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Timekeeper", "label": "Timekeeper"}, {"value": "Tire Technician", "label": "Tire Technician"}, {"value": "Title Clerk", "label": "Title Clerk"}, {"value": "Title Examiner", "label": "Title Examiner"}, {"value": "Title Officer", "label": "Title Officer"}, {"value": "Title Processor", "label": "Title Processor"}, {"value": "Title Searcher", "label": "Title Searcher"}, {"value": "Toll Collector", "label": "Toll Collector"}, {"value": "Tool And Die Maker", "label": "Tool And Die Maker"}, {"value": "<PERSON><PERSON>ant", "label": "<PERSON><PERSON>ant"}, {"value": "Tool Designer", "label": "Tool Designer"}, {"value": "Tool Maker", "label": "Tool Maker"}, {"value": "<PERSON><PERSON> Engineer", "label": "<PERSON><PERSON> Engineer"}, {"value": "Toolmaker", "label": "Toolmaker"}, {"value": "Tour Guide", "label": "Tour Guide"}, {"value": "Tour Manager", "label": "Tour Manager"}, {"value": "Tow Truck Driver", "label": "Tow Truck Driver"}, {"value": "Tower Climber", "label": "Tower Climber"}, {"value": "Tower Technician", "label": "Tower Technician"}, {"value": "Toxicologist", "label": "Toxicologist"}, {"value": "Toy Designer", "label": "Toy Designer"}, {"value": "Tractor Trailer Driver", "label": "Tractor Trailer Driver"}, {"value": "Trademark Paralegal", "label": "Trademark Paralegal"}, {"value": "Trader", "label": "Trader"}, {"value": "Tradesman", "label": "Tradesman"}, {"value": "Trading Analyst", "label": "Trading Analyst"}, {"value": "Trading Assistant", "label": "Trading Assistant"}, {"value": "Traffic Controller", "label": "Traffic Controller"}, {"value": "Traffic Coordinator", "label": "Traffic Coordinator"}, {"value": "Traffic Engineer", "label": "Traffic Engineer"}, {"value": "Traffic Manager", "label": "Traffic Manager"}, {"value": "Trailer Mechanic", "label": "Trailer Mechanic"}, {"value": "Train Conductor", "label": "Train Conductor"}, {"value": "Train Dispatcher", "label": "Train Dispatcher"}, {"value": "Train Engineer", "label": "Train Engineer"}, {"value": "Trainee", "label": "Trainee"}, {"value": "Trainer", "label": "Trainer"}, {"value": "Training Administrator", "label": "Training Administrator"}, {"value": "Training Consultant", "label": "Training Consultant"}, {"value": "Training Coordinator", "label": "Training Coordinator"}, {"value": "Training Director", "label": "Training Director"}, {"value": "Training Instructor", "label": "Training Instructor"}, {"value": "Training Manager", "label": "Training Manager"}, {"value": "Training Officer", "label": "Training Officer"}, {"value": "Training Specialist", "label": "Training Specialist"}, {"value": "Training Supervisor", "label": "Training Supervisor"}, {"value": "Transaction Coordinator", "label": "Transaction Coordinator"}, {"value": "Transcriber", "label": "Transcriber"}, {"value": "Transcriptionist", "label": "Transcriptionist"}, {"value": "Transit Bus Driver", "label": "Transit Bus Driver"}, {"value": "Transit Operator", "label": "Transit Operator"}, {"value": "Translator", "label": "Translator"}, {"value": "Transmission Engineer", "label": "Transmission Engineer"}, {"value": "Transplant Coordinator", "label": "Transplant Coordinator"}, {"value": "Transport Driver", "label": "Transport Driver"}, {"value": "Transportation Analyst", "label": "Transportation Analyst"}, {"value": "Transportation Coordinator", "label": "Transportation Coordinator"}, {"value": "Transportation Dispatcher", "label": "Transportation Dispatcher"}, {"value": "Transportation Driver", "label": "Transportation Driver"}, {"value": "Transportation Engineer", "label": "Transportation Engineer"}, {"value": "Transportation Inspector", "label": "Transportation Inspector"}, {"value": "Transportation Manager", "label": "Transportation Manager"}, {"value": "Transportation Operations Manager", "label": "Transportation Operations Manager"}, {"value": "Transportation Planner", "label": "Transportation Planner"}, {"value": "Transportation Security Officer", "label": "Transportation Security Officer"}, {"value": "Transportation Specialist", "label": "Transportation Specialist"}, {"value": "Transportation Supervisor", "label": "Transportation Supervisor"}, {"value": "Transporter", "label": "Transporter"}, {"value": "Trash Collector", "label": "Trash Collector"}, {"value": "Trauma Surgeon", "label": "Trauma Surgeon"}, {"value": "Travel", "label": "Travel"}, {"value": "Travel Agent", "label": "Travel Agent"}, {"value": "Travel Assistant", "label": "Travel Assistant"}, {"value": "Travel Cna", "label": "Travel Cna"}, {"value": "Travel Consultant", "label": "Travel Consultant"}, {"value": "Travel Coordinator", "label": "Travel Coordinator"}, {"value": "Travel Counselor", "label": "Travel Counselor"}, {"value": "Travel Manager", "label": "Travel Manager"}, {"value": "Travel Nurse", "label": "Travel Nurse"}, {"value": "Travel Rn", "label": "Travel Rn"}, {"value": "Travel Specialist", "label": "Travel Specialist"}, {"value": "Traveler", "label": "Traveler"}, {"value": "Traveling Nurse", "label": "Traveling Nurse"}, {"value": "Treasurer", "label": "Treasurer"}, {"value": "Treasury Accountant", "label": "Treasury Accountant"}, {"value": "Treasury Analyst", "label": "Treasury Analyst"}, {"value": "Treasury Manager", "label": "Treasury Manager"}, {"value": "Tree Climber", "label": "Tree Climber"}, {"value": "Tree Trimmer", "label": "Tree Trimmer"}, {"value": "Triage Nurse", "label": "Triage Nurse"}, {"value": "Trial Attorney", "label": "Trial Attorney"}, {"value": "Truck Dispatcher", "label": "Truck Dispatcher"}, {"value": "Truck Driver", "label": "Truck Driver"}, {"value": "Truck Loader", "label": "Truck Loader"}, {"value": "Trucker", "label": "Trucker"}, {"value": "Truss Designer", "label": "Truss Designer"}, {"value": "Trust Accountant", "label": "Trust Accountant"}, {"value": "Trust Officer", "label": "Trust Officer"}, {"value": "Tutor", "label": "Tutor"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Ui Designer", "label": "Ui Designer"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Ultrasonographer", "label": "Ultrasonographer"}, {"value": "Ultrasound Technician", "label": "Ultrasound Technician"}, {"value": "Ultrasound Technologist", "label": "Ultrasound Technologist"}, {"value": "Umpire", "label": "Umpire"}, {"value": "Unarmed Security Guard", "label": "Unarmed Security Guard"}, {"value": "Underground Miner", "label": "Underground Miner"}, {"value": "Underwriter", "label": "Underwriter"}, {"value": "Underwriting Assistant", "label": "Underwriting Assistant"}, {"value": "Underwriting Manager", "label": "Underwriting Manager"}, {"value": "Union Carpenter", "label": "Union Carpenter"}, {"value": "Union Organizer", "label": "Union Organizer"}, {"value": "Unit Assistant", "label": "Unit Assistant"}, {"value": "Unit Clerk", "label": "Unit Clerk"}, {"value": "Unit Coordinator", "label": "Unit Coordinator"}, {"value": "Unit Manager", "label": "Unit Manager"}, {"value": "Unit Secretary", "label": "Unit Secretary"}, {"value": "Universal Banker", "label": "Universal Banker"}, {"value": "Unix Administrator", "label": "Unix Administrator"}, {"value": "Unix Engineer", "label": "Unix Engineer"}, {"value": "Unix System Administrator", "label": "Unix System Administrator"}, {"value": "Unloader", "label": "Unloader"}, {"value": "Upholsterer", "label": "Upholsterer"}, {"value": "Urban Designer", "label": "Urban Designer"}, {"value": "Urban Planner", "label": "Urban Planner"}, {"value": "Urologist", "label": "Urologist"}, {"value": "Usability Engineer", "label": "Usability Engineer"}, {"value": "User Experience Designer", "label": "User Experience Designer"}, {"value": "User Experience Researcher", "label": "User Experience Researcher"}, {"value": "User Interface Designer", "label": "User Interface Designer"}, {"value": "<PERSON>her", "label": "<PERSON>her"}, {"value": "Utility Engineer", "label": "Utility Engineer"}, {"value": "Utility Locator", "label": "Utility Locator"}, {"value": "Utility Operator", "label": "Utility Operator"}, {"value": "Utility Worker", "label": "Utility Worker"}, {"value": "Utilization Review Nurse", "label": "Utilization Review Nurse"}, {"value": "Ux Designer", "label": "Ux Designer"}, {"value": "Ux Developer", "label": "Ux Developer"}, {"value": "Vaccinator", "label": "Vaccinator"}, {"value": "Valet", "label": "Valet"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "<PERSON><PERSON>", "label": "<PERSON><PERSON>"}, {"value": "Validation Engineer", "label": "Validation Engineer"}, {"value": "Validation Manager", "label": "Validation Manager"}, {"value": "Validation Specialist", "label": "Validation Specialist"}, {"value": "Valuation Analyst", "label": "Valuation Analyst"}, {"value": "Stream Manager", "label": "Stream Manager"}, {"value": "<PERSON>", "label": "<PERSON>"}, {"value": "Vascular Sonographer", "label": "Vascular Sonographer"}, {"value": "Vascular Surgeon", "label": "Vascular Surgeon"}, {"value": "Vascular Technician", "label": "Vascular Technician"}, {"value": "Vascular Technologist", "label": "Vascular Technologist"}, {"value": "<PERSON><PERSON> Teller", "label": "<PERSON><PERSON> Teller"}, {"value": "Vba Developer", "label": "Vba Developer"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Vendor Manager", "label": "Vendor Manager"}, {"value": "Venue Manager", "label": "Venue Manager"}, {"value": "Verification Engineer", "label": "Verification Engineer"}, {"value": "Veterinarian", "label": "Veterinarian"}, {"value": "Veterinary", "label": "Veterinary"}, {"value": "Veterinary Assistant", "label": "Veterinary Assistant"}, {"value": "Veterinary Pathologist", "label": "Veterinary Pathologist"}, {"value": "Veterinary Receptionist", "label": "Veterinary Receptionist"}, {"value": "Veterinary Technician", "label": "Veterinary Technician"}, {"value": "Vfx Artist", "label": "Vfx Artist"}, {"value": "Vice President", "label": "Vice President"}, {"value": "Vice President Of Finance", "label": "Vice President Of Finance"}, {"value": "Vice President Of Human Resources", "label": "Vice President Of Human Resources"}, {"value": "Vice President Of Marketing", "label": "Vice President Of Marketing"}, {"value": "Vice President Of Operations", "label": "Vice President Of Operations"}, {"value": "Vice President Of Sales", "label": "Vice President Of Sales"}, {"value": "Vice Principal", "label": "Vice Principal"}, {"value": "Victim Advocate", "label": "Victim Advocate"}, {"value": "Video Coordinator", "label": "Video Coordinator"}, {"value": "Video Editor", "label": "Video Editor"}, {"value": "Video Engineer", "label": "Video Engineer"}, {"value": "Video Game Designer", "label": "Video Game Designer"}, {"value": "Video Game Programmer", "label": "Video Game Programmer"}, {"value": "Video Journalist", "label": "Video Journalist"}, {"value": "Video Producer", "label": "Video Producer"}, {"value": "Video Production", "label": "Video Production"}, {"value": "Video Production Assistant", "label": "Video Production Assistant"}, {"value": "Videographer", "label": "Videographer"}, {"value": "Virtual Assistant", "label": "Virtual Assistant"}, {"value": "Virtual Teacher", "label": "Virtual Teacher"}, {"value": "Virtualization Engineer", "label": "Virtualization Engineer"}, {"value": "Visual Designer", "label": "Visual Designer"}, {"value": "Visual Information Specialist", "label": "Visual Information Specialist"}, {"value": "Visual Manager", "label": "Visual Manager"}, {"value": "Visual Merchandiser", "label": "Visual Merchandiser"}, {"value": "Visual Merchandising Manager", "label": "Visual Merchandising Manager"}, {"value": "Vmware Administrator", "label": "Vmware Administrator"}, {"value": "Vmware Engineer", "label": "Vmware Engineer"}, {"value": "Vocational Counselor", "label": "Vocational Counselor"}, {"value": "Vocational Rehabilitation Counselor", "label": "Vocational Rehabilitation Counselor"}, {"value": "Voice Actor", "label": "Voice Actor"}, {"value": "Voice Engineer", "label": "Voice Engineer"}, {"value": "Voip Engineer", "label": "Voip Engineer"}, {"value": "Volleyball Coach", "label": "Volleyball Coach"}, {"value": "Volunteer", "label": "Volunteer"}, {"value": "Volunteer Coordinator", "label": "Volunteer Coordinator"}, {"value": "Volunteer Manager", "label": "Volunteer Manager"}, {"value": "Vp Engineering", "label": "Vp Engineering"}, {"value": "Vp Marketing", "label": "Vp Marketing"}, {"value": "Vp Of Engineering", "label": "Vp Of Engineering"}, {"value": "Vp Of Finance", "label": "Vp Of Finance"}, {"value": "Vp Of Information Technology", "label": "Vp Of Information Technology"}, {"value": "Vp Sales", "label": "Vp Sales"}, {"value": "Wait Staff", "label": "Wait Staff"}, {"value": "Waiter", "label": "Waiter"}, {"value": "Waitress", "label": "Waitress"}, {"value": "Ward Clerk", "label": "Ward Clerk"}, {"value": "Warehouse Administrator", "label": "Warehouse Administrator"}, {"value": "Warehouse Assistant", "label": "Warehouse Assistant"}, {"value": "Warehouse Associate", "label": "Warehouse Associate"}, {"value": "Warehouse Clerk", "label": "Warehouse Clerk"}, {"value": "Warehouse Coordinator", "label": "Warehouse Coordinator"}, {"value": "Warehouse Driver", "label": "Warehouse Driver"}, {"value": "Warehouse Lead", "label": "Warehouse Lead"}, {"value": "Warehouse Manager", "label": "Warehouse Manager"}, {"value": "Warehouse Operations Manager", "label": "Warehouse Operations Manager"}, {"value": "Warehouse Operator", "label": "Warehouse Operator"}, {"value": "Warehouse Packer", "label": "Warehouse Packer"}, {"value": "Warehouse Picker", "label": "Warehouse Picker"}, {"value": "Warehouse Selector", "label": "Warehouse Selector"}, {"value": "Warehouse Specialist", "label": "Warehouse Specialist"}, {"value": "Warehouse Supervisor", "label": "Warehouse Supervisor"}, {"value": "Warehouse Technician", "label": "Warehouse Technician"}, {"value": "Warehouse Worker", "label": "Warehouse Worker"}, {"value": "Warehouseman", "label": "Warehouseman"}, {"value": "Warranty Administrator", "label": "Warranty Administrator"}, {"value": "Warranty Clerk", "label": "Warranty Clerk"}, {"value": "Warranty Manager", "label": "Warranty Manager"}, {"value": "Wastewater Operator", "label": "Wastewater Operator"}, {"value": "Wastewater Treatment Operator", "label": "Wastewater Treatment Operator"}, {"value": "Watchmaker", "label": "Watchmaker"}, {"value": "Water Engineer", "label": "Water Engineer"}, {"value": "Water Operator", "label": "Water Operator"}, {"value": "Water Resource Engineer", "label": "Water Resource Engineer"}, {"value": "Water Treatment Plant Operator", "label": "Water Treatment Plant Operator"}, {"value": "Web Administrator", "label": "Web Administrator"}, {"value": "Web Analyst", "label": "Web Analyst"}, {"value": "Web Application Developer", "label": "Web Application Developer"}, {"value": "Web Architect", "label": "Web Architect"}, {"value": "Web Content Editor", "label": "Web Content Editor"}, {"value": "Web Content Manager", "label": "Web Content Manager"}, {"value": "Web Designer", "label": "Web Designer"}, {"value": "Web Developer", "label": "Web Developer"}, {"value": "Web Editor", "label": "Web Editor"}, {"value": "Web Graphic Designer", "label": "Web Graphic Designer"}, {"value": "Web Manager", "label": "Web Manager"}, {"value": "Web Producer", "label": "Web Producer"}, {"value": "Web Programmer", "label": "Web Programmer"}, {"value": "Web Project Manager", "label": "Web Project Manager"}, {"value": "Webmaster", "label": "Webmaster"}, {"value": "Website Administrator", "label": "Website Administrator"}, {"value": "Wedding Coordinator", "label": "Wedding Coordinator"}, {"value": "Wedding Planner", "label": "Wedding Planner"}, {"value": "<PERSON>lder", "label": "<PERSON>lder"}, {"value": "Welder Apprentice", "label": "Welder Apprentice"}, {"value": "Welder Fabricator", "label": "Welder Fabricator"}, {"value": "<PERSON><PERSON> Helper", "label": "<PERSON><PERSON> Helper"}, {"value": "Welding", "label": "Welding"}, {"value": "Welding Engineer", "label": "Welding Engineer"}, {"value": "Welding Inspector", "label": "Welding Inspector"}, {"value": "Welding Instructor", "label": "Welding Instructor"}, {"value": "Welding Supervisor", "label": "Welding Supervisor"}, {"value": "Wellness Coach", "label": "Wellness Coach"}, {"value": "Wellness Consultant", "label": "Wellness Consultant"}, {"value": "Wellness Coordinator", "label": "Wellness Coordinator"}, {"value": "Wellness Director", "label": "Wellness Director"}, {"value": "Wellness Nurse", "label": "Wellness Nurse"}, {"value": "Wholesale Account Executive", "label": "Wholesale Account Executive"}, {"value": "Wholesaler", "label": "Wholesaler"}, {"value": "<PERSON><PERSON>utritionist", "label": "<PERSON><PERSON>utritionist"}, {"value": "Wildlife Biologist", "label": "Wildlife Biologist"}, {"value": "Wildlife Rehabilitator", "label": "Wildlife Rehabilitator"}, {"value": "Wildlife Technician", "label": "Wildlife Technician"}, {"value": "Wind Technician", "label": "Wind Technician"}, {"value": "Wind Turbine Technician", "label": "Wind Turbine Technician"}, {"value": "Window Cleaner", "label": "Window Cleaner"}, {"value": "Windows Administrator", "label": "Windows Administrator"}, {"value": "Windows Server Administrator", "label": "Windows Server Administrator"}, {"value": "Windows System Administrator", "label": "Windows System Administrator"}, {"value": "Wine Consultant", "label": "Wine Consultant"}, {"value": "Wine Sales Representative", "label": "Wine Sales Representative"}, {"value": "Winemaker", "label": "Winemaker"}, {"value": "Wire Technician", "label": "Wire Technician"}, {"value": "Wireless Consultant", "label": "Wireless Consultant"}, {"value": "Wireless Engineer", "label": "Wireless Engineer"}, {"value": "Wireless Network Engineer", "label": "Wireless Network Engineer"}, {"value": "Woodworker", "label": "Woodworker"}, {"value": "Word Processor", "label": "Word Processor"}, {"value": "Wordpress Developer", "label": "Wordpress Developer"}, {"value": "Workforce Analyst", "label": "Workforce Analyst"}, {"value": "Workforce Management Analyst", "label": "Workforce Management Analyst"}, {"value": "Workforce Manager", "label": "Workforce Manager"}, {"value": "Worship Leader", "label": "Worship Leader"}, {"value": "Wound Care Nurse", "label": "Wound Care Nurse"}, {"value": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"value": "Wrestling Coach", "label": "Wrestling Coach"}, {"value": "Writer", "label": "Writer"}, {"value": "Writer Editor", "label": "Writer Editor"}, {"value": "Writing Assistant", "label": "Writing Assistant"}, {"value": "Writing Tutor", "label": "Writing Tutor"}, {"value": "X Ray Technician", "label": "X Ray Technician"}, {"value": "Yard Driver", "label": "Yard Driver"}, {"value": "Yard Hostler", "label": "Yard Hostler"}, {"value": "Yard Jockey", "label": "Yard Jockey"}, {"value": "Yard Laborer", "label": "Yard Laborer"}, {"value": "Yard Manager", "label": "Yard Manager"}, {"value": "Yard Spotter", "label": "Yard Spotter"}, {"value": "Yard Supervisor", "label": "Yard Supervisor"}, {"value": "Yard Worker", "label": "Yard Worker"}, {"value": "Yield Engineer", "label": "Yield Engineer"}, {"value": "Yoga Instructor", "label": "Yoga Instructor"}, {"value": "Youth Advocate", "label": "Youth Advocate"}, {"value": "Youth Care Worker", "label": "Youth Care Worker"}, {"value": "Youth Coordinator", "label": "Youth Coordinator"}, {"value": "Youth Counselor", "label": "Youth Counselor"}, {"value": "Youth Development Specialist", "label": "Youth Development Specialist"}, {"value": "Youth Director", "label": "Youth Director"}, {"value": "Youth Mentor", "label": "Youth Mentor"}, {"value": "Youth Minister", "label": "Youth Minister"}, {"value": "Youth Pastor", "label": "Youth Pastor"}, {"value": "Youth Program Director", "label": "Youth Program Director"}, {"value": "Youth Specialist", "label": "Youth Specialist"}, {"value": "Youth Worker", "label": "Youth Worker"}, {"value": "<PERSON>ambon<PERSON>", "label": "<PERSON>ambon<PERSON>"}, {"value": "Zookeeper", "label": "Zookeeper"}, {"value": "Zoologist", "label": "Zoologist"}, {"value": "<PERSON><PERSON> Instructor", "label": "<PERSON><PERSON> Instructor"}, {"value": "dot2-net-developer", "label": ".Net Developer"}, {"value": "3D Animator", "label": "3D Animator"}, {"value": "3D Artist", "label": "3D Artist"}, {"value": "3D Designer", "label": "3D Designer"}, {"value": "3D Modeler", "label": "3D Modeler"}, {"value": "911 Dispatcher", "label": "911 Dispatcher"}, {"value": "911 Operator", "label": "911 Operator"}]