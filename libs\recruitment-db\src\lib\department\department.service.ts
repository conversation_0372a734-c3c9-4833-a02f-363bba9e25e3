import { Injectable } from '@nestjs/common';
import * as data from './data.json';
import { InjectModel } from '@nestjs/sequelize';
import { Department } from './department.model';
import { Op } from "sequelize";

@Injectable()
export class DepartmentService {
  constructor(
    @InjectModel(Department) private departmentRepository: typeof Department,
  ) {}

  async seed() {
    await this.departmentRepository.bulkCreate(data, {
      validate: true,
    }).then(async () => this.departmentRepository.sequelize.query(`ALTER SEQUENCE "${this.departmentRepository.tableName}_id_seq" RESTART WITH ${await this.departmentRepository.count() + 1}`));
    return true;
  }

  async findByLabel(find?: string) {
    let where = {}
    if (find && find.length > 0) {
      where = {
        label: {
          [Op.iLike]: `%${find}%`,
        },
      }
    }
    return await this.departmentRepository.findAll({
      where,
      limit: 40,
    });
  }

  async getByPk(id: number) {
    return await this.departmentRepository.findByPk(id)
  }

  async getByIdArrays(ids: [string]) {
    return await this.departmentRepository.findAll(
      {
        attributes: ["id", "value", "label"],
        where: {
          id: {
            [Op.in]: ids,
          }
        }
      }
    )
  }
}
