import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>umn, <PERSON>T<PERSON>, Foreign<PERSON>ey, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsJSON, IsInt } from "class-validator";
import { DomainAssessment } from "./domain-assessment.model";

export enum QuestionsType {
  single = "single",
  multiple = "multiple",
  text = "text",
}

@Table({
  tableName: "domain-questions",
})
export class DomainQuestions extends Model<DomainQuestions> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "Test",
    description: "Name of question",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @ApiProperty({
    enum: QuestionsType,
    oneOf: [{ type: QuestionsType.multiple }, { type: QuestionsType.single }, { type: QuestionsType.text }],
  })
  @IsEnum(QuestionsType)
  @Column({
    type: DataType.ENUM("single", "multiple", "text"),
    allowNull: false,
  })
  type: QuestionsType;

  @IsJSON()
  @ApiProperty({
    example: "",
    description: "Stringify JSON data of all answers with boolean isRight for one or multiple answers",
  })
  @Column({ type: DataType.TEXT, allowNull: false })
  answers: string;

  @IsInt()
  @ApiProperty({
    example: "10",
    description: "score",
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
  score: number;

  @ForeignKey(() => DomainAssessment)
  domainAssessmentId: number;

  @BelongsTo(() => DomainAssessment)
  domainAssessments: DomainAssessment;
}
