import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class findAndUpdate {
  @ApiProperty({ example: 'take-home', description: 'type of assessment' })
    assessmentType: AssessmentType;

  @ApiProperty({ example: "1", description: "User ID" })
    candidateId: number;
  
  @ApiProperty({ example: "1", description: "Job ID" })
    jobId: number;
    
  @ApiProperty({ example: "Not Started", description: "testStatus" })
  @IsOptional()
  testStatus: TestStatus;
  
  @ApiProperty({ example: "1", description: "startTime" })
  @IsOptional()
    startTime?: Date;

}

export type TestStatus = 'Not Started' | 'In Progress' | 'Completed';
  
export type AssessmentType = "Domain Assessment" | "Coding Assessment";
