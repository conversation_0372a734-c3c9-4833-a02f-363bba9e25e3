import { createCipheriv, randomBytes, scrypt, createDecipheriv } from 'crypto';
import { promisify } from 'util';
import { HttpException, HttpStatus, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Integrations } from './integrations.model';
import { GmailAuthService } from './gmail/auth.service';
import { GcalAuthService } from './gcal/auth.service';
import { OutlookAuthService } from './outlook/auth.service';
import { McalAuthService } from './mcal/auth.service';
import { IntegrationAuthService } from '../integrationAuth/integrationAuth.service';

@Injectable()
export class IntegrationsService {
  constructor(
    @InjectModel(Integrations) private integrationsRepository: typeof Integrations,
    private gmail: GmailAuthService,
    private gcal: GcalAuthService,
    private outlook: OutlookAuthService,
    private mcal: McalAuthService,
    private integrationAuthService: IntegrationAuthService,
  ) {}

  async connectionStatus(providers: any,  user: any) {
    const result = [];
    const userId=user['https://urecruits.com/userId'];

    for (let i = 0; i < providers.length; i++) {
      const connectionStatus = {
        key: providers[i],
        status: '',
        connectUrl: '',
        disconnectEndpoint: '',
      };

      switch (providers[i]) {
      case 'gmail':
      case 'gcal':
      case 'outlook':
      case 'mcal': {
        const row = await this.integrationsRepository.findOne({
          where: { provider: providers[i], userId },
        });
        if (row && row.data) {
          connectionStatus.status = await this[providers[i]].checkConnectionStatus(row.data);
          connectionStatus.disconnectEndpoint = `${process.env.MICROSERVICES_PRA_URL}/api/integrations/auth-revoke?provider=${providers[i]}`;
        } else {
          connectionStatus.status = 'not-connected';
          connectionStatus.connectUrl = await this[providers[i]].getConnectUrl(await this.encrypt([providers[i],userId]));
        }
        break;
      }
      default:
        connectionStatus.status = 'provider-not-found';
      }

      result.push(connectionStatus);
    }

    return result;
  }

  async oauth2Callback(query, res) {
    const args = {
      provider: '',
      userId: 0,
      data: '',
      error: '',
    };

    if (query.error) {
      args.error = query.error;
      Logger.error(query);
    } else if (query.state) {
      const state = await this.decrypt(query.state);
      if (['gmail', 'gcal', 'outlook', 'mcal'].indexOf(state[0]) > -1) {
        args.provider = state[0];
        args.userId = Number(state[1]);
        const { data, error } = await this[args.provider].oauth2Callback(query.code,args.userId);
        args.data = data;
        args.error = error;

        
      } else {
        args.error = 'provider_not_found';
      }
    } else {
      args.error = 'empty_state';
    }

    return await this.authCallback(args.provider, args.userId, args.data, args.error, res);
  }

  async authCallback(provider, userId, data, error, res) {
    const group1 = ['gmail', 'outlook'];
    const group2 = ['gcal', 'mcal'];
    const args = {
      tab: 'integrations',
      action: 'connect',
      provider,
      success: String(!error),
      error,
    };

    if (!error) {
      try {
        let otherProvider;
        if(provider ==="gmail" || provider==="outlook"){
           otherProvider = provider === 'gmail' ? 'outlook' : 'gmail';
        }else{
          otherProvider = provider === 'gcal' ? 'mcal' : 'gcal';
        }
        
        const otherRow = await this.integrationsRepository.findOne({
          where: { provider: otherProvider, userId },
        });

        if (otherRow && otherRow.data) {
          await this.integrationsRepository.destroy({where:{
            provider: otherProvider,
            userId,
          }});
          await this.integrationAuthService.delete(userId,otherProvider);
        }
        const row = await this.integrationsRepository.findOne({
          where: { provider, userId },
        });
        if (row) {
          row.data = data;
          await row.save();
        } else {
          await this.integrationsRepository.create({
            provider,
            userId,
            data,
          });
        }
        delete args.error;
      } catch (e) {
        args.error = e.name || 'callback_error';
      }
    }
    return res.redirect(`${process.env.WEB_APPS_URL}/profile-setting?${new URLSearchParams(args).toString()}`);
  }

  async encrypt(arr) {
    try {
      const iv = randomBytes(16);
      const key : any  = await promisify(scrypt)(process.env.CRYPTO_PASS, 'salt', 32);
      const cipher = createCipheriv('aes-256-cbc', Buffer.from(key), iv);
      const data = Buffer.from(arr.join(','), 'utf-8');
      const encrypted = Buffer.concat([cipher.update(data), cipher.final()]);
  
      return JSON.stringify({
        i: iv.toString('hex'),
        t: encrypted.toString('hex'),
      });
    } catch (error) {
      console.error("Encryption error:", error);
      throw error;
    }
  }
  
  async  decrypt(json) {
    try {
      const hash = JSON.parse(json);
      const key : any= await promisify(scrypt)(process.env.CRYPTO_PASS, 'salt', 32);
      const decipher = createDecipheriv('aes-256-cbc', Buffer.from(key), Buffer.from(hash.i, 'hex'));
      const decrypted = Buffer.concat([decipher.update(Buffer.from(hash.t, 'hex')), decipher.final()]);
  
      return decrypted.toString('utf-8').split(',');
    } catch (error) {
      console.error("Decryption error:", error);
      throw error;
    }
  }

  async authRevoke(query, userId) {
    const { provider } = query;
    if (!provider) {
      throw new HttpException('Required argument \'provider\' is missing', HttpStatus.BAD_REQUEST);
    }

    const row = await this.integrationsRepository.findOne({
      where: { provider, userId },
    });
    if (!row) {
      throw new HttpException('Connection not found', HttpStatus.NOT_FOUND);
    }

    try {
      switch (provider) {
      case 'gmail':
      case 'gcal':
      case 'outlook':
      case 'mcal': {
      await this[provider].authRevoke(row.data,userId);
      }
      }
    } finally {
      try {
      await this.integrationsRepository.destroy({
        where: { id: row.id ,provider},
      });
      await this.integrationAuthService.delete(userId,provider);
    }catch (err) {
      Logger.log(err);
    }
      
    }

    return true;
  }

  async getProviderClient(provider: string, userId: number) {
    const row = await this.integrationsRepository.findOne({
      where: { provider, userId },
    });

    if (!row || !row.data) return false;

    const providerClient = await this[provider].getClient(row.data);

    if (providerClient.data) {
      row.data = providerClient.data;
      await row.save();
    }

    return providerClient.client ? providerClient.client : false;
  }

  async getProvider(id){
    try{
      const provider = await this.integrationsRepository.findAll({
        where: {  userId: id },
      });
      let mailbox=""
      let calendar=""
      provider.map((data)=>{
      if(data.provider==="gmail" ||data.provider==="outlook"){
        mailbox=data.provider==="gmail"?"gmail":data.provider==="outlook"?"outlook":""
      }else{
        calendar=data.provider==="gcal"?"gcal":data.provider==="mcal"?"mcal":""
      }})

      return {mailbox,calendar}
    }catch(e){
    throw new NotFoundException(e)
    }
  }
}
