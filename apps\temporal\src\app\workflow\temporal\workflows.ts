import {
  TerminatedFailure,
  TimerInput,
  condition,
  defineSignal,
  proxyActivities,
  setHandler,
  sleep,
} from "@temporalio/workflow";
import type { ActivitiesService } from "../activities/activities.service";
import { roundType, roundStatus } from "../enum/round.enum";
import * as wf from "@temporalio/workflow";
// Can't use `@app/shared` here because for some reason Temporal's Webpack
// build complains that "node_modules/@app/shared" doesn't exist in Jest.

const {
  workflowStart,
  appointmentBooked,
  interviewStarted,
  interviewCompleted,
  roundCompleted,
  roundRejected,
  roundInitiated,
  proposeTimeSlot,
  changeRoundStatus,
  informTimeSlot,
  scoreUpdateReminder,
  interviewReminder,
  assessmentInitiated,
  assessmentSubmitted,
  ReviewAssessmentReminder,
  domainAssessmentCompleted,
  domainAssessmentRejected,
  assessmentDeadline,
  changeOfferLetterStatus,
} = proxyActivities<ActivitiesService>({
  startToCloseTimeout: "1 minute",
  retry: {
    maximumAttempts: 3,
  },
});

export const timeSlotProposingSignal = defineSignal<[any]>(
  "timeSlotProposingSignal"
);

export const getProgress = wf.defineQuery<number>("getProgress");

export const scoreUpdated = defineSignal<[any]>("scoreUpdated");

export const appointmentBookingSignal = defineSignal<[any]>(
  "appointmentBookingSignal"
);

export const interviewStartedSignal = defineSignal<[string]>(
  "interviewStartedSignal"
);

export const interviewCompletedSignal = defineSignal<[string]>(
  "interviewCompletedSignal"
);

export const roundCompletedSignal = defineSignal<[any]>("roundCompletedSignal");

export const roundRejectedSignal = defineSignal<[string]>(
  "roundRejectedSignal"
);

export const roundInitiatedSignal = defineSignal<[string]>(
  "roundInitiatedSignal"
);

export const roundSubmittedSignal = defineSignal<[string]>(
  "roundSubmittedSignal"
);

export const domainAssessmentInitiatedSignal = defineSignal<[string]>(
  "domainAssessmentInitiatedSignal"
);

export const assessmentSubmittedSignal = defineSignal<[any]>(
  "assessmentSubmittedSignal"
);

export const domainAssessmentCompletedSignal = defineSignal<[string]>(
  "domainAssessmentCompletedSignal"
);

export const domainAssessmentRejectedSignal = defineSignal<[string]>(
  "domainAssessmentRejectedSignal"
);

export const assessmentStartedSignal = defineSignal<[any]>(
  "assessmentStartedSignal"
);

export const domainAssessmentDeadlineSignal = defineSignal<[any]>(
  "domainAssessmentDeadlineSignal"
);

export const assessmentReviewedSignal = defineSignal<[any]>(
  "assessmentReviewedSignal"
);

export const candidateRejectedSignal = defineSignal<[any]>(
  "candidateRejectedSignal"
);

export const jobClosedSignal = defineSignal<[any]>(
  "jobClosedSignal"
);

export async function recruitmentWorkflow(data): Promise<any> {
  let appointmentData;
  // let takeHomeSubmitted=false
  let i = 0;

  let isTimeSlotPropsosed = {
    state: false,
    action: "pass",
  };

  let isScoreUpdated = {
    state: false,
    action: "pass",
  };

  let isBookAppointment = {
    state: false,
    action: "pass",
  };
  let isInterviewStarted = {
    state: false,
    action: "pass",
  };
  let isInterviewCompleted = {
    state: false,
    action: "pass",
  };
  let isRoundCompleted = {
    state: false,
    action: null,
  };
  let isRoundRejected = {
    state: false,
    action: "pass",
  };
  let isTakeHomeTaskInitiated = {
    state: false,
    action: "pass",
  };
  let isDomainAssessmentInitiated = {
    state: false,
    action: "pass",
  };
  let isAssessmentSubmitted = {
    state: false,
    action: null,
  };
  let isTechnicalAssessmentSubmitted = {
    state: false,
    action: "pass",
  };
  let isDomainAssessmentCompleted = {
    state: false,
    action: "pass",
  };
  let isDomainAssessmentRejected = {
    state: false,
    action: "pass",
  };
  let isAssessmentStarted = {
    state: false,
    action: null,
  };
  let isDomainAssessmentDeadline = {
    state: false,
    action: "pass",
  };

  let isAssessmentReviewed = {
    state: false,
    action: null,
  };

  let isCandidateRejected = {
    state: false,
    action: null
  }

  let isJobClosed = {
    state: false,
    action: null
  }

  setHandler(timeSlotProposingSignal, (action) => {
    appointmentData = action?.data;
    isTimeSlotPropsosed = {
      state: true,
      action: action,
    };
  });

  setHandler(scoreUpdated, (action) => {
    appointmentData = action?.data;
    isScoreUpdated = {
      state: true,
      action: action,
    };
  });

  setHandler(appointmentBookingSignal, (action) => {
    appointmentData = action?.data;
    isBookAppointment = {
      state: true,
      action: action,
    };
  });
  setHandler(interviewStartedSignal, (action) => {
    isInterviewStarted = {
      state: true,
      action: action,
    };
  });
  setHandler(interviewCompletedSignal, (action) => {
    isInterviewCompleted = {
      state: true,
      action: action,
    };
  });
  setHandler(roundRejectedSignal, (action) => {
    isRoundRejected = {
      state: true,
      action: "pass",
    };
  });
  setHandler(roundCompletedSignal, (action) => {
    isRoundCompleted = {
      state: true,
      action: action,
    };
  });
  setHandler(roundInitiatedSignal, (action) => {
    isTakeHomeTaskInitiated = {
      state: true,
      action: action,
    };
  });
  setHandler(roundSubmittedSignal, (action) => {
    isTakeHomeTaskInitiated = {
      state: true,
      action: action,
    };
  });
  setHandler(domainAssessmentInitiatedSignal, (action) => {
    isDomainAssessmentInitiated = {
      state: true,
      action: action,
    };
  });

  setHandler(assessmentSubmittedSignal, (action) => {
    isAssessmentSubmitted = {
      state: true,
      action: action.data,
    };
  });
  setHandler(domainAssessmentCompletedSignal, (action) => {
    isDomainAssessmentCompleted = {
      state: true,
      action: action,
    };
  });

  setHandler(assessmentStartedSignal, (action) => {
    isAssessmentStarted = {
      state: true,
      action: action.data,
    };
  });

  setHandler(assessmentReviewedSignal, (action) => {
    isAssessmentReviewed = {
      state: true,
      action: action.data,
    };
  });

  setHandler(candidateRejectedSignal, (action) => {
    isCandidateRejected = {
      state: true,
      action: action.data
    }
  });

  setHandler(jobClosedSignal, (action) => {
    isJobClosed = {
      state: true,
      action: action.data
    }
  })

  const processAssessment = async (data, currentRound) => {
    await changeRoundStatus(data, currentRound);
    await assessmentInitiated(data, currentRound);
    let status = "initiated";

    const deadline = Number(currentRound === roundType.FUNCTIONAL_DOMAIN_ASSESSMENT ? (data?.workflowData?.domainDeadline / 24) : data?.workflowData?.takeHomeTaskDeadline) * 24 * 60 + 5;

    let currentProgress;
    isAssessmentStarted.state = false; // Reset the state

    if (
      await condition(
        () =>
          (status === "initiated" && isAssessmentStarted.state) ||
          isCandidateRejected.state || isJobClosed.state,
        `${deadline} Minutes`
      )
    ) {
      if (isCandidateRejected.state && !isAssessmentStarted.state) {
        return "Candidate Is Rejected !!";
      }
      if (isJobClosed.state) {
        return "Job is closed!!"
      }

      isAssessmentStarted.state = false;
      const assessmentDuration = isAssessmentStarted.action.timeDuration * 60;
      currentProgress = assessmentDuration;

      if (
        await condition(
          () => isAssessmentSubmitted.state,
          `${+assessmentDuration + 1} Minutes`
        )
      ) {
        wf.setHandler(getProgress, () => currentProgress);
        while ( currentProgress >= 0 && !isAssessmentSubmitted.state) {
          await wf.sleep("1m");
          currentProgress -= 1;
          wf.log.info(`${currentProgress} Minutes`);
        }
      }

      const result = await assessmentSubmitted(
        data,
        currentRound,
        isAssessmentSubmitted.action?.domainResultId
      );
      status = "submitted";
      wf.log.info("Assessment submission is triggered...");


      while (status === "submitted") {
        if (
          await condition(
            () => isAssessmentReviewed.state || isCandidateRejected.state || isJobClosed.state,
            "24 Hours"
          )
        ) {
          isAssessmentReviewed.state = false;
          await changeRoundStatus(data, currentRound, roundStatus.COMPLETED);
          status = "completed"
          break;
        } else {
          await ReviewAssessmentReminder(
            data,
            currentRound,
            isAssessmentSubmitted.action?.domainResultId
          );
        }
      }
    } else {
      await assessmentDeadline(data, currentRound);
      wf.CancellationScope.current().cancel();
    }
  };

  const processMeeting = async (data, roundType) => {
    await changeRoundStatus(data, roundType);
    let roundStatus;
    await proposeTimeSlot(data, roundType);
    while (true) {
      if (
        await condition(
          () => isTimeSlotPropsosed.state || roundStatus || isCandidateRejected.state || isJobClosed.state,
          "24 Hours"
        )
      ) {

        if (isCandidateRejected.state || isJobClosed.state) break;
        roundStatus = roundStatus ?? "scheduled";
        isTimeSlotPropsosed.state = false;

        if (roundStatus === "scheduled") {
          await informTimeSlot(data, roundType, appointmentData);
          // step-2 : handle case of rescheduling
          const currentTime = Date.now() / 1000; // Current time in seconds (UTC)

          // Convert ISO UTC start date to timestamp
          const scheduledTime = new Date(appointmentData.utcStartDate).getTime() / 1000;
          const remainingTime = scheduledTime - currentTime;

          // Calculate wait time for score update reminder
          let waitTimeForScoreUpdateReminder = 3600; // Default 1 hour if utcEndDate is missing
          if (appointmentData.utcEndDate) {
            const endTime = new Date(appointmentData.utcEndDate).getTime() / 1000;
            const scoreUpdateTime = endTime - scheduledTime;
            waitTimeForScoreUpdateReminder = scoreUpdateTime || 3600;
          }

          if (await condition(() => isTimeSlotPropsosed.state || isCandidateRejected.state || isInterviewCompleted.state, `${remainingTime} Seconds`)) {
            if (isCandidateRejected.state) break;
            if(isInterviewCompleted.state){
              roundStatus = "interviewCompleted";
              isInterviewCompleted.state = false;
              await scoreUpdateReminder(data, roundType, appointmentData);
            }
            continue;
          }

          if (await condition(() => isInterviewCompleted.state || isCandidateRejected.state, `${waitTimeForScoreUpdateReminder} Seconds`)) {
            if (isCandidateRejected.state || isJobClosed.state) break;
            isInterviewCompleted.state = false;
            if(roundStatus === "interviewCompleted"){
              continue
            }else{
              roundStatus = "interviewCompleted";
              await scoreUpdateReminder(data, roundType, appointmentData);
            }
          }
          if (roundStatus !== "interviewCompleted") {
            roundStatus = "interviewConducted";
            await scoreUpdateReminder(data, roundType, appointmentData);
          }
        }

        if (await condition(() => isRoundCompleted.state || isCandidateRejected.state || isJobClosed.state || isTimeSlotPropsosed.state, "24 Hours")) {
          if (isTimeSlotPropsosed.state) {
            roundStatus = "scheduled";
            continue;
          }

          if (isCandidateRejected.state || isJobClosed.state) break;

          isRoundCompleted.state && (roundStatus = "completed");
          isRoundCompleted.state = false;
          await roundCompleted(data, roundType);
          break;
        } else {
          roundStatus = "scoreUpdateIsPending";
          await scoreUpdateReminder(data, roundType, appointmentData);
        }
      } else {
        await proposeTimeSlot(data, roundType);
      }
    }
  };

  const processRound = async (data, roundName) => {
    isRoundCompleted.state = false;
    isCandidateRejected.state = false;
    isJobClosed.state = false;
    await changeRoundStatus(data, roundName);
    await roundInitiated(data, roundName);

    // Set wait time (264 hours for normal rounds, wait indefinitely for offer round)
    const waitTime = roundName !== roundType.CONDITIONAL_OFFFER_LETTER_GENERATION ? 264 : null;

    const waitCondition = async () => {
      return await condition(
        () => isRoundCompleted.state || isCandidateRejected.state || isJobClosed.state,
        waitTime ? `${waitTime} Hours` : undefined
      );
    };

    if (await waitCondition()) {
      if (isCandidateRejected.state || isJobClosed.state) return;

      if (isRoundCompleted.state) {
        await changeRoundStatus(data, roundName, roundStatus.COMPLETED);
        await roundCompleted(data, roundName);
        isRoundCompleted.state = false
      }
    } else {
      if (roundName !== roundType.CONDITIONAL_OFFFER_LETTER_GENERATION) {
        await wf.CancellationScope.current().cancel();
      }
    }
  };



  const workflowStartResult = await workflowStart(data);

  while (i < data.workflowData.workflow.length) {
    if (isCandidateRejected.state) {
      isCandidateRejected.state = false;
      await roundRejected(data, data.workflowData.workflow[i].name);
      await wf.CancellationScope.current().cancel();
    }

    if (isJobClosed.state) {
      isJobClosed.state = false;
      await changeRoundStatus(data, data.workflowData.workflow[i].name, roundStatus.TERMINATED)
      await wf.CancellationScope.current().cancel();
    }

    switch (data.workflowData.workflow[i].name) {
      case roundType.FUNCTIONAL_DOMAIN_ASSESSMENT:
      case roundType.TAKE_HOME_CODING_ASSESSMENT:
        await processAssessment(data, data.workflowData.workflow[i].name);
        break;

      case roundType.TECHNICAL_VIDEO_AUDIO_INTERVIEW:
      case roundType.HR_AUDIO_VIDEO_INTERVIEW:
      case roundType.SENIOR_HR_AUDIO_VIDEO_INTERVIEW:
      case roundType.LIVE_TASK_CODING_ASSESSMENT:
        await processMeeting(data, data.workflowData.workflow[i].name);
        break;
      case roundType.CONDITIONAL_OFFFER_LETTER_GENERATION:
      case roundType.BACKGROUND_SCREENING:
      case roundType.DRUG_SCREENING:
        await processRound(data, data.workflowData.workflow[i].name);
        break;
      default:
        console.log("Unknown stage:", data.workflowData.workflow[i].name);
        break;
    }

    i++;
  }

  return { message: "Workflow Completed" };
}
