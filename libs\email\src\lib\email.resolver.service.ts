import * as fs from 'fs';
import * as path from 'path';
import { Injectable } from '@nestjs/common';
import { jobDetail } from './templates/jobDetail';

@Injectable()
export class EmailResolverService {
  upload(email: string) {
    const pathToEmail = path.resolve(__dirname, `assets/templates/${email}`);

    return fs.readFileSync(pathToEmail, 'utf-8');
  }

  recruitment(template: string) {
    const pathToEmail = path.resolve(__dirname, `assets/templates/${template}`);

    return fs.readFileSync(pathToEmail, 'utf-8');
  }

  recruitmentActivities(template: string) {
    return jobDetail;
  }
}
