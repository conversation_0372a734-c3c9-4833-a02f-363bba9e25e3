import {
    BelongsTo,
    Column,
    DataType,
    ForeignKey,
    Model,
    Table,
  } from "sequelize-typescript";
  import { ApiProperty } from "@nestjs/swagger";
  import { Company } from "../companies/companies.model";
  
  @Table({ tableName: "universal-integration", createdAt: true, updatedAt: true })
  export class UniversalIntegration extends Model<UniversalIntegration> {
    @ApiProperty({ example: "1", description: "Primary key" })
    @Column({
      type: DataType.INTEGER,
      unique: true,
      autoIncrement: true,
      primaryKey: true,
    })
    id: number;
  
    @ApiProperty({ example: "1", description: "Company  ID" })
    @ForeignKey(() => Company)
    @Column({ type: DataType.INTEGER, allowNull: false })
    companyId: number;
  
    @ApiProperty({ example: "123456", description: "Account Number" })
    @Column({ type: DataType.STRING, allowNull: false })
    accountNumber: string;
  
    @ApiProperty({ example: "talexndar", description: "User Name" })
    @Column({ type: DataType.STRING, allowNull: false })
    userName: string;

    @ApiProperty({ example: "xxxxxxxx", description: "Account Password" })
    @Column({ type: DataType.STRING, allowNull: false, defaultValue:process.env.UNIVERSAL_PASSWORD })
    password: string;
  
    @BelongsTo(() => Company)
    company: Company;
  }
  