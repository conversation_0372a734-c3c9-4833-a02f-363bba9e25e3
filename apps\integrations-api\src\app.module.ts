import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { HealthcheckModule } from './healthcheck/healthcheck.module';
import { GoogleModule } from './google/google.module';
import { FacebookModule } from './facebook/facebook.module';
import { EmailModule, EmailService } from '@microservices/email';
import { HttpModule } from '@nestjs/axios';

@Module({
  imports: [
    ClientsModule.register([
      {
        name: 'INTEGRATIONS_SERVICE',
        transport: Transport.REDIS,
        options: {
          url: process.env.REDIS_URL || 'redis://localhost:6379',
        },
      },
    ]),
    HealthcheckModule,
    GoogleModule,
    FacebookModule,
    EmailModule,
    HttpModule
  ],
  providers: [
    EmailService,
  ],
})
export class AppModule {}
