import { BelongsTo, Column, DataType, Foreign<PERSON>ey, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Questions } from '../questions/questions.model';

@Table({ tableName: 'testcase' })
export class TestCase extends Model<TestCase> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'Hello', description: 'Imput for test case' })
  @Column({ type: DataType.STRING, allowNull: false })
  input: string;

  @ApiProperty({ example: 'Hello World!', description: 'Expected Output' })
  @Column({ type: DataType.STRING, allowNull: false })
  output: string;

  @ApiProperty({ example: 'true', description: 'Boolean if test case is checked' })
  @Column({ type: DataType.BOOLEAN, allowNull: true })
  checked?: boolean;

  @ApiProperty({ example: '1', description: 'id of related question' })
  @ForeignKey(() => Questions)
  @Column({ type: DataType.INTEGER, allowNull: false })
  questionId: number;

  @BelongsTo(() => Questions)
  questions: Questions;
}
