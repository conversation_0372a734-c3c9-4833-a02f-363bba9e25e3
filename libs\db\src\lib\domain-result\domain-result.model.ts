import { <PERSON><PERSON>s<PERSON><PERSON>, <PERSON>umn, <PERSON>Type, ForeignKey, HasMany, HasOne, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsInt, IsEnum, IsString, IsOptional } from "class-validator";
import { DomainAssessment } from "../domain-questions/domain-assessment.model";
import { DomainAnswers } from "./domain-answer.model";

@Table({
  tableName: "domain-results",
})
export class DomainResults extends Model<DomainResults> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @IsString()
  @ApiProperty({
    example: "10",
    description: "duration",
  })
  @Column({ type: DataType.STRING })
  duration: string;

  @IsOptional()
  @ApiProperty({
    example: "75",
    description: "total score",
  })
  @Column({ type: DataType.INTEGER })
  totalScore?: number;

  @ApiProperty({
    example: "1",
    description: "Job id",
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  jobId: number;

  @ApiProperty({
    example: "1",
    description: "Candidate id",
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @ApiProperty({
    example: "1",
    description: "Domain Assessment Id",
  })
  @ForeignKey(() => DomainAssessment)
  @Column({ type: DataType.INTEGER, allowNull: true })
  assessmentId: number;

  @BelongsTo(()=>DomainAssessment)
  domainAssessment:DomainAssessment
  

  @HasMany(() => DomainAnswers, { onDelete: "CASCADE" })
  candidateAnswers: DomainAnswers[];
}
