import { <PERSON>ongsTo, Column, DataType, ForeignKey, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Questions } from '../questions/questions.model';
import { LiveCoding } from '../live-coding/live-coding.model';
import { LiveCodingDrafts } from '../live-coding/live-drafts/live-drafts.model';
import { Package } from '../packages/packages.model';

@Table({
  tableName: 'assessment-database',
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class AssessmentDatabase extends Model<AssessmentDatabase> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    description: 'Id of tenant',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    tenantId?: string;

  @ApiProperty({
    example: 'Angular developer',
    description: 'Name of the database that would be used to manage db schema',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    title: string;

  @ApiProperty({
    description: 'Description of the database that would be used to manage db schema',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    description: string;

    @ForeignKey(() => Package)
    @Column({ type: DataType.INTEGER, allowNull: true })
    packageId: number;
  
    @BelongsTo(() => Package)
    packages: Package;

  @ApiProperty({
    example: 'CREATE TABLE users(first_name varchar(50), last_name varchar(50), email varchar(100) )',
    description: 'SQL query to create Database',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    script: string;

  @ForeignKey(() => Questions)
  @Column({ type: DataType.INTEGER, allowNull: true })
    questionId?: number;

  @ForeignKey(() => LiveCoding)
  @Column({ type: DataType.INTEGER, allowNull: true })
    liveCodingId?: number;

  @ForeignKey(() => LiveCodingDrafts)
  @Column({ type: DataType.INTEGER, allowNull: true })
    liveCodingDraftId?: number;

  @BelongsTo(() => Questions)
    question?: Questions;

  @BelongsTo(() => LiveCoding)
    liveCodign?: LiveCoding;

  @BelongsTo(() => LiveCodingDrafts)
    liveCodignDraft?: LiveCodingDrafts;
}
