import {
    BelongsTo,
    Column,
    DataType,
    ForeignKey,
    Model,
    Table,
} from 'sequelize-typescript';
import { User } from '../users/users.model'
import { Company } from '../companies/companies.model';

@Table({
    tableName: 'user-assignments',
    createdAt: false,
    updatedAt: false,
})
export class UserAssignments extends Model<UserAssignments> {
    @Column({
        type: DataType.INTEGER,
        unique: true,
        autoIncrement: true,
        primaryKey: true,
    })
    id: number;

    @ForeignKey(() => Company)
    @Column({ type: DataType.INTEGER })
    companyId: number;

    @Column({ type: DataType.BOOLEAN })
    hasActiveAssignment: boolean;

    @Column({ type: DataType.INTEGER })
    assignmentId: number;

    @Column({ type: DataType.STRING })
    assignmentName: string;

    @ForeignKey(() => User)
    @Column({ type: DataType.INTEGER })
    userId: number;

    @BelongsTo(() => User)
    user: User;

    @BelongsTo(() => Company)
    company: Company;
}
