import { Injectable, InternalServerErrorException, NotFoundException } from "@nestjs/common";
import { Subscribe } from "../subscribe/subscribe.model";
import { OfferModel } from "../offers/offer.model";
import { Op } from "sequelize";
import { Company } from "../companies/companies.model";
import { InjectModel } from "@nestjs/sequelize";
import { HellosignService } from "../integrations/hellosign/hellosign.service";
import { FilterCandidateOffersDTO } from "./dto/candidate-offers.dto";
import { OfferService } from "../offers/offer.service";
import { EditOfferDto } from "../offers/dto/offer.dto";

@Injectable()
export class CandidateOffersService {
  constructor(
    @InjectModel(OfferModel) private offerRepository: typeof OfferModel,
    private readonly hellosignService: HellosignService,
    private readonly offerService: OfferService,
  ) { }

  async getCandidateOffers(userId: number, query: FilterCandidateOffersDTO) {
    const where: any = {
      hellosignSignatureRequestId: {
        [Op.not]: null
      }
    };
    if (query.searchValue) {
      where.jobTitle = {
        [Op.iLike]: `%${query.searchValue}%`
      }
    }

    const data = await Subscribe.findAll({
      where: {
        applyJob: true,
        userId,
        offerId: {
          [Op.not]: null
        },
      },
      include: [
        {
          model: OfferModel,
          attributes: ["id", "jobId", "jobTitle", "candidateStatus", "companyId"],
          where,
          include: [
            {
              model: Company,
              attributes: ["id", "name", "avatar"]
            }
          ]
        }
      ],
      limit: query.limit,
      offset: query.offset
    })

    return { count: data.length, rows: data }
  }

  async getOfferById(userId: number, offerId: number) {
    return await this.offerRepository.findOne({
      where: {
        id: offerId, userId
      },
    });
  }

  async updateOfferById(offerId: number, dto: EditOfferDto, companyId: number) {
    return this.offerService.updateById(offerId, dto, companyId)
  }


  async getSignatureDocumentPreview(offerId: number, userId: number) {
    try {
      const offer = await this.offerRepository.findOne({
        where: {
          id: offerId, userId
        },
      });
      if (offer.dataValues?.hellosignSignatureRequestId) {
        const response =
          await this.hellosignService.getSignatureDocumentPreview(
            offer.dataValues?.hellosignSignatureRequestId
          );
        return response.body;
      } else {
        throw new NotFoundException("Offer Letter Is Not Generated");
      }
    } catch (er) {
      throw new InternalServerErrorException(er)
    }
  }
}