import { ApiProperty } from '@nestjs/swagger';

export class UpgradeSubscriptionDto {
  @ApiProperty({
    example: 'price_1xxx',
    description:
      'Subscription Plan ID.',
  })
  readonly planId: number;

  @ApiProperty({
    example: 'y',
    description:
      "Subscription duration.",
  })
  readonly duration: string;

  @ApiProperty({
    example: 'price_1xxx',
    description:
      'Subscription packageId ID.',
  })
  readonly packageId: number;
}
