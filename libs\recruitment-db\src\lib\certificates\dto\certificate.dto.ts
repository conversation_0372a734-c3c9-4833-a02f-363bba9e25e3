import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class CertificateDto {
  @ApiPropertyOptional({ example: "Adobe Certified Expert-Adobe Commerce", description: "Certificate Name" })
  @IsOptional()
  readonly certificateName?: string;

  @ApiPropertyOptional({ example: "Adobe", description: "Institute name" })
  @IsOptional()
  readonly instituteName?: string;

  @ApiPropertyOptional({ example: "2021-12-28T22:00:00.000Z", description: "Certificate validity start" })
  @IsOptional()
  readonly validityStart?: string;

  @ApiPropertyOptional({ example: "2021-12-28T22:00:00.000Z", description: "Certificate validity end" })
  @IsOptional()
  readonly validityEnd?: string;

  @ApiPropertyOptional({ example: "2021-12-28T22:00:00.000Z", description: "Expire date" })
  @IsOptional()
  readonly expireDate?: string;

  @ApiPropertyOptional({ example: "Notes", description: "Notes" })
  @IsOptional()
  readonly notes?: string;

  @ApiPropertyOptional({ example: "file.pdf", description: "File name" })
  @IsOptional()
  readonly fileName?: string;

  @ApiPropertyOptional({ example: "213122 - file.pdf", description: "File key" })
  @IsOptional()
  readonly fileKey?: string;
}
