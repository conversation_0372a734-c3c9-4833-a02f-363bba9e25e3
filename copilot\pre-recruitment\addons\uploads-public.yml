Parameters:
  App:
    Type: String
    Description: Your application's name.
  Env:
    Type: String
    Description: The environment name your service, job, or workflow is being deployed to.
  Name:
    Type: String
    Description: The name of the service, job, or workflow being deployed.
Resources:
  uploadspublicBucket:
    Metadata:
      'aws:copilot:description': 'An Amazon S3 bucket to store and retrieve objects for uploads-public'
    Type: AWS::S3::Bucket
    Properties:
      AccessControl: PublicRead
      BucketName: !Sub '${App}-${Env}-${Name}-uploads-public'
      BucketEncryption:
        ServerSideEncryptionConfiguration:
        - ServerSideEncryptionByDefault:
            SSEAlgorithm: AES256
      PublicAccessBlockConfiguration:
        BlockPublicAcls: false
        BlockPublicPolicy: false

  uploadspublicBucketPolicy:
    Metadata:
      'aws:copilot:description': 'A bucket policy to deny unencrypted access to the bucket and its contents'
    Type: AWS::S3::BucketPolicy
    DeletionPolicy: Retain
    Properties:
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: PublicReadGetObject
            Effect: Allow
            Principal: '*'
            Action: s3:GetObject
            Resource:
              - !Sub ${ uploadspublicBucket.Arn}/*
              - !Sub ${ uploadspublicBucket.Arn}
      Bucket: !Ref uploadspublicBucket

  uploadspublicAccessPolicy:
    Metadata:
      'aws:copilot:description': 'An IAM ManagedPolicy for your service to access the uploads-public bucket'
    Type: AWS::IAM::ManagedPolicy
    Properties:
      Description: !Sub
        - Grants CRUD access to the S3 bucket ${Bucket}
        - { Bucket: !Ref uploadspublicBucket }
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: S3ObjectActions
            Effect: Allow
            Action:
              - s3:GetObject
              - s3:PutObject
              - s3:PutObjectACL
              - s3:PutObjectTagging
              - s3:DeleteObject
              - s3:RestoreObject
            Resource: !Sub ${ uploadspublicBucket.Arn}/*
          - Sid: S3ListAction
            Effect: Allow
            Action: s3:ListBucket
            Resource: !Sub ${ uploadspublicBucket.Arn}

Outputs:
  uploadspublicName:
    Description: "The name of a user-defined bucket."
    Value: !Ref uploadspublicBucket
  uploadspublicAccessPolicy:
    Description: "The IAM::ManagedPolicy to attach to the task role"
    Value: !Ref uploadspublicAccessPolicy
