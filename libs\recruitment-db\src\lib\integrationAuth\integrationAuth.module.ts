import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { IntegrationAuth } from './integrationAuth.model';
import { IntegrationAuthService } from './integrationAuth.service';


@Module({
  imports: [SequelizeModule.forFeature([IntegrationAuth])],
  providers:[IntegrationAuthService],
  exports: [SequelizeModule,IntegrationAuthService],
})
export class IntegrationAuthModule {}
