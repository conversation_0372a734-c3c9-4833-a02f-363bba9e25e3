import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { TestManagerService } from "@microservices/db";
import {
  Body,
  Controller,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards,
  Logger,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiBearerAuth, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { findAndUpdate } from "libs/db/src/lib/test-manager/dto/findAndUpdate.dto";
import { HttpService } from "@nestjs/axios";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import {
  assessmentStartedSignal,
  assessmentSubmittedSignal,
  getProgress,
} from "apps/temporal/src/app/workflow/temporal/workflows";

@Controller('test-manager')
export class TestManagerController {
  constructor(
    private testManagerService: TestManagerService,
    private readonly httpService: HttpService
  ) {}

  @ApiOperation({ summary: "Create the task manager record" })
  @ApiResponse({ status: 201 })
  @Post()
  create(@Body() dto) {
    return this.testManagerService.create(dto);
  }

  @ApiOperation({ summary: "Get the task manager record by id" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("candidate")
  async get(@Query() dto, @AuthUser() user) {
    return await this.testManagerService.findOne(
      user["https://urecruits.com/userId"],
      dto
    );
  }

  @ApiOperation({ summary: "Get the task manager record by id" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/get")
  @Permissions("recruiter", 'assessment:view')
  getById(@Query() dto, @AuthUser() user: any) {
    return this.testManagerService.findOne(dto?.candidateId, dto, user);
  }

  @ApiOperation({ summary: "Get the task manager record by id" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/getByJobId/:userId/:jobId")
  @Permissions("recruiter", 'assessment:view', 'job-post:view')
  findByJobId(@Param("userId") userId, @Param("jobId") jobId) {
    return this.testManagerService.findByJobId(userId, jobId);
  }

  @ApiOperation({ summary: "update the task manager record by id" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth("access-token")
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch()
  @Permissions("candidate")
  async update(@Query() dto: findAndUpdate, @AuthUser() user) {
    const candidateId = user["https://urecruits.com/userId"];
    const { jobId, assessmentType, testStatus } = dto;

    const url = `https://recruitment-micro.urecruits.com/api/temporal-workflow?jobId=${jobId}&id=${candidateId}`;
    let response;
    try {
      const res = await this.httpService.get(url).toPromise();
      response = res?.data;
    } catch (error) {
      Logger.error("Error fetching data:", error);
      return;
    }

    try {
      const client = await connectToTemporal();
      if (!client || !response) {
        Logger.log("Getting issue to connect the client or get response in Test Manager Controller");
        return;
      }

      const handle = client.getHandle(response?.workflowid);
      if (!handle) {
        Logger.log("Failed to get workflow handle");
        return;
      }

      if (testStatus === "In Progress") {
        const testData = await this.testManagerService.findOne(candidateId, dto);
        await handle.signal(assessmentStartedSignal, {
          message: "Assessment is started",
          data: { timeDuration: testData.timeDuration },
        });
      } else if (assessmentType === "Coding Assessment" && testStatus === "Completed") {
        await handle.signal(assessmentSubmittedSignal, {
          message: "Take Home/Coding Assessment is Submitted",
        });
      }
    } catch (error) {
      Logger.error("Error handling Temporal client operations:", error);
    }

    return await this.testManagerService.update(candidateId, dto);
  }
}
