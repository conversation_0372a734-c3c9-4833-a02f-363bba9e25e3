version: 0.2

phases:
  install:
    runtime-versions:
      docker: 20
      ruby: 2.7.0
    commands:
      - echo "cd into $CODEBUILD_SRC_DIR"
      - cd $CODEBUILD_SRC_DIR
      # Download the copilot linux binary.
      - wget -q https://ecs-cli-v2-release.s3.amazonaws.com/copilot-linux-v1.20.0
      - mv ./copilot-linux-v1.20.0 ./copilot-linux
      - chmod +x ./copilot-linux

  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - $(aws ecr get-login --no-include-email --region $AWS_DEFAULT_REGION)

  build:
    commands:
      - echo "Building the hr-analytics service..."
      # Find the pipeline environments.
      - export pipeline=$(cat $CODEBUILD_SRC_DIR/copilot/pipelines/pipeline-urecruits-dev-hr-analytics/manifest.yml | ruby -ryaml -rjson -e 'puts JSON.pretty_generate(YAML.load(ARGF))')
      - export pl_envs=$(echo $pipeline | jq -r '.stages[].name')
      - export hr_analytics_service="hr-analytics"
      # Generate CloudFormation templates only for the hr-analytics service.
      - >
        for env in $pl_envs; do
          tag=$(sed 's/:/-/g' <<<"${CODEBUILD_BUILD_ID##*:}-${env}" | rev | cut -c 1-128 | rev)
          ./copilot-linux svc package -n $hr_analytics_service -e $env --output-dir './infrastructure' --tag $tag --upload-assets;
          if [ $? -ne 0 ]; then
            echo "CloudFormation stack and config files were not generated for hr-analytics. Please check build logs to see if there was a manifest validation error." 1>&2;
            exit 1;
          fi
        done;

  post_build:
    commands:
      - echo "Build completed for hr-analytics service."
      - ls -lah ./infrastructure

artifacts:
  files:
    - "infrastructure/*"
