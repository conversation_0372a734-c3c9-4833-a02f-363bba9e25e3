{"root": "libs/qdrant", "sourceRoot": "libs/qdrant/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/qdrant/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/qdrant"], "options": {"jestConfig": "libs/qdrant/jest.config.js", "passWithNoTests": true}}}, "tags": []}