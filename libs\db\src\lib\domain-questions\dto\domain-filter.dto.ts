import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional, Matches } from "class-validator";

export class DomainFilterDto {
  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiPropertyOptional({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: "desc", description: "Sort type" })
  @IsOptional()
  @Matches(/ASC|DESC|asc|desc/, { message: 'Enter correct sort; Example: DESC,ASC,asc,desc' })
  sortType?: string;
}


export class CreateDomainAssessmentDto{
  departmentId:number;
  duration:string;
  industryId:number;
  additionalDepartment?:string;
  name:string;
  passing:number;
  companyId:number;
  instruction:string;
  questions:any[];
}