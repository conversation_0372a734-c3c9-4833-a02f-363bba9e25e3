import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { Industry } from './industries.model';
import { Candidate } from '../candidates/candidates.model'

@Table({
  tableName: 'candidate_industries',
  createdAt: false,
  updatedAt: false,
})
export class CandidateIndustry extends Model<CandidateIndustry> {
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => Industry)
  @Column({ type: DataType.INTEGER })
  industryId: number;

  @ForeignKey(() => Candidate)
  @Column({ type: DataType.INTEGER })
  profileId: number;

  @BelongsTo(() => Candidate)
  candidateProfile: Candidate;

  @BelongsTo(() => Industry)
  industry: Industry;
}
