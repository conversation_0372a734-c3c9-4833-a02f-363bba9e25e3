import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class SubscribeDto {
  @ApiPropertyOptional({ example: 87, description: "Match" })
  @IsOptional()
  match?: number;

  @ApiPropertyOptional({ example: false, description: "Save job status" })
  @IsOptional()
  saveJob?: boolean;

  @ApiPropertyOptional({ example: false, description: "Apply job status" })
  @IsOptional()
  applyJob?: boolean;

  @ApiPropertyOptional({ example: false, description: "Subscribe job status" })
  @IsOptional()
  subscribeJob?: boolean;

  @ApiPropertyOptional({ example: [], description: "Summary" })
  @IsOptional()
  summary?: any;

  @ApiPropertyOptional({ example: "64413de7-439f-41a8-bb4c-1a72b000705e", description: "JobTarget GUID" })
  @IsOptional()
  JT_GUID?: string;
}
