import {
  Injectable,
  Inject,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { Client } from "@opensearch-project/opensearch";
import * as moment from "moment";
import {
  jobsAnalyticsQueryBody,
  jobsAreaAnalyticsQueryBody,
  jobsPieAnalyticsQueryBody,
} from "./jobs-analytics.query";
import { eventType } from "../../consts";
import { OpensearchService } from "../opensearch/opensearch.service";

@Injectable()
export class JobsAnalyticsService {
  constructor(
    private readonly opensearchService: OpensearchService
  ) { }

  async getJobsAnalyticsPie(duration, companyId) {
    try {
      const startOf = moment().startOf(duration).utc().toDate();
      const endOf = moment().endOf(duration).utc().toDate();
      const queryBody = jobsPieAnalyticsQueryBody(startOf, endOf, companyId);
      const response = await this.opensearchService.searchIndexSpecificData(queryBody, "jobs");
      const aggregations = response.body.aggregations;

      const transformedAggregations = {
        total_jobs: aggregations.total_jobs,
        jobs_status: {
          buckets: Object.keys(aggregations.jobs_status.buckets).reduce((acc, status) => {
            acc[status] = {
              doc_count: aggregations.jobs_status.buckets[status].unique_jobs.value
            };
            return acc;
          }, {})
        }
      };

      return transformedAggregations;
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(error, "Failed to fetch data");
    }
  }

  async getJobsAnalyticsArea(duration, jobstatus, companyId) {
    try {
      const startOf = moment().startOf(duration).utc().toDate();
      const endOf = moment().endOf(duration).utc().toDate();
      const interval = duration === "week" || duration === "month" ? "day" : "month";
      const queryBody = jobsAreaAnalyticsQueryBody(startOf, endOf, jobstatus, interval, Number(companyId));
      const response = await this.opensearchService.searchIndexSpecificData(queryBody, "jobs");
      const aggregations = response.body.aggregations;

      // Transform the response to match the original format (doc_count instead of unique_jobs.value)
      const transformedAggregations = {
        jobs_status: {
          buckets: aggregations.jobs_status.buckets.map(bucket => ({
            key: bucket.key,
            key_as_string: bucket.key_as_string,
            doc_count: bucket.unique_jobs.value // Use unique_jobs.value as doc_count
          }))
        }
      };

      return transformedAggregations;
    } catch (error) {
      Logger.log("Error:", error);
      throw new InternalServerErrorException(error, "Failed to fetch data");
    }
  }

  async getJobsAnalytics(queryParams, companyId) {
    try {
      const {
        jobId,
        dateFrom,
        dateTo,
        status,
        search,
        sortBy,
        sortType,
        limit,
        offset,
      } = queryParams;

      const body: any = jobsAnalyticsQueryBody(sortBy, sortType, limit, offset, companyId);

      if (jobId) {
        body.query.bool.filter.push({
          match: { jobId },
        });
      }

      if (dateFrom) {
        body.query.bool.filter.push({
          range: { [`${eventType.JOB_POSTED}.createdAt`]: { gte: dateFrom } },
        });
      }

      if (dateTo) {
        body.query.bool.filter.push({
          range: { [`${eventType.JOB_POSTED}.createdAt`]: { lte: dateTo } },
        });
      }

      if (status) {
        if (Array.isArray(status)) {
          body.query.bool.filter.push({
            bool: {
              should: status.map((s) => ({
                match: {
                  [`${eventType.JOB_POSTED}.status.keyword`]: s,
                },
              })),
            },
          });
        } else {
          body.query.bool.filter.push({
            match: { [`${eventType.JOB_POSTED}.status.keyword`]: status },
          });
        }
      }

      if (search) {
        const searchFilter = {
          bool: {
            should: [
              {
                wildcard: {
                  [`${eventType.JOB_POSTED}.title`]: {
                    value: search + "*",
                    boost: 1.0
                  }
                }
              },
              {
                wildcard: {
                  [`${eventType.JOB_POSTED}.description`]: {
                    value: search + "*",
                    boost: 1.0
                  }
                }
              }
            ],
            minimum_should_match: 1
          }
        }
        if (!body.query.bool.must) {
          body.query.bool.must = [];
        }
        body.query.bool.must.push(searchFilter);
      }

      const response = await this.opensearchService.searchIndexSpecificData(body, "jobs");
      return response.body.aggregations;
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(error, "Failed to fetch data");
    }
  }
}
