import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Address } from "./addresses.model";
import { UpdateAddressDto } from "./dto/update-address.dto";
import { CreateAddressDto } from "./dto/create-address.dto";

@Injectable()
export class AddressesService {
  constructor (
    @InjectModel(Address) private addressRepository: typeof Address,
  ) {}

  async create (companyId: number) {
    return await this.addressRepository.create({ companyId });
  }

  async createWithData (dto: CreateAddressDto) {
    return await this.addressRepository.create(dto);
  }

  async update (dto: UpdateAddressDto) {
    const data = await this.get(dto.id, dto.companyId);
    Object.assign(data, dto);
    return await data.save();
  }

  async delete (dto: UpdateAddressDto) {
    const data = await this.get(dto.id, dto.companyId);
    await data.destroy();
    return true;
  }

  async get (id: number, companyId: number) {
    const data = await this.addressRepository.findOne({
      where: { id, companyId },
    });
    if (!data) {
      throw new HttpException("Addresses not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }
}
