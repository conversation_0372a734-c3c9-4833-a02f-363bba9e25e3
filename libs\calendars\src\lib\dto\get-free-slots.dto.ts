import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class GetFreeSlotsDTO {
  @ApiPropertyOptional({ example: "date", description: "Start Date and Time" })
  date: Date;

  @ApiProperty({ example: "endDate", description: "End Date and time" })
  @IsOptional()
  endGmt: Date;

  @ApiProperty({ example: "17", description: "recruiterId" })
  recruiterId: any;
}
