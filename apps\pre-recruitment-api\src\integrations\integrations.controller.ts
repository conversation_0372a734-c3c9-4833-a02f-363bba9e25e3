import {
  Controller,
  Get,
  Post,
  Query,
  Res,
  UseGuards,
  ParseArrayPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PermissionsGuard, Permissions, AuthUser } from '@microservices/auth';
import { AuthGuard } from '@nestjs/passport';
import { IntegrationsService } from '@microservices/recruitment-db';


@ApiTags("Integrations")
@Controller("integrations")
export class IntegrationsController {
  constructor(private readonly integrationsService: IntegrationsService) {}

  @ApiOperation({ summary: "Get 3rd party services connection status" })
  @ApiResponse({ status: 200, type: "Array" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/connection-status")
  connectionStatus(
    @Query("providers", new ParseArrayPipe({ items: String, separator: "," }))
    providers: string[],
    @AuthUser() user: any
  ) {
    return this.integrationsService.connectionStatus(providers, user);
  }

  @ApiOperation({ summary: "To get the provider" })
  @ApiResponse({ status: 200, type: "Array" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/provider")
  getProvider(@AuthUser() user: any) {
    return this.integrationsService.getProvider(
      user["https://urecruits.com/userId"]
    );
  }

  @ApiOperation({ summary: "Handle the OAuth 2.0 server response" })
  @ApiResponse({ status: 302, type: "Redirect" })
  @Get("/oauth2-callback")
  oauth2Callback(@Query() query, @Res() res) {
    return this.integrationsService.oauth2Callback(query, res);
  }

  @ApiOperation({ summary: "Auth Revoke" })
  @ApiResponse({ status: 200, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/auth-revoke")
  authRevoke(@Query() query, @AuthUser() user: any) {
    return this.integrationsService.authRevoke(
      query,
      user["https://urecruits.com/userId"]
    );
  }
}
