{"root": "libs/twilio", "sourceRoot": "libs/twilio/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/twilio/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/twilio"], "options": {"jestConfig": "libs/twilio/jest.config.js", "passWithNoTests": true}}}, "tags": []}