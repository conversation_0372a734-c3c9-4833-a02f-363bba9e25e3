import { eventType } from "../../consts";

export const jobsPieAnalyticsQueryBody = (startOf, endOf, companyId) => ({
  size: 0,
  query: {
    bool: {
      must: [
        {
          term: {
            companyId: companyId
          }
        },
        {
          range: {
            [`eventDateTime`]: {
              gte: startOf,
              lte: endOf,
            },
          },
        },
      ],
    },
  },
  aggs: {
    total_jobs: {
      cardinality: {
        field: "jobId",
      },
    },
    jobs_status: {
      filters: {
        filters: {
          publish: {
            bool: {
              filter: {
                term: {
                  "job_posted.status.keyword": "publish"
                }
              }
            }
          },
          "on-hold": {
            bool: {
              filter: {
                term: {
                  "job_posted.status.keyword": "onhold"
                }
              }
            }
          },
          closed: {
            bool: {
              filter: {
                term: {
                  "job_posted.status.keyword": "closed"
                }
              }
            }
          },
          pending: {
            bool: {
              filter: {
                term: {
                  "job_posted.status.keyword": "pending"
                }
              }
            }
          },
          draft: {
            bool: {
              filter: {
                term: {
                  "job_posted.status.keyword": "draft"
                }
              }
            }
          }
        }
      },
      aggs: {
        unique_jobs: {
          cardinality: {
            field: "jobId"
          }
        }
      }
    }
  },
});

export const jobsAreaAnalyticsQueryBody = (startOf, endOf, status, duration, companyId) => ({
  size: 0,
  query: {
    bool: {
      must: [
        {
          term: { companyId: companyId }
        },
        {
          term: {
            "job_posted.status.keyword": status
          }
        },
        {
          range: {
            eventDateTime: {
              gte: startOf,
              lte: endOf,
            },
          },
        },
      ],
    },
  },
  aggs: {
    jobs_status: {
      date_histogram: {
        field: "eventDateTime",
        calendar_interval: duration,
        time_zone: "+00:00",
        min_doc_count: 0,
        extended_bounds: {
          min: startOf,
          max: endOf,
        },
      },
      aggs: {
        unique_jobs: {
          cardinality: {
            field: "jobId"
          }
        }
      }
    }
  },
});

export const jobsAnalyticsQueryBody = (sortBy, sortType, limit, offset, companyId) => {
  return {
    "size": 0,
    "query": {
      "bool": {
        "must": [
          { "term": { "companyId": companyId } },
          { "terms": { "job_posted.status.keyword": ["publish", "draft"] } }
        ],
        "filter": []
      }
    },
    "aggs": {
      "distinct_jobs": {
        "terms": {
          "field": "jobId",
          "size": 10000
        },
        "aggs": {
          "latest_record": {
            "top_hits": {
              "size": 1,
              "sort": [
                { "eventDateTime": { "order": "desc" } }
              ],
              "_source": {
                "includes": ["*"]
              }
            }
          }
        }
      },
      "total_jobs": {
        "cardinality": {
          "field": "jobId"
        }
      }
    }
  }
}
