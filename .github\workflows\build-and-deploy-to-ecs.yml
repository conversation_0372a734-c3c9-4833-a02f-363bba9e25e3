name: Deploy to Amazon ECS

on:
  push:
    branches:
      - development
    paths:
      # - 'apps/assessment-api/**'
      # - 'dockerfiles/Dockerfile.assessment'
      # - '*.json'
      # - '*.js'
      # - '*.ts'
      # - '!**/*.md'
      # - '!**/*.txt'
      # Include changes in the assessment-api app
      - 'apps/assessment-api/**'
      # Include root-level shared files and folders
      - '*.js'
      - '*.ts'
      - '*.json'
      - '*.env'
      - '*.yml'
      - '*.yaml'
      - 'libs/**'
      - 'tools/**'
      # Include only the assessment Dockerfile
      - 'dockerfiles/Dockerfile.assessment'
      # Exclude everything else inside apps and dockerfiles
      - '!apps/**'
      - 'apps/assessment-api/**'  # re-include only assessment-api
      - '!dockerfiles/**'
      - 'dockerfiles/Dockerfile.assessment'  # re-include only this Dockerfile


env:
  AWS_REGION: us-east-1 
  DEV_REPO: dev/urecruits-assessment
   

jobs:
  development-build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Build Docker Image
        run: |
          docker build -t assessment-api:latest -f dockerfiles/Dockerfile.assessment .

      - name: Push Docker Image to ECR (dev + qa)
        env:
          AWS_ACCOUNT_ID: ************
          AWS_REGION: us-east-1
        run: |
          aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
          GIT_COMMIT=${GITHUB_SHA::7}                        
          RAND_NUM=$(shuf -i 1000000-9999999 -n 1)            
          IMAGE_TAG="$GIT_COMMIT-$RAND_NUM"
          IMAGE=$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com/$DEV_REPO:$IMAGE_TAG
          echo "Tagging image as $IMAGE"
          docker tag assessment-api:latest $IMAGE
          echo "Pushing image $IMAGE"
          docker push $IMAGE
          echo "Image pushed to ECR $IMAGE"

          
