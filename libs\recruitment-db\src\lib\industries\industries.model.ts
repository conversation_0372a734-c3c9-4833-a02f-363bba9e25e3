import {
  BeforeC<PERSON>,
  BelongsToMany,
  Column,
  DataType, HasMany,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty } from '@nestjs/swagger';
import { CandidateIndustry } from './candidates-industries.model';
import { Candidate } from '../candidates/candidates.model'
import { Company } from '../companies/companies.model'
import { Jobs } from "../job/job.model";
import { slugify } from "../hooks/hooks";

interface IndustryCreationAttrs {
  value: string;
  label: string;
}

@Table({ tableName: 'industries' })
export class Industry extends Model<Industry, IndustryCreationAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'education-training', description: 'Value' })
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  value: string;

  @ApiProperty({ example: 'Education & Training', description: 'Label' })
  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  label: string;

  @BelongsToMany(() => Candidate, () => CandidateIndustry)
  candidateProfile: Candidate[];

  @HasMany(() => Company)
  companies: Company[];

  @HasMany(() => Jobs)
  jobs: Jobs[];

  @BeforeCreate
  static beforeCreateHook(instance: Industry): void {
    instance.value = slugify(instance.value);
  }
}
