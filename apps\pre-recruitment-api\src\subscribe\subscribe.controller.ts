import {
  Body,
  Controller,
  Get,
  Headers,
  Inject,
  Logger,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Query,
  UseGuards,
  forwardRef,
} from "@nestjs/common";

import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import {
  CandidateFiltersDto,
  CreateSubscribeDto,
  Department,
  DepartmentService,
  EditJobsStatus,
  EditStatusDto,
  EditSubscribeDto,
  FilterSubscribeDto,
  Jobs,
  PredefinedSkillDto,
  PredefinedSkills,
  RestartWorkflowDto,
  ScoreboardFilterDto,
  StartWorkflowDto,
  Subscribe,
  SubscribeService,
  WorkflowService,
} from "@microservices/recruitment-db";
import { AuthGuard } from "@nestjs/passport";
import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { TemporalWorkflowService } from "@microservices/recruitment-db";
import { HttpService } from "@nestjs/axios";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { recruitmentWorkflow } from "apps/temporal/src/app/workflow/temporal/workflows";
import { v4 as uuidv4 } from "uuid";

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("Subscribe")
@ApiBearerAuth("access-token")
@Controller("subscribe")
export class SubscribeController {
  constructor(
    private readonly subscribeService: SubscribeService,
    private readonly workflowService: WorkflowService,
    @Inject(forwardRef(() => TemporalWorkflowService))
    private temporalWorkflowService: TemporalWorkflowService,
    private httpService: HttpService,
  ) {}

  @ApiOperation({ summary: "Create Subscribe" })
  @ApiResponse({ status: 200, type: Subscribe })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post()
  @Permissions("candidate")
  async create(@Headers('authorization') authHeader:any,@Body() dto: CreateSubscribeDto, @AuthUser() user: any) {
    const result: any = await this.subscribeService.createSubscribe(
      dto,
      user["https://urecruits.com/userId"]
    );
    return result;
  }

  @ApiOperation({ summary: "Get All candidate who has applied for the company" })
  @ApiResponse({ status: 200})
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get('/applied-candidates')
  @Permissions("recruiter")
  async getAllAppliedCandidateByCompanyId(@Headers('authorization') authHeader: any, @Query() dto: CandidateFiltersDto, @AuthUser() user: any) {
    return this.subscribeService.getAppliedCandidatesByCompany(dto, user["https://urecruits.com/companyId"])
  }

  @ApiOperation({ summary: "Start a workflow for a candidate" })
  @ApiResponse({ status: 200})
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post('/start-workflow')
  @Permissions("recruiter","job-post:edit")
  async startWorkflow(@Headers('authorization') authHeader: any, @Body() dto: StartWorkflowDto, @AuthUser() user: any) {
    const {subscribeId, userData, job, workflowData} =  await this.subscribeService.startWorkflow(
      dto,
      user["https://urecruits.com/companyId"],
      authHeader
    );
    if (workflowData?.workflow?.length && userData&&job&&subscribeId) {
      try {
        await this.temporalWorkflowService.deleteByCandidate(
          `${dto.jobId}`,
          dto.userId,
        );
        const workflowId = `ur - ${uuidv4()} - ${job.companyId} - ${dto.jobId
          } - ${dto.userId}`;
        const client = await connectToTemporal();
        if (client) {
          const handle = await client.start(recruitmentWorkflow, {
            args: [
              {
                user: userData,
                jobData: job,
                workflowData,
                subscribeId: subscribeId,
              },
            ] as any,
            workflowId: workflowId,
            taskQueue: "recruitment",
            retry: {
              maximumAttempts: 2,
            },
          });
          await this.temporalWorkflowService.create(
            dto.userId,
            dto.jobId,
            workflowId
          );
        } else {
          Logger.log(
            "Getting issue to connect the client in start workflow of subscribe "
          );
        throw new Error("Error connecting to a client")

        }
        return {
          message: "Job Workflow successfully started for candidate",
        };
      } catch (error) {
        Logger.log(" Error while Start workflow of subscribe:"+error);
      }
    }
  }

  @ApiOperation({ summary: "Start a workflow for a candidate" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post('/assign/workflow')
  @Permissions("recruiter", "job-post:edit")
  async AssignAssignment(@Headers('authorization') authHeader: any, @Body() dto: StartWorkflowDto, @AuthUser() user: any) {
    const data = await this.subscribeService.createSubscribe({jobId:dto.jobId,subscribeJob:true,applyJob:true},dto.userId,true)
    const result: any = await this.subscribeService.startWorkflow(
      dto,
      user["https://urecruits.com/companyId"],
      authHeader
    );
    return result;
  }

  @ApiOperation({ summary: "Restart apply job workflow for candidate" })
  @ApiResponse({ status: 200, type: Subscribe })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/restart/workflow")
  @Permissions("job-post:edit")
  async restartWorkflow(@Body() dto: RestartWorkflowDto, @AuthUser() user: any) {

    const {subscribeId, userData, job, workflowData} =  await this.subscribeService.restartWorkflow(dto);
    if (workflowData?.workflow?.length && userData&&job&&subscribeId) {
      try {
        await this.temporalWorkflowService.deleteByCandidate(
          `${dto.jobId}`,
          dto.userId,
        );
        const workflowId = `ur - ${uuidv4()} - ${job.companyId} - ${dto.jobId
          } - ${dto.userId}`;
        const client = await connectToTemporal();
        if (client) {
          const handle = await client.start(recruitmentWorkflow, {
            args: [
              {
                user: userData,
                jobData: job,
                workflowData,
                subscribeId: subscribeId,
              },
            ] as any,
            workflowId: workflowId,
            taskQueue: "recruitment",
            retry: {
              maximumAttempts: 2,
            },
          });
          await this.temporalWorkflowService.create(
            dto.userId,
            dto.jobId,
            workflowId
          );
        } else {
          Logger.log(
            "Getting issue to connect the client in restart workflow of subscribe "
          );
        throw new Error("Error connecting to a client")

        }
        return {
          message: "Job Workflow successfully restarted for candidate",
        };
      } catch (error) {
        Logger.log(" Error while Restart workflow of subscribe:"+error);
      }
    }
  }

  @ApiOperation({ summary: "Edit Subscribe" })
  @ApiResponse({ status: 200, type: Subscribe })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch()
  @Permissions("OR","job-post:edit","assessment:edit")
  edit(@Body() dto: EditSubscribeDto, @AuthUser() user: any) {
    return this.subscribeService.editSubscribe(
      dto,
      user["https://urecruits.com/userId"]
    );
  }

  @ApiOperation({ summary: "Get job by id" })
  @ApiResponse({ status: 200, type: [Subscribe] })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/scoreboard/:id")
  @Permissions("OR","job-post:view","assessment:view")
  getScoreboard(
    @Param("id") id: number,
    @Query() query: ScoreboardFilterDto,
    @AuthUser() user: any
  ) {
    return this.subscribeService.getScoreboard(
      id,
      user["https://urecruits.com/companyId"],
      query
    );
  }

  @ApiOperation({ summary: "Get Summary" })
  @ApiResponse({ status: 200, type: [Subscribe] })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/summary/:jobId/:userId")
  @Permissions("OR","job-post:view","assessment:view")
  getSummary(
    @Param("jobId") jobId: number,
    @Param("userId") userId: number,
    @AuthUser() user: any
  ) {
    return this.subscribeService.getSummary(
      userId,
      jobId,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get Summary for candidate" })
  @ApiResponse({ status: 200, type: [Subscribe] })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/candidate/summary/:jobId/:companyId")
  @Permissions("candidate")
  getSummaryForCandidate(
    @Param("jobId") jobId: number,
    @Param("companyId") companyId: number,
    @AuthUser() user: any
  ) {
    return this.subscribeService.getSummary(
      user['https://urecruits.com/userId'],
      jobId,
      companyId
    );
  }

  @ApiOperation({ summary: "Edit Status" })
  @ApiResponse({ status: 200, type: Subscribe })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/company")
  @Permissions("recruiter")
  async changeStatus(@Body() dto: EditStatusDto, @AuthUser() user: any) {
    return this.subscribeService.changeStatus(
      dto,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "get Candidate scoreboard" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/candidate/scoreboard")
  @Permissions("candidate")
  getCandidateScoreboard(@Query() dto: FilterSubscribeDto, @AuthUser() user: any) {
    const userId = user['https://urecruits.com/userId']
    return this.subscribeService.getCandidateScoreBoards(dto, userId)
  }

  @ApiOperation({ summary: "get candidate scoreboards for recruiter" })
  @ApiResponse({ status: 200 })
  // @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/scoreboard")
  @Permissions("recruiter")
  getScoreboards(@Query() dto: ScoreboardFilterDto, @AuthUser() user: any) {
    const companyId = user['https://urecruits.com/companyId']
    return this.subscribeService.getScoreboards(companyId,dto)
  }

  @ApiOperation({ summary: "get today's saved job's count" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/jobs/saved/count")
  @Permissions("candidate")
  getSavedJobsCount(@AuthUser() user: any) {
    const userId = user['https://urecruits.com/userId']
    return this.subscribeService.countSavedJobs(userId)
  }

  @ApiOperation({ summary: "changes job's status by job id" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/jobs/:jobId")
  @Permissions("candidate")
  changeJobStatus(@Param('jobId', ParseIntPipe) jobId: number, @Body() query: EditJobsStatus, @AuthUser() user: any) {
    const userId = user['https://urecruits.com/userId']
    return this.subscribeService.changeJobstatus(query, jobId, userId)
  }
}