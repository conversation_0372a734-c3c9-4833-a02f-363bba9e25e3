import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  UseGuards,
  Body,
  Param,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { CompanySubscriptionDto, SubscriptionIdDto, Subscriptions, CreateSubscriptionDto, UpgradeSubscriptionDto, CreateSubscriptionWithoutCardDto, SubscriptionService, CompanyAddonsDto } from '@microservices/recruitment-db';

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   candidateId: user["https://urecruits.com/candidateId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags('Subscription')
@Controller('subscription')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @ApiOperation({
    summary: 'Get subscription plans.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns list of subscription plans.',
  })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-plans')
  @Permissions('subscription:view')
  getPlans(@AuthUser() user: any) {
    return this.subscriptionService.getPlans({companyId:user["https://urecruits.com/companyId"]});
  }

  @ApiOperation({
    summary: 'Store subscriptoin data on opensearch by id',
  })
  @Get('/opensearch/:subId')
  addonOpensearch(@Param('subId') subId: number) {
    return this.subscriptionService.addDataonOpensearch(subId);
  }

  @ApiOperation({
    summary: 'Create default subscription.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns Subscription object.',
    type: Subscriptions,
  })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/create-default-subscription-without-card')
  // @Permissions('subscription:edit')
  createDefaultSubscriptionWithoutCard(
    @AuthUser() user: any,
  ) {
    return this.subscriptionService.createDefaultSubscriptionWithoutCard({ companyId: user["https://urecruits.com/companyId"],  checkCompany: true});
  }

  @ApiOperation({
    summary: 'Create subscription without card.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns Subscription object.',
    type: Subscriptions,
  })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/create-subscription-without-card')
  // @Permissions('subscription:edit')
  createSubscriptionWithoutCard(
    @Body() dto: CreateSubscriptionWithoutCardDto,
    @AuthUser() user: any,
  ) {
    return this.subscriptionService.createSubscriptionWithoutCard({dto, companyId: user["https://urecruits.com/companyId"],  checkCompany: true});
  }

  @ApiOperation({
    summary: 'Create subscription.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns Subscription object.',
    type: Subscriptions,
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/create-subscription')
  // @Permissions('subscription:edit')
  createSubscription(
    @Body() dto: CreateSubscriptionDto,
    @AuthUser() user: any,
  ) {
    return this.subscriptionService.createSubscription(dto, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({
    summary: 'Upgrade subscription.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns Subscription object.',
    type: Subscriptions,
  })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/upgrade-subscription')
  // @Permissions('subscription:edit')
  upgradeSubscription(
    @Body() dto: UpgradeSubscriptionDto,
    @AuthUser() user: any,
  ) {
    return this.subscriptionService.upgradeSubscription(dto, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({
    summary: 'Delete subscription.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns true|false.',
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete('/delete-subscription')
  @Permissions('subscription:edit')
  deleteSubscription( @AuthUser() user: any) {
    return this.subscriptionService.deleteSubscription(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({
    summary: 'Get company subscription info.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns company main subscription object.',
    type: CompanySubscriptionDto,
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-company-main-subscription')
  @Permissions('recruiter')
  getCompanySubscription(@AuthUser() user: any) {
    return this.subscriptionService.getCompanySubscriptionDetails(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({
    summary: 'Add addons pricing in the stripe',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns company subscription object.',
    // type: CompanySubscriptionDto,
  })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/add-ons')
  @Permissions('recruiter')
  addAddonsOfCompany(@Body() dto:CompanyAddonsDto,@AuthUser() user: any) {
    return this.subscriptionService.updateSubscriptionAddon(dto,user["https://urecruits.com/companyId"]);
  }
}
