import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty } from "class-validator";

export class CreateTemporalWorkflowDto {
    @ApiProperty({ example: 1, description: 'User ID' })
    @IsNotEmpty()
    userid: any;
  
    @ApiProperty({ example: '2', description: 'jobId' })
    @IsNotEmpty()
    jobid: any;
  
    @ApiProperty({ example: 'ur - b3499d64-fd8d-4350-ba7b-7bb6578f7859 - 6 - 67 - 16', description: 'Current workflow ID' })
    @IsNotEmpty()
    workflowid: string;
}
