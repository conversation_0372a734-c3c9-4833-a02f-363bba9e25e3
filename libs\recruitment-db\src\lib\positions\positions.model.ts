import { BeforeC<PERSON>, Column, DataType, <PERSON><PERSON><PERSON>, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { Candidate } from "@microservices/recruitment-db";
import { slugify } from "../hooks/hooks";

interface PositionAttrs {
  value?: string;
  label?: string;
}

@Table({ tableName: "positions", createdAt: false, updatedAt: false })
export class Position extends Model<Position, PositionAttrs> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "administrative-assistant",
    description: "Value",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  value: string;

  @ApiProperty({
    example: "Administrative assistant",
    description: "Label",
  })
  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  label: string;

  @HasMany(() => Candidate)
  candidate: Candidate[];

  @BeforeCreate
  static beforeCreateHook(instance: Position): void {
    instance.value = slugify(instance.value);
  }
}
