import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Assignment } from "./assignment.model";
import { Deadline } from "../deadline/deadline.model";
import { LiveCoding } from "../live-coding/live-coding.model";
import { TimeDuration } from "../time-duration/time-duration.model";
import { TakeHomeTask } from "../take-home/take-home-task.model";
import { DomainAssessment } from "../domain-questions/domain-assessment.model";
import { createAssignmentDTO } from "./assignment.types";
import { updateAssignmentDTO, UpdateCandidateStatusListDto } from "./dto/update-assignment.dto";
import { GetAssignmentDto } from "./dto/get-assignment.dto";
import { Op } from "sequelize";

@Injectable()
export class AssignmentService {
  constructor(
    @InjectModel(Assignment) private assignmentModel: typeof Assignment,
    @InjectModel(Deadline) private deadlineModel: typeof Deadline,
    @InjectModel(TimeDuration) private timeDurationModel: typeof TimeDuration
  ) { }

  create(dto: createAssignmentDTO, companyId: any): Promise<Assignment> {
    try {
      const data = { ...dto, companyId }
      return this.assignmentModel.create(data);
    } catch (err) {
      Logger.log("Error :", err)
    }
  }

  async findAssignmentInfo(id: number, companyId: number): Promise<Assignment> {
    try {
      const result = await this.assignmentModel.findOne({
        where: { id, companyId },
        include: [TakeHomeTask, LiveCoding, DomainAssessment],
      });

      if (result === null) {
        throw new NotFoundException("No Assignment found");
      }
      return result;
    } catch (err) {
      throw new BadRequestException();
    }
  }

  async findAssignmentByJobId(id: number, companyId: number): Promise<Assignment> {
    try {
      const result = await this.assignmentModel.findOne({
        where: { jobId: id, isActive: true, companyId },
        include: [TakeHomeTask, LiveCoding, DomainAssessment],
      });
      return result;
    } catch (err) {
      throw new BadRequestException();
    }
  }

  getAllByUser(id: number, data: GetAssignmentDto) {
    try {
      const order = [];
      if (data.sortBy && data.sortType) {
        order.push([data.sortBy, data.sortType]);
      } else {
        order.push(["id", "DESC"]);
      }

      const where = {
        companyId: id,
      };

      if (data?.jobId) {
        where['jobId'] = Array.isArray(data.jobId)
          ? { [Op.or]: data.jobId.map(id => (id)) }
          : data.jobId;
      }

      if (data?.assignmentType) {
        switch (data.assignmentType) {
          case 'domain':
            where['$domainId$'] = { [Op.ne]: null };
            break;
          case 'coding':
            where[Op.or] = [
              { '$liveCodingId$': { [Op.ne]: null } },
              { '$takeHomeTaskId$': { [Op.ne]: null } },
            ];
            break;
        }
      }

      if (data?.search) {
        where[Op.or] = [
          { title: { [Op.iLike]: `%${data.search}%` } },
        ];
      }

      where['isActive'] = true;

      return this.assignmentModel.findAndCountAll({
        where,
        include: [
          { model: LiveCoding },
          { model: TakeHomeTask },
          { model: DomainAssessment },
        ],
        distinct: true,
        limit: data.limit || 10,
        offset: data.offset || 0,
        order,
      });
    } catch (error) {
      Logger.log("Error: " + error);
    }
  }

  async updateCandidate(id: number, dto: updateAssignmentDTO, companyId: number) {
    try {
      const { jobId, workflowId } = dto;
      const result = await this.assignmentModel.findOne({
        where: { jobId, workflowId, companyId },
      });

      if (!result) {
        return null
      }
      const isUserAlreadyPresent = result.candidate?.findIndex(i => i == id) >= 0;
      if (isUserAlreadyPresent) {
        return null
      }

      if (!isUserAlreadyPresent) {
        result.candidate = result.candidate ? [...result.candidate, id] : [id];
        result.candidateStatus = result.candidateStatus ? [...result.candidateStatus, { id, status: 'active' }] : [{ id, status: 'active' }]
        await result.save();
      }

      return result;
    } catch (error) {
      Logger.log("Error from update assignment: ", error);
      throw error;
    }
  }
  async updateCandidateList(id, dto: UpdateCandidateStatusListDto) {
    const { jobId, workflowId, companyId, status } = dto;
    try {
      const result = await this.assignmentModel.findOne({
        where: { jobId: +jobId, workflowId: +workflowId, companyId },
      });

      if (!result) {
        return null
      }
      const isUserAlreadyPresent = result.candidateStatus?.findIndex(i => i.id == id) >= 0;
      if (!isUserAlreadyPresent) {
        return null
      }
      if (isUserAlreadyPresent) {
        const candidateStatus = JSON.parse(JSON.stringify(result.candidateStatus))
        const data = candidateStatus.find(i => i.id == id)
        if (data) {
          data.status = status;
          result.candidateStatus = candidateStatus
        }
        return await result.save()
      }
    } catch (err) {
      console.log("Error updating candidate status in the list", err);
    }
  }

  update(id: number, dto: any, companyId: number) {
    return this.assignmentModel.update({ ...dto }, { where: { id, isActive: true, companyId } });
  }

  async delete(id: number, companyId: number) {
    const find = await this.assignmentModel.findByPk(id);
    if (find) {
      return await this.assignmentModel.update({ isActive: false }, {
        where: {
          id,
        },
      });
    } else {
      throw new NotFoundException(`Assignment with ID ${id} not found`);
    }
  }

  findAllDeadlines(): Promise<Deadline[]> {
    try {
      return this.deadlineModel.findAll();
    } catch (error) {
      throw new NotFoundException(`Deadlines not found`);
    }
  }

  async findAllTimeDurations() {
    try {
      return await this.timeDurationModel.findAll();
    } catch (error) {
      throw new NotFoundException(`Time Duration is not found`);
    }
  }
}