import { IsNotEmpty, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from 'class-validator';

export class createAssignmentDTO {
  @IsNotEmpty()
  @IsString()
  title: string;

  @IsNotEmpty()
  @IsNumber()
  jobId: number;

  @IsOptional()
  workflowId: number;

  @IsOptional()
  companyId: number;

  @IsOptional()
  @IsNumber()
  liveCodingId: number;

  @IsOptional()
  @IsNumber()
  takeHomeTaskId: number;

  @IsOptional()
  @IsNumber()
  domainId: number;

  @IsOptional()
  candidate: any;

  @IsOptional()
  deadline: any;

  @IsOptional()
  reviewers: number[];
};
