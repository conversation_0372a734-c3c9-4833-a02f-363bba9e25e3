import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, isNotEmpty, IsOptional } from "class-validator";

export class SendMailDto {

  @ApiProperty({ example: "20", description: "Token" })
  token: string;

  @ApiProperty({ example: "20", description: "Recipient mail" })
  to: any;

  @ApiPropertyOptional({ example: "Designer", description: "subject" })
  @IsOptional()
  subject?: any;

  @ApiProperty({ example: "id", description: "Body" })
  @IsOptional()
  body?: string;

  @ApiProperty({ example: "send/draft", description: "action" })
  @IsNotEmpty()
  action: string;

  @ApiProperty({ example: "s3FileFiles", description: "s3File" })
  @IsNotEmpty()
  s3File: any;
}
