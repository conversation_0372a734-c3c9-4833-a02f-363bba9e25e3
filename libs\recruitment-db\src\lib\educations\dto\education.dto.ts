import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class EducationDto {
  @ApiPropertyOptional({ example: "Zhytomyr Polytechnic", description: "University/Institute Name" })
  @IsOptional()
  readonly universityName?: string;

  @ApiPropertyOptional({ example: "Web Developer", description: "Course Name" })
  @IsOptional()
  readonly courseName?: string;

  @ApiPropertyOptional({ example: "Web Developer", description: "Specification" })
  @IsOptional()
  readonly specification?: string;

  @ApiPropertyOptional({ example: "January 4, 2022", description: "Course Periods start" })
  @IsOptional()
  readonly coursePeriodStart?: string;

  @ApiPropertyOptional({ example: "January 4, 2022", description: "Course Periods end" })
  @IsOptional()
  readonly coursePeriodEnd?: string;

  @ApiPropertyOptional({ example: "85%", description: "GPA / Percentage" })
  @IsOptional()
  readonly gpa?: string;

  @ApiPropertyOptional({ example: false, description: "present education?" })
  @IsOptional()
  present?: boolean;
}
