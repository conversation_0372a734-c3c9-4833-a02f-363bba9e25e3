import { Controller, Post, Body, UseGuards, Headers } from '@nestjs/common';
import { LangGraphService } from './langgraph.service';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { AuthUser } from '@microservices/auth';

@Controller('agent')
export class LangGraphController {
  constructor(private readonly langGraphService: LangGraphService) {}

  @ApiOperation({ summary: 'Invoke LangGraph workflow with user prompt' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'))
  @Post()
  async invokeGraph(
    @AuthUser() user: any,
    @Headers('authorization') authHeader: string,
    @Body() body: { sessionId: string; userInput: string }
  ) {
    const companyId = user['https://urecruits.com/companyId'];
    const userId = user['https://urecruits.com/userId'];
    const userPermissions = user.permissions || [];
    return this.langGraphService.run({
      sessionId: body.sessionId,
      userInput: body.userInput,
      userId,
      companyId,
      authHeader,
      userPermissions,
    });
  }
}
