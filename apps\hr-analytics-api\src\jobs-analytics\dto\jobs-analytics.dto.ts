import { IsEnum, IsOptional } from "class-validator";
import { Transform } from "class-transformer";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { eventType } from "@microservices/hr-analytics";

enum Duration {
  WEEK = "week",
  MONTH = "month",
  YEAR = "year"
}
enum JobStatus {
  PUBLISH = "publish",
  DRAFT = "draft",
  CLOSED = "closed",
  ONHOLD = "onhold",
  PENDING = "pending",
}

export class JobsPieAnalyticsQueryDto {
  @IsEnum(Duration)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ example: "week", description: "Duration" })
  duration: Duration;
}

export class JobsAreaAnalyticsQueryDto extends JobsPieAnalyticsQueryDto {
  @IsEnum(JobStatus)
  @Transform(({ value }) => value.toLowerCase())
  @ApiProperty({ example: "publish", description: "Job Status" })
  Jobstatus: JobStatus;
}

export class JobsAnalyticsDto {
  @ApiPropertyOptional({ example: "1", description: "Job Id" })
  @IsOptional()
  jobId?: any;

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date From" })
  @IsOptional()
  dateFrom?: string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date To" })
  @IsOptional()
  dateTo?: string;

  @ApiPropertyOptional({ example: ["publish"], description: "Status" })
  @IsOptional()
  status?: [JobStatus];

  @ApiPropertyOptional({ example: "Designer", description: "Search" })
  @Transform(({ value }) => value.toLowerCase())
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({ example: "id", description: "Sort by" })
  @Transform(({ value }) => eventType.JOB_POSTED + "." + value)
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;
}




