import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNumber, IsOptional, IsString } from "class-validator";

export class CandidateFiltersDto {
  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiPropertyOptional({ example: "1", description: "User id" })
  @IsOptional()
  userId?: number[] | number;

  @ApiPropertyOptional({ example: ["1", "2"], description: "Location id" })
  @IsOptional()
  locationId?: number[];

  @ApiPropertyOptional({ example: ["High School"], description: "Education degree" })
  @IsOptional()
  @IsString({ each: true })
  degree?: string[];

  @ApiPropertyOptional({ example: "Rob", description: "Search field" })
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiPropertyOptional({ example: "1", description: "Experience Min" })
  @IsOptional()
  experienceMin?: number;

  @ApiPropertyOptional({ example: "5", description: "Experience Max" })
  @IsOptional()
  experienceMax?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year min)" })
  @IsOptional()
  salaryYearMin?: number;

  @ApiPropertyOptional({ example: "6000", description: "Salary Range(per year max)" })
  @IsOptional()
  salaryYearMax?: number;

  @ApiPropertyOptional({ example: [], description: "Skills" })
  @IsOptional()
  @IsString({ each: true })
  skills?: string[];
}
