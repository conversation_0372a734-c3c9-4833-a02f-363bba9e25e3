import {
  Controller,
  Post,
  Body,
  Get,
  Patch,
  Delete,
  UseGuards,
  Query,
  Param,
  UseInterceptors,
  UploadedFile,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  Award,
  Candidate,
  CandidateDto,
  CandidatesService,
  Certificate,
  Education,
  Experience,
  Project,
  Skill,
  UpdateAwardDto,
  UpdateEducationDto,
  UpdateExperienceDto,
  UpdateCandidateDto,
  UpdateCertificateDto,
  UpdateProjectsDto,
  UpdateSkillsDto,
  SkillsService,
  CertificatesService,
  AwardsService,
  ProjectsService,
  EducationsService,
  ExperiencesService,
  CandidateFiltersDto,
  User,
  EducationDto,
  ExperienceDto,
  InviteCandidateDto,
  CandidateVerificationDto,
  ResendActivationMailDto
} from "@microservices/recruitment-db";
import { SkillsDto } from "libs/recruitment-db/src/lib/skills/dto/skills.dto";
import { CertificateDto } from "libs/recruitment-db/src/lib/certificates/dto/certificate.dto";
import { AwardDto } from "libs/recruitment-db/src/lib/awards/dto/award.dto";
import { ProjectsDto } from "libs/recruitment-db/src/lib/projects/dto/projects.dto";
import { FileInterceptor } from "@nestjs/platform-express";
// import { QdrantClient } from "@qdrant/qdrant-js";
// import { LangchainService } from "@microservices/integrations";
//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   candidateId: user["https://urecruits.com/candidateId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

// const langchainService = new LangchainService();
@ApiTags("Candidate")
@ApiBearerAuth("access-token")
@Controller("candidate")
export class CandidateController {
  constructor(
    private readonly candidatesService: CandidatesService,
    private skillsService: SkillsService,
    private certificatesService: CertificatesService,
    private awardsService: AwardsService,
    private projectsService: ProjectsService,
    private educationsService: EducationsService,
    private experiencesService: ExperiencesService,

  ) {}

  @ApiOperation({ summary: "Create candidate" })
  @ApiResponse({ status: 201, type: Candidate })
  @Post()
  create(@Body() dto: CandidateDto) {
    return this.candidatesService.createCandidate(dto);
  }

  @ApiOperation({ summary: "Invite candidate" })
  @ApiResponse({ status: 201, type: Candidate })
  @Post("/invite")
  inviteCandidate(@Body() dto: InviteCandidateDto) {
    return this.candidatesService.inviteCandidate(dto);
  }

  @ApiOperation({ summary: "Check verification for candidate account activation" })
  @ApiResponse({ status: 200, type: Candidate })
  @Get("/check-verification/:ticket")
  checkVerification(@Param("ticket") ticket: string) {
    return this.candidatesService.checkVerification(ticket);
  }

  @ApiOperation({ summary: "Candidate Verification" })
  @ApiResponse({ status: 200, type: Candidate })
  @Patch("/verification")
  verification(@Body() dto: CandidateVerificationDto) {
    return this.candidatesService.verification(dto);
  }

  @ApiOperation({ summary: "Resend Activate Email" })
  @ApiResponse({ status: 201, type: "any" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/send-verification")
  resendActivateEmail(@Body() dto:ResendActivationMailDto, @AuthUser() user: any) {
    return this.candidatesService.resendActivateEmail(dto);
  }

  @ApiOperation({ summary: "Get candidate profile" })
  @ApiResponse({ status: 200, type: Candidate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("candidate")
  getProfile(@AuthUser() user: any) {
    return this.candidatesService.getCandidateById(
      user["https://urecruits.com/candidateId"]
    );
  }

  @ApiOperation({ summary: "Get candidate profile by userId" })
  @ApiResponse({ status: 200, type: Candidate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get(":userId")
  @Permissions("recruiter")
  getCandidateProfile(@AuthUser() user: any, @Param("userId") userId: number) {
    return this.candidatesService.getCandidateByUserId(userId);
  }

  @ApiOperation({ summary: "Update candidate" })
  @ApiResponse({ status: 201, type: Candidate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch()
  @Permissions("candidate")
  update(@Body() dto: UpdateCandidateDto, @AuthUser() user: any) {
    return this.candidatesService.updateCandidate(
      dto,
      user["https://urecruits.com/candidateId"],
      user
    );
  }

  @ApiOperation({ summary: "Create skill" })
  @ApiResponse({ status: 201, type: Skill })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/skill")
  @Permissions("candidate")
  createSkill(@AuthUser() user: any, @Body() dto: SkillsDto) {
    return this.skillsService.create(
      user["https://urecruits.com/candidateId"],
      dto
    );
  }

  @ApiOperation({ summary: "Update skill" })
  @ApiResponse({ status: 201, type: Skill })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/skill")
  @Permissions("candidate")
  updateSkill(@Body() dto: UpdateSkillsDto, @AuthUser() user: any) {
    return this.skillsService.update({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Delete skill" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/skill")
  @Permissions("candidate")
  deleteSkill(@Body() dto: UpdateSkillsDto, @AuthUser() user: any) {
    return this.skillsService.delete({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Delete certificate" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/certificate")
  @Permissions("candidate")
  deleteCertificate(@Body() dto: UpdateCertificateDto, @AuthUser() user: any) {
    return this.certificatesService.delete({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Update certificate" })
  @ApiResponse({ status: 201, type: Certificate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/certificate")
  @Permissions("candidate")
  updateCertificate(@Body() dto: UpdateCertificateDto, @AuthUser() user: any) {
    return this.certificatesService.update({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Create certificate" })
  @ApiResponse({ status: 201, type: Certificate })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/certificate")
  @Permissions("candidate")
  createCertificate(@AuthUser() user: any, @Body() dto: CertificateDto) {
    return this.certificatesService.create(
      user["https://urecruits.com/candidateId"],
      dto
    );
  }

  @ApiOperation({ summary: "Delete award" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/award")
  @Permissions("candidate")
  deleteAward(@Body() dto: UpdateAwardDto, @AuthUser() user: any) {
    return this.awardsService.delete({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Update award" })
  @ApiResponse({ status: 201, type: Award })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/award")
  @Permissions("candidate")
  updateAward(@Body() dto: UpdateAwardDto, @AuthUser() user: any) {
    return this.awardsService.update({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Create award" })
  @ApiResponse({ status: 201, type: Award })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/award")
  @Permissions("candidate")
  createAward(@AuthUser() user: any, @Body() dto: AwardDto) {
    return this.awardsService.create(
      user["https://urecruits.com/candidateId"],
      dto
    );
  }

  @ApiOperation({ summary: "Delete project" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/project")
  @Permissions("candidate")
  deleteProject(@Body() dto: UpdateProjectsDto, @AuthUser() user: any) {
    return this.projectsService.delete({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Update project" })
  @ApiResponse({ status: 201, type: Project })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/project")
  @Permissions("candidate")
  updateProject(@Body() dto: UpdateProjectsDto, @AuthUser() user: any) {
    return this.projectsService.update({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
    });
  }

  @ApiOperation({ summary: "Create project" })
  @ApiResponse({ status: 201, type: Project })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/project")
  @Permissions("candidate")
  createProject(@AuthUser() user: any, @Body() dto: ProjectsDto) {
    return this.projectsService.create(
      user["https://urecruits.com/candidateId"],
      dto
    );
  }

  @ApiOperation({ summary: "Delete education" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/education")
  @Permissions("candidate")
  deleteEducation(@Body() dto: UpdateEducationDto, @AuthUser() user: any) {
    return this.educationsService.delete({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
      recruiterId: null,
    });
  }

  @ApiOperation({ summary: "Update education" })
  @ApiResponse({ status: 201, type: Education })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/education")
  @Permissions("candidate")
  updateEducation(@Body() dto: UpdateEducationDto, @AuthUser() user: any) {
    return this.educationsService.update({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
      recruiterId: null,
    });
  }

  @ApiOperation({ summary: "Create education" })
  @ApiResponse({ status: 201, type: Education })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/education")
  @Permissions("candidate")
  createEducation(@AuthUser() user: any, @Body() dto: EducationDto) {
    return this.educationsService.create({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
      recruiterId: null,
    });
  }

  @ApiOperation({ summary: "Delete experience" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/experience")
  @Permissions("candidate")
  deleteExperience(@Body() dto: UpdateExperienceDto, @AuthUser() user: any) {
    return this.experiencesService.delete({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
      recruiterId: null,
    });
  }

  @ApiOperation({ summary: "Update experience" })
  @ApiResponse({ status: 201, type: Experience })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/experience")
  @Permissions("candidate")
  updateExperience(@Body() dto: UpdateExperienceDto, @AuthUser() user: any) {
    return this.experiencesService.update({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
      recruiterId: null,
    });
  }

  @ApiOperation({ summary: "Create experience" })
  @ApiResponse({ status: 201, type: Experience })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/experience")
  @Permissions("candidate")
  createExperience(@AuthUser() user: any, @Body() dto: ExperienceDto) {
    return this.experiencesService.create({
      ...dto,
      candidateId: user["https://urecruits.com/candidateId"],
      recruiterId: null,
    });
  }

  @ApiOperation({ summary: "Get all candidates" })
  @ApiResponse({ status: 200, type: [Candidate] })
  @UseGuards(AuthGuard("jwt"))
  @Get("/get/all")
  getAllCandidates(@Query() query: CandidateFiltersDto) {
    return this.candidatesService.getAllCandidates(query);
  }

  @ApiOperation({ summary: "Get candidate by userId" })
  @ApiResponse({ status: 200, type: User })
  @Get("/public-candidate/:candidateId")
  getPublicCandidateId(@Param("candidateId") candidateId: number) {
    return this.candidatesService.getPublicCandidateId(candidateId);
  }

  @ApiOperation({ summary: "Get candidate by userId" })
  @ApiResponse({ status: 200, type: User })
  @Get("/public-candidate/user/:candidateId")
  getPublicUserId(@Param("candidateId") candidateId: number,@Query()query:any) {
    if (query.hasOwnProperty('showAllDetails')){
      return this.candidatesService.getPublicCandidateId(candidateId,true);
    }else{
      return this.candidatesService.getPublicUserId(candidateId);
    }
  }

  @ApiOperation({ summary: "Get Statistic Home-Page" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/home-page")
  @Permissions("candidate")
  getStatsHomePage(@AuthUser() user: any) {
    return this.candidatesService.getStatsHomePage(
      user["https://urecruits.com/userId"],
      user["https://urecruits.com/candidateId"]
    );
  }

  @ApiOperation({ summary: "Upload Candidate Resume" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"))
  @ApiConsumes("multipart/form-data")
  @UseInterceptors(FileInterceptor("upload"))
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        upload: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  @Post("/resume")
  @Permissions("candidate")
  uploadAndParseCandidateResume(
    @AuthUser() user: any,
    @UploadedFile() upload: Express.Multer.File
  ) {
    return this.candidatesService.uploadAndParseCandidateResume(upload, user);
  }

  // @ApiOperation({ summary: "Feed candidate to Qdrant" })
  // @ApiResponse({ status: 200 })
  // @Post("/qdrant/seed")
  // async feedCandidateToQdrant() {
  //   const client = new QdrantClient({ url: process.env.QDRANT_DATABASE_URL, apiKey: process.env.QDRANT_API_KEY });

  //   // Fetch jobs in batches
  //   const batchSize = 10;
  //   let offset = 0;
  //   let hasMoreJobs = true;

  //   while (hasMoreJobs) {
  //     const candidates = await this.candidatesService.getAllCandidates({ limit: batchSize, offset });
  //     if (!candidates.rows?.length) {
  //       hasMoreJobs = false;
  //       break;
  //     }

  //     const points = [];
  //     for (let candidate of candidates.rows) {
  //       const data =await this.candidatesService.getUserDataForQdrant(candidate);
  //       if(!data){
  //         continue;
  //       }
  //       try {
  //         const candidateVector = await langchainService.getEmbeddings(data.str);
  //         points.push({
  //           id:data.id,
  //           vector:candidateVector,
  //           payload:data.payload
  //         })
  //       } catch (error) {
  //         console.error("Error fetching embeddings:", error);
  //       }

  //       // Add a delay to respect rate limits
  //       await new Promise(resolve => setTimeout(()=>{
  //         resolve(true)
  //       }, 3000));
  //     }

  //     try {
  //       const response = await client.upsert("users", { wait: true, points });
  //       console.log("Upsert Response:", response);
  //     } catch (error) {
  //       console.error("Upsert failed:", error);
  //     }

  //     // Move to the next batch
  //     offset += batchSize;
  //   }
  // }
}
