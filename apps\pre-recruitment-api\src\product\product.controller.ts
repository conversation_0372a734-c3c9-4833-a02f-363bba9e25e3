import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  UseGuards,
  Body,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { PaymentService } from '@microservices/recruitment-db';

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   candidateId: user["https://urecruits.com/candidateId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags('Payment')
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @ApiOperation({
    summary: 'Get company payment methods.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns an array of valid payment methods.',
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-payment-methods')
  @Permissions('payment-integration:view')
  getPaymentMethods(@AuthUser() user: any) {
    return this.paymentService.getPaymentMethods(user["https://urecruits.com/companyId"]);
  }

}
