import { Controller, Post, Body, UseGuards, Get, Delete } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";

import { FileInterceptor } from "@nestjs/platform-express";
import { FileService } from "../file/file.service";
import { HellosignIntegrationService } from "@microservices/recruitment-db";
import { HellosignIntegrationDto } from "libs/recruitment-db/src/lib/hellosign/dto/hellosign-integration.dto";
import { HellosignIntegration } from "libs/recruitment-db/src/lib/hellosign/hellosign-integration.model";

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("hellosign-integration")
@ApiBearerAuth("access-token")
@Controller("hellosign-integration")
export class HellosignIntegrationController {
  constructor(
    private readonly hellosignIntegrationService: HellosignIntegrationService
  ) {}

  @ApiOperation({ summary: "Integrate Hellosign" })
  @ApiResponse({ status: 200, type: HellosignIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post()
  @Permissions("offer:add")
  createHellosignIntegration(
    @Body() dto: HellosignIntegrationDto,
    @AuthUser() user: any
  ) {
    return this.hellosignIntegrationService.create(
      dto,
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Get Offer By Id" })
  @ApiResponse({ status: 200, type: HellosignIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("")
  @Permissions("offer:view")
  getHellosignIntegration(@AuthUser() user: any) {
    return this.hellosignIntegrationService.getHellosignIntegration(
      user["https://urecruits.com/companyId"]
    );
  }

  @ApiOperation({ summary: "Remove Hellosign" })
  @ApiResponse({ status: 200, type: HellosignIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("remove")
  @Permissions("offer:add")
  removeHellosignIntegration(@AuthUser() user: any) {
    return this.hellosignIntegrationService.removeHellosignIntegration(
      user["https://urecruits.com/companyId"]
    );
  }
}
