import { Logger, ValidationPipe } from "@nestjs/common";
import { NestFactory } from "@nestjs/core";
import { Transport } from "@nestjs/microservices";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { AppModule } from "./app.module";
import * as express from 'express';
import { QdrantMiddleware } from '@microservices/qdrant';
import { KinesisMiddleware } from '@microservices/kinesis';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const globalPrefix = "api";
  app.use(express.json({ limit: '1000mb' }));
  app.use(express.urlencoded({ limit: '1000mb', extended: true }));
  app.setGlobalPrefix(globalPrefix);
  app.connectMicroservice({
    transport: Transport.REDIS,
    options: {
      url: process.env.REDIS_URL || "redis://localhost:6379",
    },
  });
  await app.startAllMicroservices();
  app.use(new QdrantMiddleware().use)
  app.use(new KinesisMiddleware().use)
  app.useGlobalPipes(new ValidationPipe());
  const config = new DocumentBuilder()
    .setTitle("uRecruits")
    .setDescription("Recruitment api docs")
    .setVersion("1.0.0")
    .addServer(`${process.env.RECRUITMENT_API_URI}`, "Local environment")
    .addServer("https://mint-cat-locally.ngrok-free.app", "Testing environment")
    .addServer("https://recruitment-micro.urecruits.com", "Staging")
    .addBearerAuth(
      {
        description: `[just text field] Please enter token in following format: Bearer <JWT>`,
        name: "Authorization",
        bearerFormat: "Bearer",
        scheme: "Bearer",
        type: "http",
        in: "Header",
      },
      "access-token"
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup("/api", app, document);
  app.enableCors();
  const port = process.env.PRE_RECRUITMENT_PORT || 3001;
  await app.listen(port);
  Logger.log(
    `🚀 Application is running on: http://localhost:${port}/${globalPrefix}`
  );
}

bootstrap();
