{"root": "libs/calendars", "sourceRoot": "libs/calendars/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/calendars/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/calendars"], "options": {"jestConfig": "libs/calendars/jest.config.js", "passWithNoTests": true}}}, "tags": []}