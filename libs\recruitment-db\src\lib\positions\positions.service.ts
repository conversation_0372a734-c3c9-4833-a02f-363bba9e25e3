import { Injectable } from '@nestjs/common';
import * as data from './new_data.json';
import { InjectModel } from '@nestjs/sequelize';
import { Position } from './positions.model';
import { Op } from "sequelize";

@Injectable()
export class PositionsService {
  constructor(
    @InjectModel(Position) private positionsRepository: typeof Position,
  ) {}

  async seed() {
    await this.positionsRepository.bulkCreate(data, {
      validate: true,
      // individualHooks: true
    }).then(async () => this.positionsRepository.sequelize.query(`ALTER SEQUENCE "${this.positionsRepository.tableName}_id_seq" RESTART WITH ${await this.positionsRepository.count() + 1}`)).catch((error) => {
      console.log(error);
    });
    return true;
  }

  async findByLabel(find?: string) {
    let where = {}
    if (find && find.length > 0) {
      where = {
        label: {
          [Op.iLike]: `%${find}%`,
        },
      }
    }
    return await this.positionsRepository.findAll({
      where,
      limit: 30,
    });
  }

  async findByLabelWithLimit(data:any) {
    let where = {};
    if (data.find && data.find.length > 0) {
      where = {
        label: {
          [Op.iLike]: `%${data.find}%`,
        },
      };
    }
    return await this.positionsRepository.findAll({
      where,
      limit: 30,
      offset: data.offset || 0,
    });
  }

  async allPositions({limit,offset}:{limit:number,offset:number}) {
    const positions = await this.positionsRepository.findAll({limit,offset});
    return positions;
  }
}
