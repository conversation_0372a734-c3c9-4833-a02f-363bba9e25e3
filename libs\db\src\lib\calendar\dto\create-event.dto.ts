import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  ArrayMinSize,
  IsArray,
  IsBoolean,
  IsDateString,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateEventDto {
  @ApiProperty({ example: '2023-01-12T10:30:00Z', description: 'Date in GMT. When an event begins.', required: true })
  @IsDateString()
    startGmt: Date;

  @ApiPropertyOptional({ example: '2023-01-12T11:30:00Z', description: 'Date in GMT. When an event ends.', required: false })
  @IsDateString()
  @IsOptional()
    endGmt?: Date;

  @ApiProperty({ example: 'Event title', description: 'Title of event', required: true })
  @IsString()
  @IsNotEmpty()
    title: string;

  @ApiPropertyOptional({ example: 'Event description', description: 'Description of event', required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  description: string;

  @ApiProperty({ example: [{id:1,eventId:''}], description: 'IDs of event users', required: true })
  @IsNumber()
    users: Array<any>;

  @ApiProperty({ example: 'true/false', description: 'Reminder of the event' })
  @IsBoolean()
  @IsNotEmpty()
    remindMe: boolean;

  @ApiPropertyOptional({ example: '10 minutes', description: 'Remind me before time' })
  @IsOptional()
  timeBefore: string;

  @ApiPropertyOptional({ example: 11, description: 'Job ID' })
  @IsNotEmpty()
  @IsNumber()
  jobId: number;

  @ApiPropertyOptional({ example: 16, description: 'ID of the user who has booked appointment' })
  @IsNotEmpty()
  @IsNumber()
  candidateId: number;

  @ApiProperty({ example: "GHVWAS672330DHJBHJQW87YW", description: 'TWILIO room Id', required: true })
  @IsOptional()
  roomId?: string;
}
