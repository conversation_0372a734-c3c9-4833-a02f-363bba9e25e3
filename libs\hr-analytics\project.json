{"root": "libs/hr-analytics", "sourceRoot": "libs/hr-analytics/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/hr-analytics/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/hr-analytics"], "options": {"jestConfig": "libs/hr-analytics/jest.config.js", "passWithNoTests": true}}}, "tags": []}