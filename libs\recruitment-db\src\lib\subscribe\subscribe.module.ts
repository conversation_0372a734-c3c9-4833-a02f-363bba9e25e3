import { EmailModule } from '@microservices/email';
import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Candidate } from "../candidates/candidates.model";
import { Jobs } from "../job/job.model";
import { JobModule } from '../job/job.module';
import { RoundModule } from "../rounds/round.module";
import { UsersModule } from '../users/users.module';
import { Subscribe } from './subscribe.model';
import { SubscribeService } from "./subscribe.service";
import { TemporalWorkflowModule } from '../temporalWorkflow/temporal.module';
import { HttpModule } from '@nestjs/axios';
import { BackgroundModel } from '../background-screening/background.model';
import { WorkflowModule } from '../workflow/workflow.module';
import { CandidatesModule } from '../candidates/candidates.module';
import { UserAssignmentsModule } from '../user-assignments/user-assignments.module';


@Module({
  imports: [SequelizeModule.forFeature([Subscribe,Jobs,Candidate, BackgroundModel]), RoundModule,EmailModule,UsersModule,JobModule,TemporalWorkflowModule,HttpModule,WorkflowModule,CandidatesModule,UserAssignmentsModule,EmailModule],
  providers: [SubscribeService],
  exports: [SequelizeModule, SubscribeService],
})
export class SubscribeModule {}
