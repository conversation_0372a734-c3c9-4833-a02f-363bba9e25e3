import { ApiProperty } from "@nestjs/swagger";
import { Column, DataType, Model, Table } from "sequelize-typescript";

@Table({
  tableName: "time-duration",
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class TimeDuration extends Model<TimeDuration> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "12",
    description: "Time duration would be used to manage time duration schema",
  })
  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  timeDuration: string;
}
