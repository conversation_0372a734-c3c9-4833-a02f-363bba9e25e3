import { from } from "rxjs";
import { eventType } from "../../consts";

export const membersAgePieAnalyticsQueryBody = (companyId) => ({
  size: 0,
  query: {
    bool: {
      must: [
        { term: { companyId } },
        { term: { user_type: "recruiter" } }
      ]
    }
  },
  aggs: {
    age_ranges: {
      range: {
        script: {
          source: `
            long birthDateMillis = doc['birthDate'].value.toInstant().toEpochMilli();
            long currentMillis = System.currentTimeMillis();
            long ageMillis = currentMillis - birthDateMillis;
            long age = ageMillis / (1000L * 60 * 60 * 24 * 365); // approximate age in years
            return age;
          `
        },
        ranges: [
          { from: 18, to: 25, key: "18-25" },
          { from: 25, to: 35, key: "25-35" },
          { from: 35, to: 45, key: "35-45" },
          { from: 45, to: 55, key: "45-55" },
          { from: 55, to: 65, key: "55-65" },
          { from: 65, key: "65+" }
        ]
      },
      aggs: {
        distinct_user_count: {
          cardinality: {
            field: "userId"
          }
        }
      }
    }
  }
});

// export const jobsAreaAnalyticsQueryBody = (startOf, endOf, status) => ({
//   size: 0,
//   query: {
//     bool: {
//       filter: [
//         {
//           term: {
//             [`${eventType.JOB_POSTED}.status.keyword`]: status,
//           },
//         },
//         {
//           range: {
//             [`${eventType.JOB_POSTED}.createdAt`]: {
//               gte: startOf,
//               lte: endOf,
//             },
//           },
//         },
//       ],
//     },
//   },
//   aggs: {
//     jobs_status: {
//       date_histogram: {
//         field: `${eventType.JOB_POSTED}.createdAt`,
//         interval: "day",
//         time_zone: "+00:00",
//         min_doc_count: 0,
//       },
//     },
//   },
// });


export const membersAnalyticsQueryBody = (limit: number, offset: number, companyId: number, queryParams: any) => {
  const query: any = {
    size: limit,
    from: offset,
    query: {
      bool: {
        must: [
          { term: { companyId } },
          { term: { user_type: 'recruiter' } },
        ],
      },
    },
    aggs: {
      distinct_users: {
        terms: {
          field: 'userId', // Changed from 'id' to 'userId'
          size: 10000,
        },
        aggs: {
          latest_record: {
            top_hits: {
              size: 1,
              sort: [
                {
                  createdAt: { order: 'desc' }, // Sort by createdAt in descending order
                },
              ],
            },
          },
        },
      },
      total_users: {
        cardinality: {
          field: 'userId', // Changed from 'id' to 'userId'
        },
      },
    },
  };

  // Optionally filter by action if provided in queryParams
  if (queryParams.action) {
    query.query.bool.must.push({
      term: { 'data.action': queryParams.action },
    });
  }

  return query;
};


