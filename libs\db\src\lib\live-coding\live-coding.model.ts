import { <PERSON><PERSON>sTo, Column, DataType, Foreign<PERSON>ey, HasOne, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { AssessmentDatabase } from '../assessment-database/assessmentDatabase.model';
import { Package } from '../packages/packages.model';

export enum STATUS {
  active = 'ACTIVE',
  draft = 'DRAFT',
  deleted = 'DELETED',
}
export enum TYPE {
  liveTask = 'live-task',
  takeHome = 'take-home',
}

@Table({
  tableName: 'live-coding',
})
export class LiveCoding extends Model<LiveCoding> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    example: '98fc959b-25a3-4889-8ec3-ade6255a019a',
    description: 'UUID version 4',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    taskId: string;

  @ApiProperty({
    example: 'The Stolen Breakfast Drone',
    description: 'Name of live coding',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    name: string;

  @ApiProperty({ example: 'You will need to concate two array', description: 'Description of live coding' })
  @Column({ type: DataType.TEXT, allowNull: false })
    description: string;

  @ApiProperty({ example: 'function someData(){}', description: 'Teplate code if needed.' })
  @Column({ type: DataType.TEXT, allowNull: false })
    starterCode: string;

  @ApiProperty({ example: 'function someData(){}', description: 'Teplate code if needed.' })
  @Column({ type: DataType.TEXT, allowNull: false })
    instruction: string;

  @ApiProperty({ example: 'live-task', description: 'Type of live coding assessment' })
  @Column({ type: DataType.ENUM('live-task', 'take-home'), allowNull: false })
    assessmentType: TYPE;

  @ApiProperty({ example: 'ACTIVE', description: 'Type of live coding assessment' })
  @Column({ type: DataType.ENUM('DRAFT', 'ACTIVE'), allowNull: true })
    status: STATUS;

  @ApiProperty({ example: '1', description: 'Id of related language' })
  @Column({ type: DataType.INTEGER, allowNull: false })
  languageId: number;

  @ApiProperty({ example: '1', description: 'Company Id' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;

  @ForeignKey(() => Package)
  @Column({
    type: DataType.INTEGER,
    allowNull : true
  })
  packageId: number;

  @ForeignKey(()=> AssessmentDatabase)
  @Column({ type: DataType.INTEGER})
  databaseId?: number;
}
