export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function parseDate(unixTimestamp: string, type: string | null) {
  const parsedStamp = (parseInt(unixTimestamp) + 86400 )* 1000; //added one day
  const newDate = new Date(parsedStamp);
  if (type === 'start') {
    newDate.setUTCHours(0, 0, 0)
    const startDate = new Date(newDate);
    return startDate.toUTCString();
  } else {
    newDate.setUTCHours(23, 59, 59)
    const endDate = new Date(newDate);
    return endDate.toUTCString();
  }
}
