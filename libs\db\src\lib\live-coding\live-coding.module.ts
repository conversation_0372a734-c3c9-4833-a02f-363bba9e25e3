import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { LiveCoding } from './live-coding.model';
import { Package } from '../packages/packages.model';
import { EmailModule } from '@microservices/email';
import { LiveCodingDrafts } from './live-drafts/live-drafts.model';
import { LiveCodingService } from './live-coding.service';
@Module({
  imports: [SequelizeModule.forFeature([LiveCoding,Package,LiveCodingDrafts]), EmailModule],
  providers: [LiveCodingService],
  exports: [SequelizeModule, LiveCodingService],
})
export class LiveCodingModule {}
