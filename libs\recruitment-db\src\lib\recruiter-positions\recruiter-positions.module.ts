import { Module } from '@nestjs/common';
import { RecruiterPositionsService } from './recruiter-positions.service';
import { SequelizeModule } from '@nestjs/sequelize';
import { RecruiterPositions } from './recruiter-positions.model';

@Module({
  providers: [RecruiterPositionsService],
  imports: [SequelizeModule.forFeature([RecruiterPositions])],
  exports: [SequelizeModule, RecruiterPositionsService],
})
export class RecruiterPositionsModule {}
