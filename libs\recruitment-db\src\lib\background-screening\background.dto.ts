import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { FilterCompanyJobsDto } from "../job/dto/filter-job.dto";
import { BGVStatus } from "./background.enum";

export class TypicalTableDto {
  @ApiPropertyOptional({ example: "Designer", description: "Search" })
  @IsOptional()
  search?: string;

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;
}

export class FilterJobsBackgroundDto extends TypicalTableDto {
  @ApiPropertyOptional({ example: [], description: "Job Location" })
  @IsOptional()
  locations: [string];
}

export class FilterCandidatesBackgroundDto extends TypicalTableDto{
  @ApiPropertyOptional({ example: "Universal", description: "3rd Party Vendor" })
  @IsOptional()
  vendor : string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date Range start limit fir BGV status" })
  @IsOptional()
  dateFrom?: string;

  @ApiPropertyOptional({ example: "2022-10-13", description: "Date Range end limit fir BGV status" })
  @IsOptional()
  dateTo?: string;

  @ApiPropertyOptional({ example: "Verified", description: "Candidate BGV Status" })
  @IsOptional()
  status?: string;

}

export class BackgroundDto {
  @ApiProperty({ example: "1", description: "User Id" })
  userId: number;

  @ApiProperty({ example: "1", description: "Job Id" })
  jobId: number;

  @ApiProperty({ example: "1", description: "Company Id" })
  companyId: number;

  @ApiProperty({
    example: "Universal",
    description: "Vendor Selected for Background Screening",
  })
  vendor: string;

  @ApiProperty({ example: "Verified", description: "status of a background" })
  status: BGVStatus;

}

export class ChangeBackgroundDto{
  @ApiProperty({ example: "1", description: "User Id" })
  userId: number;

  @ApiProperty({ example: "1", description: "Job Id" })
  jobId: number;

  @ApiProperty({ example: "Verified", description: "status of a background" })
  status: BGVStatus;
}
