import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { DomainAssessment } from './domain-assessment.model';
import { DomainQuestions } from './domain-questions.model';
import { DomainQuestionsService } from './domain-questions.service';

@Module({
  imports: [
    SequelizeModule.forFeature([
      DomainQuestions,
      DomainAssessment,
    ]),
  ],
  providers: [DomainQuestionsService],
  exports: [SequelizeModule, DomainQuestionsService],
})
export class DomainQuestionsModule {}
