import { ApiProperty } from "@nestjs/swagger";

export class UpdatePriceDto{
  @ApiProperty({example:'price_xxx',description:"Price Id of the Stripe Product"})
  priceId?: string;

  @ApiProperty({example:'prod_xxx',description:"Product Id of the Stripe Product"})
  productId?: string;

  @ApiProperty({example:'500',description:"Price of the Stripe Price"})
  price?: number;

  @ApiProperty({example:'5',description:"Free quanity incase of addons products"})
  freeQty?: number;

  @ApiProperty({example:'price_xxx',description:"Active status of the Stripe price"})
  active?: boolean;

  @ApiProperty({example:'price_xxx',description:"Period of the price created for the product"})
  monthlyYearly?: string;
}

export class UpdateAllPricePlanPackageDto{
  @ApiProperty({example:'prod_xxx',description:"Product Id of the Stripe Product"})
  productId?: string;

  @ApiProperty({example:'5',description:"PlanId incase to be set in price"})
  planId?: number;

  @ApiProperty({example:'9',description:"Plan PackageId to be set in price"})
  planPackageId?: number;
}


export class GetPricesDto{

  @ApiProperty({example:'1',description:"Price id in the database"})
  id?: number;

  @ApiProperty({example:'price_xxx',description:"Price Id of the Stripe Product"})
  priceId?: string;

  @ApiProperty({example:'prod_xxx',description:"Product Id of the Stripe Product"})
  productId?: string;

  @ApiProperty({example:'5',description:"Plan Id of the Product"})
  planId?: number;

  @ApiProperty({example:'3',description:"packagePlanId of the Product"})
  planPackageId?: number;

  @ApiProperty({example:'true',description:"Active status of the Stripe price"})
  active?: boolean;

  @ApiProperty({example:'true',description:"default price for default package and plan"})
  isDefaultPackagePlan?: boolean;

  @ApiProperty({example:'m',description:"Period of the price created for the product"})
  monthlyYearly?: string;

  @ApiProperty({example:'productId',description:"Name of the sort by property"})
  sortBy?: string;

  @ApiProperty({example:'ASC/DESC',description:"type of the sort"})
  sortType?: string;
  
}