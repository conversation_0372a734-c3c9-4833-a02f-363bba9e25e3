{"root": "apps/integrations-api", "sourceRoot": "apps/integrations-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/integrations-api", "main": "apps/integrations-api/src/main.ts", "tsConfig": "apps/integrations-api/tsconfig.app.json", "assets": ["apps/integrations-api/src/assets"], "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/integrations-api/src/environments/environment.ts", "with": "apps/integrations-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "integrations-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/integrations-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/integrations-api"], "options": {"jestConfig": "apps/integrations-api/jest.config.js", "passWithNoTests": true}}}, "tags": []}