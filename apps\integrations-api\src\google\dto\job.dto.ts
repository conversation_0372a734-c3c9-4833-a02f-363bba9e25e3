import { ApiProperty } from '@nestjs/swagger';

export class JobDto {
  @ApiProperty({
    example: '123abc',
    description: 'Job requisition ID, aka Posting ID. Unique per job. Can be ID from uRecruits database. The maximum number of allowed characters is 225.',
    required: true,
  })
  readonly requisitionId: string;

  @ApiProperty({
    example: 'Software Engineer.',
    description: 'The job title. The maximum number of allowed characters is 500.',
    required: true,
  })
  readonly title: string;

  @ApiProperty({
    example: 'The description of the job.',
    description: 'This field accepts and sanitizes HTML input, and accepts bold, italic, ordered list, and unordered list markup tags. The maximum number of allowed characters is 100,000.',
    required: true,
  })
  readonly description: string;

  @ApiProperty({
    example: 'https://www.example.org/job-posting/123',
    description: 'Use this URI field to direct an applicant to a website, for example to link to an online application form. The maximum number of allowed characters for each entry is 2,000.',
    required: false,
  })
  readonly jobApplicationUrl: string;

  @ApiProperty({
    example: '1600 Amphitheatre Parkway, Mountain View, CA 94043',
    description: 'Job location. The maximum number of allowed characters is 500.',
    required: false,
  })
  readonly address: string;

  @ApiProperty({
    example: 'en-US',
    description: 'Language code.',
    required: false,
  })
  readonly languageCode: string;
}
