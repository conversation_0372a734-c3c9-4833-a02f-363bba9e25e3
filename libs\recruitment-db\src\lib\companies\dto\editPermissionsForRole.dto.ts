import { ApiProperty } from "@nestjs/swagger";

export class EditPermissionsDto {
  @ApiProperty({ example: ['resource:read'], description: "permissions array" })
  permissions:any[]

  @ApiProperty({ example: [], description: "permissions array" })
  dbPermissions:any[]

  @ApiProperty({example:'',description:"auth0 id of role"})
  roleId:string;

  @ApiProperty({example:"Admin", description:"Role name"})
  label:string;
}
