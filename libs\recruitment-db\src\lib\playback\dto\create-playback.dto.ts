import { BelongsTo, Column, DataType, ForeignKey, HasMany, HasOne, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';

export class PlaybackDto {
  @ApiProperty({
    example: '123456',
    description: 'Playback Id',
  })
    playbackId: string;

  @ApiProperty({
    example: '123456',
    description: 'Playback Id',
  })
  @IsOptional()
    assessmentId: string;

  @ApiProperty({ example: 'take-home', description: 'type of assessment' })
    assessmentType?: string;

  @ApiProperty({ example: "1", description: "User ID" })
    candidateId: number;
  
  @ApiProperty({ example: "1", description: "Job ID" })
    jobId: number;
}
