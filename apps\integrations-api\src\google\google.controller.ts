import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GoogleService } from './google.service';
import { JobDto } from './dto/job.dto';

// TODO: this controller should be removed after the job is finished
@ApiTags('Google')
@Controller('google')
export class GoogleController {
  constructor(private readonly googleService: GoogleService) {}

  @ApiOperation({ summary: 'Create a Job.' })
  @ApiResponse({
    status: 201, description: 'Returns a Job object.',
  })
  @ApiResponse({
    status: 400,
    description: 'Returns an Error object.',
  })
  @Post('/create-job')
  createJob(
    @Body() dto: JobDto,
  ) {
    return this.googleService.createJob(dto);
  }

  @ApiOperation({
    summary: 'Update a Job.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns a Job object.',
  })
  @ApiResponse({
    status: 400,
    description: 'Returns an Error object.',
  })
  @Put('/update-job/:jobId')
  updateJob(
    @Param('jobId') jobId: string,
    @Body() dto: JobDto,
  ) {
    return this.googleService.updateJob(jobId, dto);
  }

  @ApiOperation({
    summary: 'Delete a Job.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns a Job object.',
  })
  @ApiResponse({
    status: 400,
    description: 'Returns an Error object.',
  })
  @Delete('/delete-job/:jobId')
  deleteJob(
    @Param('jobId') jobId: string,
  ) {
    return this.googleService.deleteJob(jobId);
  }

  @ApiOperation({
    summary: 'List all jobs. If present optional parameter "requisitionId" result will be filtered by that value.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns an Array of Job objects.',
  })
  @ApiResponse({
    status: 400,
    description: 'Returns an Error object.',
  })
  @Get('/list-jobs/:requisitionId?')
  listJobs(
    @Param('requisitionId') requisitionId: string,
  ) {
    return this.googleService.listJobs(requisitionId);
  }

  @ApiOperation({
    summary: 'Retrieve a Job by ID (Google side ID).',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns a Job object.',
  })
  @ApiResponse({
    status: 400,
    description: 'Returns an Error object.',
  })
  @Get('/retrieve-job/:jobId')
  retrieveJob(
    @Param('jobId') jobId: string,
  ) {
    return this.googleService.retrieveJob(jobId);
  }
}
