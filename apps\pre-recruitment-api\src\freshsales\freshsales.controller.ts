import {
    <PERSON>,
    <PERSON>,
    Logger,
    Body
  } from "@nestjs/common";
  import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
  } from "@nestjs/swagger";
  import {
   FreshsalesService, FreshsalesDto
  } from "@microservices/recruitment-db";

  //   companyId: user["https://urecruits.com/companyId"],
  //   tenantId: user["https://urecruits.com/tenantId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   userId: user["https://urecruits.com/userId"],

  @ApiTags("Freshsales")
  @ApiBearerAuth("access-token")
  @Controller("freshsales")
  export class FreshsalesController {
    constructor(private readonly freshsalesservice: FreshsalesService) {}

    @ApiOperation({ summary: "Upsert Record" })
    @ApiResponse({ status: 200 })
    @Post("/upsert")
    async upsertContact(@Body() dto:FreshsalesDto){
           return await this.freshsalesservice.upsertContact(dto,'contactUs');
    }
  }
