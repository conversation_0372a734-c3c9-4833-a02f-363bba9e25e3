import path = require('path');
import * as fs from 'fs';
import { NativeConnection, Worker } from '@temporalio/worker';
import { EmailService } from 'libs/email/src/lib/email.service';
import { ActivitiesService } from './activities/activities.service';
import { encodedCodeString } from './temporal/workflowCode';


const clientCert = fs.readFileSync(process.env.CLIENT_CA_PATH);
const clientKey = fs.readFileSync(process.env.CLIENT_KEY_PATH);

export const WorkerProvider = [
  {
    provide: 'RECRUITER_WORKER',
    inject: [ActivitiesService, EmailService],
    useFactory: async (activitiesService: ActivitiesService) => {
      const activities = {
        workflowStart: activitiesService.workflowStart.bind(activitiesService),
        appointmentBooked:
          activitiesService.appointmentBooked.bind(activitiesService),
        interviewStarted:
          activitiesService.interviewStarted.bind(activitiesService),
        interviewCompleted:
          activitiesService.interviewCompleted.bind(activitiesService),
        roundCompleted:
          activitiesService.roundCompleted.bind(activitiesService),
        roundRejected: activitiesService.roundRejected.bind(activitiesService),
        roundInitiated:
          activitiesService.roundInitiated.bind(activitiesService),
        changeRoundStatus:
          activitiesService.changeRoundStatus.bind(activitiesService),
        proposeTimeSlot:
          activitiesService.proposeTimeSlot.bind(activitiesService),
        informTimeSlot:
          activitiesService.informTimeSlot.bind(activitiesService),
        scoreUpdateReminder:
          activitiesService.scoreUpdateReminder.bind(activitiesService),
        interviewReminder:
          activitiesService.interviewReminder.bind(activitiesService),
        assessmentInitiated:
          activitiesService.assessmentInitiated.bind(activitiesService),
        assessmentSubmitted:
          activitiesService.assessmentSubmitted.bind(activitiesService),
        ReviewAssessmentReminder:
          activitiesService.ReviewAssessmentReminder.bind(activitiesService),
        domainAssessmentCompleted:
          activitiesService.domainAssessmentCompleted.bind(activitiesService),
        assessmentDeadline:
          activitiesService.assessmentDeadline.bind(activitiesService),
        domainAssessmentRejected:
          activitiesService.domainAssessmentRejected.bind(activitiesService),
        changeOfferLetterStatus:
          activitiesService.changeOfferLetterStatus.bind(activitiesService),
      };

      const workflowCode = Buffer.from(encodedCodeString, 'base64').toString(
        'utf-8'
      );

      const connectionOptions = {
        address: `${process.env.TEMPORAL_SERVER}`,
        tls: {
          clientCertPair: {
            crt: clientCert,
            key: clientKey,
          },
        },
      };

      const connection = await NativeConnection.connect(connectionOptions);
      const worker = await Worker.create({
        workflowBundle: {
          code: workflowCode,
        },
        taskQueue: 'recruitment',
        activities,
        connection,
        namespace: `${process.env.TEMPORAL_NAMESPACE}`,
      });
      worker.run();
      console.log('temporal worker start');
      return worker;
    },
  },
];
