import {
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Candidate } from "./candidates.model";
import { UsersService } from "../users/users.service";
import { Auth0Service } from "@microservices/auth";
import { CandidateDto } from "./dto/candidate.dto";
import { IndustriesService } from "../industries/industries.service";
import { SkillsService } from "../skills/skills.service";
import { CertificatesService } from "../certificates/certificates.service";
import { AwardsService } from "../awards/awards.service";
import { ProjectsService } from "../projects/projects.service";
import { EducationsService } from "../educations/educations.service";
import { ExperiencesService } from "../experiences/experiences.service";
import { RolesService } from "../roles/roles.service";
import { User } from "../users/users.model";
import { UpdateCandidateDto } from "./dto/update-candidate.dto";
import { Industry } from "../industries/industries.model";
import { CandidateFiltersDto } from "./dto/candidate-filters.dto";
import { Op, Sequelize } from "sequelize";
import { Location } from "../locations/location.model";
import { Position } from "../positions/positions.model";
import { Skill } from "../skills/skills.model";
import { Experience } from "../experiences/experiences.model";
import { Education } from "../educations/educations.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { Jobs } from "../job/job.model";
import { Company } from "../companies/companies.model";
import { Recruiter } from "../recruiters/recruiters.model";
import { FileService } from "apps/pre-recruitment-api/src/file/file.service";
import { LangchainService } from "@microservices/integrations";
import { v4 as uuidv4 } from 'uuid';
import { EmailService } from "@microservices/email";
import * as bcrypt from "bcrypt";
import { CandidateVerificationDto, InviteCandidateDto, ResendActivationMailDto } from "./dto/invite-candidate.dto";
import { Role } from "../roles/roles.model";

@Injectable()
export class CandidatesService {
  constructor(
    @InjectModel(Candidate) private profileRepository: typeof Candidate,
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(Subscribe) private subscribeRepository: typeof Subscribe,
    @InjectModel(Company) private companyRepository: typeof Company,
    private userService: UsersService,
    private industriesService: IndustriesService,
    private skillsService: SkillsService,
    private certificatesService: CertificatesService,
    private awardsService: AwardsService,
    private projectsService: ProjectsService,
    private educationsService: EducationsService,
    private experiencesService: ExperiencesService,
    private rolesService: RolesService,
    private auth0Service: Auth0Service,
    private emailService: EmailService,
    private readonly fileService: FileService,
    private readonly langchainService: LangchainService
  ) { }

  async createCandidate(dto: CandidateDto) {
    await this.userService.isExistUser(dto.email);
    const user = await this.userService.createUser(dto);
    const candidate = await this.profileRepository.create({
      ...{},
      ...dto,
      userId: user.id,
      status: 'Active',
    });
    // const candidate = await this.profileRepository.create({
    //   candidate_type: dto.candidate_type,
    //   positionId: dto.positionId,
    //   userId: user.id,
    //   status: 'Active',
    // });
    const auth0User = await this.auth0Service.createCandidate({
      ...dto,
      candidateId: candidate.id,
      userId: user.id,
    });
    Object.assign(user, { authId: auth0User.user_id });
    await user.save();
    if (dto.industries) {
      for (const item of dto.industries) {
        await candidate.$add("industries", [item.id]);
      }
    }
    await this.skillsService.create(candidate.id);
    await this.certificatesService.create(candidate.id);
    await this.awardsService.create(candidate.id);
    await this.projectsService.create(candidate.id);
    await this.educationsService.create({ candidateId: candidate.id });
    await this.experiencesService.create({ candidateId: candidate.id });
    const role = await this.rolesService.getRole({
      // value: "rol_knAZ2LRaIxsg7Uth", // Get from the env
      value: process.env.AUTH0_CANDIDATE_ROLE, // Get from the env
      label: "Candidate",
    });
    await user.$add("roles", [role.id]);
    const candidateCreated = await this.getCandidateById(candidate.id);
    const recordData = await this.getUserDataForQdrant(candidateCreated)
    console.log(recordData, 'recordData')
    return { response: candidateCreated, recordData }
  }

  async inviteCandidate(dto: InviteCandidateDto) {
    let candidate, skill, user;
    try {
      await this.userService.isExistUser(dto.email);
      const password = "123456qQ@";
      user = await this.userService.createUser({ ...dto, password: password });
      const activateEmail = uuidv4();
      candidate = await this.profileRepository.create({
        ...dto,
        userId: user.id,
        inviteLink: activateEmail,
        candidate_type: 'fresher',
      });

      skill = await this.skillsService.create(candidate.id);
      const role = await this.rolesService.getRoleByLabel("Candidate");
      if (role) {
        await user.$add("roles", [role.id]);
      }

      const auth0User = await this.auth0Service.createCandidate({
        ...dto,
        password,
        candidateId: candidate.id,
        userId: user.id,
      });
      Object.assign(user, { authId: auth0User.user_id });
      await user.save();

      Object.assign(candidate, { status: 'Pending' })
      await candidate.save()

      await this.emailService.sendRecruitment('activateAccount.html', { link: `https://urecruits.com/email-verification?candidate&ticket=${activateEmail}`, userName: user.firstname, userId: user?.id }, user.email);
      const candidateCreated = await this.getCandidateById(candidate.id);
      const recordData = await this.getUserDataForQdrant(candidateCreated)
      return { response: candidateCreated, recordData }
    } catch (err) {
      if (user && user.authId) {
        await this.auth0Service.deleteUser(user.authId)
      }
      if (skill && skill.id) {
        await this.skillsService.delete({ id: skill.id })
      }
      if (candidate && candidate.id) {
        await this.profileRepository.destroy({ where: { id: candidate.id } })
      }
      if (user && user.id) {
        await this.userRepository.destroy({ where: { id: user.id } })
      }
    }
  }

  async checkVerification(inviteLink: string) {
    const candidate = await this.profileRepository.findOne({
      where: {
        status: {
          [Op.notILike]: "%Active%",
        },
        inviteLink,
      },
      attributes: ["status", "inviteLink", "id"],
    });
    if (!candidate) {
      throw new HttpException("Not found", HttpStatus.NOT_FOUND);
    }
    return candidate;
  }

  async verification(dto: CandidateVerificationDto) {
    const candidate = await this.profileRepository.findOne({
      where: {
        status: {
          [Op.notILike]: "%Active%",
        },
        inviteLink: dto.inviteLink,
      },
      attributes: ["status", "inviteLink", "id"],
      include: [
        {
          model: User,
          attributes: ["authId"],
        },
      ],
    });
    candidate.status = "Active";
    if (!candidate) {
      throw new HttpException("Candidate not found", HttpStatus.NOT_FOUND);
    }
    const user = await this.userService.getUserByAuthId(candidate.user.authId);
    user.password = await bcrypt.hash(dto.newPassword, 10);
    await this.auth0Service.changePassword(dto.newPassword, user.authId);
    await user.save();
    await candidate.save();
    const candidateCreated = await this.getCandidateById(candidate.id);
    const recordData = await this.getUserDataForQdrant(candidateCreated)
    return { response: candidateCreated, recordData }
  }

  async resendActivateEmail(dto: ResendActivationMailDto) {
    const candidate = await this.profileRepository.findOne({
      where: {
        id: dto.candidateId,
        status: {
          [Op.notILike]: '%Active%',
        }
      },
      attributes: ["id", "inviteLink"],
      include: [
        {
          model: User,
          attributes: ["email", "firstname"],
        }
      ]
    });
    if (!candidate) {
      throw new HttpException("Candidate not found", HttpStatus.NOT_FOUND);
    }
    return await this.emailService.sendRecruitment('activateAccount.html', { link: `https://urecruits.com/email-verification?candidate&ticket=${candidate.inviteLink}`, userName: candidate.user?.firstname, userId: dto.candidateId }, candidate.user?.email);
  }

  async getCandidateById(id: number) {
    return await this.profileRepository.findOne({
      where: {
        id,
      },
      include: [
        {
          model: User,
          attributes: { exclude: ["password"] },
          include: [
            {
              model: Role,
              attributes: ["label", "value", "id"],
            },
          ],
        },
        {
          all: true,
        },
      ],
      order: [
        ["educations", "id", "ASC"],
        ["experiences", "id", "ASC"],
        ["skills", "id", "ASC"],
        ["certificates", "id", "ASC"],
        ["awards", "id", "ASC"],
        ["projects", "id", "ASC"],
      ],
    });
  }

  async getCandidateByUserId(id: number) {
    return await this.profileRepository.findOne({
      where: {
        userId: id,
      },
      include: [
        {
          model: User,
          attributes: {
            exclude: ["password", "authId"],
          },
        },
        {
          all: true,
        },
      ],
    });
  }

  async updateCandidate(dto: UpdateCandidateDto, id: number, user) {
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 15);
    let Key = `${user["https://urecruits.com/companyId"] ? "company" : "candidate"
      }/${user.sub}/${timestamp}_${randomString}_resume`;
    const profile = await this.profileRepository.findOne({
      where: {
        id,
      },
      include: [
        {
          model: Industry,
        },
      ],
    });
    if (!profile) {
      throw new HttpException("User not found", HttpStatus.NOT_FOUND);
    }
    if (dto.industries) {
      const removeIds = [];
      const addIds = [];
      if (profile.industries) {
        for (const item of profile.industries) {
          const exist = dto.industries.find((x) => x.id === item.id);
          if (!exist) {
            removeIds.push(item.id);
          }
        }
      }

      for (const item of dto.industries) {
        const exist = profile.industries.find((x) => x.id === item.id);
        if (!exist) {
          addIds.push(item.id);
        }
      }
      if (addIds.length > 0) {
        await profile.$add("industries", addIds);
      }
      if (removeIds.length > 0) {
        await profile.$remove("industries", removeIds);
      }
    }
    if (dto.birthday) {
      dto.birthday = new Date(dto.birthday);
    }
    Object.assign(profile, {
      ...dto,
      userId: profile.userId,
      personalEmail: profile.personalEmail,
    });
    if (dto?.cvKey && dto.cvKey.includes("/temp/")) {
      // Remove file from temp folder and add it to actual folder.
      await this.fileService.moveFile({
        sourceKey: dto.cvKey,
        destinationKey: Key,
        bucketName: process.env.UPLOADSPRIVATE_NAME,
      });
      if (profile.cvKey) {
        // Remove file from actual folder if there is any previous file for same user.
        await this.fileService.removeFile({
          Bucket: process.env.UPLOADSPRIVATE_NAME,
          Key: profile.cvKey,
        });
      }
    } else {
      Key = dto.cvKey;
    }
    Object.assign(profile, {
      ...dto,
      cvKey: Key,
      userId: profile.userId,
      personalEmail: profile.personalEmail,
    });
    await profile.save();
    const candidateCreated = await this.getCandidateById(profile.id);
    const recordData = await this.getUserDataForQdrant(candidateCreated)
    return { response: candidateCreated, recordData }
  }

  async getAllCandidates(dto: CandidateFiltersDto) {
    try {
      const where: any = {};
      const whereUser: any = {};
      const whereSkills: any = {};
      const order = [];
      if (dto.sortBy && dto.sortType) {
        order.push([dto.sortBy, dto.sortType]);
      } else {
        order.push(["id", "DESC"]);
      }
      if (dto.search) {
        whereUser[Op.or] = [
          {
            firstname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
          {
            lastname: {
              [Op.iLike]: `%${dto.search}%`,
            },
          },
        ];
      }
      if (dto.degree) {
        if (dto.degree instanceof Array) {
          where.degree = {
            [Op.iLike]: { [Op.any]: dto.degree },
          };
        } else {
          where.degree = {
            [Op.iLike]: `%${dto.degree}%`,
          };
        }
      }
      if (dto.locationId) {
        if (dto.locationId instanceof Array) {
          where.locationId = {
            [Op.in]: dto.locationId,
          };
        } else {
          where.locationId = {
            [Op.eq]: dto.locationId,
          };
        }
      }
      if (dto.salaryYearMin && dto.salaryYearMax) {
        where.preferencesExpectedCTC = {
          [Op.between]: [dto.salaryYearMin, dto.salaryYearMax],
        };
      } else if (dto.salaryYearMin || dto.salaryYearMax) {
        if (dto.salaryYearMin) {
          where.preferencesExpectedCTC = {
            [Op.gte]: dto.salaryYearMin,
          };
        }
        if (dto.salaryYearMax) {
          where.preferencesExpectedCTC = {
            [Op.lte]: dto.salaryYearMax,
          };
        }
      }
      if (dto.experienceMin && dto.experienceMax) {
        where.experience = {
          [Op.between]: [dto.experienceMin, dto.experienceMax],
        };
      } else if (dto.experienceMin || dto.experienceMax) {
        if (dto.experienceMin) {
          where.experience = {
            [Op.gte]: dto.experienceMin,
          };
        }
        if (dto.experienceMax) {
          where.experience = {
            [Op.lte]: dto.experienceMax,
          };
        }
      }
      if (dto.skills) {
        if (dto.skills instanceof Array) {
          whereSkills[Op.or] = [
            {
              name: {
                [Op.iLike]: { [Op.any]: dto.skills },
              },
            },
          ];
        } else {
          whereSkills.name = {
            [Op.iLike]: `%${dto.skills}%`,
          };
        }
      }
      if (dto.userId) {
        if (Array.isArray(dto.userId)) {
          where.userId = {
            [Op.in]: dto.userId
          }
        } else {
          where.userId = dto.userId
        }
      } else {
        where.userId = {
          [Op.ne]: null
        }
      }

      return await this.profileRepository.findAndCountAll({
        where,
        attributes: [
          "id",
          "experience",
          "preferencesExpectedCTC",
          "currentCtc",
          "personalEmail",
          "updatedAt",
          "positionId",
          "locationId",
          "userId",
          "status"
        ],
        include: [
          {
            model: User,
            where: whereUser,
            attributes: [
              "id",
              "email",
              "avatar",
              "phone",
              "firstname",
              "lastname",
              "middlename",
            ],
          },
          {
            model: Location,
            attributes: ["city", "id", "state", "st"],
          },
          {
            model: Position,
            attributes: ["value", "id", "label"],
          },
          {
            model: Skill,
            where: whereSkills,
            attributes: ["name", "id", "years"],
          },
        ],
        order,
        limit: +dto.limit,
        offset: +dto.offset,
        distinct: true,
      });
    } catch (error) {
      throw new InternalServerErrorException(
        "Error Something went wrong",
        error
      );
    }
  }

  async getPublicCandidateId(id: number, isUserId = false) {
    const data = await this.profileRepository.findOne({
      where: {
        ...(isUserId ? { userId: id } : { id: id })
      },
      attributes: [
        "id",
        "positionId",
        "experience",
        "locationId",
        "cvKey",
        "cvName",
        "degree",
        "personalEmail",
      ],
      include: [
        {
          model: Skill,
          attributes: ["id", "name"],
        },
        {
          model: Experience,
          attributes: [
            "id",
            "companyName",
            "position",
            "location",
            "start",
            "end",
            "present",
          ],
        },
        {
          model: Education,
          attributes: [
            "id",
            "universityName",
            "courseName",
            "specification",
            "coursePeriodStart",
            "coursePeriodEnd",
            "gpa",
            "present",
          ],
        },
        {
          model: Position,
          attributes: ["id", "value", "label"],
        },
        {
          model: Location,
          attributes: ["id", "city", "state", "st"],
        },
        {
          model: User,
          attributes: [
            "id",
            "avatar",
            "email",
            "phone",
            "firstname",
            "middlename",
            "lastname",
          ],
        },
      ],
      order: [
        ["educations", "id", "ASC"],
        ["experiences", "id", "ASC"],
        ["skills", "id", "ASC"],
      ],
    });
    if (!data) {
      throw new HttpException("Candidate not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }

  async getPublicUserId(id: number) {
    const data = await this.profileRepository.findOne({
      where: { userId: id },
      include: [
        {
          model: User,
          attributes: [
            "id",
            "avatar",
            "email",
            "phone",
            "firstname",
            "middlename",
            "lastname",
          ],
        },
      ],
    });
    if (!data) {
      throw new HttpException("Candidate not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }

  async getStatsHomePage(userId: number, candidateId: number) {
    const topMatches = await this.subscribeRepository.findAll({
      where: {
        userId,
      },
      attributes: ["id", "match"],
      limit: 10,
      order: [["match", "DESC"]],
      include: [
        {
          model: Jobs,
          where: { isAssessment: false },
          attributes: ["id", "title"],
          include: [
            {
              model: Company,
              attributes: ["id", "avatar", "name"],
            },
          ],
        },
      ],
    });

    const START = new Date();
    START.setHours(0, 0, 0, 0);
    const NOW = new Date();
    const todayMatchingJobs = await this.subscribeRepository.count({
      where: {
        userId,
        createdAt: {
          [Op.between]: [START.toISOString(), NOW.toISOString()],
        },
      },
      include: [{
        model: Jobs,
        attributes: [],
        where: { isAssessment: false }
      }]
    });

    const todaySavedJobs = await this.subscribeRepository.count({
      where: {
        userId,
        saveJob: true,
        createdAt: {
          [Op.between]: [START.toISOString(), NOW.toISOString()],
        },
      },
      include: [{
        model: Jobs,
        attributes: [],
        where: { isAssessment: false }
      }]
    });

    const lastAppliedJobs = await this.subscribeRepository.findAll({
      limit: 5,
      where: {
        userId,
        applyJob: true,
      },
      attributes: ["id", "userId", "applyJob"],
      order: [["id", "DESC"]],
      include: [
        {
          model: Jobs,
          where: { isAssessment: false },
          attributes: [
            "id",
            "title",
            "employer",
            "salaryMonthMin",
            "salaryMonthMax",
            "salaryYearMin",
            "salaryYearMax",
            "status",
          ],
        },
      ],
    });

    const topCompanies = await this.companyRepository.findAll({
      attributes: [
        "id",
        "name",
        "tenantId",
        "avatar",
        [
          Sequelize.fn("COUNT", Sequelize.col("recruiters.id")),
          "recruitersCount",
        ],
      ],
      limit: 6,
      include: [
        {
          model: Recruiter,
          as: "recruiters",
          attributes: [],
          required: false,
          duplicating: false,
        },
      ],
      group: ["Company.id"],
      order: [["recruitersCount", "DESC"]],
    });

    return {
      topMatches,
      todayMatchingJobs,
      todaySavedJobs,
      lastAppliedJobs,
      topCompanies,
    };
  }

  async uploadAndParseCandidateResume(upload, user) {
    try {
      const { originalname, mimetype, size, buffer } = upload;
      const timestamp = new Date().getTime();
      const randomString = Math.random().toString(36).substring(2, 15);
      const Key = `${user["https://urecruits.com/companyId"] ? "company" : "candidate"
        }/temp/${timestamp}_${randomString}_resume`;

      // const res{umeContent = await this.fileService.getTextFromPdf({
      //   Name: Key,
      // });
      const data = await this.langchainService.getTextFromPdf(buffer);

      if (!!data.length && data[0].pageContent) {
        let parsedData: any = "";
        if (!this.isResume(data[0].pageContent)) {
          throw new Error("Uploaded resume is not a valid resume. Please upload a valid resume")
        }
        try {
          parsedData = await this.langchainService.resumeParser(
            data[0].pageContent
          );
          const response = {
            fileS3Key: Key,
            ...parsedData,
          };
          await this.fileService.uploadOnS3({
            Bucket: process.env.UPLOADSPRIVATE_NAME,
            Key,
            Body: buffer,
            ContentType: mimetype,
            ACL: "private",
          })

          return response;
        } catch (err) {
          if (err?.response?.error?.code) {
            const err_code = err?.response?.error?.code;
            if (err_code === "context_length_exceeded") {
              await this.fileService.uploadOnS3({
                Bucket: process.env.UPLOADSPRIVATE_NAME,
                Key,
                Body: buffer,
                ContentType: mimetype,
                ACL: "private",
              })
              return { fileS3Key: Key, }
            }
          }
        }
      } else {
        throw new HttpException(
          "Something went Wrong please Try Again",
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }
    } catch (error) {
      Logger.log("Error From Resume Parsing:", error);
      throw new InternalServerErrorException(
        "Error From Resume Parsing:",
        error
      );
    }
  }

  isResume(text) {
    const keywords = [
      "experience", "education", "skills", "objective", "summary",
      "responsibilities", "professional", "work history", "certifications",
      "languages", "contact", "references", "employment", "projects"
    ];
    const emailRegrex = /\b[A-Za-z]+@\w+\.\w+\b/g;        // Email addresses
    const lowerCaseText = text.toLowerCase();
    let keywordCount = 0;

    for (const keyword of keywords) {
      if (lowerCaseText.includes(keyword)) {
        keywordCount++;
      }
    }

    if (emailRegrex.test(text)) {
      keywordCount++;
    }
    return keywordCount >= 3;
  }

  async getCandidateRecordData(candidateCreated: Candidate) {
    const role = await this.rolesService.getRoleByLabel("Candidate");
    const recordData = {
      for: 'user', data: {
        userId: candidateCreated?.user.id,
        candidateId: candidateCreated.id,
        type: 'candidate',
        email: candidateCreated?.user.email,
        phone: candidateCreated?.user.phone,
        location: candidateCreated?.location?.dataValues,
        position: candidateCreated?.position?.dataValues,
        name: {
          firstname: candidateCreated?.user?.firstname,
          middleName: candidateCreated?.user?.middlename,
          lastName: candidateCreated?.user?.lastname
        },
        avatar: candidateCreated?.user?.avatar,
        degree: candidateCreated.degree,
        role: [
          {
            id: role.id,
            name: role.label,
          }
        ],
        gender: candidateCreated.gender,
        personalEmail: candidateCreated?.personalEmail,
        profileStatus: candidateCreated?.status,
        preferenceRole: candidateCreated.preferencesRole,
        preferenceFunctionalArea: candidateCreated.preferencesFunctionalArea,
        preferencesJobType: candidateCreated.preferencesJobType,
        preferencesPreferableShift: candidateCreated.preferencesPreferableShift,
        preferencesJobLocation: candidateCreated.preferencesJobLocation,
        preferencesExpectedCTC: candidateCreated.preferencesExpectedCTC,
        preferencesAbleJoin: candidateCreated.preferencesAbleJoin,
        remoteLocation: candidateCreated.remoteLocation,
        skills: candidateCreated.skills,
        educations: candidateCreated.educations,
        experiences: candidateCreated.experiences,
        experienceYear: candidateCreated.experience,
        createdAt: Date.now()
      }
    }
    return recordData;
  }

  async getUserDataForQdrant(candidate: Candidate, action = 'create') {
    const role = await this.rolesService.getRoleByLabel("Candidate");
    const vectorStr = action === 'create' ? [
      candidate.id ? `CandidateId:${candidate.id}` : '',
      candidate.user.firstname ? `FirstName:${candidate.user.firstname}` : '',
      candidate.user.lastname ? `LastName:${candidate.user.lastname}` : '',
      candidate.user.middlename ? `MiddleName:${candidate.user.middlename}` : '',
      candidate.user.avatar ? `ProfilePicture:${candidate.user.avatar}` : '',
      candidate.user.email ? `Email:${candidate.user.email}` : '',
      candidate.user.phone ? `Phone:${candidate.user.phone}` : '',
      role ? `Roles:${role.label}` : '',
      `- Is a Candidate`,
      candidate.degree ? `Has Degree: ${candidate.degree}` : '',
      candidate.birthday ? `Birthday: ${candidate.birthday}` : '',
      candidate.personalEmail ? `Personal Email: ${candidate.personalEmail}` : '',
      candidate.status ? `Profile Status: ${candidate.status}` : '',
      candidate.gender ? `Gender: ${candidate.gender}` : '',
      candidate.preferencesRole ? `Preference Role: ${candidate.preferencesRole}` : '',
      candidate.preferencesFunctionalArea ? `Functional Area: ${candidate.preferencesFunctionalArea}` : '',
      candidate.preferencesJobType ? `Job Type: ${candidate.preferencesJobType}` : '',
      candidate.preferencesPreferableShift ? `Preferable Shift: ${candidate.preferencesPreferableShift}` : '',
      candidate.preferencesJobLocation ? `Job Location: ${candidate.preferencesJobLocation}` : '',
      candidate.preferencesExpectedCTC ? `Expected CTC: ${candidate.preferencesExpectedCTC}` : '',
      candidate.preferencesAbleJoin ? `Able to Join: ${candidate.preferencesAbleJoin}` : '',
      candidate.remoteLocation ? `Remote Location: ${candidate.remoteLocation}` : '',
      candidate.skills ? `Skills: ${candidate.skills.map(skill => skill.name).join(', ')}` : '',
      candidate.experience ? `Experience Year: ${candidate.experience}` : '',
      candidate.linkedin ? `Linkedin: ${candidate.linkedin}` : '',
      candidate.twitter ? `Twitter: ${candidate.twitter}` : '',
      candidate.facebook ? `Facebook: ${candidate.facebook}` : '',
      candidate.instagram ? `Instagram: ${candidate.instagram}` : '',
      candidate.locationId ? `Location: ${candidate.location?.city}, ${candidate.location?.state}` : '',
    ].filter(Boolean).join(', ') : '';

    return {
      for: 'users',
      id: candidate.user.id,
      action,
      ...(action === 'create' && {
        str: vectorStr,
        payload: {
          id: candidate.user.id,
          user_type: 'candidate',
          candidateId: candidate.id,
          firstname: candidate.user.firstname,
          lastname: candidate.user.lastname,
          middlename: candidate.user.middlename,
          avatar: candidate.user.avatar,
          email: candidate.user.email,
          phone: candidate.user.phone,
          role: [role],
          cvKey: candidate.cvKey,
          cvName: candidate.cvName,
          location: candidate.location?.dataValues,
          position: candidate.position?.dataValues,
          degree: candidate.degree,
          birthday: candidate.birthday,
          personalEmail: candidate.personalEmail,
          profileStatus: candidate.status,
          gender: candidate.gender,
          maritalStatus: candidate.maritalStatus,
          preferencesRole: candidate.preferencesRole,
          preferencesFunctionalArea: candidate.preferencesFunctionalArea,
          preferencesJobType: candidate.preferencesJobType,
          preferencesPreferableShift: candidate.preferencesPreferableShift,
          preferencesJobLocation: candidate.preferencesJobLocation,
          preferencesExpectedCTC: candidate.preferencesExpectedCTC,
          preferencesAbleJoin: candidate.preferencesAbleJoin,
          remoteLocation: candidate.remoteLocation,
          skills: candidate.skills,
          educations: candidate.educations,
          experiences: candidate.experiences,
          experienceYear: candidate.experience,
          linkedin: candidate.linkedin,
          twitter: candidate.twitter,
          facebook: candidate.facebook,
          instagram: candidate.instagram,
          createdAt: candidate.createdAt,
        }
      })
    }
  }
}
