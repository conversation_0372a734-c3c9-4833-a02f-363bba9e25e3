import {
  Req,
  Res,
  Injectable,
  NotFoundException,
  BadRequestException,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import * as multer from "multer";
import * as AWS from "aws-sdk";
import * as multerS3 from "multer-s3";

const AWS_S3_PUBLIC = process.env.UPLOADSPUBLIC_NAME;
const AWS_S3_PRIVATE = process.env.UPLOADSPRIVATE_NAME;
const s3 = new AWS.S3();
const textractClient = new AWS.Textract();
AWS.config.update({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
});

interface UploadParams extends AWS.S3.PutObjectRequest {
  // Define any additional properties you want to include in the params
}

@Injectable()
export class FileService {
  async avatar(@Req() req, @Res() res) {
    try {
      req.dir = `public/${
        req.user["https://urecruits.com/companyId"]
          ? "company/" + req.user["https://urecruits.com/tenantId"]
          : "candidate"
      }/${req.user.sub}`;
      this.upload(req, res, function (error) {
        if (error) {
          console.log(error);
          return res.status(404).json(`Failed to upload image file: ${error}`);
        }
        return res.status(201).json(req.files[0].location);
      });
    } catch (error) {
      console.log(error);
      return res.status(500).json(`Failed to upload image file: ${error}`);
    }
  }

  async multipleUpload(@Req() req, @Res() res) {
    try {
      req.dir = `multiple-public/${
        req.user["https://urecruits.com/companyId"]
          ? "company/" + req.user["https://urecruits.com/tenantId"]
          : "candidate"
      }/${req.user.sub}`;
      this.upload(req, res, function (error) {
        if (error) {
          console.log(error);
          return res.status(404).json(`Failed to upload image file: ${error}`);
        }
        return res.status(201).json(req.files);
      });
    } catch (error) {
      console.log(error);
      return res.status(500).json(`Failed to upload image file: ${error}`);
    }
  }

  async privateFile(@Req() req, @Res() res) {
    try {
      req.dir = `${
        req.user["https://urecruits.com/companyId"] ? "company" : "candidate"
      }/${req.user.sub}`;
      this.privateUpload(req, res, function (error) {
        if (error) {
          console.log(error);
          return res.status(404).json(`Failed to upload file: ${error}`);
        }
        return res.status(201).json(req.files[0]);
      });
    } catch (error) {
      console.log(error);
      return res.status(500).json(`Failed to upload file: ${error}`);
    }
  }

  async getPrivateFileUrl(data, @Res() res) {
    const params = { Bucket: AWS_S3_PRIVATE, Key: data.key };
    const result = await s3.getSignedUrl("getObject", params);
    return res.status(201).json(result);
  }

  async getAllFilesFromS3Bucket(req) {
    const path = `${
      req.user["https://urecruits.com/companyId"] ? "company" : "candidate"
    }/${req.user.sub}`;
    const params = {
      Bucket: AWS_S3_PRIVATE,
      Prefix: "candidate/auth0|6352a71463dab2ae318b18b3",
    };
    try {
      let obj = { key: "", fileName: "", size: "", date: new Date() };
      const data = await s3.listObjectsV2(params).promise();
      if (data.KeyCount === 0) {
        throw new NotFoundException("Files not Available");
      }

      const result = data.Contents.map((object) => {
        if (object.Size === 0) {
          return "0 Bytes";
        }
        const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
        const i = Math.floor(Math.log(object.Size) / Math.log(1024));
        const value = parseFloat((object.Size / Math.pow(1024, i)).toFixed(2));
        const size = `${value} ${sizes[i]}`;
        const delimiter = "-";
        const file = object.Key.split("/")
          .pop()
          .split(delimiter)
          .slice(1)
          .join("-");
        obj.key = object.Key;
        obj.fileName = file;
        obj.date = object.LastModified;
        obj.size = `${size}`;
        return { ...obj };
      });

      return { data: result };
    } catch (error) {
      throw new NotFoundException(error.response);
    }
  }

  async downloadFileFromS3(key) {
    const params = {
      Bucket: AWS_S3_PRIVATE,
      Key: key,
    };

    try {
      const { Body } = await s3.getObject(params).promise();
      return Body;
    } catch (error) {
      throw new BadRequestException("Failed to download file from S3.");
    }
  }

  async uploadOnS3(parmas: UploadParams) {
    try {
      const staticParams = {
        Bucket: AWS_S3_PRIVATE,
        ...parmas,
      };
      return await s3.upload(staticParams).promise();
    } catch (error) {
      Logger.log("Error uploading file to S3:", error);
      throw new InternalServerErrorException(
        "Error uploading file to S3:",
        error
      );
    }
  }
  async getFromS3(parmas) {
    try {
      const signedUrlParams = {
        Bucket: AWS_S3_PRIVATE,
        Expires: 3600,
        ...parmas,
      };

      return await s3.getSignedUrlPromise("getObject", signedUrlParams);
    } catch (error) {
      Logger.log("Error getting file to S3:", error);
      throw new InternalServerErrorException(
        "Error getting file to S3:",
        error
      );
    }
  }

  async removeFile({ Bucket, Key }) {
    s3.deleteObject({ Bucket: Bucket, Key: Key }, (err, data) => {
      if (err) {
        console.error("Error deleting original object:", err);
      } else {
        console.log("Successfully deleted original object:", data);
      }
    });
  }

  async moveFile({ sourceKey, destinationKey, bucketName }) {
    try {
      // Define parameters for the copy object operation
      const params = {
        Bucket: bucketName,
        CopySource: `/${bucketName}/${sourceKey}`, // Source path
        Key: destinationKey, // Destination path
      };

      // Copy object from source to destination
      s3.copyObject(params, (err, data) => {
        if (err) {
          console.error("Error copying object:", err);
        } else {
          console.log("Successfully copied object:", data);
          // Delete the original object after successful copy, if needed
          this.removeFile({ Bucket: bucketName, Key: sourceKey });
        }
      });
    } catch (error) {
      Logger.log("Error getting file to S3:", error);
      throw new InternalServerErrorException(
        "Error getting file to S3:",
        error
      );
    }
  }

  async getTextFromPdf(parmas) {
    try {
      const locationParams = {
        Document: {
          S3Object: {
            Bucket: process.env.UPLOADSPRIVATE_NAME,
            ...parmas,
          },
        },
      };

      const data = await textractClient
        .detectDocumentText(locationParams)
        .promise();

      const extractedText = data.Blocks?.reduce((acc: string, block) => {
        if (block.BlockType === "LINE") {
          acc += block.Text + "\n";
        }
        return acc;
      }, "");

      return extractedText;
    } catch (error) {
      Logger.log("Error getting file to S3:", error);
      throw new InternalServerErrorException(
        "Error getting file to S3:",
        error
      );
    }
  }

  upload = multer({
    storage: multerS3({
      s3: s3,
      bucket: AWS_S3_PUBLIC,
      contentType: multerS3.AUTO_CONTENT_TYPE,
      acl: "public-read",
      key: function (request, file, cb) {
        cb(
          null,
          `${request.dir}/${Date.now().toString()}-${file.originalname}`
        );
      },
    }),
  }).array("upload", 6);

  privateUpload = multer({
    storage: multerS3({
      s3: s3,
      bucket: AWS_S3_PRIVATE,
      contentType: multerS3.AUTO_CONTENT_TYPE,
      acl: "aws-exec-read",
      key: function (request, file, cb) {
        cb(
          null,
          `${request.dir}/${Date.now().toString()}-${file.originalname}`
        );
      },
    }),
  }).array("upload", 1);
}
