const moment = require('moment');

export function getStartEndDate(period) {
  let startDate, endDate;

  switch (period) {
    case 'Last 30 Days':
      startDate = moment().subtract(30, 'days').startOf('day').local().toDate();
      endDate = moment().endOf('day').local().toDate();
      break;
    case 'Last 90 Days':
      startDate = moment().subtract(90, 'days').startOf('day').toDate();
      endDate = moment().endOf('day').toDate();
      break;
    case 'Last 3 months':
      startDate = moment().subtract(3, 'months').startOf('month').toDate();
      endDate = moment().endOf('month').toDate();
      break;
    case 'Last 6 months':
      startDate = moment().subtract(6, 'months').startOf('month').toDate();
      endDate = moment().endOf('month').toDate();
      break;
    case 'Last 12 months':
      startDate = moment().subtract(12, 'months').startOf('month').toDate();
      endDate = moment().endOf('month').toDate();
      break;
    case 'This year':
      startDate = moment().startOf('year').toDate();
      endDate = moment().endOf('year').toDate();
      break;
    case 'Last year':
      startDate = moment().subtract(1, 'year').startOf('year').toDate();
      endDate = moment().subtract(1, 'year').endOf('year').toDate();
      break;
    case 'All time':
      startDate = new Date(0);  // Epoch start (1970-01-01)
      endDate = moment().endOf('day').toDate();  // Current date and time
      break;
    default:
      console.error('Invalid period provided');
      return { startDate: null, endDate: null }; // Return null for invalid periods
  }

  return { startDate, endDate };
}
