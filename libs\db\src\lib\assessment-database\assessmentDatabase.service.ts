import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectConnection, InjectModel } from '@nestjs/sequelize';
import { AssessmentDatabase } from './assessmentDatabase.model';
import { Package } from '../packages/packages.model';

@Injectable()
export class AssessmentDatabaseService {
  constructor(
    @InjectModel(AssessmentDatabase) private assessmentDbModel: typeof AssessmentDatabase,
    @InjectConnection('assessmentPG') private pgConnection,
    @InjectConnection('assessmentMYSQL') private mysqlConnection,
  ) {}

  createAssessmentDatabase(dto: AssessmentDatabase,tenantId) {
    const data={...dto,tenantId}
      return this.assessmentDbModel.create(data);
  }

  findAssessmentDatabase(tenantId) {
    try{
      return this.assessmentDbModel.findAll({where:{tenantId},include: [{ model: Package }],});
    }catch(error){
      console.log(error)
    }
  }

  getById(id) {
    try{
      return this.assessmentDbModel.findOne({where:{id},include: [{ model: Package }],});
    }catch(error){
      console.log(error)
    }
  }

  delete(id: number) {
    return this.assessmentDbModel.destroy({
      where: {
        id,
      },
    });
  }

  createPgSchema(name: string, sql: string) {
    return this.pgConnection.query(`CREATE SCHEMA ${name}; ${sql}`);
  }

  dropPgSchema(name: string) {
    return this.pgConnection.query(`DROP SCHEMA ${name}`);
  }

  createMysqlSchema(name: string, sql: string) {
    return this.mysqlConnection.query(`CREATE SCHEMA ${name}; ${sql}`);
  }

  dropMysqlSchema(name: string) {
    return this.mysqlConnection.query(`DROP SCHEMA ${name}`);
  }
}
