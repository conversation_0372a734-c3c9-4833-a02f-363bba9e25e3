import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { putRecordOnQdrant } from './utils/put-records';

@Injectable()
export class QdrantMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {

    const originalSend = res.send;

    res.send = function (body: any) {
      try {
        if (typeof body === 'string' && body.trim().startsWith('{')) {
          let responseBody = JSON.parse(body);
          if (responseBody && responseBody.recordData) {
            const data = Array.isArray(responseBody.recordData)
              ? responseBody.recordData
              : [responseBody.recordData];
              putRecordOnQdrant(data);
            delete responseBody.recordData;
            return originalSend.call(this, responseBody?.response);
          }
        }
        // If not JSON or no modification needed, send the body as is
        return originalSend.call(this, body);
      } catch (error) {
        console.error('Error parsing or modifying response body:', error);
        return originalSend.call(this, body);
      }
    };

    next();
  }
}
