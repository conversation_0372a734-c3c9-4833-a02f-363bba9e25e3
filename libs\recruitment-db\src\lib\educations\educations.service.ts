import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from '@nestjs/sequelize'
import { Education } from './educations.model'
import { CreateEducationDto } from './dto/create-education.dto'
import { UpdateEducationDto } from "./dto/update-education.dto";

@Injectable()
export class EducationsService {
  constructor (
    @InjectModel(Education) private educationRepository: typeof Education,
  ) {}

  async create (dto: CreateEducationDto) {
    return await this.educationRepository.create(dto)
  }

  async update (dto: UpdateEducationDto) {
    const data = await this.get(dto);
    Object.assign(data, dto);
    return await data.save();
  }

  async delete (dto: UpdateEducationDto) {
    const data = await this.get(dto);
    await data.destroy();
    return true;
  }

  async get (dto: UpdateEducationDto) {
    const data = await this.educationRepository.findOne({
      where: {
        id: dto.id,
        candidateId: dto.candidateId,
        recruiterId: dto.recruiterId
      },
    });
    if (!data) {
      throw new HttpException("Education not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }
}
