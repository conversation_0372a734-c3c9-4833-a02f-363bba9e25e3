import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EmailResolverService } from './email.resolver.service';
import { EmailService } from './email.service';
import { HttpModule } from "@nestjs/axios";

@Module({
  imports: [ConfigModule.forRoot(),HttpModule],
  providers: [EmailService, EmailResolverService],
  exports: [EmailService, EmailModule, EmailResolverService],
})
export class EmailModule {}
