import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  Location, LocationsService,
} from "@microservices/recruitment-db";
import { AuthGuard } from "@nestjs/passport";
import { PermissionsGuard } from "@microservices/auth";

@ApiTags("Location")
@Controller("location")
export class LocationsController {
  constructor (private readonly locationsService: LocationsService) {}

  @ApiOperation({ summary: "Get locations with Limit" })
  @ApiResponse({ status: 200 })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/filter')
  getAllWithLimit (@Query() find: any) {
    return this.locationsService.findByCityWithLimit(find);
  }

  @ApiOperation({ summary: "Get locations" })
  @ApiResponse({ status: 200, type: [Location] })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get(':city?')
  getAll (@Param('city') city?: string,) {
    return this.locationsService.findByCity(city);
  } 
}
