import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { CompaniesModule } from '../companies/companies.module';
import { SequelizeModule } from '@nestjs/sequelize';
import { Payment } from './payment.model';
import { UsersModule } from '../users/users.module';
import { SubscriptionModule } from '../subscription/subscription.module';
import { ProductModule } from '../product/product.module';
import { PriceModule } from '../price/price.module';
import { PlanModule } from '../plan/plan.module';
import { PlanPackageModule } from "../plan-packages/plan-packages.module";
import { EmailModule } from '@microservices/email';
import { SubscriptionAddon } from '../subscription/subscription-addon.model';

@Module({
  imports: [SequelizeModule.forFeature([Payment,SubscriptionAddon]), CompaniesModule,UsersModule,SubscriptionModule,ProductModule,PriceModule,PlanModule,EmailModule,PlanPackageModule],
  providers: [PaymentService],
  exports: [SequelizeModule, PaymentService]
})
export class PaymentModule {}
