import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';
import { PlaybackDto, PlaybackService, SearchPlaybackDto } from '@microservices/recruitment-db';
import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards, Logger } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiOperation, ApiResponse } from '@nestjs/swagger';

@Controller('playback')
export class PlaybackController {
  constructor(private playbackService: PlaybackService) {}


  @ApiOperation({ summary: 'Create Take home task' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Get(":playbackId")
  findByPlaybackId(@Param("playbackId") playbackId,@AuthUser() user) {
    return this.playbackService.findByPlaybackId(playbackId);
  }

  @ApiOperation({ summary: 'Create Take home task' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Get()
  findAll(@Query() data,@AuthUser() user) {
    return this.playbackService.findAll(data,user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Create Take home task' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post()
  create(@Body() createCodingInstructionDto) {
    return this.playbackService.create(createCodingInstructionDto);
  }

  @ApiOperation({ summary: 'Get take home task by id' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get()
  findOne(@Query() data:SearchPlaybackDto) {
    try{
      return this.playbackService.findOneById(data as SearchPlaybackDto);
    }catch(error){
      Logger.log("Error",error)
    }
  }
}
