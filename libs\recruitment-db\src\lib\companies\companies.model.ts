import {
  <PERSON><PERSON>s<PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>Type, ForeignKey, HasMany,
  Model,
  Table,
} from 'sequelize-typescript'
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { User } from '../users/users.model'
import { Industry } from '../industries/industries.model'
import { Recruiter } from '../recruiters/recruiters.model'
import { Jobs } from "../job/job.model";

@Table({ tableName: 'companies' })
export class Company extends Model<Company> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'uRecruits', description: 'Company Name' })
  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Contact Person Email',
  })
  @Column({
    type: DataType.STRING,
    allowNull: false,
  })
  email: string;

  @ApiProperty({ example: '8622365552', description: 'Contact Person Phone' })
  @Column({ type: DataType.STRING, allowNull: false })
  phone: string;

  @ApiProperty({ example: 'company-name', description: 'uRecruits tenantId' })
  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  tenantId: string;

  @ApiProperty({ example: 'cus_xxx', description: 'Stripe customer ID' })
  @Column({ type: DataType.STRING, allowNull: true, unique: true })
  stripeId: string;

  @ApiProperty({ example: 'Corporate', description: 'Company Type' })
  @Column({ type: DataType.STRING, allowNull: true })
  company_type: string;

  @ApiProperty({ example: 'Matthew', description: 'Contact Person First Name' })
  @Column({ type: DataType.STRING, allowNull: true })
  firstname: string;

  @ApiProperty({ example: 'James', description: 'Contact Person Middle Name' })
  @Column({ type: DataType.STRING, allowNull: true })
  middlename: string;

  @ApiProperty({ example: 'Stark', description: 'Contact Person Last Name' })
  @Column({ type: DataType.STRING, allowNull: true })
  lastname: string;

  @ApiProperty({ example: 0, description: 'Company specialists count' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  companySpecialists: number;

  @ApiProperty({
    example: 'About the Company',
    description: 'About the Company',
  })
  @Column({ type: DataType.TEXT, allowNull: true })
  about: string;

  @Column({ type: DataType.INTEGER, allowNull: true })
  @ForeignKey(() => Industry)
  industryId: number;

  @Column({ type: DataType.INTEGER, allowNull: false })
  @ForeignKey(() => User)
  ownerId: number;

  @ApiProperty({
    example: 'https://domain.com/avatar.jpg',
    description: 'Avatar',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  avatar: string;

  @ApiPropertyOptional({
    example: 'https://domain.com',
    description: 'website',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  website: string;

  @ApiProperty({ example: [], description: "Gallery" })
  @Column({ type: DataType.JSONB, allowNull: true })
  gallery: any;

  @ApiProperty({ example: [], description: "Addresses" })
  @Column({ type: DataType.JSONB, allowNull: true })
  addresses: any;

  @ApiProperty({
    example: '****************...',
    description: 'Token for sending emails via Gmail on behalf of company',
  })
  @Column({
    type: DataType.STRING,
    allowNull: true,
  })
  gmailToken: string;

  @ApiProperty({
    example: 'jt-12345',
    description: 'JobTarget account identifier',
  })
  @Column({ type: DataType.STRING, allowNull: true, field: 'job_target_id', })
  jobTargetId: string;

  @BelongsTo(() => User)
  owner: User;

  @BelongsTo(() => Industry)
  industry: Industry;

  @HasMany(() => Recruiter)
  recruiters: Recruiter[];

  @HasMany(() => Jobs, {
    foreignKey: 'companyId'
  })
  jobs: Jobs[];

  @HasMany(() => User)
  members: User[];
}
