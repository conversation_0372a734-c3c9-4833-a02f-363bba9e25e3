{"root": "libs/integrations", "sourceRoot": "libs/integrations/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/integrations/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/integrations"], "options": {"jestConfig": "libs/integrations/jest.config.js", "passWithNoTests": true}}}, "tags": []}