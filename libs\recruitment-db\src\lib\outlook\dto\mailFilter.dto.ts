import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class MailFilterDto {
  @ApiPropertyOptional({ example: 'Designer', description: "Search" })
  @IsOptional()
  search?: any;

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiProperty({ example: "20", description: "Limit" })
  @IsOptional()
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  @IsOptional()
  offset: number;

  @ApiProperty({ example: "0", description: "Offset" })
  @IsOptional()
  token: any;

  @ApiProperty({ example: "2023/06/21", description: "after date" })
  @IsOptional()
  newer_than: string;

  @ApiProperty({ example: "inbox", description: "Source of the list of mails" })
  @IsOptional()
  source: 'inbox' | 'sent' | 'draft';


}

