import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Languages } from './languages.model';

@Injectable()
export class LanguagesService {
  constructor(
    @InjectModel(Languages) private languagesModel: typeof Languages,
  ) {}

  createLanguages(dto) {
    const data={...dto}
      return this.languagesModel.create(data);
  }

  findLanguages(limit=null,offset=null) {
    try{
      return this.languagesModel.findAll({
        ...(limit&&{limit}),
        ...(offset&&{offset})
      });
    }catch(error){
      console.log(error)
    }
  }

  getById(id) {
    try{
      return this.languagesModel.findOne({where:{id}});
    }catch(error){
      console.log(error)
    }
  }

 }
