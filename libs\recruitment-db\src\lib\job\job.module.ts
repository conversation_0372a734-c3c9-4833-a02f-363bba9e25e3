import { Modu<PERSON> } from '@nestjs/common';
import { JobService } from './job.service';
import { SequelizeModule } from '@nestjs/sequelize';
import { Jobs } from './job.model';
import { GoogleModule, LangchainModule } from "@microservices/integrations";
import { EmailModule } from "@microservices/email";
import { HttpModule } from '@nestjs/axios';
import { CandidatesModule } from '../candidates/candidates.module';
import { UsersModule } from '../users/users.module';
import { Candidate } from '../candidates/candidates.model';
import { User } from '../users/users.model';
import { WorkflowModule } from '../workflow/workflow.module';
import { JobCreationChatbotModule } from './chatbot/job-creation-chatbot/job-creation-chatbot.module';
import { JobTargetIntegration } from '../jobtarget/jobtarget.model';
import { JobTargetService } from '../jobtarget/jobtarget.service';

@Module({
  providers: [JobService, JobTargetService],
  imports: [
    SequelizeModule.forFeature([Jobs, Candidate, User, JobTargetIntegration]),
    GoogleModule,
    EmailModule, 
    LangchainModule,
    HttpModule, CandidatesModule, UsersModule,WorkflowModule,
    JobCreationChatbotModule
  ],
  exports: [JobService, SequelizeModule],
})
export class JobModule {}
