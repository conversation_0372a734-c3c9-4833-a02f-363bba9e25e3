import { ApiProperty, PartialType } from "@nestjs/swagger";
import { JobDto } from "./job.dto";
import { IsOptional } from "class-validator";

export class UpdateJobDto extends PartialType(JobDto) {
  @ApiProperty({ example: "1", description: "Primary key" })
  id: number;

  @ApiProperty({ example: "1", description: "Approver id" })
  @IsOptional()
  approverId?: number;

  @ApiProperty({ example: "1", description: "Approver name" })
  @IsOptional()
  approvalName?: string;

  @ApiProperty({ example: "1", description: "Approver email" })
  @IsOptional()
  approvalEmail?: string;
}
