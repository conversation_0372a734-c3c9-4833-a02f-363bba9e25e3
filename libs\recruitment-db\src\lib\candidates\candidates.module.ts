import { Module } from "@nestjs/common";
import { SequelizeModule } from "@nestjs/sequelize";
import { Candidate } from "./candidates.model";
import { UsersModule } from "../users/users.module";
import { Auth0Module } from "@microservices/auth";
import { IndustriesModule } from "../industries/industries.module";
import { SkillsModule } from "../skills/skills.module";
import { CertificatesModule } from "../certificates/certificates.module";
import { AwardsModule } from "../awards/awards.module";
import { ProjectsModule } from "../projects/projects.module";
import { EducationsModule } from "../educations/educations.module";
import { ExperiencesModule } from "../experiences/experiences.module";
import { RolesModule } from "../roles/roles.module";
import { CandidatesService } from "./candidates.service";
import { Subscribe } from "../subscribe/subscribe.model";
import { Company } from "../companies/companies.model";
import { FileModule } from "apps/pre-recruitment-api/src/file/file.module";
import { LangchainModule } from "@microservices/integrations";
import { EmailModule } from "@microservices/email";
import { CandidateIndustry } from "../industries/candidates-industries.model";
import { Industry } from "../industries/industries.model";
@Module({
  imports: [
    SequelizeModule.forFeature([Candidate, Subscribe, Company, CandidateIndustry, Industry]),
    UsersModule,
    Auth0Module,
    IndustriesModule,
    SkillsModule,
    CertificatesModule,
    AwardsModule,
    EducationsModule,
    ExperiencesModule,
    RolesModule,
    ProjectsModule,
    FileModule,
    LangchainModule,
    EmailModule
  ],
  providers: [CandidatesService],
  exports: [SequelizeModule, CandidatesService],
})
export class CandidatesModule {}
