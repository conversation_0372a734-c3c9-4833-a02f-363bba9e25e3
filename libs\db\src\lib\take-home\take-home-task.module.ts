import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TakeHomeTask } from './take-home-task.model';
import { TakeHomeDrafts } from './home-drafts/home-drafts.model';
import { TakeHomeDraftsModule } from './home-drafts/home-drafts.module';
import { TakeHomeTaskService } from './take-home-task.service';
import { QuestionsModule } from '../questions/questions.module';
import { TestCaseModule } from '../testcases/testcase.module';
import { PackagesModule } from '../packages/packages.module';

@Module({
  imports: [
    SequelizeModule.forFeature([TakeHomeTask, TakeHomeDrafts]),
    TakeHomeDraftsModule,
    QuestionsModule,
    TestCaseModule,
    PackagesModule
  ],
  providers: [TakeHomeTaskService],
  exports: [SequelizeModule, TakeHomeTaskService],
})
export class TakeHomeTaskModule { }
