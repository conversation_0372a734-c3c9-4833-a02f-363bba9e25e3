import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Project } from "./projects.model";
import { UpdateProjectsDto } from "./dto/update-projects.dto";
import { ProjectsDto } from "./dto/projects.dto";

@Injectable()
export class ProjectsService {
  constructor(
    @InjectModel(Project) private projectRepository: typeof Project
  ) {}

  async create(candidateId: number, dto?: ProjectsDto) {
    return await this.projectRepository.create({ ...dto, candidateId: candidateId });
  }

  async update(dto: UpdateProjectsDto) {
    const data = await this.get(dto.id, dto.candidateId);
    Object.assign(data, dto);
    return await data.save();
  }

  async delete(dto: UpdateProjectsDto) {
    const data = await this.get(dto.id, dto.candidateId);
    await data.destroy();
    return true;
  }

  async get(id: number, candidateId: number) {
    const data = await this.projectRepository.findOne({
      where: { id, candidateId },
    });
    if (!data) {
      throw new HttpException("Project not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }
}
