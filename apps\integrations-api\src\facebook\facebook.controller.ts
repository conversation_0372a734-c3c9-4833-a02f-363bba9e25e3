import { <PERSON>, Get, Header } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FacebookService } from './facebook.service';

// TODO: this controller should be removed after the job is finished
@ApiTags('Facebook')
@Controller('facebook')
export class FacebookController {
  constructor(private readonly facebookService: FacebookService) {}

  @ApiOperation({
    summary: 'List of jobs in XML format for facebook.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns an XML document.',
  })
  @ApiResponse({
    status: 400,
    description: 'Returns an Error object.',
  })
  @Get('/jobs.xml')
  @Header('Content-Type', 'text/xml')
  listJobs() {
    return this.facebookService.jobsXml();
  }
}
