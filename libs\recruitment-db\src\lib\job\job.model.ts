import {
  BelongsTo, BelongsToMany,
  Column,
  DataType,
  ForeignKey, HasMany,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { Company } from "../companies/companies.model";
import { Industry, Location, Position, Subscribe, User, Workflow, Round } from "@microservices/recruitment-db";
import { LocationJobs } from "../locations/location-jobs.model";
import { bool } from "aws-sdk/clients/signer";

@Table({ tableName: "jobs" })
export class Jobs extends Model<Jobs> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "1", description: "Job id" })
  @ForeignKey(() => Position)
  @Column({ type: DataType.INTEGER, allowNull: true })
  positionId: number;

  @BelongsTo(() => Position)
  position: Position;

  @ApiProperty({ example: "1", description: "Author id" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  authorId: number;

  @BelongsTo(() => User)
  author: User;

  @ApiProperty({ example: "Angular Developer", description: "Job Title" })
  @Column({ type: DataType.STRING, allowNull: true })
  title: string;

  @ApiProperty({ example: "Urecruits", description: "Employer" })
  @Column({ type: DataType.STRING, allowNull: true })
  employer: string;

  @ApiProperty({ example: "Consultancy", description: "Consultancy" })
  @Column({ type: DataType.STRING, allowNull: true })
  consultancy: string;

  @ApiProperty({ example: "Arizona,United State", description: "Job Location" })
  @Column({ type: DataType.STRING, allowNull: true })
  location: string;

  @ApiProperty({ example: "false", description: "Remote Location" })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  remoteLocation: boolean;

  @ApiProperty({ example: "500", description: "Salary Range(per month min)" })
  @Column({ type: DataType.FLOAT, allowNull: true })
  salaryMonthMin: number;

  @ApiProperty({ example: "500", description: "Salary Range(per month max)" })
  @Column({ type: DataType.FLOAT, allowNull: true })
  salaryMonthMax: number;

  @ApiProperty({ example: "6000", description: "Salary Range(per year min)" })
  @Column({ type: DataType.FLOAT, allowNull: true })
  salaryYearMin: number;

  @ApiProperty({ example: "6000", description: "Salary Range(per year max)" })
  @Column({ type: DataType.FLOAT, allowNull: true })
  salaryYearMax: number;

  @ApiProperty({ example: "6000", description: "Salary Range(per hour min)" })
  @Column({ type: DataType.FLOAT, allowNull: true, defaultValue: 0 })
  salaryHourMin: number;

  @ApiProperty({ example: "6000", description: "Salary Range(per hour max)" })
  @Column({ type: DataType.FLOAT, allowNull: true, defaultValue: 0 })
  salaryHourMax: number;

  @ApiProperty({ example: "false", description: "Negotiable" })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  negotiable: boolean;

  @ApiProperty({ example: "TEXT", description: "Job Description" })
  @Column({ type: DataType.TEXT, allowNull: true })
  description: string;

  @ApiProperty({ example: "TEXT", description: "Job Short Description" })
  @Column({ type: DataType.TEXT, allowNull: true })
  shortDescription: string;

  @ApiProperty({ example: "65", description: "№ of Openings" })
  @Column({ type: DataType.STRING, allowNull: true })
  numberOpenings: string;

  @ApiProperty({ example: "Full Time", description: "Job Type" })
  @Column({ type: DataType.STRING, allowNull: true })
  jobType: string;

  @ApiProperty({ example: "Morning Shift", description: "Preferable Shift" })
  @Column({ type: DataType.STRING, allowNull: true })
  preferableShift: string;

  @Column({ type: DataType.INTEGER, allowNull: true })
  @ForeignKey(() => Industry)
  industryId: number;

  @ApiProperty({ example: "Functional Area", description: "Functional Area" })
  @Column({ type: DataType.STRING, allowNull: true })
  functionalArea: string;

  @ApiProperty({ example: "2 Months", description: "Notice Period" })
  @Column({ type: DataType.STRING, allowNull: true })
  noticePeriod: string;

  @ApiProperty({ example: "[\"Photoshop\", \"Figma\", \"Adobe XD\"]", description: "Skills [JSON array]" })
  @Column({ type: DataType.JSONB, allowNull: true })
  skills: any;

  @ApiProperty({ example: "1", description: "Experience Min" })
  @Column({ type: DataType.FLOAT, allowNull: true })
  experienceMin: number;

  @ApiProperty({ example: "5", description: "Experience Max" })
  @Column({ type: DataType.FLOAT, allowNull: true })
  experienceMax: number;

  @ApiProperty({ example: "Master Degree", description: "Education" })
  @Column({ type: DataType.STRING, allowNull: true })
  education: string;

  @ApiProperty({ example: "[\"Photoshop?\", \"Figma?\", \"Adobe XD?\"]", description: "Screening Questions" })
  @Column({ type: DataType.JSONB, allowNull: true })
  screeningQuestions: any;

  @ApiProperty({ example: "\"[\"Photoshop\", \"Figma\", \"Adobe XD\"]", description: "Benefit(s)" })
  @Column({ type: DataType.JSONB, allowNull: true })
  benefits: any;

  @ApiProperty({ example: "TEXT", description: "About Company" })
  @Column({ type: DataType.TEXT, allowNull: true })
  aboutCompany: string;

  @ApiProperty({ example: "Enter employer", description: "Career Portal" })
  @Column({ type: DataType.STRING, allowNull: true })
  careerPortal: string;

  @ApiProperty({ example: "url", description: "Linkedin" })
  @Column({ type: DataType.STRING, allowNull: true })
  linkedin: string;

  @ApiProperty({ example: "url", description: "Facebook" })
  @Column({ type: DataType.STRING, allowNull: true })
  facebook: string;

  @ApiProperty({ example: "url", description: "Twitter" })
  @Column({ type: DataType.STRING, allowNull: true })
  twitter: string;

  @ApiProperty({ example: "url", description: "Instagram" })
  @Column({ type: DataType.STRING, allowNull: true })
  instagram: string;

  @ApiProperty({ example: "true", description: "Career Page" })
  @Column({ type: DataType.STRING, allowNull: true, defaultValue: false })
  careerPage: boolean;

  @ApiProperty({ example: "true", description: "Public Search" })
  @Column({ type: DataType.STRING, allowNull: true, defaultValue: false })
  publicSearch: boolean;

  @ApiProperty({ example: "\"[\"Photoshop\", \"Figma\", \"Adobe XD\"]", description: "Job Boards" })
  @Column({ type: DataType.JSONB, allowNull: true })
  jobBoards: any;

  @ApiProperty({ example: "Json", description: "Application Form Json" })
  @Column({ type: DataType.JSONB, allowNull: true })
  applicationForm: any;

  @ApiProperty({ example: "draft", description: "Status" })
  @Column({ type: DataType.STRING, allowNull: false, defaultValue: "draft" })
  status: string;

  @ApiProperty({ example: "1", description: "Position Workflow ID" })
  @ForeignKey(() => Workflow)
  @Column({ type: DataType.INTEGER, allowNull: true })
  workflowId: number;

  @ApiProperty({ example: "1", description: "Approval user ID" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: true })
  approverId: number;

  @ApiProperty({ example: "1", description: "Company ID" })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({
    example: '{"jobManager":"https://...","marketplace":"https://..."}',
    description: 'SSO links JSON string containing jobManager and marketplace URLs',
    required: false,
  })
  @Column({ type: DataType.TEXT, allowNull: true })  // TEXT for JSON string storage
  sso_link: string;


  @ApiProperty({ example: "197546", description: "Job Target Id" })
  @Column({ type: DataType.INTEGER, allowNull: true })
  jobTargetId: number;

  @ApiProperty({ example: true, description: "Whether the source of the job creation is assessment or not" })
  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  isAssessment: boolean;

  // @BelongsTo(() => User)
  // user: User;

  @BelongsTo(() => Company)
  company: Company;

  @BelongsTo(() => Workflow)
  workflow: Workflow;

  @BelongsTo(() => Industry)
  industry: Industry;

  @BelongsToMany(() => Location, () => LocationJobs)
  locations: Location[];

  @HasMany(() => Subscribe)
  subscribes: Subscribe;

  @HasMany(() => Round)
  rounds: Round;
}
