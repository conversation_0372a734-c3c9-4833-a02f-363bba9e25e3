import { Module } from "@nestjs/common";
import { SequelizeModule } from "@nestjs/sequelize";
import { Assignment } from "./assignment.model";
import { Deadline } from "../deadline/deadline.model";
import { TimeDuration } from "../time-duration/time-duration.model";

@Module({
  imports: [SequelizeModule.forFeature([Assignment, Deadline, TimeDuration])],
  exports: [SequelizeModule],
})
export class AssignmentModule {}
