[{"id": 1, "label": "Company Owner", "value": "rol_fHqezI6c5WbbPpT8", "description": "Grants full access to all features and permissions within the uRecruits platform, including user management, settings, and data control.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Manage Company Profile", "key": "company-profile", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Manage Team Members", "key": "team-members", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Manage Password", "key": "password", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Subscription Management", "key": "subscription", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Payment Integration", "key": "payment-integration", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Setting", "key": "setting", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Assessment", "key": "assessment", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": true}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": true}]}, {"id": 2, "label": "Recruitment Manager", "value": "rol_U1dLyeQTiI1Tb2xZ", "description": "Grants access to manage recruitment processes, including job postings, candidate screening, and interview scheduling.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}, {"id": 3, "label": "Company Admin", "value": "rol_mmFpPpO3dW3Mg7FM", "description": "Grants access to administrative features for managing users, permissions, and specific company settings.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": true, "edit": true, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}, {"id": 4, "label": "Functional Recruiter", "value": "rol_gB4Vexr64YaP1HQi", "description": "Grants access to manage and process candidate applications, conduct interviews, and oversee recruitment activities within specific job functions.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}, {"id": 5, "label": "HR Recruiter", "value": "rol_44Hmnm8Ky5MvgNY3", "description": "Grants access to candidate management, job postings, and recruitment tools within the uRecruits.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}, {"id": 6, "label": "HR Team Member", "value": "rol_LkFvK07RMsGmHHFk", "description": "Grants access to manage job postings, review applications, and assist with candidate selection within the uRecruits.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}, {"id": 7, "label": "Technical Recruiter", "value": "rol_95CTAMYP6HwP2BuJ", "description": "Grants access to manage job postings, review applicants, and coordinate interviews within the uRecruits.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}, {"id": 8, "label": "Candidate", "value": "rol_YCGnrcYxOuDtD93W", "description": "Grants access to apply for jobs, manage profiles, and view application statuses within the uRecruits.", "permissions": ""}, {"id": 9, "label": "Company Leaders", "value": "rol_VYYr4KqAZHWJRElR", "description": "Grants access to manage teams, view reports, and oversee company operations within uRecruits.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}, {"id": 10, "label": "Company Sr. Executive", "value": "rol_GjURMK1X8VvPfwPq", "description": "Grants access to manage teams, oversee operations, and analyze company performance within the uRecruits.", "permissions": [{"name": "Profile Management", "key": "profile-management", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Manage Company Profile", "key": "company-profile", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Manage Team Members", "key": "team-members", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Manage Password", "key": "password", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Subscription Management", "key": "subscription", "view": true, "create": false, "edit": false, "delete": false}, {"name": "Payment Integration", "key": "payment-integration", "view": false, "create": false, "edit": false, "delete": false}, {"name": "Setting", "key": "setting", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Job Post Management", "key": "job-post", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Assessment", "key": "assessment", "view": true, "create": false, "edit": true, "delete": false}, {"name": "Offer", "key": "offer", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Drug Screening", "key": "drug", "view": true, "create": true, "edit": true, "delete": false}, {"name": "Background Screening", "key": "background", "view": true, "create": true, "edit": true, "delete": false}]}]