import { HttpException, HttpStatus, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import * as JwtGenerator from "@convergence/jwt-util";
import { Op } from "sequelize";
import { parseDate } from "tools/utils";
import { v4 as uuidv4 } from "uuid";
import { request } from "undici";
import { Filter } from "./filter.interface";
import { LiveCoding, STATUS, TYPE } from "./live-coding.model";
import { Package } from "../packages/packages.model";
import { liveCodingDto } from "./live-coding.dto";
import { EmailService } from "@microservices/email";
import { LiveCodingDrafts } from "./live-drafts/live-drafts.model";
import { getDummyLiveTask } from "../utils/fillDummyAssessmentData";
import { putRecordOnQdrant } from '@microservices/qdrant'

@Injectable()
export class LiveCodingService {
  constructor(
    @InjectModel(LiveCoding)
    private liveCodingModel: typeof LiveCoding,
    @InjectModel(LiveCodingDrafts)
    private liveDraftModel: typeof LiveCodingDrafts,
    @InjectModel(Package)
    private packageModel: typeof Package,
    private emailService: EmailService

  ) { }

  async findOneById(id: number, user: any): Promise<LiveCoding> {
    const companyId = user && user["https://urecruits.com/companyId"] ? user["https://urecruits.com/companyId"] : null;
    const liveCoding = await this.liveCodingModel.findOne({
      where: {
        id,
        ...(companyId ?? '')
      }
    });
    if (!liveCoding) {
      throw new NotFoundException(`LiveCoding with ID ${id} not found`);
    }
    return liveCoding;
  }
  async findOneByTaskId(taskId: string, user: any) {
    const companyId = user && user["https://urecruits.com/companyId"] ? user["https://urecruits.com/companyId"] : null;
    return await this.liveCodingModel.findOne({
      where: {
        taskId,
        ...(companyId ?? '')
      }
    });
  }
  async removeByTaskId(taskId, companyId: number, userId: number) {
    const liveCoding = await this.liveCodingModel.findOne({ where: { taskId, companyId } });
    if (liveCoding) {
      const id = liveCoding.id
      const recordData = this.addDataonOpenSearch(liveCoding, userId, 'Deleted', 'deleted');
      await this.liveCodingModel.destroy({ where: { id, companyId } });
      return { response: liveCoding, recordData };
    } else {
      return null;
    }
  }



  findAll(companyId: number): Promise<LiveCoding[]> {
    const find = this.liveCodingModel.findAll({
      where: {
        companyId
      }
    })
    if (find) {
      return find;
    }
    else {
      throw new NotFoundException(`LiveCoding not found`);
    }
  }

  findAllPackages(): Promise<Package[]> {
    try {
      return this.packageModel.findAll();
    } catch (error) {
      throw new HttpException('Error getting package', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async create(dto: liveCodingDto, companyId: number, userId: number) {

    let recordData: any[] = [];

    // Validate languageId
    if (!dto.languageId) {
      throw new Error('languageId is required');
    }

    if (dto.taskId) {
      const draft = await this.liveDraftModel.findOne({ where: { taskId: dto.taskId, companyId } });
      if (draft) {
        await draft.destroy();
        recordData.push(this.getLiveCodingDataForQdrant(draft, 'delete'));
      } else {
        const liveCoding = await this.removeByTaskId(dto.taskId, companyId, userId);
        if (liveCoding) {
          recordData.push(liveCoding.recordData);
        }
      }
    }

    const livecoding = await this.liveCodingModel.create({
      ...dto,
      taskId: uuidv4(),
      status: STATUS.active,
      assessmentType: TYPE.liveTask,
      companyId,
    });

    // Handle Qdrant data separately
    await this.getLiveCodingDataForQdrant(livecoding, 'create');

    // Return OpenSearch data
    const openSearchData = this.addDataonOpenSearch(livecoding, userId, STATUS.active, 'create');
    return {
      response: livecoding,
      recordData: openSearchData
    };
  }

  async update(id: number, dto: LiveCoding, companyId: number, userId: number) {
    await this.liveCodingModel.update({ ...dto }, { where: { id, companyId } });
    const livecoding = await this.liveCodingModel.findOne({ where: { id, companyId } });
    await this.getLiveCodingDataForQdrant(livecoding);
    const openSearchData = this.addDataonOpenSearch(livecoding, userId, STATUS.active, 'update');
    return { response: livecoding, recordData: openSearchData };
  }

  async remove(id: number, companyId: number, userId: number) {
    const liveCoding = await this.liveCodingModel.findOne({ where: { id, companyId } });
    if (!liveCoding) {
      throw new NotFoundException(`LiveCoding with ID ${id} not found`);
    }
    await this.getLiveCodingDataForQdrant(liveCoding, 'delete');
    const openSearchData = this.addDataonOpenSearch(liveCoding, userId, STATUS.deleted, 'delete');
    await this.liveCodingModel.destroy({ where: { id, companyId } });
    return { response: liveCoding, recordData: openSearchData };
  }

  async search(query: Filter, companyId: number) {
    const parsedQuery = {
      database: query.database
        ? query.database?.split(",").map((el) => parseInt(el))
        : null,
      language: query.language
        ? query.language?.split(",").map((el) => parseInt(el))
        : null,
      package: query.package ? query.package : null,
      createdOn: query.createdOn ? query.createdOn : null,
    };

    Object.keys(parsedQuery).forEach((key) => {
      if (parsedQuery[key] === null) {
        delete parsedQuery[key];
      }
    });

    const propertyArray = [];
    const includeArray = [];

    for (const key in parsedQuery) {
      if (key === "language") {
        propertyArray.push({ languageId: parsedQuery[key] });
      } else if (key === "package") {
        propertyArray.push({ packageId: parsedQuery[key] });
      } else if (key === "createdOn") {
        propertyArray.push({
          createdAt: {
            [Op.gt]: parseDate(parsedQuery[key], "start"),
            [Op.lt]: parseDate(parsedQuery[key], null),
          },
        });
      } else if (key === "database") {
        includeArray.push({
          association: "assessment-database",
          where: { id: parsedQuery[key] },
        });
      }
    }
    let whereSearch: any[] = []
    if (query.search) {
      whereSearch.push({
        name: { [Op.iLike]: `%${query.search}%` }
      })
      whereSearch.push({
        description: { [Op.iLike]: `%${query.search}%` }
      })
    }

    if (parsedQuery.database) {
      return this.liveCodingModel.findAll({
        where: {
          [Op.and]: propertyArray,
          ...(!!whereSearch?.length && { [Op.or]: whereSearch }),
          companyId
        },
        include: includeArray,
      });
    } else {
      return this.liveCodingModel.findAll({
        where: {
          [Op.and]: propertyArray,
          ...(!!whereSearch?.length && { [Op.or]: whereSearch }),
          companyId
        },
      });
    }
  }

  async getConvergenceToken(authToken) {
    const gen = new JwtGenerator(
      "default",
      process.env.CONVERGENCE_PRIVATE_KEY
    );

    const { body } = await request(
      `${process.env.MICROSERVICES_PRA_URL}/api/user`,
      {
        method: "GET",
        headers: {
          Accept: "application/json",
          Authorization: authToken,
        },
      }
    );
    const user: any = await body.json();

    return gen.generate(user.id.toString(), {
      firstName: user.firstname,
      lastName: user.lastname,
      displayName: `${user.firstname} ${user.lastname}`.trim(),
      email: user.email,
    });
  }

  async inviteInterviewer(interviewer, redirectLink) {
    //TODO: The below service should be executed to get company logo
    //  const user =  await this.usersService.getUserByUserId(interviewer.id);

    return this.emailService.sendRecruitmentActivityService(
      "jobDetail.html",
      {

        companyLogo: "",
        position: "",
        body: `
         <p>Dear ${interviewer.name},</p>
         <p>We hope this message finds you well.</p>
         <p>You are invited to join an ongoing live coding interview to evaluate the candidate's performance as part of our recruitment process. We value your expertise and look forward to your insights.</p>
         <p>Click below link to join the meeting.</p>
         <br/>
         <a href="${redirectLink}"
         style="
           text-decoration: none;
           color: inherit;
           display: block;
           width: 100%;
           height: 100%;
         ">
         <button style="
         font-size: 14px;
         font-weight: 900;
         line-height: 100%;
         color: white;
         padding: 16px 28px;
         border: none;
         cursor: pointer;
         background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
         border-radius: 4px;
         ">Join Meeting</button>
         </a>`,
        userId: interviewer.id
      },
      interviewer.email,
      "Invitation To Evaluate Live Coding Assessment"
    )
  }

  async createDummyLiveCodingData(companyId: number) {
    const liveCodings = getDummyLiveTask(companyId);
    let recordData: any[] = [];
    if (liveCodings?.length) {
      for (let livetask of liveCodings) {
        try {

          if (livetask.status == 'DRAFT') {
            const liveDraft = await this.liveDraftModel.create({
              ...livetask,
              taskId: uuidv4(),
              status: STATUS.draft,
              assessmentType: TYPE.liveTask,
              companyId
            })
            recordData.push(this.getLiveCodingDataForQdrant(liveDraft, 'create'));
          } else {
            const liveTask = await this.create(livetask, companyId, 1);
            recordData.push(this.getLiveCodingDataForQdrant(liveTask.response, 'create'));
          }
        } catch (err) {
          console.log("Error creating dummy live coding data", livetask.name);
          continue;
        }
      }
      if (recordData?.length) {
        try {
          await putRecordOnQdrant(recordData);
          console.log('Dummy data inserted successfully for live-tasks');
        } catch (err) {
          console.log("Error inserting dummy live coding data", err);
        }
      }
    }
  }


  addDataonOpenSearch(livecoding: LiveCoding | LiveCodingDrafts, userId: number, status: string, action: string) {
    return {
      for: 'assessment',
      data: {
        ...(status === 'draft' ? { liveCodingDraftId: livecoding.id } : { liveCodingId: livecoding.id }),
        assessmentType: livecoding.assessmentType,
        name: livecoding.name,
        companyId: livecoding.companyId,
        createdBy: userId,
        status: status,
        action: action.toLowerCase(),
        liveCoding: livecoding,
        createdAt: new Date()
      }
    };
  }

  async getAllLiveCoding({ limit, offset }: { limit: number, offset: number }) {
    return await this.liveCodingModel.findAll({ limit, offset })
  }

  getLiveCodingDataForQdrant(liveCoding: LiveCoding | LiveCodingDrafts, action = 'create') {
    const str = `Live Coding Assessment: ${liveCoding.name} (Type: ${liveCoding.assessmentType}, Status: ${liveCoding.status})\nDescription: ${liveCoding.description}\n`;
    return {
      for: 'live-coding',
      id: liveCoding.taskId,
      action,
      ...(action === 'create' && {
        str,
        payload: {
          assessmentId: liveCoding.id,
          name: liveCoding.name,
          companyId: liveCoding.companyId,
          assessmentType: liveCoding.assessmentType,
          description: liveCoding.description,
          status: liveCoding.status,
          languageId: liveCoding.languageId,
          packageId: liveCoding.packageId,
          createdAt: liveCoding.createdAt,
          updatedAt: liveCoding.updatedAt,
        }
      })
    }
  }

}
