import { BadRequestException, HttpException, HttpStatus, Injectable, InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { DrugModel } from "./drug.model";
import { Op, Sequelize } from "sequelize";
import { Jobs } from "../job/job.model";
import { Location } from "../locations/location.model";
import { User } from "../users/users.model";
import { Subscribe } from "../subscribe/subscribe.model";
import { FilterJobsDrugDto, FilterCandidatesDrugDto, DrugDto } from "./drug.dto";
import { DSStatus } from "./drug.enum";
import { Round } from "../rounds/round.model";
import { ChangeBackgroundDto } from "../background-screening/background.dto";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { BGVStatus } from "../background-screening/background.enum";
import { roundCompletedSignal, roundRejectedSignal } from "apps/temporal/src/app/workflow/temporal/workflows";
import { HttpService } from "@nestjs/axios";
import { FileService } from "apps/pre-recruitment-api/src/file/file.service";
import { Company } from "../companies/companies.model";
@Injectable()
export class DrugService {
  constructor(
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(DrugModel)
    private drugRepository: typeof DrugModel,
    @InjectModel(Jobs) private jobRepository: typeof Jobs,
    @InjectModel(Subscribe) private subscribeRepository: typeof Subscribe,
    private readonly httpService: HttpService,
    private readonly fileService: FileService
  ) { }

  async getAllJobsCompanyWise(dto: FilterJobsDrugDto, companyId: number) {
    const where: any = { companyId, isAssessment: false, status: 'publish' };
    const locationWhere: any = {};
    const order = [];
    if (dto.search) {
      where.title = {
        [Op.iLike]: `%${dto.search}%`,
      };
    }
    if (dto.locations) {
      if (!Array.isArray(dto.locations)) {
        locationWhere.id = { [Op.eq]: dto.locations };
      } else {
        locationWhere.id = { [Op.in]: dto.locations };
      }
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }

    const verifiedInDSQuery = `(SELECT COUNT(*) FROM "drug-screening" AS ds WHERE ds."jobId" = "Jobs"."id" AND ds."DS_status" ILIKE '%${DSStatus.VERIFIED}%')`;
    const rejectedInDSQuery = `(SELECT COUNT(*) FROM "drug-screening" AS ds WHERE ds."jobId" = "Jobs"."id" AND ds."DS_status" ILIKE '%${DSStatus.REJECTED}%')`;
    const sentForDSQuery = `(SELECT COUNT(*) FROM "drug-screening" AS ds WHERE ds."jobId" = "Jobs"."id" AND ds."DS_status" NOT LIKE '%${DSStatus.IN_PROGRESS}%')`;
    const NotSentForDSQuery = `(SELECT COUNT(*) FROM "drug-screening" AS ds WHERE ds."jobId" = "Jobs"."id" AND ds."DS_status" ILIKE '%${DSStatus.NOT_INITIATED}%')`;

    return await this.jobRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where,
      order,
      attributes: [
        "id",
        "title",
        "numberOpenings",
        [Sequelize.literal(verifiedInDSQuery), "VerifiedCandidatesCount"],
        [Sequelize.literal(rejectedInDSQuery), "rejectedCandidatesCount"],
        [Sequelize.literal(sentForDSQuery), "sentCandidatesForDSCount"],
        [Sequelize.literal(NotSentForDSQuery), "NotSentCandidatesForDSCount"],
      ],

      include: [
        {
          model: Location,
          where: locationWhere,
        },
      ],
    });
  }

  async getAllCandidatesJobWise(
    dto: FilterCandidatesDrugDto,
    companyId: number,
    jobId: number
  ) {
    const userWhere: any = {};
    const drugWhere: any = { companyId };
    let where: any = { jobId }
    let hasUserFilter = false;
    let hasDrugFilter = false;
    const order = [];
    if (dto.search) {
      userWhere[Op.or] = [
        { firstname: { [Op.iLike]: `%${dto.search}%` } },
        { lastname: { [Op.iLike]: `%${dto.search}%` } },
      ];
    }

    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType]);
    } else {
      order.push(["createdAt", "DESC"]);
    }

    if (dto.vendor) {
      drugWhere.vendor = {
        [Op.eq]: dto.vendor,
      };
    }

    if (dto.dateFrom && dto.dateTo) {
      drugWhere.createdAt = {
        [Op.between]: [new Date(dto.dateFrom), new Date(dto.dateTo)],
      };
    } else if (dto.dateFrom || dto.dateTo) {
      if (dto.dateFrom) {
        drugWhere.createdAt = {
          [Op.gte]: new Date(dto.dateFrom),
        };
      }
      if (dto.dateTo) {
        drugWhere.createdAt = {
          [Op.lte]: new Date(dto.dateTo),
        };
      }
    }

    if (dto.search) {
      hasUserFilter = true;
    }

    if (dto.vendor || dto.status || dto.dateFrom || dto.dateTo) {
      hasDrugFilter = true;
    }
    if (dto.status) {
      const statusArray = dto.status?.split(",");
      const filteredStatus = statusArray.filter((item) => item !== "Not Initiated");

      if (filteredStatus.length > 0) {
        drugWhere.DS_status = { [Op.in]: filteredStatus };
        hasDrugFilter = true;
      }
      if (statusArray.includes("Not Initiated")) {
        where.drugOrderId = { [Op.is]: null };
        hasDrugFilter = false;
      }
    }
    return await this.subscribeRepository.findAndCountAll({
      limit: dto.limit,
      offset: dto.offset,
      distinct: true,
      where,
      include: [
        {
          model: User,
          where: userWhere,
          required: hasUserFilter,

          include: [
            {
              model: DrugModel,
              where: drugWhere,
              required: hasDrugFilter,
            },
          ],
        },
        {
          model: Round
        }
      ],
      attributes: { exclude: ["summary"] },
    });
  }

  async createDrugOrder(dto: DrugDto, companyId: number) {
    try {
      const data = await this.drugRepository.findOne({
        where: {
          jobId: dto.jobId,
          userId: dto.userId,
          companyId,
          DS_status: {
            [Op.not]: DSStatus.REJECTED,
          },
        },
        attributes: ["id"],
      });

      if (data) {
        throw new HttpException(
          "The Screening of this Candidate is balance or in progress",
          HttpStatus.CONFLICT
        );
      } else {
        const drugOrder = await this.drugRepository.create({
          ...dto,
          DS_status: dto.status,
          companyId,
        });

        await this.subscribeRepository.update(
          { drugOrderId: drugOrder.id },
          {
            where: {
              jobId: dto.jobId,
              userId: dto.userId,
            },
          }
        );

        if (drugOrder.DS_status === BGVStatus.VERIFIED || drugOrder.DS_status === BGVStatus.REJECTED) {
          await this.sendSignalToTemporal(drugOrder.DS_status, drugOrder.jobId, drugOrder.userId)
        }
        return drugOrder;
      }
    } catch (error) {
      Logger.error(error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getAllDrugOrderCompanyWise(companyId: number) {
    try {

      return await this.drugRepository.findAndCountAll({
        where: {
          companyId
        },
        include: [{
          model: User,
          attributes: ["id", "firstname", "lastname", "avatar"]
        }, {
          model: Jobs,
          where: { isAssessment: false },
          attributes: ["id", "title", "companyId"]
        }]
      })
    } catch (error) {
      Logger.error(error);
      throw new HttpException(error, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async changeDrugStatus(companyId, dto: ChangeBackgroundDto) {
    const { userId, jobId, status } = dto

    if (!userId) {
      throw new BadRequestException("User Id is not provided.")
    }
    if (!jobId) {
      throw new BadRequestException("Job Id is not provided.")
    }
    const drug = await this.drugRepository.findOne({ where: { userId: userId, jobId: jobId, companyId } })
    if (drug && status) {
      Object.assign(drug, { DS_status: status });
      await drug.save()
      await this.sendSignalToTemporal(status, drug.jobId, drug.userId)
    }
    return true;
  }
  async getDrugOrderById(drugId, companyId) {
    return await this.drugRepository.findOne({ where: { id: drugId, companyId }, include: [{ model: Jobs, where: { isAssessment: false }, attributes: ["id", "title", "status"] }] })
  }

  async getDrugOrderByUserId(userId, companyId, jobId = null) {
    return await this.drugRepository.findOne({ where: { userId, companyId, ...(!!jobId ? { jobId } : '') }, include: [{ model: Jobs, where: { isAssessment: false }, attributes: ["id", "title", "status"] }, Company] })
  }

  async uploadDrugReport({ body, userId, companyId }) {
    try {

      const user = await this.userRepository.findOne({ where: { id: userId } })
      if (user && body) {
        const Key = `company/${user.authId}/${userId}_${companyId}_drug_report`;

        await this.fileService.uploadOnS3(
          {
            Bucket: process.env.UPLOADSPRIVATE_NAME,
            Key,
            Body: body,
            ContentType: "application/pdf",
            ACL: 'private'
          }
        );
      }
    } catch (err) {
      Logger.log("Error uploading drug report.")
      throw new InternalServerErrorException("Error uploading drug report." + err)
    }
  }

  async getDrugReport({ userId, companyId }) {
    if (!userId) {
      throw new BadRequestException("UserId is not provided");
    }

    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (user && companyId) {
      const Key = `company/${user.authId}/${userId}_${companyId}_drug_report`;

      const pdfUrl = await this.fileService.getFromS3({
        Bucket: process.env.UPLOADSPRIVATE_NAME,
        Key,
      });

      if (pdfUrl) {
        try {

          const pdfResponse = await this.httpService.get(pdfUrl, { responseType: 'arraybuffer' }).toPromise();
          if (pdfResponse) {
            return pdfResponse.data;
          }
        } catch (error) {
          console.log(error)
          return;
        }
      }
    }
  }

  async sendSignalToTemporal(status, jobId, userId) {
    try {
      const url = `https://recruitment-micro.urecruits.com/api/temporal-workflow?jobId=${jobId}&id=${userId}`;
      let response
      try {
        const res = await this.httpService.get(url).toPromise();
        response = res?.data;
      } catch (error) {
        console.error("Error fetching data:", error);
      }

      const client = await connectToTemporal()
      if (client && response?.workflowid) {
        const handle = client.getHandle(response.workflowid)
        if (status === BGVStatus.VERIFIED) {
          await handle.signal(
            roundCompletedSignal,
            `Drug Screening is completed`
          );
        } else if (status === BGVStatus.REJECTED) {
          await handle.signal(
            roundRejectedSignal,
            `Drug Screening is Rejected`
          );
        }
      }
    } catch (error) {
      Logger.log("ERROR while signaling to temporal", error)
    }
  }
}
