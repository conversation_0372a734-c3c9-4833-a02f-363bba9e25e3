import { ApiProperty } from '@nestjs/swagger';
import { UpgradeSubscriptionDto } from './upgradeSubscription.dto';

export class CreateSubscriptionDto extends UpgradeSubscriptionDto {
  
  @ApiProperty({ example: 'pm_xxx', description: 'Payment method ID' })
  readonly paymentMethodId: string;
  @ApiProperty({
    example: '1',
    description:
      'Subscription Package ID. Do not required when Subscription ID is provided.',
  })
  readonly packageId: number;

  @ApiProperty({
    example: '3',
    description:
      'Subscription Plan ID. Do not required when Subscription ID is provided.',
  })
  readonly planId: number;

  @ApiProperty({
    example: 'price_xxx',
    description:
      'Subscription Price ID. Do not required when Subscription ID is provided.',
  })
  readonly priceId: string;

  @ApiProperty({
    example: 'sub_xxx',
    description:
      "Subscription ID. We'll try to update this subscription instead of create new one.",
  })
  readonly subscriptionId: string;

  @ApiProperty({
    example: {},
    description:
      "billing address of user",
  })
  readonly billingAddress: BillingAddress;
}

interface BillingAddress {
  line1: string;
  line2?: string;
  city?: string;
  state: string;
  postal_code: string;
  country: string;
}