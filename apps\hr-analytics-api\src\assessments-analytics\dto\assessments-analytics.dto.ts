import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class AssessmentsAnalyticsQueryDto {
  @ApiPropertyOptional({ example: "", description: "Search value to be found in the assessments data" })
  @IsOptional()
  search?: string;

  @ApiPropertyOptional({ example: "week", description: "Duration for area chart (week/month/year)" })
  @IsOptional()
  duration?: "week" | "month" | "year";

  @ApiPropertyOptional({ example: "ACTIVE", description: "Assessment status filter" })
  @IsOptional()
  Jobstatus?: string;

  @ApiPropertyOptional({ example: "assessmentId", description: "Sort field" })
  @IsOptional()
  sortBy?: string;

  @ApiPropertyOptional({ example: "asc", description: "Sorting order" })
  @IsOptional()
  sortType?: string;

  @ApiPropertyOptional({ example: 10, description: "Limit" })
  @IsOptional()
  limit?: number;

  @ApiPropertyOptional({ example: 0, description: "Offset" })
  @IsOptional()
  offset?: number;
}
