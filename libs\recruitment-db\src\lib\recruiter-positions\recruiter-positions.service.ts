import { Injectable } from '@nestjs/common';
import * as data from './data.json';
import { InjectModel } from '@nestjs/sequelize';
import { RecruiterPositions } from './recruiter-positions.model';
import { Op } from "sequelize";

@Injectable()
export class RecruiterPositionsService {
  constructor(
    @InjectModel(RecruiterPositions) private recruiterPositionsRepository: typeof RecruiterPositions,
  ) {}

  async seed() {
    await this.recruiterPositionsRepository.bulkCreate(data, {
      validate: true,
    }).then(async () => this.recruiterPositionsRepository.sequelize.query(`ALTER SEQUENCE "${this.recruiterPositionsRepository.tableName}_id_seq" RESTART WITH ${await this.recruiterPositionsRepository.count() + 1}`));
    return true;
  }

  async findByLabel(find?: string) {
    let where = {}
    if (find && find.length > 0) {
      where = {
        label: {
          [Op.iLike]: `%${find}%`,
        },
      }
    }
    return await this.recruiterPositionsRepository.findAll({
      where,
      limit: 40,
    });
  }
}
