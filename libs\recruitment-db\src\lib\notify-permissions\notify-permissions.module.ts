import { SequelizeModule } from "@nestjs/sequelize";
import { NotifyPermissionsModel } from "./notify-permissions.model";
import { Module } from "@nestjs/common";
import { NotifyPermissionsService } from "./notify-permissions.service";
import { NotificationsModule } from "../notifications/notifications.module";
import { SubscribeModule } from "../subscribe/subscribe.module";

@Module({
  imports: [SequelizeModule.forFeature([NotifyPermissionsModel]),NotificationsModule,SubscribeModule],
  providers: [NotifyPermissionsService],
  exports: [NotifyPermissionsService],
})
export class NotifyPermissionsModule {}
