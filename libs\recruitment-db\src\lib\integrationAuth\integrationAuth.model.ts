import { ApiProperty } from '@nestjs/swagger';
import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({ tableName: 'integrationAuth'})
export class IntegrationAuth extends Model<IntegrationAuth> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id?: number;

  @ApiProperty({ example: 'gmail', description: 'gmail, gcal or outlook' })
  @Column({ type: DataType.STRING })
  provider: string;

  @ApiProperty({ example: '1', description: 'Id of related user' })
  @Column({ type: DataType.INTEGER })
    userId: any;

  @ApiProperty({ example: 'pig latin', description: 'Encoded access token' })
  @Column({ type: DataType.TEXT('long')})
    accessToken:any;

  @ApiProperty({ example: 'pig latin', description: 'Encoded refresh token' })
  @Column({ type: DataType.TEXT('long') })
    refreshToken:any;

  @ApiProperty({ example: '653276122', description: 'Expiry Date' })
  @Column({ type: DataType.STRING, allowNull: true })
  expiryDate: string;  

  @ApiProperty({ example: 'https://mail.google.com', description: 'scope of the provider' })
  @Column({ type: DataType.STRING, allowNull: true })
  scope: string;
}
