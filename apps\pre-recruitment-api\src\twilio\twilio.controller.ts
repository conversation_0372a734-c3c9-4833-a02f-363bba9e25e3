import { <PERSON>, Get, Post, Body, Query, Param, UseGuards,Headers } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { TwilioVideoService } from "../../../../libs/twilio/src/lib/twilio.video.service";
import { Permissions, PermissionsGuard } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import { TwilioConversationService } from "@microservices/twilio";

@ApiTags("twilio-video-calling")
@Controller("twilio")
export class TwilioController {
  constructor(
    private readonly twilioVideoService: TwilioVideoService,
    private readonly twilioConversationService: TwilioConversationService
    ) {}

  @ApiOperation({ summary: "create the twilio video room" })
  @ApiResponse({ status: 200 })
  @Get("/join-chat")
  async createChatChannelAndToken(@Query() data:any) {
    return this.twilioConversationService.createChatChannelAndToken(data);
  }

  @ApiOperation({ summary: "create the twilio video room" })
  @ApiResponse({ status: 200 })
  @Get("/create-room")
  async createRoom(@Query() data:any) {
    return this.twilioVideoService.createVideoRoom(data);
  }

  @ApiOperation({ summary: "get the token to join the room" })
  @ApiResponse({ status: 200 })
  @Post("/access-token")
  async getToken(@Body() data) {
    return this.twilioVideoService.releaseTokenForVideo(
      data.userName,
      data.roomName
    );
  }

  @ApiOperation({ summary: "complete the meeting" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/video-complete")
  async completeMeeting(@Query("roomName") data: string,@Headers('authorization') authHeader:any) {
    return this.twilioVideoService.completeVideoRoom(data,authHeader);
  }
}
