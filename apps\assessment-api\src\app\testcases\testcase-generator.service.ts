import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class TestCaseGeneratorService {
  private readonly openAIApiKey: string;
  private readonly openAIEndpoint = 'https://api.openai.com/v1/chat/completions';
  private readonly model = 'gpt-4o';
  // Store generated test cases temporarily
  private generatedTestCasesCache = new Map<string, any>();

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {
    this.openAIApiKey = this.configService.get<string>('OPEN_AI_SECRET_KEY');
  }

  async generateTestCases(languageId: number, questionDescription: string, nameQuestion: string) {
    try {
      const languageName = this.getLanguageName(languageId);

      // Construct prompt for OpenAI
      const prompt = `You are an expert programmer tasked with generating test cases for a coding problem.

Problem Name: ${nameQuestion}
Problem Description: ${questionDescription}
Programming Language: ${languageName}

Please analyze the problem description carefully and generate appropriate test cases. Include:
1. A mix of normal inputs, edge cases, and boundary conditions relevant to this specific problem
2. Clear descriptions for each input parameter based on the problem requirements
3. Expected outputs that correctly solve the given problem
4. Choose DataTypes from the following based on the problem description:
    - INTEGER = "Integer"
    - STRING = "String"
    - ARRAY_INTEGER = "Integer Array"
    - ARRAY_STRING = "String Array"
5. Generate 5 test cases for the problem, ensuring coverage of:
   - Normal cases with typical valid inputs
   - Edge cases (e.g., empty inputs, minimum/maximum values, single elements)
   - Boundary conditions specific to the problem domain
   - Corner cases that might cause errors if not handled properly
   - Different scenarios that test various aspects of the solution

Your response should be a valid JSON object with the following structure:
{
  "testCaseInputs": [
    {
      "name": "string",
      "type": "string",
      "description": "string"
    }
  ],
  "testCases": [
    {
      "inputs": [
        { "name": "string", "value": any }
      ],
      "output": any
    }
  ]
}

Important:
- Ensure all JSON is valid and properly formatted
- Include at least 5 test cases covering different scenarios
- Make sure the types are appropriate for the language (${languageName})
- Include proper descriptions for each input parameter
- Analyze the problem description to determine the correct output format:
  * If the problem asks for a sorted array, return an array
  * If the problem asks for a number (sum, count, average, etc.), return a number
  * If the problem asks for a string, return a string
  * If the problem asks for a boolean (true/false), return a boolean
- The output should match exactly what the problem is asking for
- For problems involving arrays that should return arrays, DO return arrays as the output
- For mathematical problems, ensure calculations are correct
- For string problems, ensure string manipulations are accurate
- Test edge cases appropriately for the specific problem type
- IMPORTANT: The database can only store string outputs, so complex outputs like arrays will be automatically converted to JSON strings when saved`;

      // Call OpenAI API
      const openAIResponse = await this.callOpenAI(prompt);

      try {
        const responseContent = openAIResponse.choices[0]?.message?.content || '';
        const jsonMatch = responseContent.match(/\{[\s\S]*\}/);

        if (jsonMatch) {
          const testCasesData = JSON.parse(jsonMatch[0]);

          // Validate test cases structure
          if (!testCasesData.testCaseInputs || !Array.isArray(testCasesData.testCaseInputs) ||
            !testCasesData.testCases || !Array.isArray(testCasesData.testCases)) {
            throw new Error('Invalid test cases format: testCaseInputs or testCases array is missing');
          }

          // Ensure outputs are valid and handle edge cases
          testCasesData.testCases = testCasesData.testCases.map((testCase: any) => {
            if (testCase.output === undefined || testCase.output === null || testCase.output === '') {
              throw new Error('Invalid test case: output is empty or undefined');
            }
            return testCase;
          });

          // Generate a unique ID for this set of test cases
          const generationId = `gen_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

          // Store in cache with metadata
          this.generatedTestCasesCache.set(generationId, {
            id: generationId,
            languageId,
            questionName: nameQuestion,
            questionDescription,
            generatedAt: new Date().toISOString(),
            testCasesData
          });

          return {
            id: generationId,
            testCasesData
          };
        } else {
          throw new Error('Could not parse OpenAI response');
        }
      } catch (error) {
        throw new Error('Failed to generate test cases: Could not parse AI response');
      }
    } catch (error) {
      throw new Error(`Failed to generate test cases: ${error.message}`);
    }
  }

  getGeneratedTestCases(id: string) {
    const data = this.generatedTestCasesCache.get(id);
    return data;
  }

  removeFromCache(id: string) {
    return this.generatedTestCasesCache.delete(id);
  }

  // Format test cases for database storage based on the schema
  formatTestCasesForDB(testCasesData: any, questionId: number) {
    const testCases = [];

    if (testCasesData && testCasesData.testCases) {

      for (let i = 0; i < testCasesData.testCases.length; i++) {
        const tc = testCasesData.testCases[i];

        try {
          // Format input as JSON string
          const inputObj: any = {};
          tc.inputs.forEach((input: any) => {
            inputObj[input.name] = input.value;
          });

          const inputStr = JSON.stringify(inputObj);

          // Ensure output is a string, not an object or array
          let outputStr: string;
          if (typeof tc.output === 'object' || Array.isArray(tc.output)) {
            outputStr = JSON.stringify(tc.output);
          } else {
            outputStr = String(tc.output);
          }

          // Validate output string length
          if (outputStr.length > 255) {
            Logger.warn(`Output too long (${outputStr.length} chars), truncating: ${outputStr.substring(0, 50)}...`);
            outputStr = outputStr.substring(0, 250) + '...';
          }

          const testCase = {
            questionId,
            input: inputStr,
            output: outputStr,
            checked: false
          };

          testCases.push(testCase);
        } catch (error) {
        }
      }

    } else {
      if (testCasesData) {
        Logger.warn(`testCasesData keys: ${Object.keys(testCasesData)}`);
      } else {
        Logger.warn('testCasesData is null or undefined');
      }
    }

    return testCases;
  }

  // Format test case inputs for storage in the questions table
  formatTestCaseInputsForQuestion(testCasesData: any) {
    if (testCasesData && testCasesData.testCaseInputs) {
      const formattedInputs = JSON.stringify(testCasesData.testCaseInputs);
      Logger.log(`Formatted test case inputs: ${formattedInputs}`);
      return formattedInputs;
    }
    Logger.warn('No test case inputs found to format');
    if (testCasesData) {
      Logger.warn(`testCasesData keys: ${Object.keys(testCasesData)}`);
    } else {
      Logger.warn('testCasesData is null or undefined');
    }
    return null;
  }

  private async callOpenAI(prompt: string) {
    try {
      const payload = {
        model: this.model,
        messages: [
          {
            role: 'system',
            content: 'You are a helpful assistant that generates test cases for coding problems.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 2000
      };

      const headers = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openAIApiKey}`
      };

      const response = await firstValueFrom(
        this.httpService.post(this.openAIEndpoint, payload, { headers })
      );

      return response.data;
    } catch (error) {
      Logger.error('Error calling OpenAI API', error.response?.data || error.message);
      throw new Error('Failed to connect to OpenAI API');
    }
  }

  private getLanguageName(languageId: number): string {
    // Map language IDs to language names
    const languageMap = {
      45: 'Assembly',
      46: 'Bash',
      47: 'Basic',
      50: 'C',
      54: 'C++',
      51: 'C#',
      55: 'Common Lisp',
      56: 'D',
      57: 'Elixir',
      58: 'Erlang',
      44: 'Executable',
      59: 'Fortran',
      60: 'Go',
      61: 'JavaScript',
      62: 'Java',
      78: 'Kotlin',
      63: 'Lua',
      64: 'OCaml',
      65: 'Octave',
      66: 'Pascal',
      67: 'PHP',
      68: 'Prolog',
      69: 'Python',
      70: 'Ruby',
      71: 'Rust',
      72: 'TypeScript',
      73: 'Swift',
      74: 'VB.NET',
      75: 'Perl',
      76: 'Clojure',
      77: 'F#',
      79: 'Scala',
      80: 'SQL',
      81: 'Dart',
      82: 'COBOL',
      83: 'Julia',
      84: 'R',
      85: 'Haskell',
      86: 'Crystal',
      87: 'Groovy',
      88: 'Nim',
      89: 'Objective-C',
    };

    return languageMap[languageId] || 'Unknown';
  }
} 