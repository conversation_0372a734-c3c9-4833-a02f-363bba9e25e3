import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { TemporalWorkflowService } from "@microservices/recruitment-db";
import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";

@ApiTags("TemporalWorkflow")
@Controller("temporal-workflow")
export class TemporalWorkflowController {
  constructor (private readonly temporalWorkflowService: TemporalWorkflowService) {}

  @ApiOperation({ summary: "Get positions" })
  @ApiResponse({ status: 200 })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get()
  // @Permissions('job-post:view')
  getAll (@Query() data: any) {
    return this.temporalWorkflowService.find(data?.id,(data?.jobId));
  }

  @ApiOperation({ summary: "Get workflows by job id" })
  @ApiResponse({ status: 200 })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/jobs')
  // @Permissions('job-post:view')
  getAllByJob (@Query() data: any) {
    return this.temporalWorkflowService.findAllByJob(data?.jobId);
  }

}
