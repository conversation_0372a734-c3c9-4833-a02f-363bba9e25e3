import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Event } from './event.model';
import { EventUser } from './event-user.model';
import { CalendarService } from './calendar.service';

@Module({
  imports: [SequelizeModule.forFeature([Event, EventUser])],
  providers: [CalendarService],
  exports: [SequelizeModule, CalendarService],
})
export class CalendarModule {}
