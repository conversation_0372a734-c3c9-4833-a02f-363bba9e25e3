import { BadRequestException, HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectStripe } from "nestjs-stripe";
import Stripe from "stripe";
// import { PaymentMethodDto } from "./dto/paymentMethod.dto";
// import { PaymentMethodIdDto } from "./dto/paymentMethodId.dto";
import { InjectModel } from "@nestjs/sequelize";
import { Product } from "./product.model";
import { Op } from "sequelize";
import { CreateProductDto } from "./dto/create-product.dto";
import { GetProductsDto, UpdateProductDto } from "./dto/update-product.dto";
import * as productData from "./productdata.json";

@Injectable()
export class ProductService {
  public constructor (
    @InjectModel(Product) private productRepository: typeof Product,
    @InjectStripe() private readonly stripeClient: Stripe,
  ) {}

  async seed() {
    await this.productRepository
      .bulkCreate(productData, {
        validate: true,
      })
      .then(async () =>
        this.productRepository.sequelize.query(
          `ALTER SEQUENCE "${
            this.productRepository.tableName
          }_id_seq" RESTART WITH ${
            (await this.productRepository.count()) + 1
          }`
        )
      );
    return true;
  }

  async getProduct(productId){
    if(!productId){
      throw new BadRequestException("Product ID is not provided")
    }
    const product = await this.productRepository.findOne({where:{productId}})
    return product
  }

  async getAllProducts(dto:GetProductsDto){
    let where:any={}
    if(dto.productId){
      where.productId = dto.productId
    }
    if(dto.name){
      where.name={
        [Op.iLike]:`%${dto.name}%`
      }
    }
    if(dto.planId){
      where.planId = dto.planId
    }

    if(dto.productType){
      where.productType={
        [Op.iLike] : `%${dto.productType}%`
      }
    }
    if(dto.hasOwnProperty('active')){
      where.active = dto.active
    }

    return await this.productRepository.findAll({where})
  }

  async createProduct(dto:CreateProductDto){
    try{
      return await this.productRepository.create(dto)
    }catch(error){
      throw new Error("Error while creating product"+error)
    }
  }

  async updateProduct(dto:UpdateProductDto){
    try{
      if(!dto.productId){
        throw new BadRequestException("Product ID is not provided")
      }
      const product = await this.productRepository.findOne({where:{productId:dto.productId}})
      if (product) {
        Object.assign(product, { ...dto })
        return await product.save()
      } else {
        return await this.productRepository.create(dto);
      }
    }catch(error){
      throw new Error("Error while updating product"+error)
    }
  }
  async deleteProduct(dto:{productId:string}){
    try{
      const product = await this.productRepository.findOne({where:{productId:dto.productId}})
      if(!dto.productId && product){
        throw new BadRequestException("Product ID is not provided")
      }
      return await this.productRepository.destroy({where:{productId:dto.productId}})
    }catch(error){
      throw new Error("Error while deleting product"+error)
    }

  }
}
