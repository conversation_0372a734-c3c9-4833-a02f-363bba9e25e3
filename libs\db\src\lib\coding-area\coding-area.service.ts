import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { CodingArea } from './coding-area.model';

@Injectable()
export class CodingAreaService {
  constructor(
    @InjectModel(CodingArea) private codingAreaModel: typeof CodingArea
  ) {}

  create(dto: CodingArea): Promise<CodingArea> {
    return this.codingAreaModel.create(dto);
  }

  find(id: number): Promise<CodingArea> {
    return this.codingAreaModel.findByPk(id);
  }

  findAllByAssignmentId(assignmentId: number): Promise<CodingArea[]> {
    return this.codingAreaModel.findAll({
      where: {
        assignmentId,
      },
      order: [
        ['timestamp', 'ASC'],
      ],
    });

  }

  deleteAll(assignmentId: number) {
    return this.codingAreaModel.destroy({
      where: {
        assignmentId,
      },
    });
  }
}
