import { HttpService } from "@nestjs/axios";
import {
  BadRequestException,
  InternalServerErrorException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import {
  PersonType,
  UniversalDto,
  UniversalIntegrationDto,
} from "../../universal/universal.dto";
import { BackgroundService } from "../../background-screening/background.service";
import { DrugService } from "../../drug-screening/drug.service";
import { ScreeningType } from "../../universal/universal.enum";
import { User } from "../../users/users.model";
import { UniversalIntegration } from "../../universal/universal.model";
import { Op } from "sequelize";
import { BGVStatus } from "../../background-screening/background.enum";
import { NotifyPermissionsService } from "../../notify-permissions/notify-permissions.service";

@Injectable()
export class UniversalService {
  private universalAPI = process.env.UNIVERSAL_API_URL;

  private universalUsername;
  private universalPassword;

  private universalAccountDetails = {
    AccountNumber: "",
    PartnerSystem: "urecruits",
    Username: "",
    Package: "uRecruits",
    FCRAPurpose: "Employment",
    Certification:
      "By checking this box and clicking the Send Invitation button below, I hereby request that a link to a background screening ordering system (e-Forms Invitation) be sent to the subject's (consumer) email address provided. I certify that that: 1) the disclosure and authorization forms contained therein have been reviewed by my company and legal counsel and they satisfy all Fair Credit Reporting Act and other legal requirements, including a clear and conspicuous disclosure in a document that consists solely of the disclosure; 2) my order should not be processed before this written disclosure has been made to the consumer and his or her authorization obtained in writing; 3) the report will not be used in violation of any applicable federal or state equal employment opportunity law or regulation, and its use will comply with all applicable laws; and 4) I understand my obligations, have complied with, and will comply with all applicable laws pertaining to Consumer Reports and/or Investigative Consumer Reports as defined in the Fair Credit Reporting Act as amended (including but not limited to adverse action requirements); and (5) if the subject is a resident of a country other than the United States, I assume responsibility and agree to comply with any applicable data protection laws, including but not limited to, the General Data Protection Regulations (GDPR) as established by the European Union. I further certify that if an income level has been indicated for an Employment Screening purpose, that the individual may be employed at an annual salary which equals, or which may reasonably be expected to equal (or exceed), the indicated amount.",
    InvitationType: "",
    ReasonForTest: "",
    DrugTest: "",
  };

  constructor(
    private readonly httpService: HttpService,
    private readonly backgroundService: BackgroundService,
    private readonly drugService: DrugService,
    private readonly notificationsService: NotifyPermissionsService,
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(UniversalIntegration)
    private universalRepository: typeof UniversalIntegration
  ) { }

  async initiateScreening(
    dto: UniversalDto,
    companyId,
    accountDetails: UniversalIntegrationDto
  ) {
    try {
      let userBgScreening: any = {};
      let userDrugScreening: any = {};
      let drugStatus = BGVStatus.IN_PROGRESS;
      let bgStatus = BGVStatus.IN_PROGRESS;
      let initiateScreening = true;

      switch (dto.screeningType) {
        case ScreeningType.BACKGROUND: {
          userBgScreening = await this.backgroundService.getBackgroundByUserId(dto.userId, companyId);
          if (userBgScreening) {
            bgStatus = userBgScreening.BGV_status
            if (userBgScreening.BGV_status === BGVStatus.VERIFIED) {
              initiateScreening = false; // Skip if background is verified
            }
          }
          break;
        }
        case ScreeningType.DRUG: {
          userDrugScreening = await this.drugService.getDrugOrderByUserId(dto.userId, companyId);
          if (userDrugScreening) {
            drugStatus = userBgScreening.DS_status

            if (userDrugScreening.DS_status === BGVStatus.VERIFIED) {
              initiateScreening = false; // Skip if drug screening is verified
            }
          }
          break;
        }
        case ScreeningType.COMBO: {
          userBgScreening = await this.backgroundService.getBackgroundByUserId(dto.userId, companyId);
          userDrugScreening = await this.drugService.getDrugOrderByUserId(dto.userId, companyId);

          if (userBgScreening) {
            bgStatus = userBgScreening.BGV_status
          }
          if (userDrugScreening) {
            drugStatus = userBgScreening.DS_status
          }
          // Skip API call only if both are VERIFIED
          if (userBgScreening && userBgScreening.BGV_status === BGVStatus.VERIFIED &&
            userDrugScreening && userDrugScreening.DS_status === BGVStatus.VERIFIED) {
            initiateScreening = false; // Skip if both are verified
          }
          break;
        }
        default: {
          throw new BadRequestException(
            "The selected screening Type is not Valid. It must be either background, drug, or combo."
          );
        }
      }

      if (initiateScreening) {
        this.universalAccountDetails.InvitationType = dto.screeningType;

        Object.defineProperties(this.universalAccountDetails, {
          AccountNumber: {
            value: accountDetails.accountNumber,
          },
          Username: {
            value: accountDetails.userName,
          },
        });

        this.universalUsername = `${accountDetails.accountNumber}@universal`;
        this.universalPassword = accountDetails.password;
        let screeningType: string = ''

        switch (dto.screeningType) {
          case ScreeningType.BACKGROUND: {
            screeningType = ScreeningType.BACKGROUND
            break;
          }
          case ScreeningType.DRUG: {
            screeningType = ScreeningType.DRUG
            this.universalAccountDetails.ReasonForTest = "PRE";
            this.universalAccountDetails.DrugTest = "10PANELURINE";
            break;
          }
          case ScreeningType.COMBO: {
            screeningType = ScreeningType.COMBO
            this.universalAccountDetails.ReasonForTest = "PRE";
            this.universalAccountDetails.DrugTest = "10PANELURINE";
            break;
          }
          default: {
            throw new BadRequestException(
              "The selected screening Type is not Valid. It must be either of background, drug or combo"
            );
          }
        }

        const user = await this.userRepository.findOne({
          where: {
            id: {
              [Op.eq]: dto.userId,
            },
          },
        });

        const personData = {
          ApplicantID: dto.userId.toString() + "-" + dto.jobId + "-" + companyId + '-' + screeningType,
          FirstName: user.dataValues.firstname,
          MiddleName: user.dataValues.middlename,
          LastName: user.dataValues.lastname,
          Email: user.dataValues.email,
        };
        const data = {
          ...this.universalAccountDetails,
          Person: {
            ...personData,
          },
        };
        const config = {
          params: {
            format: "json",
          },
          auth: {
            username: this.universalUsername,
            password: this.universalPassword,
          },
        };
        const response = await this.httpService
          .post(this.universalAPI, data, config)
          .toPromise();

      }


      switch (dto.screeningType) {
        case ScreeningType.BACKGROUND: {
          await this.backgroundService.createBackgroundOrder({ ...dto, status: bgStatus }, companyId);
          break;
        }
        case ScreeningType.DRUG: {
          await this.drugService.createDrugOrder({ ...dto, status: drugStatus }, companyId);
          break;
        }
        case ScreeningType.COMBO: {
          await this.backgroundService.createBackgroundOrder({ ...dto, status: bgStatus }, companyId);
          await this.drugService.createDrugOrder({ ...dto, status: drugStatus }, companyId);
          break;
        }
        default: {
          throw new BadRequestException(
            "The selected screening Type is not Valid. It must be either of background, drug or combo"
          );
        }
      }
      return "Successfully initiated Screening"
    } catch (error) {
      Logger.log(error);
      throw new InternalServerErrorException(error);
    }
  }

  async addUniversalAccount(dto: UniversalIntegrationDto, companyId) {
    try {
      const data = await this.universalRepository.findOne({
        where: {
          [Op.or]: { accountNumber: dto.accountNumber, userName: dto.userName },
          companyId,
        },
        attributes: ["id"],
      });

      if (data) {
        throw new HttpException("Record already exists!", HttpStatus.CONFLICT);
      }

      if (!dto.password.trim()) {
        Object.defineProperty(dto, "password", {
          value: process.env.UNIVERSAL_PASSWORD,
        });
      }
      return await this.universalRepository.create({
        ...dto,
        companyId,
      });
    } catch (error) {
      Logger.log("Error generated during inserting Account Details", error);
    }
  }

  async removeUniversalAccount(companyId: number) {
    try {
      return await this.universalRepository.destroy({
        where: {
          companyId,
        },
      });
    } catch (error) {
      Logger.log(
        "Error generated during removing record from universal-integration table",
        error
      );
    }
  }

  async getAccountDetails(companyId: number) {
    try {
      return await this.universalRepository.findOne({
        where: {
          companyId,
        },
        attributes: ["accountNumber", "userName", "password"],
      });
    } catch (error) {
      Logger.log(
        "Error generated during getting record from universal-integration table",
        error
      );
    }
  }

  async handleUniversalEvents(body) {
    try {
      const applicantId = body.ApplicantID || null;
      if (!applicantId) return;
      console.log({ applicantId })

      const [userId, jobId, companyId, screeningType] = applicantId.split('-');
      let status = '';
      if (body.OrderStatus.includes('In Progress')) {
        status = BGVStatus.IN_PROGRESS
      } else if (body.OrderStatus.includes('Reviewed')) {
        status = BGVStatus.REVIEWED
      } else if (body.OrderStatus.includes('Ready for Review')) {
        status = BGVStatus.READY_FOR_REVIEW
      }

      if (!status) return;
      const document = !!body.Documents?.length && body.Documents[0];

      if (status === BGVStatus.READY_FOR_REVIEW) {
        if (screeningType === ScreeningType.COMBO) {
          const type = body.OrderStatus?.split(' ')
          if (type && type.length) {
            if (type[0] === 'Background') {
              await this.sendReviewEmail(ScreeningType.BACKGROUND, companyId, userId, body.OrderUrl);
            } else if (type[0] === 'Drug') {
              await this.sendReviewEmail(ScreeningType.DRUG, companyId, userId, body.OrderUrl);
            }
          }
        } else {
          await this.sendReviewEmail(screeningType, companyId, userId, body.OrderUrl);
        }
      }

      if (status === BGVStatus.REVIEWED && document && document?.FileContent) {
        const buffer = Buffer.from(document.FileContent, 'base64');
        let uploadBackground = false, uploadDrug = false, reportName = '';
        try {
          if (screeningType === ScreeningType.BACKGROUND) {
            uploadBackground = true
            reportName = 'background'
          } else if (screeningType === ScreeningType.DRUG) {
            uploadDrug = true
            reportName = 'drug'
          } else if (screeningType === ScreeningType.COMBO) {
            const name = document.FileName?.split('-');
            if (name && name.length) {
              if (name[4] === 'Background') {
                uploadBackground = true
                reportName = 'background'
              } else if (name[4] === 'Drug') {
                uploadDrug = true
                reportName = 'drug'
              }
            }
          }

          if (uploadBackground) {
            await this.backgroundService.uploadBackgroundReport({ body: buffer, userId, companyId });
          }
          if (uploadDrug) {
            await this.drugService.uploadDrugReport({ body: buffer, userId, companyId });
          }
          const candidate = await this.userRepository.findOne({ where: { id: userId } })
          const reportData = reportName === 'background' ? await this.backgroundService.getBackgroundByUserId(userId, companyId, jobId) : await this.drugService.getDrugOrderByUserId(userId, companyId, jobId)
          if (reportData) {
            await this.notificationsService.sendNotification({
              image: '',
              title: `Verify ${reportName} Report for ${candidate?.firstname} ${candidate?.lastname}`,
              message: `The ${reportName} report for ${candidate.firstname} ${candidate.lastname} is pending verification. Please review and verify the report at your earliest convenience.`,
              userId: reportData?.company?.ownerId,
              jobData: { link: reportName == 'background' ? `/recruitment/${reportName}-screening/candidates/${jobId}/${reportData?.id}` : `/recruitment/drug-screening/candidates/${jobId}` }
            })
          }
        } catch (error) {
          console.error("Error while uploading report on s3:", error);
        }
      }

      await this.changeStatus(screeningType, companyId, jobId, status, userId);
      console.log("Successfully changed status");
    } catch (error) {
      console.error("Error while handling universal events:", error);
    }
  }

  async sendReviewEmail(screeningType, companyId, userId, orderUrl) {
    const reportType = screeningType === ScreeningType.BACKGROUND ? 'Background' : 'Drug';
    await this.backgroundService.sendReviewMailToCompany(companyId, userId, orderUrl, reportType);
  };

  async changeStatus(screeningType, companyId, jobId, status, userId) {
    if (screeningType === ScreeningType.BACKGROUND) {
      await this.backgroundService.changeBackgroundStatus(companyId, { jobId, status, userId });
    } else if (screeningType === ScreeningType.DRUG) {
      await this.drugService.changeDrugStatus(companyId, { jobId, status, userId });
    } else if (screeningType === ScreeningType.COMBO) {
      await this.backgroundService.changeBackgroundStatus(companyId, { jobId, status, userId });
      await this.drugService.changeDrugStatus(companyId, { jobId, status, userId });
    }
  };


}
