import {
  Injectable,
  Inject,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { Client } from "@opensearch-project/opensearch";
import * as moment from "moment";
import {
  membersAgePieAnalyticsQueryBody,
  membersAnalyticsQueryBody,
} from "./users-analytics.query";
import { OpensearchService } from "../opensearch/opensearch.service";
import { getStartEndDate } from "../utils/getDateOfJoiningRanges"
import { MembersAnalyticsQueryDto } from "./dto/members-analytics.dto";

@Injectable()
export class UsersAnalyticsService {
  constructor(
    private readonly opensearchService: OpensearchService
  ) { }

  async getMembersAgeAnalyticsPie(companyId) {
    try {

      const queryBody = membersAgePieAnalyticsQueryBody(companyId);
      const response = await this.opensearchService.searchIndexSpecificData(queryBody, 'users');
      return response.body.aggregations;
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException(error, "Failed to fetch data");
    }
  }

  async getMembersAnalytics(queryParams: MembersAnalyticsQueryDto, companyId) {
    console.log('helloooooooooo')
    try {
      const {
        department,
        jobLocation,
        search,
        sortBy,
        sortType,
        limit,
        offset,
        dateOfBirthFrom,
        dateOfBirthTo,
        dateOfJoiningFrom,
        dateOfJoiningTo,
        dateOfJoiningDuration,
        education,
        roles
      } = queryParams;

      let departmentQuery = department?.split(',');
      let educationQuery = education?.split(',');
      let jobLocationQuery = jobLocation?.split(',').map(i => +i);
      let rolesQuery = roles?.split(',').map(i => +i);

      const body: any = membersAnalyticsQueryBody(limit, offset, companyId, queryParams);

      if (departmentQuery?.length) {
        const departmentFilter = {
          bool: {
            should: departmentQuery.map(department => ({
              match: {
                "department": department
              }
            })),
            minimum_should_match: 1
          }
        };

        if (!body.query.bool.filter) {
          body.query.bool.filter = [];
        }
        body.query.bool.filter.push(departmentFilter);
      }

      if (educationQuery?.length) {
        const educationFilter = {
          bool: {
            should: educationQuery.map(degree => ({
              wildcard: {
                "degree": `${degree}*`
              }
            })),
            minimum_should_match: 1
          }
        };

        if (!body.query.bool.filter) {
          body.query.bool.filter = [];
        }
        body.query.bool.filter.push(educationFilter);
      }

      if (rolesQuery?.length) {
        const rolesFilter = {
          terms: {
            "role.id": rolesQuery
          }
        };

        if (!body.query.bool.must) {
          body.query.bool.must = [];
        }
        body.query.bool.must.push(rolesFilter);
      }

      if (dateOfBirthFrom && dateOfBirthTo) {
        const dateFrom = new Date(dateOfBirthFrom);
        const dateTo = new Date(dateOfBirthTo);
        const startOf = moment(dateFrom).startOf('day').toDate();
        const endOf = moment(dateTo).endOf('day').toDate();

        const dateOfBirthFilter = {
          "range": {
            "birthDate": {
              "gte": startOf,
              "lte": endOf
            }
          }
        };
        if (!body.query.bool.filter) {
          body.query.bool.filter = [];
        }
        body.query.bool.filter.push(dateOfBirthFilter);
      }

      if (dateOfJoiningFrom && dateOfJoiningTo) {
        const dateFrom = new Date(dateOfJoiningFrom);
        const dateTo = new Date(dateOfJoiningTo);
        const startOf = moment(dateFrom).startOf('day').toDate();
        const endOf = moment(dateTo).endOf('day').toDate();

        const dateOfJoiningFilter = {
          "range": {
            "dateJoining": {
              "gte": startOf,
              "lte": endOf
            }
          }
        };
        if (!body.query.bool.filter) {
          body.query.bool.filter = [];
        }
        body.query.bool.filter.push(dateOfJoiningFilter);
      } else if (dateOfJoiningDuration && dateOfJoiningDuration !== 'All time') {
        const { startDate, endDate } = getStartEndDate(dateOfJoiningDuration);
        const dateOfJoiningFilter = {
          "range": {
            "dateJoining": {
              "gte": startDate,
              "lte": endDate
            }
          }
        };

        if (!body.query.bool.filter) {
          body.query.bool.filter = [];
        }
        body.query.bool.filter.push(dateOfJoiningFilter);
      }

      if (search) {
        const searchQuery = {
          bool: {
            should: [
              {
                wildcard: {
                  "name.firstname": {
                    value: search + "*",
                    boost: 1.0
                  }
                }
              },
              {
                wildcard: {
                  "name.lastName": {
                    value: search + "*",
                    boost: 1.0
                  }
                }
              }
            ],
            minimum_should_match: 1
          }
        };


        if (!body.query.bool.must) {
          body.query.bool.must = [];
        }
        body.query.bool.must.push(searchQuery);
      }

      const response = await this.opensearchService.searchIndexSpecificData(body, 'users');
      return { data: response.body.aggregations.distinct_users?.buckets, totalMembers: response.body.aggregations.total_users };
    } catch (error) {
      Logger.error('Error:', error);
      throw new InternalServerErrorException(error, 'Failed to fetch data');
    }
  }

}
