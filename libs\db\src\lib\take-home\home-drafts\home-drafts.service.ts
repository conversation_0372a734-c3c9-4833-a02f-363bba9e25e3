import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { parseDate } from 'tools/utils';
import { v4 as uuidv4 } from 'uuid';
import { Filter } from '../../live-coding/filter.interface';
import { STATUS, TYPE } from '../../live-coding/live-coding.model';
import { TakeHomeDrafts } from './home-drafts.model';
import { QuestionsService } from '../../questions/questions.service';
import { TestCaseService } from '../../testcases/testcase.service';
import { Questions } from '../../questions/questions.model';
import { TestCase } from '../../testcases/testcase.model';

@Injectable()
export class TakeHomeDraftsService {
  constructor(
    @InjectModel(TakeHomeDrafts)
    private takeHomeDraftsModel: typeof TakeHomeDrafts,
    private questionsService: QuestionsService,
    private testCaseService: TestCaseService,
  ) { }

  async getAllTakeHomeDrafts({limit,offset}:{limit:number,offset:number}){
    console.log({limit,offset});
    return await this.takeHomeDraftsModel.findAll({limit,offset,include:[{association:'questions',required:false,include:[{association:'testcases',required:false}]}]})
  }
  findOneById(id: number): Promise<TakeHomeDrafts> {
    return this.takeHomeDraftsModel.findByPk(id);
  }

  findByTaskId(taskId, companyId) {
    return this.takeHomeDraftsModel.findOne({
      where: { taskId, companyId },
      include: [{ association: 'questions', include: [{ association: 'testcases', required: false }] }],
    })
  }

  findAll(companyId: number): Promise<TakeHomeDrafts[]> {
    return this.takeHomeDraftsModel.findAll({
      where: { companyId },
      include: [{ association: 'questions', include: [{ association: 'testcases', required: false }] }],
    });
  }
  async create(dto, companyId: number) {

    const data = dto.taskId && await this.takeHomeDraftsModel.findOne({ where: { taskId: dto.taskId } })
    if (data) {
      const takeHome = await data.update({ ...dto, status: 'DRAFT', assessmentType: TYPE.takeHome, companyId }, {
        include: [
          {
            association: 'questions',
            include: [{ association: 'testcases', required: false }],
          },
        ],
      })
      const recordData = this.getTakeHomeDraftDataForQdrant(takeHome);
      return {response:takeHome,recordData};
    } else {
      const takeHome = await this.takeHomeDraftsModel.create(
        { ...dto, taskId: uuidv4(), status: 'DRAFT', assessmentType: TYPE.takeHome, companyId },
        {
          include: [
            {
              association: 'questions',
              include: [{ association: 'testcases', required: false }],
            },
          ],
        }
      );
      const recordData = this.getTakeHomeDraftDataForQdrant(takeHome);
      return {response:takeHome,recordData};
    }
  }

  async update(id: number, dto: TakeHomeDrafts, companyId: number, userId: number) {
    const takeHome = await this.takeHomeDraftsModel.findOne({
      where: { id, companyId }, include: [
        {
          association: 'questions',
          include: [{ association: 'testcases', required: false }],
        },
      ]
    });
    if (takeHome) {
      if (dto.questions) {
        for (let i = 0; i < dto.questions.length; i++) {
          let question = typeof dto.questions[i].id !== 'string' && await this.questionsService.findOneById(dto.questions[i].id);
          const questionData = {
            ...dto.questions[i],
            takeHomeDraftId: id
          };
          if (question) {
            await this.questionsService.update(dto.questions[i].id, questionData as Questions);
          } else {
            delete questionData.id
            question = await this.questionsService.create(questionData as Questions);
          }
          if (dto.questions[i].testcases && question) {
            for (let j = 0; j < dto.questions[i].testcases.length; j++) {
              const test = await this.testCaseService.update(dto.questions[i].testcases[j].id, { ...dto.questions[i].testcases[j], questionId: question?.id } as TestCase);

            }
          }
        }
      }
      await takeHome.update({ ...dto }, { where: { id, companyId } })
      const recordData = this.getTakeHomeDraftDataForQdrant(takeHome);
      return {response:takeHome,recordData};
    }
  }

  async updateByTaskId(taskId: string, dto: TakeHomeDrafts, companyId: number, userId: number) {
    const takeHome = await this.takeHomeDraftsModel.findOne({ where: { taskId, companyId } });
    if (takeHome) {
      const updatedTakeHome = await this.update(takeHome.id, dto, companyId, userId);
      return updatedTakeHome;
    }
  }

  async remove(id: number) {
    const takeHomeDraft = await this.takeHomeDraftsModel.findOne({
      where: { id },
      include: [
        {
          association: 'questions',
          include: [{ association: 'testcases', required: false }],
        },
      ]
    });
    if (takeHomeDraft) {
      if (takeHomeDraft.questions && takeHomeDraft.questions.length > 0) {
        for (let i = 0; i < takeHomeDraft.questions.length; i++) {
          await this.questionsService.remove(takeHomeDraft.questions[i].id);
        }
      }
      const recordData = this.getTakeHomeDraftDataForQdrant(takeHomeDraft,"delete");
      await takeHomeDraft.destroy()
      return {response:takeHomeDraft,recordData};
    }
  }

  async search(query: Filter, companyId: number) {
    const parsedQuery = {
      database: query.database ? query.database?.split(',').map((el) => parseInt(el)) : null,
      language: query.language ? query.language?.split(',').map((el) => parseInt(el)) : null,
      package: query.package ? query.package : null,
      createdOn: query.createdOn ? query.createdOn : null,
    };

    Object.keys(parsedQuery).forEach((key) => {
      if (parsedQuery[key] === null) {
        delete parsedQuery[key];
      }
    });

    const propertyArray = [];
    const includeArray = [];

    for (const key in parsedQuery) {
      if (key === 'language') {
        propertyArray.push({ languageId: parsedQuery[key] });
      } else if (key === 'package') {
        propertyArray.push({ packageId: parsedQuery[key] });
      } else if (key === 'createdOn') {
        propertyArray.push({
          createdAt: { [Op.gt]: parseDate(parsedQuery[key], 'start'), [Op.lt]: parseDate(parsedQuery[key], null) },
        });
      } else if (key === 'database') {
        includeArray.push({ association: 'assessment-database', where: { id: parsedQuery[key] } });
      }
    }

    let whereSearch: any[] = []
    if (query.search) {
      whereSearch.push({
        name: { [Op.iLike]: `%${query.search}%` }
      })
      whereSearch.push({
        description: { [Op.iLike]: `%${query.search}%` }
      })
    }

    if (parsedQuery.database) {
      return this.takeHomeDraftsModel.findAll({
        where: {
          companyId,
          ...(!!whereSearch?.length && { [Op.or]: whereSearch })
        },
        include: [
          { association: 'questions', where: { [Op.and]: propertyArray } },
          {
            association: 'assessment-database',
            where: { id: includeArray },
          },
        ],
      });
    } else {
      return this.takeHomeDraftsModel.findAll({
        where: {
          companyId,
          ...(!!whereSearch?.length && { [Op.or]: whereSearch })
        },
        include: [{ association: 'questions', where: { [Op.and]: propertyArray } }],
      });
    }
  }

  getTakeHomeDraftDataForQdrant(takeHomeDraft: TakeHomeDrafts, action='create') {
    const { name, assessmentType, description, questions } = takeHomeDraft;
    let formattedString = `Take home Task Draft Assessment: ${name} (Type: ${assessmentType}, Status: ${takeHomeDraft.status})\nDescription: ${description}\n\nQuestions:\n`;

    questions?.forEach((q, index) => {
        const sampleTestCase = q.testcases?.[0];
        let sampleInput = sampleTestCase ? JSON.parse(sampleTestCase.input)[0] : null;
        let sampleOutput = sampleTestCase ? sampleTestCase.output : null;

        formattedString += `${index + 1}. ${q.name} - ${q.description}\n`;
        if (sampleInput && sampleOutput) {
            formattedString += `   Sample Input: ${sampleInput} → Expected Output: ${sampleOutput}\n`;
        }
        formattedString += `\n`;
    });

    return {
      for:"take-home",
      id:takeHomeDraft.taskId,
      action,
      ...(action === 'create' && {
      str:formattedString,
      payload:{
        assessmentId:takeHomeDraft.id,
        name,
        companyId:takeHomeDraft.companyId,
        assessmentType,
        description,
        questions,
        status:takeHomeDraft.status,
        createdAt:takeHomeDraft.createdAt,
        updatedAt:takeHomeDraft.updatedAt,
      }
      })
    }
  }
}
