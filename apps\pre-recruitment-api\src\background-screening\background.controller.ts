import {
    Controller,
    Post,
    Body,
    UseGuards,
    Get,
    Query,
    Patch,
    Delete,
    Param,
    Res,
  } from "@nestjs/common";
  import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
  } from "@nestjs/swagger";
  import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
  import { AuthGuard } from "@nestjs/passport";
  import {
    BackgroundService,
    BackgroundModel,
    FilterJobsBackgroundDto,
    FilterCandidatesBackgroundDto,
    BackgroundDto,
    ChangeBackgroundDto
  } from "@microservices/recruitment-db";

  //   companyId: user["https://urecruits.com/companyId"],
  //   tenantId: user["https://urecruits.com/tenantId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   userId: user["https://urecruits.com/userId"],

  @ApiTags("Background Screening")
  @ApiBearerAuth("access-token")
  @Controller("background")
  export class BackgroundController {
    constructor(private readonly backgroundService: BackgroundService) {}

    @ApiOperation({ summary: "Get Jobs In BackgrounScreening" })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/jobs")
    @Permissions("background:view","job-post:view")
    getAllJobsCompanyWise(
      @Query() query: FilterJobsBackgroundDto,
      @AuthUser() user: any
    ) {
      return this.backgroundService.getAllJobsCompanyWise(
        query,
        user["https://urecruits.com/companyId"]
      );
    }

    @ApiOperation({ summary: "Get background list of candidates for dashboard" })
    @ApiResponse({ status: 200, type: BackgroundModel })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/company")
    @Permissions("background:view")
    getBackgroundByCompanyId( @AuthUser() user: any) {
      return this.backgroundService.getAllBackgroundCompanyWise(user["https://urecruits.com/companyId"])
    }
    
    @ApiOperation({ summary: "Get Candidates In BackgrounScreening" })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/candidates/:jobId")
    @Permissions("background:view")
    getAllCandidatesJobWise(
      @Param("jobId") jobId: number,
      @Query() query: FilterCandidatesBackgroundDto,
      @AuthUser() user: any
    ) {
      return this.backgroundService.getAllCandidatesJobWise(
        query,
        user["https://urecruits.com/companyId"],
        jobId
      );
    }
  

    @ApiOperation({ summary: "Edit Background By BackgroundId" })
    @ApiResponse({ status: 200, type: BackgroundModel })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Patch("/:backgroundId")
    @Permissions("background:edit")
    updateById(
      @Body() dto: BackgroundDto,
      @AuthUser() user: any,
      @Param("backgroundId") backgroundId: number
    ) {
      return this.backgroundService.updateById(
        backgroundId,
        dto,
        user["https://urecruits.com/companyId"]
      );
    }

    @ApiOperation({ summary: "Get Background By BackgroundId" })
    @ApiResponse({ status: 200, type: BackgroundModel })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/:backgroundId")
    @Permissions("background:edit")
    getById(
      @AuthUser() user: any,
      @Param("backgroundId") backgroundId: number,
    ) {
      return this.backgroundService.getBackgroundById(
        backgroundId,
        user["https://urecruits.com/companyId"]
      );
    }

    @ApiOperation({ summary: "Edit Background status By BackgroundId" })
    @ApiResponse({ status: 200 })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Patch("/report/change-status")
    @Permissions("background:edit")
    updateStatus(
      @Body() dto: ChangeBackgroundDto,
      @AuthUser() user: any,
    ) {
      return this.backgroundService.changeBackgroundStatus(
        user["https://urecruits.com/companyId"],
        dto,
      );
    }

    @ApiOperation({ summary: "Get Background screening report By BackgroundId" })
    @ApiResponse({ status: 200 })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/report/:userId")
    @Permissions("background:view")
    async getReportById(
      @AuthUser() user: any,
      @Res() res: any,
      @Param("userId") userId: number
    ) {
      const pdfBuffer = await this.backgroundService.getBackgroundReport({
        userId,
        companyId: user["https://urecruits.com/companyId"]
      });

      if (pdfBuffer && pdfBuffer.length > 0) {
        res.setHeader('Content-Type', 'application/pdf');
        res.setHeader('Content-Disposition', 'inline; filename="report.pdf"');
        res.send(pdfBuffer);
      } else {
        console.error("Received empty PDF buffer");
        res.status(404).send("No report available");
      }
    }

    @ApiOperation({ summary: "Create Background Order" })
    @ApiResponse({ status: 200, type: BackgroundModel })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Post()
    @Permissions("background:add")
    create(@Body() dto: BackgroundDto, @AuthUser() user: any) {
      return this.backgroundService.createBackgroundOrder(
        dto,
        user["https://urecruits.com/companyId"]
      );
    }

  }
