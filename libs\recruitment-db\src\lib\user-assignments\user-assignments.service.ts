import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { UserAssignments } from "./user-assignments.model";
import { ChangeUserAssignmentStatusDto, CreateUserAssignmentDto } from "./dto/user-assignments.dto";
@Injectable()
export class UserAssignmentsService {
  constructor(
    @InjectModel(UserAssignments) private userAssignmentRepository: typeof UserAssignments,
  ) { }

  async create(dto: CreateUserAssignmentDto, companyId: number){
    if(!dto.userId){
      throw new BadRequestException("UserId is not provided");
    }
    if (!dto.assignmentId) {
      throw new BadRequestException("Assignment Id is not provided");
    }
    if (!dto.assignmentName) {
      throw new BadRequestException("assignmentName is not provided");
    }
    if(!dto.hasOwnProperty('hasActiveAssignment')){
      dto.hasActiveAssignment = false
    }

    const data = await this.userAssignmentRepository.findOne({where:{userId:dto.userId,companyId}})
    if(data){
      Object.assign(data,dto)
      return await data.save()
    }else{
      return await this.userAssignmentRepository.create({...dto,companyId})
    }
  }

  async delete(userId, companyId){
    if (!userId) {
      throw new BadRequestException("UserId is not provided.");
    }
    await this.userAssignmentRepository.destroy({where:{userId:userId,companyId:companyId}})
    return 'Successfully deleted data.'
  }

  async changeActiveAssignmentStatus(dto:CreateUserAssignmentDto, companyId){
    const { hasActiveAssignment,userId} = dto
    if (!userId) {
      throw new BadRequestException("UserId is not provided");
    }
    const data = await this.userAssignmentRepository.findOne({where:{companyId,userId}})
    if(data && hasActiveAssignment !== undefined){
      Object.assign(data,dto);
      await data.save()
    }
  }

  async getAssignmentStatusByUser(userId,companyId){
    if (!userId) {
      throw new BadRequestException("UserId is not provided");
    }
    return await this.userAssignmentRepository.findOne({ where: { companyId, userId } })
  }

}
