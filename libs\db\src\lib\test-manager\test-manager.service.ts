import {
  BadRequestException,
  HttpException,
  Injectable, Logger, NotFoundException
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";

import { TestManager } from "./test-manager.model";
import { TestManagerDto } from "./dto/create-test-manager.dto";
import { findAndUpdate } from "./dto/findAndUpdate.dto";

@Injectable()
export class TestManagerService {
  constructor(
    @InjectModel(TestManager)
    private testManagerModel: typeof TestManager
  ) {}


  async create(dto:TestManagerDto){
    try {
      const existingPlayback = await this.testManagerModel.findOne({
        where: {
          jobId: dto.jobId,
          candidateId: dto.candidateId,
          assessmentType: dto.assessmentType,
        }
      });

      if (existingPlayback) {
        throw new HttpException("Test Id already exists!", 400);
      } else {
        await this.testManagerModel.create(dto);
        return { message: "Record created successfully", status: 201 };
      }
    } catch (error) {
      Logger.log("Error : ",error);
      if (error?.status === 400) {
        throw new BadRequestException({
          message: error?.message,
          statusCode: error?.status,
        });
      } else if (error?.status === 404) {
        throw new NotFoundException({
          message: error?.message,
          statusCode: error?.status,
        });
      }
    }
  }

  async findOne(id,dto, user?){
    const companyId = user ? user["https://urecruits.com/companyId"] : '';
    if (!dto.jobId || !dto.assessmentType || !id || (user && !companyId)) {
      throw new BadRequestException({
        message: "Bad Request",
        statusCode: 400,
      });
    }


    try {
      const testManager = await this.testManagerModel.findOne({
        where: {
          jobId: dto.jobId,
          candidateId: id,
          assessmentType: dto.assessmentType,
          ...(companyId ? {companyId} : ''),
        },
      }).catch(()=>{
        throw new NotFoundException({
          message: "Test not scheduled yet",
          statusCode: 404,
        });
      })

      if (!testManager) {
        throw new NotFoundException({
          message: "Test not scheduled yet",
          statusCode: 404,
        });
      }
      return testManager;
    } catch (error) {
      Logger.error("Error:", error);
      if(error?.status===400){
        throw new BadRequestException({
          message: error?.message,
          statusCode: error?.status,
        });
      }else if(error?.status===404){
        throw new NotFoundException({
          message: error?.message,
          statusCode: error?.status,
        });
      }

    }
  }

  async findByJobId(userId,jobId){
    if (!jobId || !userId) {
      throw new BadRequestException({
        message: "Bad Request",
        statusCode: 400,
      });
    }


    try {
      const testManager = await this.testManagerModel.findAll({
        where: {
          jobId: jobId,
          candidateId: userId,
        },
      }).catch(()=>{
        throw new NotFoundException({
          message: "Test not scheduled yet",
          statusCode: 404,
        });
      })

      if (!testManager) {
        throw new NotFoundException({
          message: "Test not scheduled yet",
          statusCode: 404,
        });
      }
      return testManager;
    } catch (error) {
      Logger.error("Error:", error);
      if(error?.status===400){
        throw new BadRequestException({
          message: error?.message,
          statusCode: error?.status,
        });
      }else if(error?.status===404){
        throw new NotFoundException({
          message: error?.message,
          statusCode: error?.status,
        });
      }

    }
  }

  async update(id: number, dto:findAndUpdate) {
    if (!dto.jobId || !dto.assessmentType || !id) {
      throw new BadRequestException({
        message: "Bad Request",
        statusCode: 400,
      });
    }
  
    let testManager = await this.testManagerModel.findOne({
      where: {
        jobId: dto.jobId,
        candidateId: id,
        assessmentType: dto.assessmentType,
      },
    }).catch(() => {
      throw new NotFoundException({
        message: "Test not scheduled yet",
        statusCode: 404,
      });
    });
  
    if (!testManager) {
      throw new NotFoundException({
        message: "Test not scheduled yet",
        statusCode: 404,
      });
    }
  
    if (testManager.testStatus === TestStatus.NOT_STARTED && dto.startTime) {
      await testManager.update(
        {
          testStatus: TestStatus.IN_PROGRESS,
          startTime: dto.startTime,
        },
        { where: { candidateId: id } }
      );
    } else if (testManager.testStatus === TestStatus.IN_PROGRESS &&  dto?.testStatus==="Completed") {
      await testManager.update(
        { testStatus: TestStatus.COMPLETED },
        { where: { candidateId: id } }
      );
    }
  
    return { message: "Record updated successfully", status: 200 };
  }
}
enum TestStatus {
  NOT_STARTED = 'Not Started',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
}