import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Observable } from 'rxjs';
import { Reflector } from '@nestjs/core';

@Injectable()
export class PermissionsGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const routePermissions = this.reflector.get<string[]>('permissions', context.getHandler());

    const userPermissions = context.getArgs()[0].user.permissions;

    if (!routePermissions) {
      return true;
    }

    if(routePermissions.includes('OR')){
      const newRoutePermissions = routePermissions.filter( (route: string) => route != 'OR');
      const hasPermission = () => newRoutePermissions.some((routePermission) => userPermissions.includes(routePermission));
      return hasPermission();
    }else if( routePermissions.includes('AND')){
      const newRoutePermissions = routePermissions.filter( (route: string) => route != 'AND');
      const hasPermission = () => newRoutePermissions.every((routePermission) => userPermissions.includes(routePermission));

      return hasPermission();
    }else{
      const hasPermission = () => routePermissions.every((routePermission) => userPermissions.includes(routePermission));
  
      return hasPermission();
    }

  }
}
