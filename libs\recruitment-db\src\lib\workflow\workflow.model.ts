import {
  <PERSON><PERSON>s<PERSON><PERSON>,
  <PERSON>umn,
  <PERSON>Type,
  <PERSON><PERSON>ey, <PERSON>Many,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { Company } from "../companies/companies.model";
import { Jobs, User } from "@microservices/recruitment-db";

@Table({ tableName: "workflows", createdAt: true, updatedAt: true })
export class Workflow extends Model<Workflow> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiPropertyOptional({
    example: "",
    description: "workflow title",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  title: string;

  @ApiProperty({ example: "1", description: "Company ID" })
  @ForeignKey(() => Company)
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({ example: "1", description: "User ID" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  authorId: number;

  @ApiPropertyOptional({ example: "1", description: "Domain Assessment ID" })
  @Column({ type: DataType.INTEGER, allowNull: true })
  domainId: number;

  @ApiPropertyOptional({ example: "1", description: "live Coding Assessment ID" })
  @Column({ type: DataType.INTEGER, allowNull: true })
  liveCodingId: number;

  @ApiPropertyOptional({ example: "1", description: "Coding Time" })
  @Column({ type: DataType.INTEGER, allowNull: true })
  liveCodingTime: number;

  @ApiPropertyOptional({ example: "1", description: "Domain Reviewer" })
  @Column({ type: DataType.JSONB, allowNull: true })
  domainReviewer: string;

  @ApiPropertyOptional({ example: '8', description: 'Domain Deadline' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  domainDeadline: number;

  @ApiPropertyOptional({
    example: "",
    description: "Question Type",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  questionType: string

  @ApiPropertyOptional({ example: "1", description: " take home task Coding Assessment ID" })
  @Column({ type: DataType.INTEGER, allowNull: true })
  takeHomeTaskId: number;
  
  @ApiPropertyOptional({ example: '9', description: 'Domain Deadline' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  takeHomeTaskDeadline: number;

  @ApiPropertyOptional({ example: "1", description: "Coding Time" })
  @Column({ type: DataType.INTEGER, allowNull: true })
  takeHomeTaskTime: number;

  @ApiPropertyOptional({ example: [], description: "Workflow" })
  @Column({ type: DataType.JSONB, allowNull: true })
  workflow: any;

  @ApiPropertyOptional({ example: [], description: "Available Days" })
  @Column({ type: DataType.JSONB, allowNull: true })
  availableDays: string;

  @ApiPropertyOptional({ example: [], description: "Available Time" })
  @Column({ type: DataType.JSONB, allowNull: true })
  availableTime: string;

  @BelongsTo(() => Company)
  company: Company;

  @BelongsTo(() => User)
  author: User;

  @HasMany(() => Jobs, {
    foreignKey: 'workflowId'
  })
  jobs: Jobs[];
}
