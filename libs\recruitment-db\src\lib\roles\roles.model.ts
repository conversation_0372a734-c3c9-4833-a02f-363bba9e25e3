import {
  <PERSON>ongsTo<PERSON>any,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { UserRoles } from './user-roles.model';
import { User } from '../users/users.model'
import { Company } from '../companies/companies.model';

interface RoleCreationAttrs {
  value: string;
  label: string;
  description?: string;
  permissions?: any;
  companyId:number
}

@Table({ tableName: 'roles', createdAt: false, updatedAt: false })
export class Role extends Model<Role, RoleCreationAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: '1', description: 'companyId' })
  @ForeignKey(()=>Company)
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;

  @ApiProperty({ example: 'Candidate', description: 'Value' })
  @Column({
    type: DataType.STRING,
    unique: true,
    allowNull: false,
  })
  value: string;

  @ApiProperty({ example: 'Candidate', description: 'Label' })
  @Column({ type: DataType.STRING, allowNull: false,unique:true })
  label: string;

  @ApiProperty({ example: 'Description text', description: 'Description' })
  @Column({ type: DataType.TEXT, allowNull: true })
  description: string;

  @ApiProperty({ example: 'JSON', description: 'Permissions' })
  @Column({ type: DataType.JSON, allowNull: true })
  permissions: string;

  @BelongsToMany(() => User, () => UserRoles)
  users: User[];
}
