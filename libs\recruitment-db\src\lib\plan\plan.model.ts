import {
  Column,
  DataType,
  Model,
  Table,
  ForeignKey,
  BelongsTo,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
// import { PlanPackages } from '../plan-packages/plan-packages.model';

interface PlansAttr {
  name:string;
  description:string;
  // planPackageId: number;
}

@Table({
  tableName: 'plans',
  timestamps:false,
  createdAt:false,
  updatedAt:false
 })
export class Plans extends Model<Plans, PlansAttr> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'Tier1', description: 'name of the plan' })
  @Column({ type: DataType.STRING, unique: false, allowNull: false })
  name: string;

  @ApiProperty({ example: 'Tier1', description: 'Description of the plan' })
  @Column({ type: DataType.STRING, unique: false, allowNull: true })
  description: string;

  // @ApiProperty({ example: '1', description: 'package id of plan' })
  // @ForeignKey(()=>PlanPackages)
  // @Column({ type: DataType.INTEGER, allowNull: false })
  // planPackageId: number;

  // @BelongsTo(() => PlanPackages)
  // planPackage: PlanPackages;
}
