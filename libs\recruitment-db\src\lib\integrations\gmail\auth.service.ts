import { Auth, google } from 'googleapis';
import { Injectable, Logger } from '@nestjs/common';
import { IntegrationAuthService } from "../../integrationAuth/integrationAuth.service";
import { randomBytes, scrypt, createCipheriv, createDecipheriv } from 'crypto';
import { promisify } from 'util';

@Injectable()
export class GmailAuthService {
  private oauth2Client: Auth.OAuth2Client;
  private scope=  "https://www.googleapis.com/auth/gmail.send”"
  constructor(private integrationAuthService: IntegrationAuthService) {
    this.oauth2Client = new google.auth.OAuth2({
      clientId:process.env.GMAIL_AUTH_CLIENT_ID,
      clientSecret: process.env.GMAIL_AUTH_CLIENT_SECRET,
      redirectUri: `${process.env.MICROSERVICES_PRA_URL}/api/integrations/oauth2-callback`,
    });
  }

  async checkConnectionStatus(refreshToken) {
    // maybe check connection status on API side
    return "connected";
  }

  async getConnectUrl(state) {
    return this.oauth2Client.generateAuthUrl({
      access_type: "offline",
      scope: "https://www.googleapis.com/auth/gmail.send”",
      include_granted_scopes: true,
      state,
      prompt: "consent",
    });
  }

  async oauth2Callback(code, userId) {
    const result = {
      data: "",
      error: "",
    };

    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      const newTokens = {
        userId: userId,
        provider:"gmail",
        accessToken:await this.encrypt([tokens.access_token]) ,
        refreshToken:await this.encrypt([tokens.refresh_token]) ,
        expiryDate: tokens.expiry_date,
        scope: tokens.scope,
      };
      const isAvailable = await this.integrationAuthService.findByProvider(userId,"gmail");
      if (isAvailable) {
        await this.integrationAuthService.update(userId, newTokens,this.scope);
      } else {
        await this.integrationAuthService.create(newTokens);
      }
      result.data = tokens.refresh_token;
    } catch (e) {
      result.error = e.response?.data?.error || "callback_error";
      Logger.error(e);
    }

    return result;
  }

  async authRevoke(refreshToken,userId) {
    try {
      await this.oauth2Client.revokeToken(refreshToken);
    } catch (e) {
      Logger.error(e);
    }
  }

  getClient(refreshToken) {
    try {
      this.oauth2Client.setCredentials({
        refresh_token: refreshToken,
      });
      return {
        client: google.gmail({ version: "v1", auth: this.oauth2Client }),
      };
    } catch (error) {
      return { message: error };
    }
  }

  async encrypt(arr) {
    const iv = randomBytes(16);
    const key = (await promisify(scrypt)(process.env.CRYPTO_PASS, 'salt', 32)) as Buffer;
    const cipher = createCipheriv('aes-256-ctr', key, iv);
    const encrypted = Buffer.concat([
      cipher.update(arr.join(',')),
      cipher.final(),
    ]);

    return JSON.stringify({
      i: iv.toString('hex'),
      t: encrypted.toString('hex'),
    });
  }

  async decrypt(json) {
    const hash = JSON.parse(json);
    const key = (await promisify(scrypt)(process.env.CRYPTO_PASS, 'salt', 32)) as Buffer;
    const decipher = createDecipheriv('aes-256-ctr', key, Buffer.from(hash.i, 'hex'));
    const decrypted = Buffer.concat([decipher.update(Buffer.from(hash.t, 'hex')), decipher.final()]);

    return decrypted.toString().split(',');
  }
}
