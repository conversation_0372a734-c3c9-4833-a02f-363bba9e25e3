{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2017", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@microservices/auth": ["libs/auth/src/index.ts"], "@microservices/calendars": ["libs/calendars/src/index.ts"], "@microservices/code-executor": ["libs/code-executor/src/index.ts"], "@microservices/db": ["libs/db/src/index.ts"], "@microservices/editor-manager": ["libs/editor-manager/src/index.ts"], "@microservices/email": ["libs/email/src/index.ts"], "@microservices/hr-analytics": ["libs/hr-analytics/src/index.ts"], "@microservices/integrations": ["libs/integrations/src/index.ts"], "@microservices/qdrant": ["libs/qdrant/src/index.ts"], "@microservices/kinesis": ["libs/kinesis/src/index.ts"], "@microservices/recruitment-db": ["libs/recruitment-db/src/index.ts"], "@microservices/twilio": ["libs/twilio/src/index.ts"]}, "resolveJsonModule": true}, "exclude": ["node_modules", "tmp"]}