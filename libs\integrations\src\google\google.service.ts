import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import * as talent from '@google-cloud/talent';

/**
 * Job Search hierarchy:
 * 1. Tenant -  uRecruits.
 *              Tenant can include many Companies.
 * 2. Company - for now uRecruits only, in the future can be any company registered in uRecruits database.
 *              Each company can include many Jobs.
 * 3. Job
 */

@Injectable()
export class GoogleService {
  private readonly opts = {
    credentials: {
      'client_email': process.env.GOOGLE_SERVICE_ACCOUNT_CLIENT_EMAIL,
      'private_key': process.env.GOOGLE_SERVICE_ACCOUNT_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
  };
  private readonly projectId = 'urecruits-2021';
  private readonly defaultTenantId = '2cc9ad17-3f00-0000-0000-00fce8b59334';
  private readonly defaultCompanyId = '53d2f60e-82dc-4b86-a0d5-d4c2c703a3bb';

  /**
   * Create a job.
   *
   * @param job
   *
   * @throws Error
   *
   * @returns {object}
   */
  async createJob(job) {
    const client = new talent.JobServiceClient(this.opts);

    const formattedParent = client.projectPath(this.projectId);

    const args = this.mapJobArgs(job);
    
    const request = {
      parent: formattedParent,
      job: args,
    };

    const response = await client.createJob(request).catch((err) => {
      return err;
    });

    if (!response[0]) {
      Logger.error(response);

      return false;
    }

    const name = response[0].name.split('/');

    return {
      project: name[1],
      tenant: name[3],
      jobId: name[5],
    };
  }

  mapJobArgs(job) {
    const args = {
      company: this.defaultCompanyId,
      requisitionId: job.id.toString(),
      title: job.title,
      description: job.description,
      applicationInfo: {
        uris: [`${process.env.FRONT_URL}/job/${job.id}`],
      },
      addresses: job.locations.map((item) => `${item.city}, ${item.state}`),
      postingRegion: talent.protos.google.cloud.talent.v4.PostingRegion.NATION,
      visibility:talent.protos.google.cloud.talent.v4.Visibility.SHARED_WITH_PUBLIC,
    };

    if (job.status !== 'publish') {
      args['postingExpireTime'] = {
        seconds: Math.floor(new Date().getTime() / 1000).toString(),
      };
    }

    return args;
  }

  /**
   *
   * @param job
   * @param data
   *
   */
  async updateJob(job, data) {
    if (!data?.jobId) return false;

    const client = new talent.JobServiceClient(this.opts);

    const formattedName = client.jobPath(this.projectId, this.defaultTenantId, data.jobId);

    const args = this.mapJobArgs(job);

    const request = {
      job: { name: formattedName, ...args },
    };

    const response = await client.updateJob(request).catch((err) => {
      return err;
    });

    if (!response[0]) {
      Logger.error(response);

      return false;
    }

    return response[0];
  }

  /**
   * Delete a Job.
   *
   * @param jobId {string} Job ID
   *
   * @throws Error
   *
   * @returns {object}
   */
  async deleteJob(data) {
    if (!data?.jobId) return false;

    const client = new talent.JobServiceClient(this.opts);

    const formattedName = client.jobPath(this.projectId, this.defaultTenantId, data.jobId);
    const request = {
      name: formattedName,
    };

    const response = await client.deleteJob(request).catch((err) => {
      return err;
    });
    if (!response[0]) {
      Logger.error(response);

      return false;
    }

    return response[0];
  }

  /**
   * List Jobs.
   *
   * @param requisitionId {string}
   *
   * @throws Error
   *
   * @returns {array}
   */
  async listJobs(requisitionId: string) {
    const client = new talent.JobServiceClient(this.opts);

    const formattedParent = client.projectPath(this.projectId);
    let filter = `companyName = "projects/${this.projectId}/companies/${this.defaultCompanyId}"`;
    if (requisitionId) {
      filter += ` AND requisitionId ="${requisitionId}"`;
    }
    const request = {
      parent: formattedParent,
      filter,
    };

    const response = await client.listJobs(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * Retrieve a Job.
   *
   * @param jobId {string} Job ID
   *
   * @throws Error
   *
   * @returns {object}
   */
  async retrieveJob(jobId: string) {
    const client = new talent.JobServiceClient(this.opts);

    const formattedName = client.jobPath(this.projectId, this.defaultTenantId,
      jobId);
    const request = {
      name: formattedName,
    };

    const response = await client.getJob(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * List Tenants.
   *
   * @throws Error
   *
   * @returns {array}
   */
  async listTenants() {
    const client = new talent.TenantServiceClient(this.opts);

    const formattedParent = client.projectPath(this.projectId);
    const request = {
      parent: formattedParent,
    };

    const response = await client.listTenants(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * Create a Company.
   * For now only uRecruits, in the future it can be any company registered in the uRecruits database.
   * Each company can include many Jobs.
   *
   * @param displayName {string} Company Name
   * @param externalId {string} Identifier of this company on uRecruits side
   *
   * @throws Error
   *
   * @returns {object}
   */
  async createCompany(displayName, externalId) {
    const client = new talent.CompanyServiceClient(this.opts);

    const formattedParent = client.tenantPath(this.projectId,
      this.defaultTenantId);
    const company = {
      displayName,
      externalId,
    };
    const request = {
      parent: formattedParent,
      company,
    };

    const response = await client.createCompany(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * List Companies.
   *
   * @throws Error
   *
   * @returns {array}
   */
  async listCompanies() {
    const client = new talent.CompanyServiceClient(this.opts);

    const formattedParent = client.tenantPath(this.projectId,
      this.defaultTenantId);
    const request = {
      parent: formattedParent,
    };

    const response = await client.listCompanies(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }

  /**
   * Delete a Company.
   *
   * @param companyId {string} Company ID
   *
   * @throws Error
   *
   * @returns {object}
   */
  async deleteCompany(companyId) {
    const client = new talent.CompanyServiceClient(this.opts);

    const formattedName = client.companyPath(this.projectId, this.defaultTenantId, companyId);
    const request = {
      name: formattedName,
    };

    const response = await client.deleteCompany(request).catch((err) => {
      throw new HttpException(err, HttpStatus.BAD_REQUEST);
    });

    return response[0];
  }
}
