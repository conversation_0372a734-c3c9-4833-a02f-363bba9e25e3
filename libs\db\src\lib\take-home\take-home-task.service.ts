import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op } from 'sequelize';
import { parseDate } from 'tools/utils';
import { v4 as uuidv4 } from 'uuid';
import { Filter } from '../live-coding/filter.interface';
import { TYPE } from '../live-coding/live-coding.model';
import { QuestionsService } from '../questions/questions.service';
import { TestCaseService } from '../testcases/testcase.service';
import { TakeHomeTask, STATUS } from './take-home-task.model';
import { Package } from '../packages/packages.model';
import { TakeHomeDrafts } from './home-drafts/home-drafts.model';
import { Questions } from '../questions/questions.model';
import { TestCase } from '../testcases/testcase.model';
import { getDummyTakeHomeTasks } from '../utils/fillDummyAssessmentData';
import { putRecordOnQdrant } from '@microservices/qdrant';
import { TakeHomeDraftsService } from './home-drafts/home-drafts.service';

@Injectable()
export class TakeHomeTaskService {
  constructor(
    @InjectModel(TakeHomeTask)
    private takeHomeTaskModel: typeof TakeHomeTask,
    @InjectModel(TakeHomeDrafts)
    private takeHomeTaskDraftModel: typeof TakeHomeDrafts,
    private questionsService: QuestionsService,
    private testCaseService: TestCaseService,
    @InjectModel(Package)
    private packageModel: typeof Package,
    private takehomeDraftService: TakeHomeDraftsService
  ) { }

  findOneById(id: number): Promise<TakeHomeTask> {
    return this.takeHomeTaskModel.findByPk(id, {
      include: [
        {
          association: 'questions',
          include: [{ association: 'testcases', required: false }],
        },
      ],
    });
  }

  findAll(companyId: number): Promise<TakeHomeTask[]> {
    return this.takeHomeTaskModel.findAll({
      where: { companyId },
      include: [{ association: 'questions', include: [{ association: 'testcases', required: false }] }],
    });
  }

  async findOne(id: number, user: number) {
    const companyId = user["https://urecruits.com/companyId"] ?? '';
    const candidateId = !companyId ? user["https://urecruits.com/userId"] : '';
    const takeHome = await this.takeHomeTaskModel.findOne({
      where: { id, ...(companyId ?? '') },
      include: [{ association: 'questions', include: [{ association: 'testcases', required: false }] }],
    });
    const recordData = this.getTakeHomeTaskRecordData(takeHome, user["https://urecruits.com/userId"], "Active", "created");
    return { response: takeHome, recordData };
  }
  findByTaskId(taskId, companyId) {
    return this.takeHomeTaskModel.findOne({
      where: { taskId, companyId },
      include: [{ association: 'questions', include: [{ association: 'testcases', required: false }] }],
    })
  }

  findAllPackages(): Promise<Package[]> {
    try {
      return this.packageModel.findAll();
    } catch (error) {
      throw new HttpException('Error getting package', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async create(dto, companyId: number) {
    let recordData: any[] = [];
    if (dto.taskId) {
      const takeHomeDraft = await this.takeHomeTaskDraftModel.findOne({
        where: { taskId: dto.taskId },
        include: [{
          association: 'questions',
          required: false
        }]
      })
      if (takeHomeDraft) {
        if (takeHomeDraft.questions && takeHomeDraft.questions.length > 0) {
          for (let i = 0; i < takeHomeDraft.questions.length; i++) {
            if (takeHomeDraft.questions[i].testcases && takeHomeDraft.questions[i].testcases.length > 0) {
              for (let j = 0; j < takeHomeDraft.questions[i].testcases.length; j++) {
                await this.testCaseService.remove(takeHomeDraft.questions[i].testcases[j].id);
              }
            }
            await this.questionsService.remove(takeHomeDraft.questions[i].id);
          }
        }
        await takeHomeDraft.destroy();
        recordData.push(this.getTakeHomeTaskDataForQdrant(takeHomeDraft, "delete"));
      }
    }
    const data = dto.taskId && await this.takeHomeTaskModel.findOne({ where: { taskId: dto.taskId } })
    if (data) {
      const takeHome = await data.update({ ...dto, status: 'ACTIVE', assessmentType: TYPE.takeHome, companyId }, {
        include: [
          {
            association: 'questions',
            include: [{ association: 'testcases', required: false }],
          },
        ],
      })
      if (takeHome) {
        recordData.push(this.getTakeHomeTaskDataForQdrant(takeHome));
        return { response: takeHome, recordData };
      }
    } else {
      const takeHome = await this.takeHomeTaskModel.create(
        { ...dto, taskId: uuidv4(), status: STATUS.active, assessmentType: TYPE.takeHome, companyId },
        {
          include: [
            {
              association: 'questions',
              include: [{ association: 'testcases', required: false }],
            },
          ],
        }
      );
      await this.getTakeHomeTaskDataForQdrant(takeHome, 'create');

      // Return OpenSearch data
      const openSearchData = this.addDataOnOpenSearch(takeHome, STATUS.active, 'create');
      return {
        response: takeHome,
        recordData: openSearchData // Include for: 'assessment'
      };
    }
  }

  async update(id: number, dto: TakeHomeTask, companyId: number, userId: number) {
    const takeHome = await this.takeHomeTaskModel.findOne({
      where: { id, companyId }, include: [
        {
          association: 'questions',
          include: [{ association: 'testcases', required: false }],
        },
      ]
    });
    if (takeHome) {
      if (dto.questions) {
        for (let i = 0; i < dto.questions.length; i++) {
          let question = typeof dto.questions[i].id !== 'string' && await this.questionsService.findOneById(dto.questions[i].id);
          const questionData = {
            ...dto.questions[i],
            takeHomeTaskId: id
          };
          if (question) {
            await this.questionsService.update(dto.questions[i].id, questionData as Questions);
          } else {
            delete questionData.id
            question = await this.questionsService.create(questionData as Questions);
          }
          if (dto.questions[i].testcases && question) {
            for (let j = 0; j < dto.questions[i].testcases.length; j++) {
              const test = await this.testCaseService.update(dto.questions[i].testcases[j].id, { ...dto.questions[i].testcases[j], questionId: question?.id } as TestCase);
            }
          }
        }
      }


      await takeHome.update({ ...dto }, { where: { id, companyId } })
      const recordData = this.getTakeHomeTaskDataForQdrant(takeHome);
      return { response: takeHome, recordData };
    }
  }

  async updateByTaskId(taskId: string, dto: TakeHomeTask, companyId: number, userId: number) {
    const takeHome = await this.takeHomeTaskModel.findOne({ where: { taskId, companyId }, });
    if (takeHome) {
      const updatedTakeHome = await this.update(takeHome.id, dto, companyId, userId);
      return updatedTakeHome;
    }
  }

  async remove(id: number, companyId: number, userId: number) {
    const takeHome = await this.takeHomeTaskModel.findOne({
      where: { id, companyId },
      include: [
        {
          association: 'questions',
          include: [{ association: 'testcases', required: false }],
        },
      ]
    });
    if (takeHome) {
      if (takeHome.questions && takeHome.questions.length > 0) {
        for (let i = 0; i < takeHome.questions.length; i++) {
          if (takeHome.questions[i].testcases && takeHome.questions[i].testcases.length > 0) {
            for (let j = 0; j < takeHome.questions[i].testcases.length; j++) {
              await this.testCaseService.remove(takeHome.questions[i].testcases[j].id);
            }
          }
          await this.questionsService.remove(takeHome.questions[i].id);
        }
      }
      const recordData = this.getTakeHomeTaskDataForQdrant(takeHome, "delete");
      await this.takeHomeTaskModel.destroy({ where: { id, companyId } });
      return { response: takeHome, recordData };
    }
  }

  async removeQuestionById(id: number, companyId: number, userId: number) {
    return await this.questionsService.remove(id);
  }

  async removeTestCasesByQuestionId(questionId: number, companyId: number, userId: number) {
    try {
      // Find the question to verify it exists and belongs to the company
      const question = await this.questionsService.findOneById(questionId);

      if (!question) {
        throw new HttpException('Question not found', HttpStatus.NOT_FOUND);
      }

      // If question has a takeHomeTaskId, verify it belongs to the company
      if (question.takeHomeTaskId) {
        const takeHome = await this.takeHomeTaskModel.findOne({
          where: { id: question.takeHomeTaskId, companyId }
        });

        if (!takeHome) {
          throw new HttpException('Unauthorized', HttpStatus.UNAUTHORIZED);
        }
      }

      // Delete all test cases for the question
      const testCases = await this.testCaseService.findAllByQuestionId(questionId);


      for (const testCase of testCases) {
        await this.testCaseService.remove(testCase.id);
      }

      return {
        success: true,
        message: `Successfully deleted all test cases for question ID ${questionId}`,
        deletedCount: testCases.length
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Failed to delete test cases', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  async getTestCasesByQuestionId(questionId: number, companyId: number, userId: number) {
    return await this.testCaseService.findAllByQuestionId(questionId);
  }

  async search(query: Filter, companyId) {
    const parsedQuery = {
      database: query.database ? query.database?.split(',').map((el) => parseInt(el)) : null,
      language: query.language ? query.language?.split(',').map((el) => parseInt(el)) : null,
      package: query.package ? query.package : null,
      createdOn: query.createdOn ? query.createdOn : null,
    };

    Object.keys(parsedQuery).forEach((key) => {
      if (parsedQuery[key] === null) {
        delete parsedQuery[key];
      }
    });

    const propertyArray = [];
    const includeArray = [];

    for (const key in parsedQuery) {
      if (key === 'language') {
        propertyArray.push({ languageId: parsedQuery[key] });
      } else if (key === 'package') {
        propertyArray.push({ packageId: parsedQuery[key] });
      } else if (key === 'createdOn') {
        propertyArray.push({
          createdAt: { [Op.gt]: parseDate(parsedQuery[key], 'start'), [Op.lt]: parseDate(parsedQuery[key], null) },
        });
      } else if (key === 'database') {
        includeArray.push(parsedQuery[key]);
      }
    }

    let whereSearch: any[] = []
    if (query.search) {
      whereSearch.push({
        name: { [Op.iLike]: `%${query.search}%` }
      })
      whereSearch.push({
        description: { [Op.iLike]: `%${query.search}%` }
      })
    }


    if (parsedQuery.database) {
      return this.takeHomeTaskModel.findAll({
        where: {
          companyId,
          ...(!!whereSearch?.length && { [Op.or]: whereSearch })
        },
        include: [
          { association: 'questions', where: { [Op.and]: propertyArray } },
          {
            association: 'assessment-database',
            where: { id: includeArray },
          },
        ],
      });
    } else {
      return this.takeHomeTaskModel.findAll({
        where: {
          companyId,
          ...(!!whereSearch?.length && { [Op.or]: whereSearch })
        },
        include: [{ association: 'questions', where: { [Op.and]: propertyArray } }],
      });
    }
  }

  addDataOnOpenSearch(takeHomeTask: TakeHomeTask | TakeHomeDrafts, status, action: string) {
    return {
      for: 'assessment',
      data: {
        ...(status === 'draft' ? { takeHomeTaskDraftId: takeHomeTask.id } : { takeHomeTaskId: takeHomeTask.id }),
        assessmentType: takeHomeTask.assessmentType,
        name: takeHomeTask.name,
        companyId: takeHomeTask.companyId,
        status: 'ACTIVE',
        action: action.toLowerCase(),
        takeHomeTask: takeHomeTask,
        createdAt: new Date()
      }
    };
  }

  getTakeHomeTaskRecordData(takeHomeTask: TakeHomeTask | TakeHomeDrafts, userId, status, action) {
    return {
      for: 'assessment',
      data: {
        ...(status == 'draft' ? { takeHomeTaskDraftId: takeHomeTask.id } : { takeHomeTaskId: takeHomeTask.id }),
        assessmentType: takeHomeTask.assessmentType,
        name: takeHomeTask.name,
        // companyId: takeHomeTask.companyId,
        createdBy: userId,
        status,
        action,
        takeHomeTask,
        createdAt: new Date(),
      }
    }
  }

  async getAllTakeHome({ limit, offset }: { limit: number, offset: number }) {
    return await this.takeHomeTaskModel.findAll({ limit, offset, include: [{ association: 'questions', required: false, include: [{ association: 'testcases', required: false }] }] })
  }

  async createDummyTakeHomeTask(companyId: number) {
    const takehome = getDummyTakeHomeTasks()
    if (takehome?.length) {
      for (let i = 0; i < takehome.length; i++) {
        const takehomeTask = takehome[i]
        try {
          if (i % 2 == 0) {
            const draft = await this.takehomeDraftService.create(takehomeTask, companyId)
            if (draft?.recordData) {
              putRecordOnQdrant([draft?.recordData])
            }
          } else {
            const createdTakeHome = await this.create(takehomeTask, companyId)
            if (createdTakeHome?.recordData) {
              putRecordOnQdrant(createdTakeHome?.recordData)
            }
          }
        } catch (err) {
          continue;
        }
      }
    }
  }

  getTakeHomeTaskDataForQdrant(takeHomeTask: TakeHomeTask | TakeHomeDrafts, action = 'create') {
    const { name, assessmentType, description, questions } = takeHomeTask;
    let formattedString = `Take home Task Assessment: ${name} (Type: ${assessmentType}, Status: ${takeHomeTask.status})\nDescription: ${description}\n\nQuestions:\n`;

    questions?.forEach((q, index) => {
      const sampleTestCase = q.testcases?.[0];
      let sampleInput = sampleTestCase ? sampleTestCase.input : null;
      let sampleOutput = sampleTestCase ? sampleTestCase.output : null;

      formattedString += `${index + 1}. ${q.name} - ${q.description}\n`;
      if (sampleInput && sampleOutput) {
        formattedString += `   Sample Input: ${sampleInput} → Expected Output: ${sampleOutput}\n`;
      }
      formattedString += `\n`;
    });

    return {
      for: "take-home",
      id: takeHomeTask.taskId,
      action,
      ...(action === 'create' && {
        str: formattedString,
        payload: {
          assessmentId: takeHomeTask.id,
          name,
          companyId: takeHomeTask.companyId,
          assessmentType,
          description,
          questions,
          status: takeHomeTask.status,
          createdAt: takeHomeTask.createdAt,
          updatedAt: takeHomeTask.updatedAt,
        }
      })
    }
  }
}
