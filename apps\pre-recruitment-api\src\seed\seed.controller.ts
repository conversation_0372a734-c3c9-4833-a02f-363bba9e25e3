import { Controller, Get } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  CandidatesService,
  LocationsService,
  User,
  PositionsService,
  IndustriesService,
  CompaniesService, RolesService, RecruitersService, RecruiterPositionsService, DepartmentService, PredefinedSkillService, UsersService,
  PriceService,
  ProductService,
  PlanService,
  PlanPackageService,
} from "@microservices/recruitment-db";

@ApiTags("Seed")
@Controller("seed")
export class SeedController {
  constructor (
    private readonly candidatesService: CandidatesService,
    private readonly locationsService: LocationsService,
    private readonly positionsService: PositionsService,
    private readonly industriesService: IndustriesService,
    private readonly companiesService: CompaniesService,
    private readonly rolesService: RolesService,
    private readonly priceService: PriceService,
    private readonly productService: ProductService,
    private readonly planService: PlanService,
    private readonly planPackageService: PlanPackageService,
    private readonly recruitersService: RecruitersService,
    private readonly recruiterPositionsService: RecruiterPositionsService,
    private readonly departmentService: DepartmentService,
    private readonly predefinedSkillService: PredefinedSkillService,
    private readonly usersService: UsersService,
  ) {}

  @ApiOperation({ summary: "Create candidate" })
  @ApiResponse({ status: 201, type: User })
  @Get()
  async seed () {
    await this.locationsService.seed();
    await this.departmentService.seed();
    await this.positionsService.seed();
    await this.industriesService.seed();
    await this.rolesService.seed();
    await this.planPackageService.seed();
    await this.planService.seed();
    await this.productService.seed();
    await this.priceService.seed();
    await this.recruiterPositionsService.seed();
    await this.predefinedSkillService.seed();
    await this.candidatesService.createCandidate({
      "email": "<EMAIL>",
      "phone": "+*********",
      "firstname": "Rob",
      "middlename": "James",
      "lastname": "Stark",
      "candidate_type": "Fresher",
      "industries": [
        {
          "id": 1,
        },
        {
          "id": 2,
        },
      ],
      "positionId": 1,
      "degree": "master",
      "password": "123456qQ@",
    });
    const company = await this.companiesService.registerCompany({
      "name": "uRecruits",
      "email": "<EMAIL>",
      "phone": "+*********",
      "stripeId": "cus_xxx",
      "company_type": "Corporate",
      "firstname": "Rob",
      "middlename": "James",
      "lastname": "Stark",
      "about": "About the Company",
      "industryId": 1,
      "avatar": "",
      "tenantId": "ecdev",
      "password": "123456qQ@",
      "companyPhone": "+*********",
    });
    for (let i = 0; i < 1; i++) {
      await this.recruitersService.addRecruiter(
        {
          "avatar": "",
          "email": `devrecruiter${i}@gmail.com`,
          "phone": "+*********",
          "firstname": "Rob",
          "middlename": "James",
          "lastname": "Stark",
          "authId": "",
          "degree": "master",
          "companyId": company.id,
          "positionId": 1,
          "officeEmail": "<EMAIL>",
          "officePhone": "5455654645",
          "officePhoneExtn": "5455654645",
          "department": "Administrative",
          "locationId": 1,
          "birthday": "01.01.1990",
          "dateJoining": "01.01.1990",
          "gender": "Male",
          "maritalStatus": "Single",
          "emergencyPerson": "Rob Anderson",
          "relationEmployee": "Positive",
          "emergencyMobile": "*********",
          "personalEmail": "<EMAIL>",
          "linkedin": "https://www.linkedin.com/",
          "twitter": "https://twitter.com/",
          "facebook": "https://www.facebook.com/",
          "instagram": "https://www.instagram.com/",
          "currentStreet": "New Street",
          "currentHouseNumber": "95",
          "currentCity": "New York",
          "currentCountry": "USA",
          "currentState": "New York",
          "currentZip": "10030",
          "permanentStreet": "New Street",
          "permanentHouseNumber": "95",
          "permanentCity": "New York",
          "permanentCountry": "USA",
          "permanentState": "New York",
          "permanentZip": "10030",
          "passport": "2131232",
          "visaType": "U-1",
          "issueCountry": "USA",
          "issuedOn": "January 11, 2022",
          "expiration": "January 11, 2022",
          "nationality": "American",
          "visaStatus": "Open",
          "profileStatus": "Active",
          "roles": [
            {
              "id": 5,
              "value": "rol_XSvI7wOambKTG5L2"
            }
          ],
          "tenantId": "ecdev"
        }
      )
    }
    return "Done!";
  }

  // @ApiOperation({ summary: "Delete candidate" })
  // @ApiResponse({ status: 201, type: User })
  // @Get("/delete-users")
  // async deleteUsers () {
  //   await this.usersService.deleteAllUsers()
  //   return "Done!";
  // }
}
