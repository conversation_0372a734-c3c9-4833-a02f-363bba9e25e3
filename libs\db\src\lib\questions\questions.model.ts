import { <PERSON>ongsTo, Column, DataType, ForeignKey, HasMany, HasOne, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Assignment } from '../assignment/assignment.model';
import { TestCase } from '../testcases/testcase.model';
import { TakeHomeTask } from '../take-home/take-home-task.model';
import { TakeHomeDrafts } from '../take-home/home-drafts/home-drafts.model';
import { AssessmentDatabase } from '../assessment-database/assessmentDatabase.model';
import { Package } from '../packages/packages.model';

@Table({
  tableName: 'questions',
})
export class Questions extends Model<Questions> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @ForeignKey(() => Assignment)
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: 'The Stolen Breakfast Drone',
    description: 'Name of problem',
  })
  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @ApiProperty({ example: 'This task is build to test your ability to think', description: 'Description of the question' })
  @Column({ type: DataType.TEXT, allowNull: true })
  description?: string;

  @ApiProperty({ example: 'Some description', description: 'Output description' })
  @Column({ type: DataType.TEXT, allowNull: false })
  outputDescription: string;

  @ApiProperty({ example: '', description: 'SOme output types' })
  @Column({ type: DataType.STRING, allowNull: false })
  outputType: string;

  @ApiProperty({ example: 'You can use plain JS but in same type allowed to use TS', description: 'Instructions to fullfilled assessment' })
  @Column({ type: DataType.TEXT, allowNull: false })
  candidateInstruction: string;

  @ApiProperty({ example: 'console.log(Some string)', description: 'Code for start coding if needed' })
  @Column({ type: DataType.TEXT, allowNull: true })
  starterCode?: string;

  @ApiProperty({ example: '', description: 'Stringify JSON object with inputs for each test case' })
  @Column({ type: DataType.STRING, allowNull: true })
  testCaseInputs?: string;

  @ApiProperty({ example: '10', description: 'Id of related language' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  languageId?: number;

  @ApiProperty({ example: 'true', description: 'Boolean if test cases are included' })
  @Column({ type: DataType.BOOLEAN, allowNull: false, defaultValue: false })
  useAIGeneratedTestCases?: boolean;

  @ForeignKey(() => Package)
  @Column({
    type: DataType.INTEGER,
    allowNull: true
  })
  packageId: number;

  @ForeignKey(() => AssessmentDatabase)
  @Column({ type: DataType.INTEGER })
  databaseId?: number;

  @ForeignKey(() => TakeHomeTask)
  @Column({ type: DataType.INTEGER, allowNull: true })
  takeHomeTaskId?: number;

  @ForeignKey(() => TakeHomeDrafts)
  @Column({ type: DataType.INTEGER, allowNull: true })
  takeHomeDraftId?: number;

  @BelongsTo(() => TakeHomeTask)
  takeHomeTask: TakeHomeTask;

  @BelongsTo(() => TakeHomeDrafts)
  takeHomeDraft: TakeHomeDrafts;

  @HasMany(() => TestCase, { onDelete: 'CASCADE', })
  testcases: TestCase[];
}
