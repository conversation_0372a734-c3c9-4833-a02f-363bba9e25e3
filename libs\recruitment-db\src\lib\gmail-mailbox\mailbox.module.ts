import { Modu<PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { FileModule } from 'apps/pre-recruitment-api/src/file/file.module';
import { Mailbox } from './mailbox.model';
import { MailBoxService } from './mailbox.service';
import { EmailModule } from '@microservices/email';
import { UsersModule } from '../users/users.module';

@Module({
  imports: [SequelizeModule.forFeature([Mailbox]),FileModule,EmailModule,UsersModule],
  providers: [MailBoxService],
  exports: [SequelizeModule, MailBoxService ],
})
export class MailboxModule {}
