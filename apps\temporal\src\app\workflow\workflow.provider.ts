import { Logger } from '@nestjs/common'
import { Connection, WorkflowClient } from '@temporalio/client'
import * as fs from 'fs';

const clientCert = fs.readFileSync(process.env.CLIENT_CA_PATH);
const clientKey = fs.readFileSync(process.env.CLIENT_KEY_PATH);
export const namespace = `${process.env.TEMPORAL_NAMESPACE}`
let sharedWorkflowClient: WorkflowClient | undefined = undefined;

export async function connectToTemporal() {
  if (!sharedWorkflowClient) {
    const connectionOptions = getConnectionOptions();
    let newConnection = await Connection.connect(connectionOptions);
    if(newConnection){
      sharedWorkflowClient = new WorkflowClient({
        connection: newConnection,
        namespace,
      });
    }
  }
  return sharedWorkflowClient;
}

interface ConnectionOptions {
  address: string
  tls?: { clientCertPair: { crt: Buffer; key: <PERSON><PERSON><PERSON> } }
}

export function getConnectionOptions(): ConnectionOptions {
  Logger.log('Temporal Server URL: ', process.env.TEMPORAL_SERVER);
  return {
    address: `${process.env.TEMPORAL_SERVER}`,
    tls: {
      clientCertPair: {
        crt: clientCert,
        key: clientKey,
      },
    },
  }
}
