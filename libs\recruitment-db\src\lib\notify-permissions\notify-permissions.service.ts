import {
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { NotifyPermissionsModel } from "./notify-permissions.model";
import { NotifyPermissionsDto } from "./dto/notify-permissions.dto";
import { messaging} from "firebase-admin";
import { NotificationsService } from "../notifications/notifications.service";
import { SubscribeService } from "../subscribe/subscribe.service";



@Injectable()
export class NotifyPermissionsService {
  constructor(
    @InjectModel(NotifyPermissionsModel)
    private notifyPermissionsRepository: typeof NotifyPermissionsModel,
    private notificationsService: NotificationsService,
    private subscribeService: SubscribeService
  ) {
    const admin = require("firebase-admin");
    const serviceAccount = require("./seviceAccountKey.json");
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount)
    });
  }

  async storeNotifyPermissionToken(dto: NotifyPermissionsDto, userId: number) {
    try {
      const notify = await this.notifyPermissionsRepository.findOne({
        where:{
          userId,
        }
      })
      if(notify){
        if( notify.notifyPermissionsToken === dto.notifyPermissionsToken) return;
        Object.assign(notify,{
          ...dto,
          userId
        })
        return notify.save()
      }
      return await this.notifyPermissionsRepository.create({
        notifyPermissionsToken: dto.notifyPermissionsToken,
        userId,
      });
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException("Error " + error);
    }
  }

  async getNotifyPermissionToken(userId: number) {
    try {
      return await this.notifyPermissionsRepository.findAndCountAll({
        where: { userId },
      });
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException("Error " + error);
    }
  }

  async updateNotifyPermissionToken(dto: NotifyPermissionsDto, userId: number) {
    try {
      return await this.notifyPermissionsRepository.update(
        { notifyPermissionsToken: dto.notifyPermissionsToken },
        {
          where: {
            userId,
          },
        }
      );
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException("Error " + error);
    }
  }

  async deleteNotifyPermissionToken(userId: number) {
    try {
      return await this.notifyPermissionsRepository.destroy({
        where: {
          userId,
        },
      });
    } catch (error) {
      Logger.log("Error : ", error);
      throw new InternalServerErrorException("Error " + error);
    }
  }

  async sendNotification({userId,message,image,jobData,title="uRecruits"}) {
    try{
    const linkData = {
      data:{
        click_action:jobData?.link
      },
    }
    const tokens: string[]=[];
    let shouldSend = true;
    if(jobData && jobData?.for === 'candidate'){
      const job = await this.subscribeService.getJobSubscribeDetailsByUserId(userId,jobData?.id)
      if(!job || !job?.subscribeJob){
        shouldSend = false;
      }
    }

    if(shouldSend){
      const registrationTokens = await this.notifyPermissionsRepository.findAll({where:{userId},attributes:["notifyPermissionsToken"]})
      for( let i of registrationTokens){
        tokens.push(i.notifyPermissionsToken)
      }
      if(tokens.length>0){
      const sendMessage = {
        notification: {
          title: title,
          body: message || "You have a New notification.",
          image:image || ""
        },
        ...(jobData?.link ? linkData : ""),
        tokens
      };

      const response = await messaging().sendEachForMulticast(sendMessage)
      if(response.successCount>0)
        {

          const notification = {
            title:sendMessage.notification.title,
            description:sendMessage.notification.body,
            image:sendMessage.notification.image,
            userId,
            link: jobData?.link
          }
          await this.notificationsService.create(notification)
          return "Successfully Send notifications"
        }
      }
    }
  }catch(error){
    Logger.log("Error while sending notifications: ", error);
  }
  }
}
