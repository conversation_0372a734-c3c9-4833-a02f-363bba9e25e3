# Changes by Dhruv
# FROM node:18 as build-stage
FROM public.ecr.aws/docker/library/node:20.19.3-alpine as build-stage
WORKDIR /app

COPY . .

RUN yarn

RUN yarn run build-integrations

# Changes by Dhruv
# FROM node:18-alpine
FROM public.ecr.aws/docker/library/node:20.19.3-alpine

WORKDIR /app

COPY --from=build-stage /app/dist/apps/integrations-api .

RUN yarn --production

EXPOSE 3002

CMD "node" "main.js"
