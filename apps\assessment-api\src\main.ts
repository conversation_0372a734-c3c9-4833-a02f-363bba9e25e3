import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { Transport } from '@nestjs/microservices';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app/app.module';
import { QdrantMiddleware } from '@microservices/qdrant';
import { KinesisMiddleware } from '@microservices/kinesis';

let app
async function bootstrap() {

   app = await NestFactory.create(AppModule);

  const globalPrefix = '/api';
  app.setGlobalPrefix(globalPrefix);
  app.connectMicroservice({
    transport: Transport.REDIS,
    options: {
      url: process.env.REDIS_URL || 'redis://localhost:6379',
    },
  });
  await app.startAllMicroservices();
  app.use(new QdrantMiddleware().use)
  app.use(new KinesisMiddleware().use)
  app.useGlobalPipes(new ValidationPipe());
  const config = new DocumentBuilder()
    .setVersion('1.1')
    .addTag('urecruts')
    .addServer(`http://localhost:${process.env.ASSESSMENT_PORT || 5481}`, "Local environment")
    .addServer("https://wtt-dev.urecruits.com","Staging environment")
    .addBearerAuth(
      {
        description: `[just text field] Please enter token in following format: Bearer <JWT>`,
        name: "Authorization",
        bearerFormat: "Bearer",
        scheme: "Bearer",
        type: "http",
        in: "Header",
      },
      "access-token"
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);

  SwaggerModule.setup('swagger', app, document);
  app.enableCors({ origin: [
    /\.urecruits\.com$/,
    'http://localhost:9000',
    'http://localhost:9001',
    'http://127.0.0.1:5500',
  ] });

  const port = process.env.ASSESSMENT_PORT || 3000;
  await app.listen(port, () => {
    Logger.log(`Listening at http://localhost:${  port  }${globalPrefix}`);
  });
}

async function shutdown() {
  if (app) {
    await app.close();
  }
}

bootstrap();

process.on('SIGINT', async () => {
  await shutdown();
});
