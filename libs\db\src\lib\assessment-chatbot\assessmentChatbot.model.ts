import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';


@Table({
    tableName: 'questions'
})

export class AssessmentChatbot extends Model<AssessmentChatbot> {
    @ApiProperty({
        description: 'User input',
    })
    @Column({ type: DataType.STRING, allowNull: true })
    userInput?: string;

    @ApiProperty({
        description: 'sessionId',
    })
    @Column({ type: DataType.STRING, allowNull: true })
    sessionId?: string;

    @ApiProperty({
        description: 'isTestCaseIncluded',
    })
    @Column({ type: DataType.BOOLEAN, allowNull: true })
    isTestCaseIncluded?: boolean;
}

