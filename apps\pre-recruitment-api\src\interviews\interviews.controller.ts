import {
    Controller,
    Post,
    Body,
    UseGuards,
    Get,
    Query,
    Patch,
    Delete,
    Param,
    Res,
  } from "@nestjs/common";
  import {
    ApiTags,
    ApiOperation,
    ApiResponse,
    ApiBearerAuth,
  } from "@nestjs/swagger";
  import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
  import { AuthGuard } from "@nestjs/passport";
  import {
 InterviewsService,
 FilterJobsInterviews
  } from "@microservices/recruitment-db";

  //   companyId: user["https://urecruits.com/companyId"],
  //   tenantId: user["https://urecruits.com/tenantId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   recruiterId: user["https://urecruits.com/recruiterId"],
  //   userId: user["https://urecruits.com/userId"],

  @ApiTags("interviews")
  @ApiBearerAuth("access-token")
  @Controller("interviews")
  export class InterviewsController {
    constructor(private readonly interviewsService: InterviewsService) {}

    @ApiOperation({ summary: "Get Jobs In Interviews" })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/jobs")
    @Permissions("job-post:view")
    getAllJobsCompanyWise(
      @Query() query: FilterJobsInterviews,
      @AuthUser() user: any
    ) {
      return this.interviewsService.getAllJobsCompanyWise(
        query,
        user["https://urecruits.com/companyId"]
      );
    }

    @ApiOperation({ summary: "Get Candidates In Interviews" })
    @UseGuards(AuthGuard("jwt"), PermissionsGuard)
    @Get("/candidates/:jobId")
    @Permissions("job-post:view")
    getAllCandidatesJobWise(
      @Param("jobId") jobId: number,
      @Query() query: FilterJobsInterviews,
      @AuthUser() user: any
    ) {
      return this.interviewsService.getAllCandidatesJobWise(
        query,
        user["https://urecruits.com/companyId"],
        jobId
      );
    }
    
  }
