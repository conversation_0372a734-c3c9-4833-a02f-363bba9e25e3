{"root": "apps/temporal", "sourceRoot": "apps/temporal/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/temporal", "main": "apps/temporal/src/main.ts", "tsConfig": "apps/temporal/tsconfig.app.json", "assets": ["apps/temporal/src/assets", "apps/temporal/src/app/certs"]}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/temporal/src/environments/environment.ts", "with": "apps/temporal/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "temporal:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/temporal/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/temporal"], "options": {"jestConfig": "apps/temporal/jest.config.js", "passWithNoTests": true}}}, "tags": []}