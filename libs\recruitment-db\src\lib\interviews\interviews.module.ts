import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Jobs } from "../job/job.model";
import { Subscribe } from "../subscribe/subscribe.model";
import {InterviewsService} from "./interviews.service"
import { IntegrationsModule } from "../integrations/integrations.module";
import { User } from "../users/users.model";

@Module({
  imports: [
    SequelizeModule.forFeature([Subscribe, Jobs, User]),
    IntegrationsModule,
  ],
  providers: [InterviewsService],
  exports: [InterviewsService],
})
export class InterviewsModule {}
