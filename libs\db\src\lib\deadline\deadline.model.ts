import { ApiProperty } from '@nestjs/swagger';
import { Column, DataType, Model, Table } from 'sequelize-typescript';

@Table({
  tableName: 'assignment-deadline',
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class Deadline extends Model<Deadline> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    example: '12 Hrs',
    description: 'Time that is for the assignment deadline that would be used to manage deadline schema',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    time: string;

}
