import { ApiProperty } from "@nestjs/swagger";

export class ChangePasswordDto {
  @ApiProperty({ example: "123456qQ@", description: "Current Password" })
  readonly currentPassword: string;

  @ApiProperty({ example: "123456qQ@", description: "New Password" })
  readonly newPassword: string;

  @ApiProperty({ example: "auth0|61a1102ba9ee1000738655a0", description: "Auth id" })
  readonly authId: string;
}
