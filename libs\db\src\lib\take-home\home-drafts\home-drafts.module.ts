import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TakeHomeDrafts } from './home-drafts.model';
import { QuestionsModule } from '../../questions/questions.module';
import { TestCaseModule } from '../../testcases/testcase.module';
import { TakeHomeDraftsService } from './home-drafts.service';

@Module({
  imports: [
    SequelizeModule.forFeature([TakeHomeDrafts]),
    QuestionsModule,
    TestCaseModule
  ],
  providers: [TakeHomeDraftsService],
  exports: [SequelizeModule, TakeHomeDraftsService],
})
export class TakeHomeDraftsModule { }
