import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

const IndustryExample = [{ id: 1 }, { id: 2 }];

export class UpdateCandidateDto {
  @ApiPropertyOptional({ example: "Fresher", description: "Candidate Type" })
  @IsOptional()
  candidate_type?: string;

  @ApiPropertyOptional({ example: "1", description: "Location id" })
  @IsOptional()
  locationId?: number;

  @ApiPropertyOptional({ example: "1", description: "Job id" })
  @IsOptional()
  positionId?: number;

  @ApiPropertyOptional({ example: "5", description: "Total Experience" })
  @IsOptional()
  experience?: string;

  @ApiPropertyOptional({ example: "5000", description: "Current CTC" })
  @IsOptional()
  currentCtc?: number;

  @ApiPropertyOptional({ example: "Headlines", description: "Headlines" })
  @IsOptional()
  headlines?: string;

  @ApiPropertyOptional({ example: "Summary", description: "Summary" })
  @IsOptional()
  summary?: string;

  @ApiPropertyOptional({ example: "https://www.youtube.com/", description: "CV Url" })
  @IsOptional()
  cvKey?: string;

  @ApiPropertyOptional({ example: "youtube.pdf", description: "CV Name" })
  @IsOptional()
  cvName?: string;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Birthday data" })
  @IsOptional()
  birthday?: any;

  @ApiPropertyOptional({ example: "Male", description: "Gender" })
  @IsOptional()
  gender?: string;

  @ApiPropertyOptional({ example: "master", description: "Education degree" })
  @IsOptional()
  degree?: string;

  @ApiPropertyOptional({ example: "Single", description: "Marital Status" })
  @IsOptional()
  maritalStatus?: string;

  @ApiPropertyOptional({
    example: "Rob Anderson",
    description: "Emergency Contact Person",
  })
  @IsOptional()
  emergencyPerson?: string;

  @ApiPropertyOptional({ example: "Positive", description: "Relation with Employee" })
  @IsOptional()
  relationEmployee?: string;

  @ApiPropertyOptional({
    example: "213214124",
    description: "Emergency Contact Mobile",
  })
  @IsOptional()
  emergencyMobile?: string;

  @ApiPropertyOptional({
    example:
      "[{\"value\":\"English\",\"label\":\"English\"},{\"value\":\"Spanish\",\"label\":\"Spanish\"}]",
    description: "Languages",
  })
  @IsOptional()
  languages?: string;

  @ApiPropertyOptional({
    example: "https://www.linkedin.com/",
    description: "Linkedin",
  })
  @IsOptional()
  linkedin?: string;

  @ApiPropertyOptional({ example: "https://twitter.com/", description: "Twitter" })
  @IsOptional()
  twitter?: string;

  @ApiPropertyOptional({
    example: "https://www.facebook.com/",
    description: "Facebook",
  })
  @IsOptional()
  facebook?: string;

  @ApiPropertyOptional({
    example: "https://www.instagram.com/",
    description: "Instagram",
  })
  @IsOptional()
  instagram?: string;

  @ApiPropertyOptional({
    example: "New Street",
    description: "Current Address: Street",
  })
  @IsOptional()
  currentStreet?: string;

  @ApiPropertyOptional({
    example: "95",
    description: "Current Address: House name or number",
  })
  @IsOptional()
  currentHouseNumber?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: City" })
  @IsOptional()
  currentCity?: string;

  @ApiPropertyOptional({ example: "USA", description: "Current Address: Country" })
  @IsOptional()
  currentCountry?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: State" })
  @IsOptional()
  currentState?: string;

  @ApiPropertyOptional({
    example: "10030",
    description: "Current Address: Zip/Postcode",
  })
  @IsOptional()
  currentZip?: string;

  @ApiPropertyOptional({
    example: "New Street",
    description: "Current Address: Street",
  })
  @IsOptional()
  permanentStreet?: string;

  @ApiPropertyOptional({
    example: "95",
    description: "Current Address: House name or number",
  })
  @IsOptional()
  permanentHouseNumber?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: City" })
  @IsOptional()
  permanentCity?: string;

  @ApiPropertyOptional({ example: "USA", description: "Current Address: Country" })
  @IsOptional()
  permanentCountry?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: State" })
  @IsOptional()
  permanentState?: string;

  @ApiPropertyOptional({
    example: "10030",
    description: "Current Address: Zip/Postcode",
  })
  @IsOptional()
  permanentZip?: string;

  @ApiPropertyOptional({
    example: "matthew-stark",
    description: "Unique url slug",
  })
  @IsOptional()
  onlineProfile?: string;

  @ApiPropertyOptional({ example: "2131232", description: "Passport №" })
  @IsOptional()
  passport?: string;

  @ApiPropertyOptional({ example: "U-1", description: "Visa Type" })
  @IsOptional()
  visaType?: string;

  @ApiPropertyOptional({ example: "USA", description: "Issue Country" })
  @IsOptional()
  issueCountry?: string;

  @ApiPropertyOptional({ example: "January 11, 2022", description: "Issued On" })
  @IsOptional()
  issuedOn?: string;

  @ApiPropertyOptional({ example: "January 11, 2022", description: "Expiration" })
  @IsOptional()
  expiration?: string;

  @ApiPropertyOptional({ example: "American", description: "Nationality" })
  @IsOptional()
  nationality?: string;

  @ApiPropertyOptional({ example: "Open", description: "Status" })
  @IsOptional()
  visaStatus?: string;

  @ApiPropertyOptional({ example: "Developer", description: "Position" })
  @IsOptional()
  preferencesRole?: string;

  @ApiPropertyOptional({ example: "Administration", description: "Functional Area" })
  @IsOptional()
  preferencesFunctionalArea?: string;

  @ApiPropertyOptional({ example: "Administration", description: "Job Type" })
  @IsOptional()
  preferencesJobType?: string;

  @ApiPropertyOptional({ example: "Night", description: "Preferable Shift" })
  @IsOptional()
  preferencesPreferableShift?: string;

  @ApiPropertyOptional({ example: "New York", description: "Preferred Job Location" })
  @IsOptional()
  preferencesJobLocation?: string;

  @ApiPropertyOptional({ example: "6000", description: "Expected CTC" })
  @IsOptional()
  preferencesExpectedCTC?: number;

  @ApiPropertyOptional({ example: "Summary", description: "Able to Join" })
  @IsOptional()
  preferencesAbleJoin?: string;

  @ApiPropertyOptional({ example: IndustryExample, description: "Industries" })
  @IsOptional()
  industries?: typeof IndustryExample;

  @ApiPropertyOptional({ example: "false", description: "Remote Location" })
  @IsOptional()
  remoteLocation?: boolean;
}
