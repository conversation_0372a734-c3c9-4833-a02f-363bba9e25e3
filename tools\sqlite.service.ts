import * as sqlite3 from 'sqlite3';

export class SqliteGenerator {
  generateDb(fileName: string, sql: string[]) {
    const filePath = __dirname + `/assets/${fileName}`;
    const db = new sqlite3.Database(filePath);
    db.serialize(() => {
      for (const script of sql) {
        db.run(script);
      }
    });

    db.close(() => {
      console.log('Data successfull saved into db file');
    });
  }
}
