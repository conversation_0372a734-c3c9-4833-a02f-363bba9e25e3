import { Assignment, AssignmentService, createAssignmentDTO, GetAssignmentDto, UpdateCandidateStatusListDto} from '@microservices/db';
import { EmailService } from '@microservices/email';
import { AuthGuard } from '@nestjs/passport';
import { Controller, Get, Post, Body, Param, Delete, Patch, UseGuards, BadRequestException, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { PermissionsGuard, Permissions, AuthUser } from '@microservices/auth';
import { Deadline } from 'libs/db/src/lib/deadline/deadline.model';

@Controller('assignment')
@ApiBearerAuth("access-token")
export class AssignmentController {
  constructor(private readonly assignmentService: AssignmentService) {}

  @ApiOperation({ summary: 'Get all assignment' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 200, type: Assignment })
  @Get('/all')
  @Permissions('recruiter','assessment:view')
  async getAllByUser(@AuthUser() user,@Query() data: GetAssignmentDto) {
    return this.assignmentService.getAllByUser(user["https://urecruits.com/companyId"],data);
  }

  @ApiOperation({ summary: "Get Deadline" })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 200, type: Deadline  })
  @Get('/deadline')
  findAllDeadlines () {
    return  this.assignmentService.findAllDeadlines();
  }

  @ApiOperation({ summary: "Get Time Duration in Hours" })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get('/time-duration')
  findAllTimeDuration () {
    return  this.assignmentService.findAllTimeDurations();
  }

  @ApiOperation({ summary: 'Assign assessment' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post()
  @Permissions('recruiter','assessment:edit')
  async create(@Body() assignmentDto: createAssignmentDTO, @AuthUser() user) {
    // if (assignmentDto.candidate) {
    //   ///Find candidate email by id
    //   await this.emailService.send('schedule.html', {}, '');
    // }
      return this.assignmentService.create(assignmentDto,user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Assign assessment' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post("/job")
  @Permissions('job-post:view','assessment:edit')
  async createFromJob(@Body() assignmentDto: createAssignmentDTO, @AuthUser() user) {
    // if (assignmentDto.candidate) {
    //   ///Find candidate email by id
    //   await this.emailService.send('schedule.html', {}, '');
    // }
      return this.assignmentService.create(assignmentDto,user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get assignment by id' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 200, type: Assignment })
  @Get(':id')
  @Permissions('assessment:view')
  async getAssignment(@Param('id') assignmentId: number, @AuthUser() user) {
    return this.assignmentService.findAssignmentInfo(assignmentId,user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get assignment by job id' })
  @ApiResponse({ status: 200, type: Assignment })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/job/:id')
  @Permissions('job-post:view','assessment:view')
  async findAssignmentByJobId(@Param('id') jobId: number, @AuthUser() user) {
    return this.assignmentService.findAssignmentByJobId(jobId, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Update candidate from assignment by id' })
  @ApiResponse({ status: 200, type: Assignment })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch("/candidate/:id")
  @Permissions('recruiter','assessment:edit')
  async updateCandidate(@Body() data:any, @Param("id") id:any, @AuthUser() user) {
    return this.assignmentService.updateCandidate(id,data, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Update candidate status list from assignment by id' })
  @ApiResponse({ status: 200, type: Assignment })
  @Patch("/change-status/:id")
  async updateCandidateStatusList(@Body() data: UpdateCandidateStatusListDto, @Param("id") id: number) {
    return this.assignmentService.updateCandidateList(id, data);
  }

  @ApiOperation({ summary: 'Update assignment by id' })
  @ApiResponse({ status: 200, type: Assignment })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch("job/:id")
  @Permissions('job-post:view','assessment:edit')
  async updateWorkflowId(@Param("id") id:any,@Body() data:any, @AuthUser() user) {
    return this.assignmentService.update(id,data, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Delete assignment' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete(':id')
  @Permissions('recruiter','assessment:edit')
  remove(@Param('id') id: string, @AuthUser() user) {
    return this.assignmentService.delete(+id, user["https://urecruits.com/companyId"]);
  }
}
