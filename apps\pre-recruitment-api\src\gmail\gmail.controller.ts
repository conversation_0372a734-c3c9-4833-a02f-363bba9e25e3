import { AuthUser, PermissionsGuard } from "@microservices/auth";
import {
  GmailService,
  MailBoxService,
  NotifyPermissionsService,
} from "@microservices/recruitment-db";
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  PayloadTooLargeException,
  Post,
  Query,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { AnyFilesInterceptor } from "@nestjs/platform-express";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

@ApiTags("Gmail")
@Controller("gmail")
export class GmailController {
  constructor(
    private mailBoxService: MailBoxService,
    private notifyPermissionsService: NotifyPermissionsService
  ) {}

  @ApiOperation({ summary: "Send Mail" })
  @ApiResponse({ status: 200 })
  @Post("/send-email")
  async sendEmaile(@Body() data) {
    try {
      this.notifyPermissionsService.sendNotification({userId:data.userId,title:data?.jobData?.notificationTitle,message:data?.jobData?.notificationMsg||data?.subject,image:"",jobData:data?.jobData});
      return this.mailBoxService.saveEmailToDb(data);
    } catch (error) {
      throw new PayloadTooLargeException("Error " + error);
    }
  }
  @ApiOperation({ summary: "Send Mail" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @UseInterceptors(AnyFilesInterceptor())
  @Post("/send-mail")
  async sendEmail(
    @Body() data,
    @UploadedFiles() files: Array<Express.Multer.File>,
    @AuthUser() user
  ): Promise<any> {
    try {
      if (files) {
        const maxAllowedFileSize = 5 * 1024 * 1024;
        const totalFileSize = files.reduce((acc, file) => acc + file.size, 0);
        if (totalFileSize > maxAllowedFileSize) {
          throw new BadRequestException(
            "Total file size exceeds the allowed limit."
          );
        }
      }
      return this.mailBoxService.saveEmail(data, files, user);
    } catch (error) {
      throw new PayloadTooLargeException("Error " + error);
    }
  }

  @ApiOperation({ summary: "create the google auth url" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/get-mails")
  async getMail(@Query() query, @AuthUser() user) {
    return this.mailBoxService.getAllMails(
      query,
      user["https://urecruits.com/userId"]
    );
  }

  @ApiOperation({ summary: "create the google auth url" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/getById/:id")
  async getMailById(@Param("id") id, @AuthUser() user) {
    return this.mailBoxService.getMailById(
      id,
      user["https://urecruits.com/userId"]
    );
  }

  @ApiOperation({ summary: "create the google auth url" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/delete-mail")
  async deleteMail(@Body() id: [], @AuthUser() user) {
    return this.mailBoxService.deleteEmail(
      id,
      user["https://urecruits.com/userId"]
    );
  }

  @ApiOperation({ summary: "create the google auth url" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/mark-as-read")
  async markEmailAsRead(@Body() data, @AuthUser() user) {
    return this.mailBoxService.markEmailAsRead(
      data,
      user["https://urecruits.com/userId"]
    );
  }
}
