import { <PERSON><PERSON>sT<PERSON>, <PERSON>umn, DataType, ForeignKey, HasMany, HasOne, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';


enum AssessmentCategory {
  domainAssessment = "Domain Assessment",
  codingAssessment = "Coding Assessment",
  // thirdPartyAssessment = "Third Party Assessment",
}

enum TestStatus {
  NOT_STARTED = 'Not Started',
  IN_PROGRESS = 'In Progress',
  COMPLETED = 'Completed',
}

@Table({
  tableName: 'test-manager',
  createdAt: true,
  updatedAt: true
})
export class TestManager extends Model<TestManager> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({ example: "1", description: "User ID" })
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;
  
  @ApiProperty({ example: "1", description: "Job ID" })
  @Column({ type: DataType.INTEGER, allowNull: false })
    jobId: number;
  
  @ApiProperty({ example: "2023/12/12", description: "Date of deadline" })
  @Column({ type: DataType.DATE, allowNull: false })
    deadline: Date;
  
  @ApiProperty({ example: "2 Hrs", description: "Time Duration of the test" })
  @Column({ type: DataType.INTEGER, allowNull: false })
    timeDuration: number;
  
  @ApiProperty({ example: "2023-10-31T11:00:00.904Z", description: "Start time of the test" })
  @Column({ type: DataType.DATE, allowNull: true })
    startTime: Date;

  @ApiProperty({ example: '1', description: 'Company Id' })
  @Column({ type: DataType.INTEGER, allowNull: true })
  companyId: number;
  
  @ApiProperty({enum: TestStatus,
    oneOf: [
      { type: TestStatus.NOT_STARTED },
      { type: TestStatus.IN_PROGRESS },
      { type: TestStatus.COMPLETED }
    ], })
  @Column({ type: DataType.ENUM(
    TestStatus.NOT_STARTED,
    TestStatus.IN_PROGRESS,
    TestStatus.COMPLETED
  ), allowNull: false ,
    defaultValue: TestStatus.NOT_STARTED,})
    testStatus?: TestStatus;


    @ApiProperty({
      enum: AssessmentCategory,
      oneOf: [
        { type: AssessmentCategory.domainAssessment },
        { type: AssessmentCategory.codingAssessment },
        // { type: AssessmentCategory.thirdPartyAssessment },
      ],
    })
    @IsOptional()
    @IsEnum(AssessmentCategory)
    @Column({
      type: DataType.ENUM(
        AssessmentCategory.domainAssessment,
        AssessmentCategory.codingAssessment,
        // AssessmentCategory.thirdPartyAssessment,
      ),
      allowNull: false,
    })
    assessmentType?: AssessmentCategory;
}
