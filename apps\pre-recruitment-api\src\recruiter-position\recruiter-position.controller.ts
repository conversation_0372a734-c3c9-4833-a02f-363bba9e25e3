import { Controller, Get, Param, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import {
  Position, RecruiterPositionsService,
} from "@microservices/recruitment-db";
import { Permissions, PermissionsGuard } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";

@ApiTags("Recruiter positions")
@Controller("recruiter-position")
export class RecruiterPositionController {
  constructor (private readonly recruiterPositionsService: RecruiterPositionsService) {}

  @ApiOperation({ summary: "Get recruiter positions" })
  @ApiResponse({ status: 200, type: [Position] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/:label?')
  @Permissions("team-members:view")
  getAll (@Param('label') label?: string) {
    return this.recruiterPositionsService.findByLabel(label);
  }
}
