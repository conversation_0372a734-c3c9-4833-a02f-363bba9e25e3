import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger"
import { IsOptional } from "class-validator"

export class FilterSubscribeDto {
  @ApiProperty({ example: 10, description: "limit for pagination" })
  limit: number

  @ApiProperty({ example: 0, description: "offset for pagination" })
  offset: number

  @ApiPropertyOptional({ example: "", description: "search value" })
  @IsOptional()
  searchValue?: string

  @ApiPropertyOptional({ example: "id", description: "sort by" })
  @IsOptional()
  sortBy?: string

  @ApiPropertyOptional({ example: "ASC", description: "sort type" })
  @IsOptional()
  sortType?: string
}