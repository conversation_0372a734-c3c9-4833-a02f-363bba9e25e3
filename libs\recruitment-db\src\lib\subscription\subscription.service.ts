import { BadRequestException, HttpException, HttpStatus, Injectable, Logger } from "@nestjs/common";
import { CompaniesService } from "../companies/companies.service";
import { InjectStripe } from "nestjs-stripe";
import Stripe from "stripe";
// import { PaymentMethodDto } from "./dto/paymentMethod.dto";
// import { PaymentMethodIdDto } from "./dto/paymentMethodId.dto";
import { CreateSubscriptionDto } from "./dto/createSubscription.dto";
import { CreateSubscriptionWithoutCardDto } from "./dto/createSubscriptionWithoutCard.dto";
import { InjectModel } from "@nestjs/sequelize";
import { Subscriptions } from "./subscriptions.model";
import { CompanyAddonsDto, CompanySubscriptionDto, GetCompanySubscriptionDto, GetAllCompanySubscriptionDto } from "./dto/companySubscription.dto";
import { Product } from "../product/product.model";
import { PlanPackageService } from "../plan-packages/plan-packages.service";
import { ProductService } from "../product/product.service";
import { SubscriptionAddon } from "./subscription-addon.model";
import { PriceService } from "../price/price.service";
import { Op } from "sequelize";
import { Company } from "../companies/companies.model";
import { PlanService } from "../plan/plan.service";
import { UpgradeSubscriptionDto } from "./dto/upgradeSubscription.dto";
import { PlanPackages } from "../plan-packages/plan-packages.model";
import { Plans } from "../plan/plan.model";

@Injectable()
export class SubscriptionService {
  public constructor(
    @InjectModel(Subscriptions) private subscriptionsRepository: typeof Subscriptions,
    @InjectModel(SubscriptionAddon) private subscriptionAddonRepository: typeof SubscriptionAddon,
    @InjectModel(Company) private companyRepository: typeof Company,
    private planPackageService: PlanPackageService,
    private planService: PlanService,
    private productService: ProductService,
    private priceService: PriceService,
    @InjectStripe() private readonly stripeClient: Stripe,
  ) { }

  async getCompany(companyId: number) {
    const company = await this.companyRepository.findOne({ where: { id: companyId } })
    if (!company && !company?.email) {
      throw new HttpException(
        "Company email is empty.",
        HttpStatus.BAD_REQUEST,
      );
    }
    return {
      id: company?.id,
      stripeId: company?.stripeId,
      email: company?.email,
      createdAt: company?.createdAt,
    };
  }

  async validStripeCustomer(stripeId: string, email: string) {
    if (stripeId) {
      try {
        const stripeCustomer: any = await this.stripeClient.customers.retrieve(
          stripeId,
        );
        if (
          !(
            !stripeCustomer ||
            stripeCustomer.deleted ||
            stripeCustomer.email !== email
          )
        ) {
          return stripeCustomer;
        }
      } catch (e) { }
    }

    return false;
  }

  async createStripeCustomer(email: string) {
    try {
      const stripeCustomer = await this.stripeClient.customers.create({
        email: email,
      });
      return stripeCustomer.id;
    } catch (error) {
      Logger.log("Error while creating stripe customer", error)
    }
  }

  async getPlans({ companyId }: { companyId?: number }) {
    try {

      const packagePlanAPI = await this.planPackageService.getDefaultAndCompanyPlans((companyId ? { companyId } : null))
      const allPlans = await this.planService.getAllPlan({});
      const productsAPI = await this.productService.getAllProducts({ productType: 'main', active: true })
      const companyPackages = [];
      for (let planPackage of packagePlanAPI) {
        const packageDetails: any = {};
        packageDetails.packageId = planPackage.id;
        packageDetails.packageName = planPackage.name;
        packageDetails.tiers = [];
        for (let plan of allPlans) {
          const tierDetails: any = {};
          tierDetails.planId = plan.id;
          tierDetails.planName = plan.name;
          packageDetails.tiers.push(tierDetails);
        }
        companyPackages.push(packageDetails);
      }
      // const products: any = {}
      for (let product of productsAPI) {
        const stripeProduct = await this.stripeClient.products.retrieve(product.productId)
        const tranformPrice: any = {};
        const prices = await this.priceService.getAllPrice({ productId: product.productId, active: true });

        let packageIndex, planIndex = -1;
        for (let price of prices) {
          const period = price.monthlyYearly === 'm' ? "month" : "year";
          if (price.planPackageId && price.planId) {

            packageIndex = companyPackages.findIndex(packageIn => packageIn.packageId === price.planPackageId);
            if (packageIndex > -1) {
              planIndex = companyPackages[packageIndex].tiers.findIndex(planIn => planIn.planId === price.planId);
            }
            if (planIndex > -1) {
              tranformPrice[period] = { id: price.priceId, unit_amount: price.price };
            }
          }
        }

        const productDetails = {
          id: product.productId,
          features: stripeProduct.metadata.hasOwnProperty("features") && stripeProduct.metadata.features.split(";"),
          order: stripeProduct.metadata.hasOwnProperty("order") && stripeProduct.metadata.order,
          prices: tranformPrice,
          packageType: stripeProduct.metadata.hasOwnProperty("package_type") && stripeProduct.metadata.package_type,
        }
        if (packageIndex > -1 && planIndex > -1) {
          Object.assign(companyPackages[packageIndex].tiers[planIndex], productDetails);
        }
      }

      return companyPackages.map(pack => {
        pack.tiers = pack.tiers.filter(plan => plan.id)
        return pack;
      }).filter(data => data.tiers.length);
    } catch (error) {
      Logger.log("Error while retrieving plans ", error)
    }
  }

  async addDataonOpensearch(subId) {
    const subsc = await this.subscriptionsRepository.findOne({ where: { id: subId }, include: [Plans, PlanPackages, Company] });
    if (subsc) {
      const price = await this.priceService.getPrice({ planId: subsc.planId, planPackageId: subsc.packageId, monthlyYearly: subsc.period });
      const str = `
      ${subsc.company?.name} has subscribed to ${subsc.plans?.name} plan of ${subsc.packages?.name} package.
      The amount is ${price?.price || 0} and the duration is ${subsc.period === 'm' ? 'monthly' : 'yearly'}.
      The subscription is in ${subsc.status} status. It has started on ${subsc.subEndDate} and the next billing date is ${subsc.endDate}.
      `
      const recordData = {
        for: 'subscriptions',
        str: str,
        id: subsc.id,
        action: 'create',
        payload: {
          id: subsc.id,
          subId: subsc.subId,
          companyId: subsc.companyId,
          company: subsc.company?.dataValues,
          plan: subsc.plans?.dataValues,
          package: subsc.packages?.dataValues,
          status: subsc.status,
          type: subsc.type,
          createdAt: subsc.createdAt,
          startDate: subsc.subEndDate,
          endDate: subsc.endDate,
          amount: price?.price || 0,
          duration: subsc.period === 'm' ? 'monthly' : 'yearly',
        }
      }
      return { response: subsc, recordData }

    }
  }

  async addDataonOpensearchForAddon(subAddonId) {
    const addons = await this.subscriptionAddonRepository.findAll({
      where: {
        quantity: {
          [Op.gt]: 0
        },
        units: {
          [Op.gt]: 0
        }
      },
      include: [Company]
    });
    if (addons?.length) {
      // const recordData = addons.map(async addon => {
      //   const price = await this.priceService.getPrice({ priceId: addon.priceId });
      //   const str = `
      //   ${addon.company?.name} has increased the quantity of ${addon.addonName} by ${addon.quantity} units.
      //   The total quantity is now ${addon.quantity}.
      //   The price is ${price?.price || 0}.
      //   `
      //   return {
      //     for: 'subAddon',
      //     str: str,
      //     id: addon.id,
      //     action: 'create',
      //     payload: {
      //       id: addon.id,
      //       subscriptionId: addon.subId,
      //       subscriptionItemId: addon.subItemId,
      //       companyId: addon.companyId,
      //       company: {
      //         name: addon.company?.name,
      //         avatar: addon.company?.avatar,
      //       },
      //       addonName: addon.addonName,
      //       units: addon.units,
      //       quantity: addon.quantity,
      //       plan: price?.plans?.dataValues,
      //       package: price?.planPackage?.dataValues,
      //       actionType: 'INCREASE',
      //       createdAt: Date.now()
      //     }
      //   }
      // })
      // return { response: addons, recordData: await Promise.all(recordData) }
      return addons;
    }
  }


  split(string: string) {
    return string.split(";").map(function (i) {
      return i.trim();
    });
  }

  async createDefaultSubscriptionWithoutCard({ companyId, checkCompany }: { companyId: number, checkCompany?: boolean }) {
    try {
      const dto = new CreateSubscriptionWithoutCardDto();
      const defaultPrice = await this.priceService.getPrice({
        isDefaultPackagePlan: true,
        active: true,
        sortBy: 'updatedAt',
        sortType: 'DESC'
      });

      if (!defaultPrice) {
        throw 'No default package plan found';
      }
      Object.assign(dto, { duration: defaultPrice.monthlyYearly, planId: defaultPrice.planId, packageId: defaultPrice.planPackageId });

      if (!dto.hasOwnProperty('planId') || !dto.hasOwnProperty('packageId')) {
        throw new HttpException(
          "Package ID and Plan ID are required.",
          HttpStatus.BAD_REQUEST,
        );
      }

      const subscription = await this.createSubscriptionWithoutCard({ dto, companyId: companyId, checkCompany })
      return subscription;
    } catch (error) {
      Logger.log(`Failed to get default subscription : ${error.message}`)
      throw new HttpException(`Failed to get default subscription : ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }

  }

  async createSubscriptionWithoutCard({ dto, companyId, checkCompany }: { dto: CreateSubscriptionWithoutCardDto, companyId: number, checkCompany?: boolean }) {
    try {
      const company = await this.getCompany(companyId);
      if (checkCompany) {
        if (company.stripeId) {
          await this.validStripeCustomer(
            company.stripeId,
            company.email,
          );
        }
        else {
          // create new stripe customer
          const stripeId = await this.createStripeCustomer(company.email);
          await this.companyRepository.update({ stripeId }, { where: { id: company.id } });
          company.stripeId = stripeId;
        }
      }
      const itemsPrice = [];
      let freeTrial = 0;
      const data = new CompanySubscriptionDto();
      let amount: any = {};

      const planPackagePrices = await this.priceService.getAllPrice({ active: true, planPackageId: dto.packageId, planId: dto.planId });
      const planProducts = await this.productService.getAllProducts({ active: true });

      const companyCanceledSubscription = await this.getAllCompanySubscription({ companyId: companyId, status: 'canceled' });
      for (let product of planProducts) {
        const productPrice = planPackagePrices.filter(price => price.productId === product.productId);
        for (let price of productPrice) {
          if (product.productType === 'main') {
            if ((dto.duration === 'y' && price.monthlyYearly === 'y') || (dto.duration === 'm' && price.monthlyYearly === 'm')) {
              if (dto.duration === 'y' && price.monthlyYearly === 'y') {
                amount.price = price.price;
                amount.duration = 'yearly';
                itemsPrice.unshift({ price: price.priceId });
              }
              else if (dto.duration === 'm' && price.monthlyYearly === 'm') {
                amount.price = price.price;
                amount.duration = 'monthly';
                itemsPrice.push({ price: price.priceId });
              }
              data.productId = product.productId;
              data.priceId = price.priceId;
              data.packageId = dto.packageId;
              data.planId = dto.planId;
              const stripeProduct = await this.stripeClient.products.retrieve(product.productId)
              if (stripeProduct) {
                if (!dto.hasOwnProperty('isUpgraded') && !companyCanceledSubscription.length && stripeProduct.metadata.hasOwnProperty('trial_period_days') && (parseInt(stripeProduct.metadata.trial_period_days) > 0) && productPrice.length) {
                  freeTrial = parseInt(stripeProduct.metadata.trial_period_days);
                }
                if (stripeProduct.metadata.hasOwnProperty("capabilities")) {
                  data.capabilities = this.mapCapabilities(stripeProduct.metadata.capabilities)
                }
                if (stripeProduct.metadata.hasOwnProperty("package")) {
                  data.packageName = stripeProduct.metadata.package
                }
                if (stripeProduct.metadata.hasOwnProperty("tier")) {
                  data.planName = stripeProduct.metadata["tier"] || ""
                }
                if (stripeProduct.metadata.hasOwnProperty("features")) {
                  data.features = stripeProduct.metadata.features.split(";");
                }
                const priceDB = await this.priceService.getPrice({ priceId: price.priceId });
                if (priceDB) {
                  const yearMonth = priceDB.monthlyYearly === 'm' ? 'month' : 'year';

                  data.prices = {
                    [yearMonth]: {
                      id: priceDB.priceId,
                      unit_amount: priceDB.price
                    }
                  }
                }
              }
            }
          } else {
            itemsPrice.push({ price: price.priceId });
          }
        }
      }
      let response: any = {};
      let recordData: any = {};
      if (dto.duration === 'm') {
        const subscription = await this.createSubscriptionAccordingDuration({ ...dto, amount }, company, itemsPrice, freeTrial, 'main');
        data.subId = subscription?.subscription?.subId;
        data.status = subscription?.subscription?.status;
        data.last4 = subscription?.subscription?.last4;
        data.currentPeriodEnd = subscription?.subscription.endDate?.getTime() / 1000 || 0;
        response = [data];
        recordData = subscription?.recordData
      } else if (dto.duration === 'y') {
        const subscription = [];
        const subsc = await this.createSubscriptionAccordingDuration({ ...dto, amount }, company, [itemsPrice[0]], freeTrial, 'main');
        data.subId = subsc?.subscription?.subId;
        data.status = subsc?.subscription?.status;
        data.last4 = subsc?.subscription?.last4;
        data.currentPeriodEnd = subsc?.subscription?.endDate?.getTime() / 1000 || 0;
        subscription[0] = data;
        recordData = subsc?.recordData
        if (itemsPrice.length > 1) {
          const addonSub = (await this.createSubscriptionAccordingDuration(dto, company, itemsPrice.slice(1), freeTrial, 'addon'));
          subscription[1] = addonSub.subscription;
        }
        response = subscription;
      }
      return recordData?.length ? { response, recordData } : response
    } catch (error) {
      Logger.log(`Failed to create subscription : ${error}`)
      throw new HttpException(`Failed to create subscription : ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }

  }

  async createSubscriptionAccordingDuration(dto, company, itemsPrice, freeTrial, type) {
    try {

      // create new subscription
      const stripeSubscription = await this.stripeClient.subscriptions.create({
        customer: company.stripeId,
        items: itemsPrice,
        off_session: true,
        ...(freeTrial ? {
          trial_period_days: freeTrial,  // 30-day free trial
          payment_behavior: 'default_incomplete', // Allow creating subscription without payment details
        } :
          {
            automatic_tax: { enabled: true }
          }),
        collection_method: 'charge_automatically',
        payment_behavior: 'default_incomplete',
        pending_invoice_item_interval: {
          interval: 'day',
          interval_count: 7,
        },
      });
      const subscriptionData = {
        companyId: company.id,
        subId: stripeSubscription.id,
        planId: dto.planId,
        packageId: dto.packageId,
        status: stripeSubscription.status,
        period: dto.duration,
        startDate: stripeSubscription.billing_cycle_anchor ? new Date(stripeSubscription.billing_cycle_anchor * 1000) : new Date(),
        endDate: new Date(stripeSubscription.current_period_end * 1000),
        subEndDate: new Date(stripeSubscription.cancel_at * 1000),
        type
      };

      const subscription = await this.subscriptionsRepository.create(subscriptionData, { include: [Plans, PlanPackages] });
      const subsc = await this.subscriptionsRepository.findOne({ where: { id: subscription.id }, include: [Plans, PlanPackages] });

      if (dto.amount) {
        const str = `
        ${subsc.company?.name} has subscribed to ${subsc.plans?.name} plan of ${subsc.packages?.name} package.
        The amount is ${dto.amount.price} and the duration is ${dto.amount.duration}.
        The subscription is in ${subsc.status} status. It has started on ${subsc.subEndDate} and the next billing date is ${subsc.endDate}.
        `
        const recordData = {
          for: 'subscriptions',
          str: str,
          id: subsc.id,
          action: 'create',
          payload: {
            id: subsc.id,
            subId: subsc.subId,
            companyId: subsc.companyId,
            plan: subsc.plans?.dataValues,
            package: subsc.packages?.dataValues,
            "status": subsc.status,
            "type": subsc.type,
            "createdAt": subsc.createdAt,
            "startDate": subsc.subEndDate,
            "endDate": subsc.endDate,
            "amount": dto.amount.price,
            "duration": dto.amount.duration
          }
        }
        return { subscription, recordData }
      } else {
        return { subscription };
      }
    } catch (error) {
      Logger.log("Failed to create subscription according to duration ", error)
    }
  }
  private async processPaymentIntent(dto, companyId: number) {

    if (!dto.paymentMethodId) {
      throw new HttpException(
        "Payment method ID not provided.",
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dto.planId && !dto.duration) {
      throw new HttpException(
        "Subscription Duration and Plan Id is not provided.",
        HttpStatus.BAD_REQUEST,
      );
    }
    const company = await this.getCompany(companyId);
    const stripeCustomer = await this.validStripeCustomer(
      company.stripeId,
      company.email,
    );

    let paymentMethodAttached = false;
    let paymentMethodIsDefault = false;

    if (stripeCustomer) {
      // stripe customer is valid. Check if passed payment method is attached to the customer and if it's default
      const paymentMethods =
        await this.stripeClient.customers.listPaymentMethods(company.stripeId, {
          type: "card",
        });
      for (let i = 0; i < paymentMethods.data.length; i++) {
        if (dto.paymentMethodId === paymentMethods.data[i].id) {
          paymentMethodAttached = true;
          if (
            dto.paymentMethodId ===
            stripeCustomer.invoice_settings.default_payment_method
          ) {
            paymentMethodIsDefault = true;
          }
          break;
        }
      }
    } else {
      // create new stripe customer
      const stripeId = await this.createStripeCustomer(company.email);
      await this.companyRepository.update({ stripeId }, { where: { id: company.id } });
    }

    // Attach the payment method to the customer
    if (!paymentMethodAttached) {
      await this.stripeClient.paymentMethods.attach(dto.paymentMethodId, {
        customer: company.stripeId,
      });

      if (!dto.billingAddress || !dto.billingAddress.line1 || !dto.billingAddress.state || !dto.billingAddress.postal_code || !dto.billingAddress.country) {
        throw new HttpException(
          "Billing address is required. (street,country,state or zipcode is missing)",
          HttpStatus.BAD_REQUEST,)
      }
      // Update billing address
      await this.stripeClient.customers.update(company.stripeId, {
        address: {
          line1: dto.billingAddress.line1,
          line2: dto.billingAddress.line2 || '',
          city: dto.billingAddress.city || '',
          state: dto.billingAddress.state,
          postal_code: dto.billingAddress.postal_code,
          country: dto.billingAddress.country,
        },
        metadata: {
          updated_at: new Date().toISOString()
        }
      });

    }

    // Change the default invoice settings on the customer to the new payment method
    if (!paymentMethodIsDefault) {
      await this.stripeClient.customers.update(company.stripeId, {
        invoice_settings: {
          default_payment_method: dto.paymentMethodId,
        },
      });
    }

  }

  async createSubscription(dto: CreateSubscriptionDto, companyId: number, userId: number) {
    try {
      await this.processPaymentIntent(dto, companyId);
      const subscription = await this.createSubscriptionWithoutCard({ dto: { duration: dto.duration, planId: dto.planId, packageId: dto.packageId }, companyId: companyId, checkCompany: false })
      return subscription;
    } catch (error) {
      Logger.log("Failed to create subscription ", error)
    }
  }

  async deleteSubscription(
    companyId?: number,
    data?: { deleteAll: boolean }
  ) {

    try {
      let recordData = [];
      const companySubscriptions = await this.subscriptionsRepository.findAll({
        where: {
          companyId,
          status: {
            [Op.in]: ['active', 'trialing', 'past_due', 'incomplete_expired', 'incomplete', 'paused']
          },
        },
        include: [Plans, PlanPackages],
        order: [["id", "DESC"]]
      });
      if (!companySubscriptions || companySubscriptions.length < 1) {
        throw new HttpException(
          "There is some issue deleting subscription.",
          HttpStatus.FORBIDDEN,
        );
      }
      if (companySubscriptions && companySubscriptions.length) {
        let iscancleSubscription = false;
        for (let companySubscription of companySubscriptions) {
          const stripeSubscription = await this.stripeClient.subscriptions.retrieve(companySubscription.subId);
          if (stripeSubscription) {
            for (let item of stripeSubscription.items.data) {
              const subAddon = await this.subscriptionAddonRepository.findOne({
                where: { subId: companySubscription.subId, subItemId: item.id },
                order: [["id", "DESC"]],
              });
              if (subAddon && subAddon.units > 0) {
                const diffDays = this.getDayDifference(companySubscription.endDate, new Date());
                const newQuantity = subAddon.quantity - (diffDays * subAddon.units);
                await this.stripeClient.subscriptionItems.createUsageRecord(subAddon.subItemId, { quantity: newQuantity });
                Object.assign(subAddon, {
                  quantity: newQuantity,
                });
                await this.saveSubscriptionAddon(subAddon);
              }
            }
            // Cancel the subscription
            const canceledSubscription = await this.stripeClient.subscriptions.del(companySubscription.subId, { invoice_now: true, prorate: true });
            if (canceledSubscription) {
              recordData.push({
                for: 'subscriptions',
                id: companySubscription.id,
                action: 'delete',
              })
              iscancleSubscription = true;
            }
          }
        }
        if (data?.deleteAll) {
          await this.subscriptionAddonRepository.destroy({ where: { companyId } })
          await this.subscriptionsRepository.destroy({ where: { companyId }, cascade: true })
        }
        return { response: true, recordData };
      }
    } catch (error) {
      Logger.log("Failed to delete subscription ", error)
    }
    return false;
  }

  async getAllCompanySubscription(dto: GetAllCompanySubscriptionDto) {
    try {

      const where: any = {};
      const order: any[] = [];
      if (dto.status) {
        if (typeof dto.status === 'string') {
          where.status = {
            [Op.iLike]: `%${dto.status}%`
          }
        } else {
          where.status = {
            [Op.or]: dto.status
          }
        }
      }
      if (dto.companyId) {
        where.companyId = dto.companyId
      }
      if (dto.subId) {
        where.subId = dto.subId
      }
      if (dto.planId) {
        where.planId = dto.planId
      }
      if (dto.startFrom) {
        where.startDate = {
          [Op.gt]: dto.startFrom
        }
      }
      if (dto.endTo) {
        where.endDate = {
          [Op.lt]: dto.endTo
        }
      }
      if (dto.period) {
        where.period = {
          [Op.iLike]: `%${dto.period}%`
        }
      }

      if (dto.type) {
        where.type = {
          [Op.iLike]: `%${dto.type}%`
        }
      }

      if (dto.sortBy && dto.sortType) {
        order.push([`${dto.sortBy}`, `${dto.sortType}`])
      }
      return await this.subscriptionsRepository.findAll({ where, order })
    } catch (error) {
      Logger.log("Error while retrieving subscriptions", error)
    }

  }

  async getCompanySubscription(dto: GetCompanySubscriptionDto) {
    try {
      const where: any = {}
      const order: any[] = []
      if (dto.status) {
        if (Array.isArray(dto.status)) {
          where.status = {
            [Op.or]: dto.status
          }
        } else {
          where.status = {
            [Op.iLike]: `%${dto.status}%`
          }
        }
      }
      if (dto.companyId) {
        where.companyId = dto.companyId
      }
      if (dto.subId) {
        where.subId = dto.subId
      }
      if (dto.planId) {
        where.planId = dto.planId
      }
      if (dto.startFrom) {
        where.startDate = {
          [Op.gt]: dto.startFrom
        }
      }
      if (dto.endTo) {
        where.endDate = {
          [Op.lt]: dto.endTo
        }
      }
      if (dto.period) {
        where.period = {
          [Op.iLike]: `%${dto.period}%`
        }
      }

      if (dto.type) {
        where.type = {
          [Op.iLike]: `%${dto.type}%`
        }
      }

      if (dto.sortBy && dto.sortType) {
        order.push([`${dto.sortBy}`, `${dto.sortType}`])
      }
      const subscription = await this.subscriptionsRepository.findOne({ where, order, include: [Plans, PlanPackages] })
      return subscription
    } catch (error) {
      Logger.log("Error while retrieving company subscription", error)
    }

  }

  async getCompanySubscriptionDetails(companyId: number) {
    try {

      const company = await this.getCompany(companyId);
      const subscription = await this.subscriptionsRepository.findOne({
        where: { companyId: company.id, status: { [Op.or]: ['active', 'trialing'] }, type: { [Op.iLike]: '%main%' } },
        order: [["id", "ASC"]], //If subscription plan is yearly it will return 1st which is main product and if plan is monthly then it will return 1 which subscription which contains both main and addons product
      });

      const data = new CompanySubscriptionDto();

      if (subscription) {

        data.subId = subscription.subId;
        data.status = subscription.status;
        data.last4 = subscription.last4;
        const subDetails = await this.stripeClient.subscriptions.retrieve(subscription.subId)
        if (subDetails) {
          const subItems = subDetails.items.data;
          if (subItems.length) {
            const prices = subItems.map(i => ({ priceId: i.price.id, productId: i.plan.product }))
            for (let price of prices) {

              const product = await this.productService.getProduct(price.productId)
              if (product && product.productType === 'main') {
                const subPrice = await this.priceService.getPrice({ priceId: price.priceId })
                if (subPrice) {
                  data.productId = subPrice.productId
                  data.priceId = subPrice.priceId
                  const product = await this.stripeClient.products.retrieve(subPrice.productId)
                  if (product) {
                    if (product.metadata.hasOwnProperty("capabilities")) {
                      data.capabilities = this.mapCapabilities(product.metadata.capabilities)
                    }
                    if (product.metadata.hasOwnProperty("package")) {
                      data.packageName = product.metadata.package;
                      const packagePlan = await this.planPackageService.getPlanPackageDetails({ name: product.metadata.package })
                      if (packagePlan) {
                        data.packageId = packagePlan.id;
                      }
                    }
                    if (product.metadata?.hasOwnProperty("package_type") && (product.metadata.package_type === 'assessment' || product.metadata.package_type === 'recruitment' || product.metadata.package_type === 'fullcycle')) {
                      data.packageType = product.metadata.package_type;
                    }

                    // data.id = product.id,
                    data.acceptedPackageType = product.metadata.hasOwnProperty("accepted_packages_type") && product.metadata.accepted_packages_type.split(";");
                    data.features = product.metadata.hasOwnProperty("features") && product.metadata.features.split(";");
                    // data.order = product.metadata.hasOwnProperty("order") && product.metadata.order,
                    const priceDB = await this.priceService.getPrice({ priceId: subPrice.priceId });
                    // console.log({priceDB});
                    if (priceDB) {
                      const yearMonth = priceDB.monthlyYearly === 'm' ? 'month' : 'year';

                      data.prices = {
                        [yearMonth]: {
                          id: priceDB.priceId,
                          unit_amount: priceDB.price
                        }
                      }
                    }


                    if (product.metadata.hasOwnProperty("tier")) {
                      data.planName = product.metadata["tier"] || "";
                      const plan = await this.planService.getPlanDetails({ name: product.metadata.tier });
                      if (plan) {
                        data.planId = plan.id
                      }
                    }
                    data.currentPeriodEnd = subscription.endDate?.getTime() / 1000 || 0;
                  }
                }
              }
            }
          }
        }
        data.last4 = subscription.last4;
      }
      else {
        //no active subscription
        data.subId = "noSubscription";
      }

      return data;
    } catch (error) {
      console.log("Error getting company subscription details ", error)
    }
  }

  async getSubscriptionBySubId(subId: string) {
    try {
      if (!subId) {
        throw new BadRequestException("Subscription ID is not provided");
      }

      return await this.subscriptionsRepository.findOne({ where: { subId: { [Op.iLike]: `%${subId}%` } } });
    } catch (error) {
      Logger.log("Error while retrieving subsctiption based on subscriptionId", error)
    }
  }

  async createSubscriptionDataOnDb(data: {
    subId?: string;
    productId?: any;
    companyId?: number;
    status?: Stripe.Subscription.Status;
    startDate?: Date;
    endDate?: Date;
    period?: 'm' | 'y';
  }) {
    try {

      const { subId, productId, companyId, status, startDate, endDate, period } = data;
      if (!subId) {
        throw new BadRequestException("Subscription ID not provided.");
      }

      const updatedData = {
        ...(startDate && { startDate }),
        ...(endDate && { endDate }),
        ...(status && { status }),
        ...(period && { period }),

      }
      let subscriptionRecord = await this.getSubscriptionBySubId(subId);

      if (!subscriptionRecord) {
        if (!productId || typeof productId !== 'string') {
          throw new BadRequestException("Product ID not provided.");
        }

        const product = await this.productService.getProduct(productId);

        if (companyId && product) {
          subscriptionRecord = await this.subscriptionsRepository.create({
            companyId,
            status,
            subId,
            // planId: data.planId,
            // planId: product.planId,
            period
          });
          Object.assign(subscriptionRecord, updatedData)
          await subscriptionRecord.save()
        } else {
          throw new BadRequestException("Company Id is not provided")
        }
      } else {
        Object.assign(subscriptionRecord, updatedData);
        await subscriptionRecord.save();
      }

      return subscriptionRecord;
    } catch (error) {
      Logger.log("Failed to create subscription data on db", error)
    }
  }

  async addPaymentIntentDetails(data: { invoiceId: string }) {
    const { invoiceId } = data;

    if (!invoiceId) {
      throw new BadRequestException("Invoice ID is not provided.");
    }

    try {
      const invoiceData = await this.stripeClient.invoices.retrieve(invoiceId, { expand: ["payment_intent"] });
      const paymentIntent = invoiceData?.payment_intent;

      if (paymentIntent && typeof paymentIntent === 'object' && typeof paymentIntent.payment_method === 'string') {
        const paymentMethod = await this.stripeClient.paymentMethods.retrieve(paymentIntent.payment_method);

        const last4 = paymentMethod?.card?.last4;
        if (last4) {
          await this.subscriptionsRepository.update(
            { last4 },
            { where: { subId: invoiceData.subscription as string } }
          );
        }
      }
    } catch (error) {
      throw new HttpException(`Failed to add payment intent details: ${error.message}`, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }


  async updateSubscriptionAddon(dto: CompanyAddonsDto, companyId: number) {
    const { addonName, type = '' } = dto;

    try {
      const companySubscription = await this.subscriptionsRepository.findOne({
        where: { companyId, status: { [Op.or]: ['active', 'trialing'] } },
        order: [["id", "DESC"]],
      });

      if (!companySubscription) return;

      const subAddon = await this.subscriptionAddonRepository.findOne({
        where: { subId: companySubscription.subId, addonName },
        order: [["id", "DESC"]],
      });

      if (!subAddon) return;
      let recordData: any = {};
      if (type === 'increase') {
        recordData = await this.handleAddonIncrease(subAddon, companySubscription);
      } else if (type === 'decrease' && subAddon.units > 0) {
        recordData = await this.handleAddonDecrease(subAddon);
      }
      return "Addon successfully updated";
    } catch (error) {
      Logger.error("Error while updating subscription addon", error);
    }
  }

  private async handleAddonIncrease(subAddon, companySubscription) {
    try {

      const price = await this.priceService.getPriceByPriceId(subAddon.priceId);
      if (!price) return;

      const diffDays = this.getDayDifference(companySubscription.endDate, new Date());
      const newQuantity = subAddon.quantity + diffDays;

      await this.stripeClient.subscriptionItems.createUsageRecord(subAddon.subItemId, { quantity: newQuantity });

      Object.assign(subAddon, {
        units: subAddon.units + 1,
        quantity: newQuantity,
      });

      const addon = await this.saveSubscriptionAddon(subAddon);
      return addon;
    } catch (error) {
      Logger.log("Failed to increase accons ", error)
    }
  }

  private async handleAddonDecrease(subAddon) {
    try {
      Object.assign(subAddon, { units: !!subAddon.units ? subAddon.units - 1 : subAddon.units });
      const addon = await this.saveSubscriptionAddon(subAddon);
      return addon;
    } catch (error) {
      Logger.log("Failed to decrease addons ", error)
    }
  }

  private async saveSubscriptionAddon(subAddon) {
    try {
      return await this.subscriptionAddonRepository.create({
        subId: subAddon.subId,
        companyId: subAddon.companyId,
        subItemId: subAddon.subItemId,
        priceId: subAddon.priceId,
        addonName: subAddon.addonName,
        quantity: subAddon.quantity,
        units: subAddon.units,
      });
    } catch (error) {
      Logger.log("Error while creating subscription addons ", error)
    }
  }


  async setSubscriptionAddons(companyId: number) {
    try {
      const subscription = await this.subscriptionsRepository.findOne({
        where: { companyId, status: { [Op.or]: ['active', 'trialing'] }, type: { [Op.iLike]: 'addon' } },
        order: [["id", "DESC"]],
      });

      if (!subscription) return;

      const subscriptionItems = await this.stripeClient.subscriptionItems.list({
        subscription: subscription.subId,
      });

      if (!subscriptionItems?.data?.length) return;

      return await Promise.all(
        subscriptionItems.data.map(async (item) => {
          const addon = await this.subscriptionAddonRepository.findOne({
            where: { subId: subscription.subId, subItemId: item.id },
            order: [["id", "DESC"]],
          });

          if (addon) {
            const newQuantity = addon.units * 30;

            await this.stripeClient.subscriptionItems.createUsageRecord(addon.subItemId, { quantity: newQuantity });

            Object.assign(addon, { quantity: newQuantity });

            Logger.log("Set quantity successfully for addon", addon.addonName);
            return await this.saveSubscriptionAddon(addon);
          } else {
            return null;
          }
        })
      );
    } catch (error) {
      Logger.error("Error while setting the quantity on Stripe", error);
    }
  }


  async upgradeSubscription(dto: UpgradeSubscriptionDto, companyId, userId) {
    let recordData = []
    try {
      const companySubscriptions = await this.subscriptionsRepository.findAll({
        where: {
          companyId,
          status: {
            [Op.in]: ['active', 'trialing', 'past_due', 'incomplete_expired', 'incomplete', 'paused']
          },
        },
        include: [Plans, PlanPackages],
        order: [["id", "DESC"]]
      });
      if (companySubscriptions && companySubscriptions.length) {
        let isCancelSubscription = false
        const deleteSubscription = await this.deleteSubscription(companyId, { deleteAll: true });
        if (deleteSubscription ) {
          deleteSubscription.recordData && recordData.push(...deleteSubscription.recordData)
          isCancelSubscription = true
        }
        if (isCancelSubscription) {
          await this.processPaymentIntent(dto, companyId);
          Object.assign(dto, { isUpgraded: true });
          const upgradedSubscription = await this.createSubscriptionWithoutCard({ dto, companyId, checkCompany: false });
          if (upgradedSubscription && upgradedSubscription.recordData) {
            recordData.push(upgradedSubscription.recordData)
          }
          return { response: upgradedSubscription, recordData };
        }
      }
    } catch (error) {
      console.log("Error while upgrading subscription", error)
    }
  }

  getDayDifference(date1: Date | string, date2: Date | string): number {
    const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
    const d2 = typeof date2 === 'string' ? new Date(date2) : date2;

    if (isNaN(d1.getTime()) || isNaN(d2.getTime())) {
      throw new Error('Invalid date provided');
    }

    const diffTime: number = Math.abs(d2.getTime() - d1.getTime());

    const diffDays: number = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  }


  mapCapabilities(capId: string) {
    const capabilities = {
      cap1ats: ["publish_posts", "edit_posts"],
      cap2ats: ["publish_posts", "delete_posts"],
      cap3ats: ["publish_posts", "upload_files"],
      cap1ass: ["publish_posts", "list_users"],
      cap2ass: ["publish_posts", "export"],
      cap3ass: ["publish_posts", "import"],
    };

    return capabilities.hasOwnProperty(capId) ? capabilities[capId] : [];
  }
}
