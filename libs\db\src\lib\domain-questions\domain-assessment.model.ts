import { Column, DataType, Has<PERSON>any, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsInt, IsEnum, IsString, IsOptional } from "class-validator";
import { DomainQuestions } from "./domain-questions.model";

export enum AssessmentCategory {
  domainAssessment = "Domain Assessment",
  codingAssessment = "Coding Assessment",
  thirdPartyAssessment = "Third Party Assessment",
}

@Table({
  tableName: "domain-assessment",
})
export class DomainAssessment extends Model<DomainAssessment> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "DevOps Dev Domain Assessment",
    description: "Name of assessment",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  assessmentName?: string;

  @ApiProperty({
    example: "DevOps Engineer",
    description: "Name of job title",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  jobTitle?: string;

  @IsNotEmpty()
  @ApiProperty({
    example: "Test",
    description: "Name of question",
  })
  @Column({ type: DataType.STRING, allowNull: false })
  name: string;

  @IsString()
  @ApiProperty({
    example: "10",
    description: "duration",
  })
  @Column({ type: DataType.STRING })
  duration: string;

  @IsInt()
  @ApiProperty({
    example: "75",
    description: "passing score",
  })
  @Column({ type: DataType.INTEGER })
  passing: number;

  @IsOptional()
  @IsString()
  @ApiProperty({
    example: "Draft",
    description: "Status of specific assessment",
  })
  @Column({ type: DataType.STRING })
  status?: string;

  @ApiProperty({
    example: "1",
    description: "Industry id",
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  industryId: number;

  @ApiProperty({
    example: "Custom Department Name",
    description: "Additional department name when 'Other' is selected",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  additionalDepartment?: string;

  @ApiProperty({
    example: "1",
    description: "Company id",
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
  companyId: number;

  @ApiProperty({
    example: "1",
    description: "Some department name",
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  departmentId: number;

  @ApiProperty({ example: '1. Instruction for test', description: 'Instructions' })
  @Column({ type: DataType.TEXT, allowNull: true })
    instruction: string;

  @ApiProperty({
    enum: AssessmentCategory,
    oneOf: [
      { type: AssessmentCategory.domainAssessment },
      { type: AssessmentCategory.codingAssessment },
      { type: AssessmentCategory.thirdPartyAssessment },
    ],
  })
  @IsOptional()
  @IsEnum(AssessmentCategory)
  @Column({
    type: DataType.ENUM(
      AssessmentCategory.domainAssessment,
      AssessmentCategory.codingAssessment,
      AssessmentCategory.thirdPartyAssessment,
    ),
    allowNull: false,
  })
  category?: AssessmentCategory;

  @HasMany(() => DomainQuestions, { onDelete: "CASCADE" })
  questions: DomainQuestions[];
}
