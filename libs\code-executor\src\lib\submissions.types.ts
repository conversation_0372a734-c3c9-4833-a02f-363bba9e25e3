export type SubmissionReq = {
  language_id: number;
  source_code: string;
  stdin?: string;
  additionalFiles?: string;
  test_cases?: TestCase[];
  question?: string;
}

export type BatchSumissions = {
  language_id: number;
  source_code: string;
  stdin?: string;
  additionalFiles?: string;
  test_cases?: TestCase[];
  question?: string;
}[];

export type SubmissionRes = {
  stdin: string;
  stdout: string;
  status_id: number;
  time: string;
  memory: number;
  stderr: string;
  token: string;
  compile_output: string | null;
  additional_files?: string | null;
  status: Status;
  test_cases?: TestCaseResult[];
  overall_result?: string;
  generated_test_cases?: boolean;
}

export type Status = {
  id: number;
  description: string;
}

export type TestCase = {
  id: string;
  input: string;
  expected_output: string;
}

export type TestCaseResult = {
  id: string;
  input?: string;
  status: "passed" | "failed";
  expected_output: string;
  actual_output: string;
}
