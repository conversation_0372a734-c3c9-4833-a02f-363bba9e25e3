import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Workflow } from './workflow.model'
import { WorkflowService } from "./workflow.service";
import { WorkflowChatbotModule } from '../workflow-chatbot/workflow-chatbot.module';


@Module({
  imports: [SequelizeModule.forFeature([Workflow]),WorkflowChatbotModule],
  providers: [WorkflowService],
  exports: [SequelizeModule, WorkflowService],
})
export class WorkflowModule {}
