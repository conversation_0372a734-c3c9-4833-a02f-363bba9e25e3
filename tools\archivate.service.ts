import * as archiver from 'archiver';
import { decode } from 'base-64';
import * as fs from 'fs';
import { MultiFileSub } from './multi-file.interface';
import { streamToString } from './readStream.service';
import { sleep } from './utils';

export class ArchivateService {
  async generateZip(data: MultiFileSub) {
    //Create a zip file
    const output = fs.createWriteStream(__dirname + '/assets/my.zip');
    const archive = archiver('zip');

    archive.on('error', function (err) {
      throw err;
    });
    //Piping output of the Archiver to created file
    archive.pipe(output);
    //Data comes to backend encoded in base-64. So we need to decode it first 
    const decodedString = decode(data.content);

    const runBuffer = Buffer.from(`java -cp ${data.package}.jar Main.java`);
    const mainBuffer = Buffer.from(decodedString);

    //Adding files to archive separetly
    archive
      .append(runBuffer, { name: 'run' })
      .append(mainBuffer, { name: 'Main.java' })
      .append(fs.createReadStream(__dirname + `/assets/${data.package}.jar`), {
        name: `${data.package}.jar`,
      })
      .finalize();

    await sleep(3000);
    //Read file into enсoded string and return
    const zipContent = fs.createReadStream(__dirname + '/assets/my.zip');
    const zipParse = await streamToString(zipContent);

    return zipParse;
  }
}
