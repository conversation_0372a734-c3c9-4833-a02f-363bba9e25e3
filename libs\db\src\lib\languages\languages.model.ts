import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';

@Table({
  tableName: 'languages',
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class Languages extends Model<Languages> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    description: 'name of language',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    name?: string;

  @ApiProperty({
    example: '/usr/local/jdk17/bin/javac Main.java”',
    description: 'Compile script of the language',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    compile_script: string;

  @ApiProperty({
    example: '/usr/local/jdk17/bin/java Main',
    description: 'Run script of the language',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    run_script: string;

  @ApiProperty({
    example: 'script.js',
    description: 'name and extension of the script',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    source_file: string;
}
