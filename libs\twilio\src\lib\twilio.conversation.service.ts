import { Injectable, Logger } from '@nestjs/common';
import * as twilio from 'twilio';

@Injectable()
export class TwilioConversationService {
  private readonly _client: twilio.Twilio;
  private readonly _serviceSid = process.env.TWILIO_CHAT_SERVICE_SID;

  constructor() {
    this._client = twilio(process.env.TWILIO_ACCOUNT_SID, process.env.TWILIO_AUTH_TOKEN);
  }

  async createChatChannelAndToken({ channelName, userName }): Promise<any> {
    try {
      if (!channelName) {
        channelName = "ChatService";
      }
  
      let channel;
      try {
        channel = await this._client.chat.services(this._serviceSid)
          .channels(channelName)
          .fetch();
      } catch (error) {
        if (error.status === 404) {
          channel = await this._client.chat.services(this._serviceSid)
            .channels.create({
              uniqueName: channelName,
            });
        } else {
          console.error("Error checking or creating chat channel:", error);
          throw error;
        }
      }
  
      // Generate a token for the user
      const token = await this.generateChatToken(channelName,userName);
  
      return { channel, token };
    } catch (error) {
      console.error("Error creating chat channel and token:", error);
      throw error;
    }
  }
  
  async generateChatToken(channelName,identity: string): Promise<string> {
    const ChatGrant = twilio.jwt.AccessToken.ChatGrant;
    const AccessToken = twilio.jwt.AccessToken;
  
    const chatGrant = new ChatGrant({
      serviceSid: this._serviceSid
    });
  
    const token = new AccessToken(
      process.env.TWILIO_ACCOUNT_SID,
      process.env.TWILIO_VIDEO_API_KEY,
      process.env.TWILIO_VIDEO_API_SECRET,
      { identity }
    );
  
    token.addGrant(chatGrant);
  
    return token.toJwt();
  }

  async completeVideoRoom(name: string) {
    try {
      await this._client.conversations.services(process.env.TWILIO_CHAT_SERVICE_SID).conversations(name).remove();
    } catch (err) {
      Logger.error(err);
    }
  }
}
