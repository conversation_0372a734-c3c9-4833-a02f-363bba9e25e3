import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  Model,
  Table,
} from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { Plans } from '../plan/plan.model';

interface ProductAttrs {
  productId: string;
  name: string;
  productType: string;
  active: boolean;
  // planId:number;
}

@Table({ 
  tableName: 'product',
  createdAt: true,
  updatedAt: true,
  timestamps: true
 })
export class Product extends Model<Product, ProductAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: 'prod_xxx', description: 'Product id of stripe product' })
  @Column({ type: DataType.STRING, allowNull: false })
  productId: string;

  // @ApiProperty({ example: '1', description: 'Plan Id' })
  // @ForeignKey(()=>Plans)
  // @Column({ type: DataType.INTEGER, allowNull: false })
  // planId: number;

  @ApiProperty({ example: 'tier1', description: 'Stripe Product name' })
  @Column({ type: DataType.STRING, allowNull: false, unique: false })
  name: string;

  @ApiProperty({ example: 'main/job/user', description: 'Type of the product' })
  @Column({ type: DataType.STRING, allowNull: false })
  productType: string;
  
  @ApiProperty({ example: 'assessmens/recruitment/fullcycle', description: 'Package type for product' })
  @Column({ type: DataType.STRING, allowNull: true })
  packageType: string;

  @ApiProperty({ example: 'true', description: 'Price is active or not' })
  @Column({ type: DataType.BOOLEAN, allowNull: false })
  active: boolean;

  // @BelongsTo(()=>Plans)
  // plans:Plans

}
