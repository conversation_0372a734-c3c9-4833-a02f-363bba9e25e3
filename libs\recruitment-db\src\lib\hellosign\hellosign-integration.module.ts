import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { SequelizeModule } from "@nestjs/sequelize";
import { FileModule } from "apps/pre-recruitment-api/src/file/file.module";
import { HellosignIntegration } from "./hellosign-integration.model";
import { HellosignIntegrationService } from "./hellosign-integration.service";

@Module({
  imports: [SequelizeModule.forFeature([HellosignIntegration]), FileModule],
  providers: [HellosignIntegrationService],
  exports: [HellosignIntegrationService],
})
export class HellosignIntegrationModule {}
