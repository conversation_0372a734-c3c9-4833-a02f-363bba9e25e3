import { Injectable } from '@nestjs/common';

@Injectable()
export class EditorManagerService {
  private editorState = [];
  private startCode = '//start code';

  setEditorState = (newEditorState) => {
    const existedState = this.editorState.find((state) => state.assignmentId === newEditorState.assignmentId);

    if (existedState) {
      this.editorState.map((state) => {
        if (state.assignmentId === newEditorState.assignmentId) {
          return (state.editorState = newEditorState.editorState);
        } else {
          return state;
        }
      });
    } else {
      this.editorState = [...this.editorState, newEditorState];
    }
  };

  getEditorState = (room) => {
    const existedState = this.editorState.find((state) => state.assignmentId === room);
    if (existedState) {
      return existedState.editorState;
    } else {
      return this.startCode;
    }
  };
}
