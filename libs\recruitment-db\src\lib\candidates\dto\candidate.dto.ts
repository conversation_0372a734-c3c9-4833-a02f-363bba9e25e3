import { ApiProperty } from "@nestjs/swagger";
import { DefaultUserDto } from "../../users/dto/default-user.dto";

const IndustryExample = [{ id: 1 }, { id: 2 }];

export class CandidateDto extends DefaultUserDto {
  @ApiProperty({ example: "Fresher", description: "Candidate Type" })
  readonly candidate_type: string;

  @ApiProperty({ example: IndustryExample, description: "Industries" })
  readonly industries: typeof IndustryExample;

  @ApiProperty({ example: "1", description: "Position id" })
  readonly positionId: number;

  @ApiProperty({ example: "123456qQ@", description: "Password" })
  readonly password: string;
}
