import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { TemporalWorkflow } from './temporal.model';
import { TemporalWorkflowService } from './temporal.service';

@Module({
  imports: [
    SequelizeModule.forFeature([TemporalWorkflow]),
  ],
  providers: [TemporalWorkflowService],
  exports: [SequelizeModule,TemporalWorkflowService]

})
export class TemporalWorkflowModule {}
