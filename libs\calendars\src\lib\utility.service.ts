import { Inject, Injectable, forwardRef } from "@nestjs/common";
import { GoogleAuthService } from "./googleAuth.service";
import { MsGraphService } from "./msGraph.service";
import { IntegrationsService } from "@microservices/recruitment-db";



@Injectable()
export class UtilityService {
    constructor(
      @Inject(forwardRef(() => GoogleAuthService))
        private googleAuthService:GoogleAuthService,
        private msGraphService:MsGraphService,
        private integrationsService:IntegrationsService,
        ){}

        async getData(data){
          const provider2= await this.integrationsService.getProvider(data.recruiterId)
          const provider=provider2.calendar
            let slots;
            if(provider==="mcal"){
              slots= this.msGraphService.getFreeBusySlots(data.recruiterId,data.data)
            }else{
              slots= this.googleAuthService.getFreeBusySlots(data.recruiterId, data.data)
            }
            return slots
        }

        async createEvent(data){
          const provider2= await this.integrationsService.getProvider(data.id)
          const provider=provider2.calendar
            let slots;
            if(provider==="mcal"){
              slots= this.msGraphService.createEvent(
                data.id,
                data.data,
                data.action
              );  
            }else{
              await this.googleAuthService.createEvent(
                data.id,
                data.data,
              );         
               }
            return 
        }

}

