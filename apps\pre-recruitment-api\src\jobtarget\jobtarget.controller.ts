import { Controller, Post, Body, Delete, Get, UseGuards, Param } from '@nestjs/common';
import { JobTargetIntegrationDto, JobTargetIntegration, JobTargetService } from "@microservices/recruitment-db";
import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';
import { AuthGuard } from '@nestjs/passport';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';

@ApiTags('JobTarget Integration')
@ApiBearerAuth('access-token')
@Controller('jobtarget')
export class JobTargetController {
  constructor(private readonly jobTargetService: JobTargetService) { }

  @ApiOperation({ summary: "Integrate JobTarget Account" })
  @ApiResponse({ status: 200, type: JobTargetIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/integrate")
  //   @Permissions("OR", "jobtarget:add") // For future implementation
  integrateJobTargetAccount(
    @Body() dto: JobTargetIntegrationDto,
    @AuthUser() user: any,
  ) {
    return this.jobTargetService.integrateAccount(
      user["https://urecruits.com/companyId"],
      user["https://urecruits.com/userId"],
    );
  }

  @ApiOperation({ summary: "Get JobTarget Integration Details" })
  @ApiResponse({ status: 200, description: 'JobTarget Integration details' })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/account/get")
  @Permissions("OR", "background:view", "drug:view")
  getJobIntegrationDetails(@AuthUser() user: any) {
    const companyId = user["https://urecruits.com/companyId"];
    return this.jobTargetService.getJobIntegrationDetails(companyId);
  }

  @ApiOperation({ summary: "Get Job Details by Job ID" })
  @ApiResponse({ status: 200, description: 'Job details returned successfully' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  @UseGuards(AuthGuard("jwt"))
  @Get("/:jobId")
  @Permissions("OR", "job:view")
  getJobById(@Param("jobId") jobId: string, @AuthUser() user: any) {
    const companyId = user["https://urecruits.com/companyId"];
    return this.jobTargetService.getJobById(jobId, companyId);
  }

  @ApiOperation({ summary: "Remove Job Target Account" })
  @ApiResponse({ status: 200, type: JobTargetIntegration })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/account/remove")
  removeJobTargetAccount(@AuthUser() user: any) {
    return this.jobTargetService.removeJobTargetAccount(
      user["https://urecruits.com/companyId"]
    );
  }
}

