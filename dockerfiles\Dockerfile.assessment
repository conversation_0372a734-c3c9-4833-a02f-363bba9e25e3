# Changes by Dhruv patel
# FROM node:18-bullseye-slim AS build-stage
FROM public.ecr.aws/docker/library/node:20.19.3-bullseye-slim AS build-stage

# Install Python and other required build tools
RUN apt-get update && apt-get install python3 build-essential -y ca-certificates && rm -rf /var/lib/apt/lists/*


# Set Python for node-gyp (used for compiling native Node.js modules)
ENV PYTHON /usr/bin/python3

WORKDIR /app

COPY . .

RUN yarn

RUN yarn run build
# Final stage
# Changes by Dhruv
# FROM node:18-bullseye-slim
FROM public.ecr.aws/docker/library/node:20.19.3-bullseye-slim

# Install CA certificates
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*


WORKDIR /app

COPY --from=build-stage /app/dist/apps/assessment-api .
COPY --from=build-stage /app/node_modules ./node_modules
COPY ./apps/temporal/src/app/certs ./certs
COPY package.json .

# Set environment variables for Temporal Cloud connection
ENV TEMPORAL_SERVER=recruitment-prod.lrnb6.tmprl.cloud:7233
ENV TEMPORAL_NAMESPACE=recruitment-prod.lrnb6
ENV CLIENT_CA_PATH=/app/certs/client.pem
ENV CLIENT_KEY_PATH=/app/certs/client.key

EXPOSE 3000

CMD "node" "main.js"
