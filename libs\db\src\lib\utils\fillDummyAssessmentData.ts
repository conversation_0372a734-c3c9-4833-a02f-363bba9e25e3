export const getDummyLiveTask = (companyId: number) => 
    [
        {
            name: "Find the Longest Palindromic Substring",
            description: "Write a function that finds the longest palindromic substring in a given string.",
            instruction: "\"<ul>\n<li>Identify the longest palindrome in the given string.</li>\n<li>Use an efficient approach (expand around center or dynamic programming).</li>\n<li>Return the longest palindromic substring.</li>\n</ul>\n\"",
            languageId: 71,
            status: 'DRAFT',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
def longest_palindromic_substring(s: str) -> str:
    """
    Requirements:
    - Find the longest palindrome in the given string
    - Return the palindrome as a string
    - Handle empty strings and single characters
    - Consider both odd and even length palindromes
    
    Example:
    Input: "babad"
    Output: "bab" or "aba"
    """
    # TODO: Implement function
    pass

input_string = "babad"
print(longest_palindromic_substring(input_string))
            `
        },
        {
            name: "Merge Two Sorted Arrays",
            description: "Write a function to merge two sorted arrays into a single sorted array.",
            instruction: "\"<ul>\n<li>Take two sorted arrays as input.</li>\n<li>Merge them into one sorted array.</li>\n<li>Return the merged sorted array.</li>\n</ul>\n\"",
            languageId: 93,
            status: 'ACTIVE',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
function mergeSortedArrays(arr1, arr2) {
    /*
    Requirements:
    - Merge two sorted arrays into one sorted array
    - Maintain the sorting order
    - Handle arrays of different lengths
    - Return the merged sorted array
    
    Example:
    Input: [1, 3, 5], [2, 4, 6]
    Output: [1, 2, 3, 4, 5, 6]
    */
    // TODO: Implement function
    return [];
}

console.log(mergeSortedArrays([1, 3, 5], [2, 4, 6]));
            `
        },
        {
            name: "Find the Intersection of Two Arrays",
            description: "Write a function that returns the intersection of two arrays.",
            instruction: "\"<ul>\n<li>Find the common elements between two given arrays.</li>\n<li>Ensure the order does not matter.</li>\n</ul>\n\"",
            languageId: 93,
            status: 'ACTIVE',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
function arrayIntersection(arr1, arr2) {
    /*
    Requirements:
    - Find common elements between two arrays
    - Return unique elements only
    - Order doesn't matter
    - Handle arrays of different lengths
    
    Example:
    Input: [1,2,2,1], [2,2]
    Output: [2]
    */
    // TODO: Implement function
    return [];
}

console.log(arrayIntersection([1,2,2,1], [2,2]));
            `
        },
        {
            name: "Find the Nth Fibonacci Number",
            description: "Write a function to find the Nth Fibonacci number using memoization.",
            instruction: "\"<ul>\n<li>Calculate the Nth Fibonacci number using memoization.</li>\n<li>Handle large values of N efficiently.</li>\n</ul>\n\"",
            languageId: 48,
            status: 'DRAFT',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
#include <stdio.h>

int fibonacci(int n) {
    /*
    Requirements:
    - Calculate the nth Fibonacci number
    - Use memoization for efficiency
    - Handle large values of n
    - Return the Fibonacci number
    
    Example:
    Input: 10
    Output: 55
    */
    // TODO: Implement function
    return 0;
}

int main() {
    int n = 10;
    printf("%d", fibonacci(n));
    return 0;
}
            `
        },
        {
            name: "Train a Linear Regression Model",
            description: "In this task, you will implement a function that reads data from standard input, performs linear regression, and outputs the model coefficients.",
            instruction: "\"<ul>\n<li>Read data from standard input in CSV format.</li>\n<li>Implement linear regression using standard Python libraries.</li>\n<li>Print the coefficients of the trained model.</li>\n<li>Ensure it works with different dataset sizes.</li>\n</ul>\n\"",
            languageId: 71,
            status: 'ACTIVE',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
def train_model(data):
    """
    Requirements:
    - Train a linear regression model on the given data
    - Use feature1 and feature2 as predictors
    - Use target as the dependent variable
    - Return the model coefficients
    
    Example:
    Input: List of dictionaries with feature1, feature2, target values
    Output: List of coefficients [intercept, coef_feature1, coef_feature2]
    """
    # TODO: Implement linear regression using standard Python libraries
    # Hint: You can use numpy for matrix operations or implement the math directly
    return [0, 0, 0]

if __name__ == "__main__":
    # Example data
    data = [
        {'feature1': 1, 'feature2': 2, 'target': 3},
        {'feature2': 4, 'feature1': 2, 'target': 6},
        {'feature1': 3, 'feature2': 6, 'target': 9},
        {'feature2': 8, 'feature1': 4, 'target': 12},
        {'feature1': 5, 'feature2': 10, 'target': 15}
    ]
    coefficients = train_model(data)
    print(coefficients)
            `
        },
        {
            name: "Check if a Number is Prime",
            description: "Implement a function that checks whether a given integer is a prime number.",
            instruction: "\"<ul>\n<li>Check if the number is prime.</li>\n<li>Optimize for efficiency (O(√N) complexity).</li>\n<li>Print `true` if the number is prime, else `false`.</li>\n</ul>\n\"",
            languageId: 93,
            status: 'ACTIVE',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
function isPrime(n) {
    /*
    Requirements:
    - Check if the number is prime
    - Return true if prime, false otherwise
    - Optimize for efficiency (O(√N) complexity)
    - Handle edge cases (1, 2, negative numbers)
    
    Example:
    Input: 17
    Output: true
    */
    // TODO: Implement function
    return false;
}

const num = 17;
console.log(isPrime(num));
            `
        },
        {
            name: "Reverse a String",
            description: "Write a function that takes a string as input and returns the reversed string.",
            instruction: "\"<ul>\n<li>Reverse the input string without using built-in functions.</li>\n<li>Read input from standard input.</li>\n</ul>\n\"",
            languageId: 48,
            status: 'DRAFT',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
#include <stdio.h>
#include <string.h>

void reverseString(char *str) {
    /*
    Requirements:
    - Reverse the input string in-place
    - Handle Unicode characters and emojis
    - Don't use built-in reverse functions
    - Return void (modify string in-place)
    
    Example:
    Input: "Hello, World! 🌟"
    Output: "🌟 !dlroW ,olleH"
    */
    // TODO: Implement function
}

int main() {
    char input[] = "Hello, World! 🌟";
    reverseString(input);
    printf("%s", input);
    return 0;
}
            `
        },
        {
            name: "Generate Fibonacci Sequence",
            description: "Implement a function to generate the first N Fibonacci numbers.",
            instruction: "\"<ul>\n<li>Generate the Fibonacci sequence for a given N.</li>\n<li>Use an optimized approach (dynamic programming recommended).</li>\n</ul>\n\"",
            languageId: 73,
            status: 'ACTIVE',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
use std::io;

fn fibonacci(n: u32) -> Vec<u32> {
    /*
    Requirements:
    - Generate first n Fibonacci numbers
    - Return as a vector
    - Use dynamic programming for efficiency
    - Handle edge cases (n = 0, n = 1)
    
    Example:
    Input: 8
    Output: [0, 1, 1, 2, 3, 5, 8, 13]
    */
    // TODO: Implement function
    vec![]
}

fn main() {
    let n = 8;
    let result = fibonacci(n);
    for num in result {
        print!("{} ", num);
    }
}
            `
        },
        {
            name: "Validate Email Address",
            description: "Implement a function that validates an email address.",
            instruction: "\"<ul>\n<li>Check if the email follows standard format using regex.</li>\n<li>Return `Valid` or `Invalid`.</li>\n</ul>\n\"",
            languageId: 68,
            status: 'ACTIVE',
            companyId: companyId,
            packageId:3,
            databaseId:null,
            assessmentType: 'live-task',
            starterCode: `
<?php
function isValidEmail($email) {
    /*
    Requirements:
    - Validate email format using regex
    - Check for proper domain structure
    - Handle special characters
    - Return true if valid, false otherwise
    
    Example:
    Input: "<EMAIL>"
    Output: true
    */
    // TODO: Implement function
    return false;
}

$email = "<EMAIL>";
echo isValidEmail($email) ? "Valid" : "Invalid";
?>
            `
        },
    ]

export const getDummyTakeHomeTasks = () => [
    {
        name: "JavaScript Basics",
        description: "Basic JavaScript exercises to test problem-solving skills.",
        questions: [
            {
                name: "Find Maximum of Two Numbers",
                languageId: 63, // JavaScript
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Return the larger number of the two given integers.</p>\"",
                description: "Return the maximum number.",
                outputType: "Integer",
                outputDescription: "Returns the larger integer.",
                starterCode: `
function findMax(a, b) {
    /*
    Requirements:
    - Compare two numbers
    - Return the larger number
    - Handle equal numbers
    - Handle negative numbers
    
    Example:
    Input: 5, 10
    Output: 10
    */
    // TODO: Implement the comparison logic
    return 0;
}

const data = [{{a}}, {{b}}];
console.log(findMax(...data));
                `,
                testCaseInputs: "[{\"name\":\"a\",\"type\":\"Integer\"},{\"name\":\"b\",\"type\":\"Integer\"}]",
                testcases: [
                    { output: "10", input: "[{\"a\":5},{\"b\":10}]" },
                    { output: "30", input: "[{\"a\":30},{\"b\":15}]" }
                ]
            },
            {
                name: "Reverse a String",
                languageId: 63, // JavaScript
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Reverse the given string and return it.</p>\"",
                description: "Return the reversed string.",
                outputType: "String",
                outputDescription: "Returns the reversed string.",
                starterCode: `
function reverseString(str) {
    /*
    Requirements:
    - Reverse the input string
    - Handle empty strings
    - Handle special characters
    - Return the reversed string
    
    Example:
    Input: "hello"
    Output: "olleh"
    */
    // TODO: Implement string reversal logic
    return '';
}

const input = "{{str}}";
console.log(reverseString(input));
                `,
                testCaseInputs: "[{\"name\":\"str\",\"type\":\"String\"}]",
                testcases: [
                    { output: "\"olleh\"", input: "[{\"str\":\"hello\"}]" },
                    { output: "\"dlrow\"", input: "[{\"str\":\"world\"}]" }
                ]
            }
        ]
    },
    {
        name: "Array Manipulation",
        description: "Working with arrays in JavaScript.",
        questions: [
            {
                name: "Find Minimum in an Array",
                languageId: 71, // Python
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Return the smallest number in the array.</p>\"",
                description: "Return the minimum value.",
                outputType: "Integer",
                outputDescription: "Returns the minimum integer.",
                starterCode: `
def find_min(arr):
    """
    Requirements:
    - Find the minimum value in the array
    - Handle empty arrays
    - Handle negative numbers
    - Return the minimum value
    
    Example:
    Input: [3, 1, 4, 1, 5]
    Output: 1
    """
    # TODO: Implement minimum finding logic
    return 0

input_arr = {{arr}}
print(find_min(input_arr))
                `,
                testCaseInputs: "[{\"name\":\"arr\",\"type\":\"Inter Array\"}]",
                testcases: [
                    { output: "1", input: "[{\"arr\":[\"3\", \"1\", \"4\", \"1\", \"5\"]}]" },
                    { output: "0", input: "[{\"arr\":[\"0\", \"2\", \"3\"]}]" }
                ]
            }
        ]
    },
    {
        name: "All Positive Numbers",
        description: "A simple function to check if all numbers in an array are positive.",
        questions: [
            {
                name: "Check for Positivity",
                languageId: 72, // Ruby
                packageId: 3,
                databaseId: null,
                outputType: "String",
                outputDescription: "Returns true or false.",
                candidateInstruction: "\"<p>Return true if all elements in the array are positive.</p>\"",
                description: "Return true or false based on whether all numbers are positive.",
                starterCode: `
def all_positive(arr)
    """
    Requirements:
    - Check if all numbers in array are positive
    - Handle empty arrays
    - Handle zero values
    - Return true if all positive, false otherwise
    
    Example:
    Input: [1, 2, 3]
    Output: true
    """
    # TODO: Implement positivity check
    return false
end

input_arr = {{arr}}
puts all_positive(input_arr)
                `,
                testCaseInputs: "[{\"name\":\"arr\",\"type\":\"Inter Array\"}]",
                testcases: [
                    { output: "true", input: "[{\"arr\":[\"1\", \"2\", \"3\"]}]" },
                    { output: "false", input: "[{\"arr\":[\"-1\", \"2\", \"3\"]}]" }
                ]
            }
        ]
    },
    {
        name: "Number Operations",
        description: "Practice working with numbers.",
        questions: [
            {
                name: "Check Prime Number",
                languageId: 71, // Python
                packageId: 3,
                databaseId: null,
                outputType: "String",
                outputDescription: "Returns true or false.",
                candidateInstruction: "\"<p>Return true if the given number is prime.</p>\"",
                description: "Return true or false.",
                starterCode: `
def is_prime(n):
    """
    Requirements:
    - Check if number is prime
    - Handle edge cases (1, 2, negative numbers)
    - Optimize for efficiency
    - Return true if prime, false otherwise
    
    Example:
    Input: 5
    Output: true
    """
    # TODO: Implement prime checking logic
    return True

num = {{n}}
print(str(is_prime(num)).lower())
                `,
                testCaseInputs: "[{\"name\":\"n\",\"type\":\"Integer\"}]",
                testcases: [
                    { output: "true", input: "[{\"n\":\"5\"}]" },
                    { output: "false", input: "[{\"n\":\"4\"}]" }
                ]
            },
            {
                name: "Multiply Elements of an Array",
                languageId: 72, // Ruby
                packageId: 3,
                databaseId: null,
                outputType: "Integer",
                outputDescription: "Returns the product of all numbers in the array.",
                candidateInstruction: "\"<p>Return the product of all numbers in the array.</p>\"",
                description: "Return an integer.",
                starterCode: `
def multiply_array(arr)
    """
    Requirements:
    - Multiply all numbers in array
    - Handle empty arrays
    - Handle zero values
    - Return the product
    
    Example:
    Input: [1, 2, 3, 4, 5]
    Output: 120
    """
    # TODO: Implement array multiplication
    return 0
end

input_arr = {{arr}}
puts multiply_array(input_arr)
                `,
                testCaseInputs: "[{\"name\":\"arr\",\"type\":\"Inter Array\"}]",
                testcases: [
                    { output: "120", input: "[{\"arr\":[\"1\", \"2\", \"3\", \"4\", \"5\"]}]" },
                    { output: "0", input: "[{\"arr\":[\"0\", \"1\", \"2\"]}]" }
                ]
            }
        ]
    },
    {
        name: "String Operations",
        description: "Working with strings.",
        questions: [
            {
                name: "Count Vowels in a String",
                languageId: 71, // Python
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Return the number of vowels in the given string.</p>\"",
                description: "Return an integer.",
                outputType: "Integer",
                outputDescription: "Returns the number of vowels in the input string.",
                starterCode: `
def count_vowels(s):
    """
    Requirements:
    - Count vowels in string (a, e, i, o, u)
    - Handle empty strings
    - Case insensitive
    - Return the count
    
    Example:
    Input: "hello"
    Output: 2
    """
    # TODO: Implement vowel counting logic
    return 0

input_str = {{str}}
print(count_vowels(input_str))
                `,
                testCaseInputs: "[{\"name\":\"str\",\"type\":\"String\"}]",
                testcases: [
                    { output: "2", input: "[{\"str\":\"hello\"}]" },
                    { output: "4", input: "[{\"str\":\"banana\"}]" }
                ]
            }
        ]
    },
    {
        name: "Math Challenges",
        description: "Mathematical operations.",
        questions: [
            {
                name: "Find Factorial",
                languageId: 63, // JavaScript
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Find the factorial of a given number.</p>\"",
                description: "Return an integer.",
                outputType: "Integer",
                outputDescription: "Factorial of the given number.",
                starterCode: `
function factorial(n) {
    /*
    Requirements:
    - Calculate factorial of n
    - Handle edge cases (0, 1)
    - Handle large numbers
    - Return the factorial
    
    Example:
    Input: 5
    Output: 120
    */
    // TODO: Implement factorial calculation
    return 0;
}

const num = {{n}}
console.log(factorial(num))
                `,
                testCaseInputs: "[{\"name\":\"n\",\"type\":\"Integer\"}]",
                testcases: [
                    { output: "120", input: "[{\"n\":5}]" },
                    { output: "6", input: "[{\"n\":3}]" }
                ]
            },
            {
                name: "Find Sum of Digits",
                languageId: 63, // JavaScript
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Return the sum of the digits of a given number.</p>\"",
                description: "Return an integer.",
                outputType: "Integer",
                outputDescription: "Returns the sum of the digits.",
                starterCode: `
function sumDigits(n) {
    /*
    Requirements:
    - Sum all digits in number
    - Handle negative numbers
    - Handle single digits
    - Return the sum
    
    Example:
    Input: 123
    Output: 6
    */
    // TODO: Implement digit sum calculation
    return 0;
}

const num = {{n}}
console.log(sumDigits(num))
                `,
                testCaseInputs: "[{\"name\":\"n\",\"type\":\"Integer\"}]",
                testcases: [
                    { output: "6", input: "[{\"n\":123}]" },
                    { output: "15", input: "[{\"n\":456}]" }
                ]
            }
        ]
    },
    {
        name: "Array Operations",
        description: "Array operations",
        questions: [
            {
                name: "Find String Lengths",
                languageId: 63, // JavaScript
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Return an array containing the lengths of each string in the given array.</p>\\n<ul>\\n<li>For each string in the array, compute its length.</li>\\n</ul>\\n\"",
                description: "Write a function that returns an array of string lengths.",
                outputDescription: "Returns an array of integers representing the lengths of each string.",
                outputType: "Inter Array",
                starterCode: `
function getStringLengths(arr) {
    /*
    Requirements:
    - Get length of each string in array
    - Handle empty strings
    - Handle empty array
    - Return array of lengths
    
    Example:
    Input: ["apple", "cat", "banana"]
    Output: [5, 3, 6]
    */
    // TODO: Implement string length calculation
    return [];
}

const arr = {{arr}}
console.log(JSON.stringify(getStringLengths(arr)))
                `,
                testCaseInputs: "[{\"name\":\"arr\",\"description\":\"An array of strings.\",\"type\":\"String Array\"}]",
                testcases: [
                    { output: "[5,3,6]", input: "[{\"arr\":[\"apple\",\"cat\",\"banana\"]}]" },
                    { output: "[4,6,5]", input: "[{\"arr\":[\"pear\",\"orange\",\"grape\"]}]" },
                    { output: "[6,5,4]", input: "[{\"arr\":[\"mango\",\"peach\",\"kiwi\"]}]" }
                ]
            },
            {
                name: "Find Maximum in Array",
                languageId: 63, // JavaScript
                packageId: 3,
                candidateInstruction: "\"<p>Find the maximum number in the given integer array.</p>\\n<ul>\\n<li>Return the largest number in the array.</li>\\n</ul>\\n\"",
                databaseId: null,
                description: "Write a function that finds the maximum number in an integer array.",
                outputDescription: "Returns the largest integer in the input array.",
                outputType: "Integer",
                starterCode: `
function findMax(arr) {
    /*
    Requirements:
    - Find maximum number in array
    - Handle empty arrays
    - Handle negative numbers
    - Return the maximum value
    
    Example:
    Input: [5, 12, 42, 7]
    Output: 42
    */
    // TODO: Implement maximum finding logic
    return 0;
}

const arr = {{arr}}
console.log(findMax(arr))
                `,
                testCaseInputs: "[{\"name\":\"arr\",\"description\":\"An array of integers.\",\"type\":\"Inter Array\"}]",
                testcases: [
                    { output: "42", input: "[{\"arr\":[\"5\",\"12\",\"42\",\"7\"]}]" },
                    { output: "100", input: "[{\"arr\":[\"34\",\"67\",\"89\",\"100\"]}]" },
                    { output: "90", input: "[{\"arr\":[\"23\",\"45\",\"78\",\"90\"]}]" }
                ]
            }
        ]
    },
    {
        name: "Advanced String Manipulation",
        description: "Exercises to test your string manipulation skills.",
        questions: [
            {
                name: "Check for Anagrams",
                languageId: 71, // Python
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Return true if the two given strings are anagrams of each other.</p>\"",
                description: "Check if two strings are anagrams.",
                outputType: "String",
                outputDescription: "Returns true or false.",
                starterCode: `
def are_anagrams(str1, str2):
    """
    Requirements:
    - Check if two strings are anagrams
    - Handle empty strings
    - Case insensitive
    - Return true if anagrams, false otherwise
    
    Example:
    Input: "listen", "silent"
    Output: true
    """
    # TODO: Implement anagram checking logic
    return False

string1 = "{{str1}}"
string2 = "{{str2}}"
print(str(are_anagrams(string1, string2)).lower())
                `,
                testCaseInputs: "[{\"name\":\"str1\",\"type\":\"String\"},{\"name\":\"str2\",\"type\":\"String\"}]",
                testcases: [
                    { output: "true", input: "[{\"str1\":\"listen\"},{\"str2\":\"silent\"}]" },
                    { output: "false", input: "[{\"str1\":\"hello\"},{\"str2\":\"world\"}]" }
                ]
            },
            {
                name: "Capitalize First Letter",
                languageId: 63, // JavaScript
                packageId: 3,
                databaseId: null,
                candidateInstruction: "\"<p>Capitalize the first letter of each word in the given string.</p>\"",
                description: "Return the string with each word capitalized.",
                outputType: "String",
                outputDescription: "Returns the modified string.",
                starterCode: `
function capitalizeFirstLetter(str) {
    /*
    Requirements:
    - Capitalize the first letter of each word in the string
    - Handle empty strings
    - Handle special characters
    - Return the modified string
    
    Example:
    Input: "hello world"
    Output: "Hello World"
    */
    // TODO: Implement string capitalization logic
    return '';
}

const input = "{{str}}";
console.log(capitalizeFirstLetter(input));
                `,
                testCaseInputs: "[{\"name\":\"str\",\"type\":\"String\"}]",
                testcases: [
                    { output: "\"Hello World\"", input: "[{\"str\":\"hello world\"}]" },
                    { output: "\"JavaScript Is Fun\"", input: "[{\"str\":\"javascript is fun\"}]" }
                ]
            }
        ]
    }
];

export const getDummyDomainData=(companyId:number)=>[
    {
        "departmentId": 1,
        "duration": "30 min",
        "industryId": 2,
        "name": "Business Analysis Assessment",
        "passing": 7,
        "instruction": "<ul><li>This assessment evaluates your business analysis skills and experience</li><li>Answer all questions honestly based on your actual experience</li><li>For multiple choice questions, select all applicable options</li><li>Provide detailed responses for text-based questions</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in systems analysis?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
                {
                  "id": "c69724fe-3bae-4587-8468-4593fe318600",
                  "isCorrect": false,
                  "score": 0,
                  "name": "1 year"
                },
                {
                  "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                  "isCorrect": true,
                  "score": 2,
                  "name": "1-3 years"
                },
                {
                  "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                  "isCorrect": false,
                  "score": 0,
                  "name": "More than 3 years"
                }
              ])
          },
          {
            "name": "What methodologies are you familiar with for business analysis?",
            "score": 4,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": false,
                "score": 0,
                "name": "Waterfall"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": false,
                "score": 0,
                "name": "Agile"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": true,
                "score": 2,
                "name": "Scrum"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 2,
                "name": "Lean"
              }
            ])
          },
          {
            "name": "Describe a challenging project you worked on as a Business Analyst.",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What tools do you use for data analysis?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Excel"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Tableau"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Photoshop"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "SQL"
              }
            ])
          },
          {
            "name": "How do you prioritize requirements in a project?",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What is your approach to stakeholder management?",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
      },
      {
        "departmentId": 4,
        "duration": "15 min",
        "industryId": 9,
        "name": "Content Writing Evaluation",
        "passing": 8,
        "instruction": "<ul><li>This evaluation assesses your content writing capabilities</li><li>Demonstrate your understanding of different content types</li><li>Share your experience with various writing tools and platforms</li><li>Explain your approach to SEO and content strategy</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in content writing?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
              {
                "id": "c69724fe-3bae-4587-8468-4593fe318600",
                "isCorrect": false,
                "score": 0,
                "name": "Less than 1 year"
              },
              {
                "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                "isCorrect": true,
                "score": 2,
                "name": "1-3 years"
              },
              {
                "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                "isCorrect": false,
                "score": 0,
                "name": "More than 3 years"
              }
            ])
          },
          {
            "name": "What types of content have you written?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": true,
                "score": 1,
                "name": "Blog Posts"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": true,
                "score": 1,
                "name": "Marketing Copy"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": false,
                "score": 0,
                "name": "Technical Documentation"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 1,
                "name": "Social Media Content"
              }
            ])
          },
          {
            "name": "Describe your writing process.",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What tools do you use for writing and editing?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Google Docs"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Grammarly"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Photoshop"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "WordPress"
              }
            ])
          },
          {
            "name": "How do you handle feedback on your writing?",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What is your approach to SEO in content writing?",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
      },
      {
        "departmentId": 18,
        "duration": "30 min",
        "industryId": 15,
        "name": "Training Coordination Proficiency Test",
        "passing": 9,
        "instruction": "<ul><li>This test evaluates your training coordination expertise</li><li>Share your experience with different training methodologies</li><li>Explain your approach to program development and evaluation</li><li>Describe how you handle various training scenarios</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in training coordination?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
              {
                "id": "c69724fe-3bae-4587-8468-4593fe318600",
                "isCorrect": false,
                "score": 0,
                "name": "Less than 1 year"
              },
              {
                "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                "isCorrect": true,
                "score": 2,
                "name": "1-3 years"
              },
              {
                "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                "isCorrect": false,
                "score": 0,
                "name": "More than 3 years"
              }
            ])
          },
          {
            "name": "What training methods are you familiar with?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": true,
                "score": 1,
                "name": "In-person Training"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": true,
                "score": 1,
                "name": "Online Training"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": false,
                "score": 0,
                "name": "Self-paced Learning"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 1,
                "name": "Workshops"
              }
            ])
          },
          {
            "name": "Describe your approach to developing training programs.",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you evaluate the effectiveness of training programs?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Feedback Surveys"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Performance Metrics"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Social Media Engagement"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Pre and Post-Training Assessments"
              }
            ])
          },
          {
            "name": "How do you handle difficult trainees?",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What strategies do you use to keep training sessions engaging?",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
      },
      {
        "departmentId": 6,
        "duration": "1 Hr 30 min",
        "industryId": 2,
        "name": "Quality Assurance Expertise Assessment",
        "passing": 7,
        "instruction": "<ul><li>This assessment evaluates your QA knowledge and experience</li><li>Detail your familiarity with testing methodologies</li><li>Explain your approach to test case writing and bug reporting</li><li>Share your experience with QA tools and processes</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in quality assurance?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
              {
                "id": "c69724fe-3bae-4587-8468-4593fe318600",
                "isCorrect": false,
                "score": 0,
                "name": "Less than 1 year"
              },
              {
                "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                "isCorrect": true,
                "score": 2,
                "name": "1-3 years"
              },
              {
                "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                "isCorrect": false,
                "score": 0,
                "name": "More than 3 years"
              }
            ])
          },
          {
            "name": "What testing methodologies are you familiar with?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": true,
                "score": 1,
                "name": "Manual Testing"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": true,
                "score": 1,
                "name": "Automated Testing"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": false,
                "score": 0,
                "name": "Performance Testing"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 1,
                "name": "Regression Testing"
              }
            ])
          },
          {
            "name": "Describe your approach to writing test cases.",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you ensure the quality of software releases?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Conduct thorough testing"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Review code changes"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Skip testing if time is short"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Gather feedback from users"
              }
            ])
          },
          {
            "name": "How do you handle bugs found during testing?",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What tools do you use for test management?",
            "score": 1,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
      },
      {
        "departmentId": 6,
        "duration": "1 Hr",
        "industryId": 2,
        "name": "IT Support Skills Evaluation",
        "passing": 11,
        "instruction": "<ul><li>This evaluation tests your IT support capabilities</li><li>Demonstrate your troubleshooting methodology</li><li>Explain your approach to user support and ticket management</li><li>Share your experience with support tools and technologies</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in IT support?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
              {
                "id": "c69724fe-3bae-4587-8468-4593fe318600",
                "isCorrect": false,
                "score": 0,
                "name": "Less than 1 year"
              },
              {
                "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                "isCorrect": true,
                "score": 2,
                "name": "1-3 years"
              },
              {
                "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                "isCorrect": false,
                "score": 0,
                "name": "More than 3 years"
              }
            ])
          },
          {
            "name": "What troubleshooting methods do you use?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": true,
                "score": 1,
                "name": "Step-by-step diagnosis"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": true,
                "score": 1,
                "name": "Remote desktop support"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": false,
                "score": 0,
                "name": "Ignoring user complaints"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 1,
                "name": "Documentation of issues"
              }
            ])
          },
          {
            "name": "Describe your approach to handling difficult users.",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you prioritize support tickets?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Based on severity"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "First come, first served"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Random selection"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Based on user role"
              }
            ])
          },
          {
            "name": "What tools do you use for ticket management?",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you stay updated with the latest technology trends?",
            "score": 1,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
      },
      {
        "departmentId": 3,
        "duration": "30 min",
        "industryId": 10,
        "name": "Financial Analysis Competency Test",
        "passing": 12,
        "instruction": "<ul><li>This test assesses your financial analysis skills</li><li>Demonstrate your knowledge of financial modeling techniques</li><li>Explain your approach to financial statement analysis</li><li>Share your experience with financial tools and metrics</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in financial analysis?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
              {
                "id": "c69724fe-3bae-4587-8468-4593fe318600",
                "isCorrect": false,
                "score": 0,
                "name": "Less than 1 year"
              },
              {
                "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                "isCorrect": true,
                "score": 2,
                "name": "1-3 years"
              },
              {
                "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                "isCorrect": false,
                "score": 0,
                "name": "More than 3 years"
              }
            ])
          },
          {
            "name": "What financial modeling techniques are you familiar with?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": true,
                "score": 1,
                "name": "Discounted Cash Flow (DCF)"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": true,
                "score": 1,
                "name": "Comparable Company Analysis"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": false,
                "score": 0,
                "name": "Market Research"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 1,
                "name": "Precedent Transactions Analysis"
              }
            ])
          },
          {
            "name": "Describe your approach to analyzing financial statements.",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you assess the financial health of a company?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Analyzing liquidity ratios"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Reviewing profitability margins"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Ignoring market trends"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Evaluating debt levels"
              }
            ])
          },
          {
            "name": "How do you handle discrepancies in financial data?",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What tools or software do you use for financial analysis?",
            "score": 1,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
      },
      {
        "departmentId": 13,
        "duration": "15 min",
        "industryId": 2,
        "name": "Product Management Skills Assessment",
        "passing": 6,
        "instruction": "<ul><li>This assessment evaluates your product management expertise</li><li>Share your experience with product development methodologies</li><li>Explain your approach to feature prioritization and roadmap planning</li><li>Describe how you handle stakeholder management</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in product management?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
              {
                "id": "c69724fe-3bae-4587-8468-4593fe318600",
                "isCorrect": false,
                "score": 0,
                "name": "Less than 1 year"
              },
              {
                "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                "isCorrect": true,
                "score": 2,
                "name": "1-3 years"
              },
              {
                "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                "isCorrect": false,
                "score": 0,
                "name": "More than 3 years"
              }
            ])
          },
          {
            "name": "What methodologies are you familiar with for product development?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": true,
                "score": 1,
                "name": "Agile"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": true,
                "score": 1,
                "name": "Scrum"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": false,
                "score": 0,
                "name": "Waterfall"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 1,
                "name": "Lean Startup"
              }
            ])
          },
          {
            "name": "Describe your approach to prioritizing product features.",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you gather and analyze customer feedback?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Surveys"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "User Interviews"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Ignoring feedback"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Analytics Tools"
              }
            ])
          },
          {
            "name": "How do you handle conflicts within a product team?",
            "score": 2,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "What tools or software do you use for product management?",
            "score": 1,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
      },
      {
        "departmentId": 2,
        "duration": "1 Hr 30 min",
        "industryId": 2,
        "name": "Cybersecurity Expertise Evaluation",
        "passing": 7,
        "instruction": "<ul><li>This evaluation tests your cybersecurity knowledge</li><li>Demonstrate your understanding of security frameworks</li><li>Explain your approach to incident response and risk assessment</li><li>Share your experience with security tools and best practices</li><li>Time limit: 30 minutes</li></ul>",
        "companyId": companyId,
        "questions": [
          {
            "name": "How much experience do you have in cybersecurity?",
            "score": 2,
            "type": "single",
            "answers": JSON.stringify([
              {
                "id": "c69724fe-3bae-4587-8468-4593fe318600",
                "isCorrect": false,
                "score": 0,
                "name": "Less than 1 year"
              },
              {
                "id": "a2194381-e314-4175-aa2d-87478ce8d701",
                "isCorrect": true,
                "score": 2,
                "name": "1-3 years"
              },
              {
                "id": "dc054227-61e5-4e9e-a9f6-d9ae890a0714",
                "isCorrect": false,
                "score": 0,
                "name": "More than 3 years"
              }
            ])
          },
          {
            "name": "What cybersecurity frameworks are you familiar with?",
            "score": 4,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "1ee29217-d63e-49b8-9f20-c5f484e3b9a3",
                "isCorrect": true,
                "score": 1,
                "name": "NIST Cybersecurity Framework"
              },
              {
                "id": "cf5c44f9-7eab-4a55-9a66-3cbc6248ee29",
                "isCorrect": true,
                "score": 1,
                "name": "ISO/IEC 27001"
              },
              {
                "id": "0dac75c2-9c83-4382-bb94-8b4ae9f8c4d7",
                "isCorrect": false,
                "score": 0,
                "name": "Agile"
              },
              {
                "id": "e5a6f365-5063-4dd2-a9f3-56ddb181c87d",
                "isCorrect": true,
                "score": 1,
                "name": "CIS Controls"
              }
            ])
          },
          {
            "name": "Describe your approach to incident response.",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you stay updated with the latest cybersecurity threats?",
            "score": 3,
            "type": "multiple",
            "answers": JSON.stringify([
              {
                "id": "abcd1234-5678-90ef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Security Blogs"
              },
              {
                "id": "efgh5678-90ab-cdef-ghij-klmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Webinars and Conferences"
              },
              {
                "id": "ijkl9012-3456-78ab-cdef-ghijklmnopqr",
                "isCorrect": false,
                "score": 0,
                "name": "Ignoring updates"
              },
              {
                "id": "mnop3456-7890-abcd-efgh-ijklmnopqrstuv",
                "isCorrect": true,
                "score": 1,
                "name": "Threat Intelligence Reports"
              }
            ])
          },
          {
            "name": "What tools or software do you use for cybersecurity monitoring?",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          },
          {
            "name": "How do you conduct a risk assessment?",
            "score": 3,
            "type": "text",
            "answers": "Answer of test question"
          }
        ]
        }
    ]