import { AuthUser } from "@microservices/auth";
import { ChangeNotificationStatusDto, CreateNotificaionDto, NotificationsService } from "@microservices/recruitment-db";
import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiOperation, ApiTags } from "@nestjs/swagger";

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationsController {
  constructor (private readonly notificationsService: NotificationsService) {}

  @ApiOperation({ summary: 'Create notifications data' })
  @Post()
  async createNotifications(@Body() dto:CreateNotificaionDto){
    return this.notificationsService.create(dto)
  }

  @ApiOperation({ summary: 'Get Notifications of an user' })
  @UseGuards(AuthGuard('jwt'))
  @Get()
  async getAllNotificationsById(@Query() query, @AuthUser() user){
    return this.notificationsService.getAllNotifications(+user["https://urecruits.com/userId"],query)
  }

  @ApiOperation({ summary: 'update Notification read status of an user' })
  @UseGuards(AuthGuard('jwt'))
  @Patch("/:id")
  async changeReadStatus(@Param('id') id,@Body() dto:ChangeNotificationStatusDto, @AuthUser() user){
    return this.notificationsService.update(id,dto,+user["https://urecruits.com/userId"])
  }

  @ApiOperation({ summary: 'delete Notification of an user' })
  @UseGuards(AuthGuard('jwt'))
  @Delete("/:id")
  async deleteNotifications(@Param('id') id,@Body() dto:ChangeNotificationStatusDto){
    return this.notificationsService.delete(id)
  }
  @ApiOperation({ summary: 'update Notification read status of an user' })
  @UseGuards(AuthGuard('jwt'))
  @Patch("read/mark-all")
  async markAllNotificationsRead(@AuthUser() user){
    return this.notificationsService.markAllNotificationsRead(+user["https://urecruits.com/userId"])
  }
}