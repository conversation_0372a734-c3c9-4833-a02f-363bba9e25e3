

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { TemporalWorkflow } from './temporal.model';
import { CreateTemporalWorkflowDto } from './dto/create-workflow.dto';

@Injectable()
export class TemporalWorkflowService {
  constructor(
    @InjectModel(TemporalWorkflow) private workflowModel: typeof TemporalWorkflow

  ) {}


  async find(id,jobId)  {
   return await this.workflowModel.findOne({
      where: {
        userid:String(id),
        jobid:String(jobId)
      },
      order:[['id','DESC']]
    },
  );
  }

  async findAllByJob(jobId){
    if(jobId){
      return await this.workflowModel.findAndCountAll({where:{jobid:`${jobId}`}})
    }
  }

  async create(userId,jobId,workflowId) {
    const data:CreateTemporalWorkflowDto={
      userid:userId,jobid:jobId,workflowid:workflowId
    }
    return this.workflowModel.create(data);
  }

  update(id: number, dto):Promise<any>  {
    return this.workflowModel.update(dto, { where: { userid:id } });
  }

  delete(id: number) {
    return this.workflowModel.destroy({ where: { id } });
  }
  deleteByCandidate(jobid,userid){
    return this.workflowModel.destroy({ where: { jobid,userid } });
  }
}
