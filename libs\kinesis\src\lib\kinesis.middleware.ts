import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { putRecord } from './utils/put-records';

@Injectable()
export class KinesisMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {

    const originalSend = res.send;

    res.send = function (body: any) {
      try {
        if (typeof body === 'string' && body.trim().startsWith('{')) {
          // Attempt to parse only if it looks like JSON
          let responseBody = JSON.parse(body);
          if (responseBody && responseBody.recordData) {
            const data = Array.isArray(responseBody.recordData)
              ? responseBody.recordData
              : [responseBody.recordData];

            // Call putRecord and log on success/failure
            putRecord({ records: data })
              .then(() => {
                console.log('Successfully put record(s) on Kinesis:', data);
              })
              .catch((err) => {
                console.error('Failed to put record(s) on Kinesis:', err);
              });

            delete responseBody.recordData;
            return originalSend.call(this, JSON.stringify(responseBody?.response));
          }
        }
        // If not JSON or no modification needed, send the body as is
        return originalSend.call(this, body);
      } catch (error) {
        console.error('Error parsing or modifying response body:', error);
        return originalSend.call(this, body);
      }
    };

    next();
  }
}
