import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { Round } from "./round.model";
import { RoundService } from "./round.service";
import { Subscribe } from "../subscribe/subscribe.model";
import { TemporalWorkflowModule } from '../temporalWorkflow/temporal.module';
import { UserAssignmentsModule } from '../user-assignments/user-assignments.module';
import { HttpModule } from '@nestjs/axios';
import { JobTargetModule } from '../jobtarget/jobtarget.module';

@Module({
  imports: [SequelizeModule.forFeature([Round, Subscribe]),TemporalWorkflowModule,UserAssignmentsModule,HttpModule,JobTargetModule],
  providers: [RoundService],
  exports: [SequelizeModule,RoundService],
})
export class RoundModule {}
