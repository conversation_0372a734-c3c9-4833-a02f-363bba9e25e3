import { ApiPropertyOptional } from "@nestjs/swagger";

export class WorkflowDto {
  @ApiPropertyOptional({ example: "", description: "workflow title" })
  title?: string

  @ApiPropertyOptional({ example: "1", description: "Domain Assessment ID" })
  domainId?: number

  @ApiPropertyOptional({ example: [], description: "Domain Reviewer" })
  domainReviewer?: any

  @ApiPropertyOptional({ example: '5', description: 'Domain Deadline' })
  domainDeadline?: number

  @ApiPropertyOptional({ example: "", description: "Question Type" })
  questionType?: string

  @ApiPropertyOptional({ example: "1", description: "live Coding Assessment ID" })
  liveCodingId?: number

  @ApiPropertyOptional({ example: "1", description: "take home task Assessment ID" })
  takeHomeTaskId?: number

  @ApiPropertyOptional({ example: "1", description: " live Coding Time" })
  liveCodingTime?: number

  @ApiPropertyOptional({ example: '10', description: 'take home task Deadline' })
  takeHomeTaskDeadline?: number

  @ApiPropertyOptional({ example: "1", description: "take home task  Time" })
  takeHomeTaskTime?: number

  @ApiPropertyOptional({ example: [], description: "Workflow" })
  workflow?: any;

  @ApiPropertyOptional({ example: [], description: "Available Days" })
  availableDays?: string;

  @ApiPropertyOptional({ example: [], description: "Available Time" })
  availableTime?: string;
}
