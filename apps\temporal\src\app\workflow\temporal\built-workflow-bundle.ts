const path = require("path");
const fs = require("fs");
const { bundleWorkflowCode } = require("@temporalio/worker");

async function bundle() {
  const code = await bundleWorkflowCode({
    workflowsPath: path.resolve(
      "apps/temporal/src/app/workflow/temporal/workflows.ts"
    ),
  });

  const encodedContent = Buffer.from(code.code, 'utf-8').toString('base64');

  const tsCodeString = `export const encodedCodeString: string = '${encodedContent}';`;

  fs.writeFileSync(
    path.resolve("apps/temporal/src/app/workflow/temporal/workflowCode.ts"),
    tsCodeString,
    "utf-8"
  );
}

bundle().catch((err) => {
  console.error(err);
  process.exit(1);
});