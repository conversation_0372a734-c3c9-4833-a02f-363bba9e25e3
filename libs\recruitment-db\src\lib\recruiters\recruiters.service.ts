import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";
import { Recruiter } from "./recruiters.model";
import { ProfileRecruiterDto } from "./dto/profile-recruiter.dto";
import { AddRecruiterDto } from "./dto/add-recruiter.dto";
import { UsersService } from "../users/users.service";
import { RolesService } from "../roles/roles.service";
import { Auth0Service } from "@microservices/auth";
import { EducationsService } from "../educations/educations.service";
import { ExperiencesService } from "../experiences/experiences.service";
import { User } from "../users/users.model";
import { UpdateProfileRecruiterDto } from "./dto/update-profile-recruiter.dto";
import { EditRecruiterDto } from "./dto/edit-recruiter.dto";
import { Role } from "../roles/roles.model";
import { Op } from "sequelize";
import { GetMembersDto } from "./dto/getMembers.dto";
import { Location } from "../locations/location.model";
import { RecruiterPositions } from "../recruiter-positions/recruiter-positions.model";
import { v4 as uuidv4 } from 'uuid';
import { EmailService } from "@microservices/email";
import { SendMailDto } from "./dto/send-mail.dto";
import { VerificationDto } from "./dto/verification.dto";
import * as bcrypt from "bcrypt";
import { SubscriptionService } from "../subscription/subscription.service";
import { Company } from "../companies/companies.model";

@Injectable()
export class RecruitersService {
  constructor(
    @InjectModel(Recruiter) private recruiterRepository: typeof Recruiter,
    private usersService: UsersService,
    private rolesService: RolesService,
    private auth0Service: Auth0Service,
    private educationsService: EducationsService,
    private experiencesService: ExperiencesService,
    private emailService: EmailService,
  ) { }

  async createRecruiterProfile(dto: ProfileRecruiterDto, inviteLink = null) {
    return await this.recruiterRepository.create({ ...dto, inviteLink });
  }

  async getAllRecruiters(dto: { limit: number, offset: number }) {
    return await this.recruiterRepository.findAndCountAll({
      include: [
        {
          model: User, attributes: { exclude: ["password"] }, include: [
            { model: Role, attributes: ["label", "value", "id"] },
          ]
        },
        { model: Company, attributes: ["name", "tenantId", "id", "avatar", "website", "phone", "email", "address", "description", "createdAt", "updatedAt"] },
        { model: RecruiterPositions, attributes: ["label", "value", "id"] },
        { model: Location, attributes: ["city", "state", "st", "id"] },
      ],
      limit: dto.limit,
      offset: dto.offset,
      order: [["id", "asc"]],
    });
  }

  async getRecruiterById(id: number, companyId = null) {
    const where: any = {}
    where.id = id
    if (companyId) {
      where.companyId = companyId
    }
    return await this.recruiterRepository.findOne({
      where,
      include: [
        {
          model: User,
          attributes: { exclude: ["password"] },
          include: [
            {
              model: Role,
              attributes: ["label", "value", "id"],
            },
          ],
        },
        {
          all: true,
        },
      ],
      order: [
        ["educationDetails", "id", "ASC"],
        ["experienceDetails", "id", "ASC"],
      ],
    });
  }

  async addDataOnOpenSearch(recruiterId: number, companyId: number, actionType: 'CREATION' | 'UPDATE' | 'DELETE') {
    const recruiter = await this.getRecruiterById(recruiterId, companyId);
    if (!recruiter) {
      throw new Error(`Recruiter with ID ${recruiterId} and companyId ${companyId} not found`);
    }
    const recordData = await this.getRecruiterRecordData(recruiter, actionType)
    return { response: recruiter, recordData }
  }

  async updateRecruiterProfile(dto: UpdateProfileRecruiterDto, sendDataToQdrant = true) {
    const recruiter = await this.recruiterRepository.findOne({
      where: { id: dto.id },
    });
    if (!recruiter) {
      throw new HttpException("Recruiter not found", HttpStatus.NOT_FOUND);
    }
    if (dto.birthday) {
      dto.birthday = new Date(dto.birthday);
    }
    if (dto.dateJoining) {
      dto.dateJoining = new Date(dto.dateJoining);
    }
    Object.assign(recruiter, { ...dto, companyId: recruiter.companyId, userId: recruiter.userId });
    await recruiter.save();
    const companyMember = await this.getRecruiterById(recruiter.id, recruiter.companyId);
    if (sendDataToQdrant) {
      const recordData = await this.getUserDataForQdrant(companyMember)
      return { response: companyMember, recordData }
    } else {
      return companyMember
    }
  }

  async editMember(dto: EditRecruiterDto) {
    const user = await this.usersService.getCompanyUser(dto.userId, dto.companyId);
    await this.usersService.updateUser({ ...dto, id: user.id, authId: user.authId });
    if (dto.dateJoining) {
      dto.dateJoining = new Date(dto.dateJoining)
    }
    if (dto.birthday) {
      dto.birthday = new Date(dto.birthday)
    }
    const recruiter = await this.updateRecruiterProfile({ ...dto, id: dto.recruiterId, userId: user.id }, false);
    await this.usersService.changeCompanyMemberRoles({ ...dto, authId: user.authId }, dto.companyId);
    const companyMember = await this.getRecruiterById(dto.recruiterId, dto.companyId);
    const recordData = await this.getUserDataForQdrant(companyMember)
    // return { response: companyMember, recordData }
    return await this.addDataOnOpenSearch(dto.recruiterId, dto.companyId, 'UPDATE');
  }

  async addRecruiter(dto: AddRecruiterDto) {
    await this.usersService.isExistUser(dto.email);
    const password = "123456qQ@";
    const user = await this.usersService.createUser({ ...dto, password: password });
    if (dto.dateJoining) {
      dto.dateJoining = new Date(dto.dateJoining);
    } else {
      dto.dateJoining = null;
    }
    if (dto.birthday) {
      dto.birthday = new Date(dto.birthday);
    } else {
      dto.birthday = null;
    }
    if (!dto.positionId) {
      dto.positionId = 1
    }
    if (!dto.locationId) {
      dto.locationId = 1
    }
    const activateEmail = uuidv4();
    const recruiter = await this.createRecruiterProfile({
      ...dto,
      userId: user.id,
      officeEmail: dto.email,
      officePhone: dto.phone,
      dateJoining: dto.dateJoining,
      birthday: dto.birthday,
    }, activateEmail);

    const auth0User = await this.auth0Service.createCompanyMember(
      { ...dto, companyId: dto.companyId, recruiterId: recruiter.id, userId: user.id, password: password, tenantId: dto.tenantId });
    const addRolesAuth0 = [];
    const addRoles = [];
    for (const item of dto.roles) {
      addRolesAuth0.push(item.value);
      addRoles.push(item.id);
    }
    await user.$add("roles", addRoles);
    await this.auth0Service.assignRoles(auth0User.user_id, addRolesAuth0);
    await this.educationsService.create({ recruiterId: recruiter.id });
    await this.experiencesService.create({ recruiterId: recruiter.id });
    Object.assign(user, { authId: auth0User.user_id });
    await user.save();
    await this.emailService.sendRecruitment('activateAccount.html', { link: `https://urecruits.com/email-verification?ticket=${activateEmail}`, userName: user.firstname, userId: user?.id }, user.email);
    const companyMember = await this.getRecruiterById(recruiter.id, recruiter.companyId);
    const recordData = await this.getUserDataForQdrant(companyMember)
    return await this.addDataOnOpenSearch(recruiter.id, recruiter.companyId, 'CREATION');
  }

  async getMembers(dto: GetMembersDto, companyId: number, userId: number) {
    const where: any = {
      userId: {
        [Op.ne]: userId
      }
    };
    const whereRoles: any = {};
    const whereLocation: any = {};
    const whereUser: any = {};
    const wherePosition: any = {};
    const order = [];
    where.companyId = companyId;
    if (dto.position) {
      if (dto.position instanceof Array) {
        wherePosition.id = {
          [Op.in]: dto.position,
        };
      } else {
        wherePosition.id = {
          [Op.eq]: dto.position,
        };
      }
    }
    if (dto.department) {
      if (dto.department instanceof Array) {
        where.department = {
          [Op.iLike]: { [Op.any]: dto.department },
        };
      } else {
        where.department = {
          [Op.iLike]: `%${dto.department}%`,
        };
      }
    }
    if (dto.degree) {
      if (dto.degree instanceof Array) {
        where.degree = {
          [Op.iLike]: { [Op.any]: dto.degree },
        };
      } else {
        where.degree = {
          [Op.iLike]: `%${dto.degree}%`,
        };
      }
    }

    if (dto.dateJoiningFrom && dto.dateJoiningTo) {
      where.dateJoining = {
        [Op.between]: [new Date(dto.dateJoiningFrom), new Date(dto.dateJoiningTo)],
      }
    } else if (dto.dateJoiningFrom || dto.dateJoiningTo) {
      if (dto.dateJoiningFrom) {
        where.dateJoining = {
          [Op.gte]: new Date(dto.dateJoiningFrom),
        };
      }
      if (dto.dateJoiningTo) {
        where.dateJoining = {
          [Op.lte]: new Date(dto.dateJoiningTo),
        };
      }
    }

    if (dto.birthdayFrom && dto.birthdayTo) {
      where.birthday = {
        [Op.between]: [new Date(dto.birthdayFrom), new Date(dto.birthdayTo)],
      }
    } else if (dto.birthdayFrom || dto.birthdayTo) {
      if (dto.birthdayFrom) {
        where.birthday = {
          [Op.gte]: new Date(dto.birthdayFrom),
        };
      }
      if (dto.birthdayTo) {
        where.birthday = {
          [Op.lte]: new Date(dto.birthdayTo),
        };
      }
    }

    if (dto.roles) {
      if (dto.roles instanceof Array) {
        whereRoles.id = {
          [Op.in]: dto.roles,
        };
      } else {
        whereRoles.id = {
          [Op.eq]: dto.roles,
        };
      }
    }
    if (dto.locationId) {
      if (dto.locationId instanceof Array) {
        whereLocation.id = {
          [Op.in]: dto.locationId,
        };
      } else {
        whereLocation.id = {
          [Op.eq]: dto.locationId,
        };
      }
    }
    if (dto.search) {
      whereUser[Op.or] = [
        {
          firstname: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
        {
          lastname: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
        {
          middlename: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
        {
          email: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
        {
          phone: {
            [Op.iLike]: `%${dto.search}%`,
          },
        },
      ];
    }
    if (dto.sortBy && dto.sortType) {
      order.push([dto.sortBy, dto.sortType])
    } else {
      order.push(["id", "DESC"]);
    }

    return await this.recruiterRepository.findAndCountAll(
      {
        distinct: true,
        attributes: ["id", "officeEmail", "officePhone", "department", "degree", "dateJoining", "birthday", "profileStatus", "currentCity", "positionId", "locationId"],
        where,
        include: [
          {
            model: Location,
            where: whereLocation,
            required: true,
            attributes: ["city", "state", "id"],
          },
          {
            model: RecruiterPositions,
            required: true,
            where: wherePosition,
            attributes: ["value", "label", "id"],
          },
          {
            model: User,
            required: true,
            attributes: ["id", "firstname", "lastname", "middlename", "avatar"],
            where: whereUser,
            include: [
              {
                model: Role,
                attributes: ["value", "label", "id"],
                where: whereRoles,
              },
            ],
          },
        ],
        limit: dto.limit,
        offset: dto.offset,
        order,
      }
    );
  }

  async getNewMembersCountOfWeek(companyId) {
    const where: any = {};
    where.companyId = companyId;
    const datePairs = getLast7DaysDatePairs();
    const result = [];
    for (const datePair of datePairs) {
      const count = await this.recruiterRepository.count({
        where: {
          ...where,
          dateJoining: {
            [Op.between]: [datePair.dateTo, datePair.dateFrom],
          },
        },
      });
      result.push({ date: datePair.dateFrom, count });
    }
    return result;
  }


  async getMembersByCompanyIds(dto) {
    let where: any = {}
    if (dto.companyId) {
      if (Array.isArray(dto.companyId)) {
        const companies = dto.companyId.map(i => +i)
        where.companyId = {
          [Op.in]: companies
        }
      } else {
        where.companyId = dto.companyId
      }
    }
    return await this.recruiterRepository.findAndCountAll(
      {
        distinct: true,
        attributes: ["id", "companyId"],
        where,
        include: [
          {
            model: User,
            required: true,
            attributes: ["id", "firstname", "lastname", "middlename", "avatar"],
          },
          {
            model: Company,
            attributes: ["id", "name", "tenantId"]
          }
        ],
        limit: dto.limit,
        offset: dto.offset,
        order: [["id", "DESC"]],
      }
    );
  }

  async resendActivateEmail(dto: SendMailDto, companyId: number) {
    const recruiter = await this.recruiterRepository.findOne({
      where: {
        id: dto.recruiterId,
        companyId,
        profileStatus: {
          [Op.notILike]: '%Active%',
        }
      },
      attributes: ["id", "inviteLink"],
      include: [
        {
          model: User,
          attributes: ["email", "firstname"],
        }
      ]
    });
    if (!recruiter) {
      throw new HttpException("Recruiter not found", HttpStatus.NOT_FOUND);
    }
    return await this.emailService.sendRecruitment('activateAccount.html', { link: `https://urecruits.com/email-verification?ticket=${recruiter.inviteLink}`, userName: recruiter.user?.firstname, userId: dto.recruiterId }, recruiter.user?.email);
  }

  async verification(dto: VerificationDto) {
    const recruiter = await this.recruiterRepository.findOne({
      where: {
        profileStatus: {
          [Op.notILike]: "%Active%",
        },
        inviteLink: dto.inviteLink,
      },
      attributes: ["profileStatus", "inviteLink", "id"],
      include: [
        {
          model: User,
          attributes: ["authId"],
        },
      ],
    });
    recruiter.profileStatus = "Active";
    if (!recruiter) {
      throw new HttpException("Recruiter not found", HttpStatus.NOT_FOUND);
    }
    const user = await this.usersService.getUserByAuthId(recruiter.user.authId);
    user.password = await bcrypt.hash(dto.newPassword, 10);
    await this.auth0Service.changePassword(dto.newPassword, user.authId);
    await user.save();
    await recruiter.save();
    const companyMember = await this.getRecruiterById(recruiter.id, recruiter.companyId);
    const recordData = await this.getUserDataForQdrant(companyMember)
    return { response: recruiter, recordData }
  }

  async checkVerification(inviteLink: string) {
    const recruiter = await this.recruiterRepository.findOne({
      where: {
        profileStatus: {
          [Op.notILike]: "%Active%",
        },
        inviteLink,
      },
      attributes: ["profileStatus", "inviteLink", "id"],
    });
    if (!recruiter) {
      throw new HttpException("Not found", HttpStatus.NOT_FOUND);
    }
    return recruiter;
  }

  async getRecruiterRecordData(companyMember: Recruiter, actionType: 'CREATION' | 'UPDATE' | 'DELETE') {
    const recordData = {
      for: 'user',
      action: actionType,
      data: {
        userId: companyMember?.userId,
        user_type: 'recruiter',
        recruiterId: companyMember?.id,
        companyId: companyMember?.companyId,
        email: companyMember?.user.email,
        phone: companyMember?.user.phone,
        dateJoining: companyMember?.dateJoining,
        birthDate: companyMember.birthday,
        jobLocation: { id: companyMember.locationId, city: companyMember?.location?.city, state: companyMember?.location?.state, st: companyMember?.location?.st },
        position: { id: companyMember?.positionId, name: companyMember?.position?.label },
        name: {
          firstname: companyMember?.user?.firstname,
          middleName: companyMember?.user?.middlename,
          lastName: companyMember?.user?.lastname
        },
        avatar: companyMember?.user?.avatar,
        department: companyMember.department,
        role: companyMember?.user?.roles?.map(i => ({ id: i.id, name: i.label })),
        gender: companyMember.gender,
        degree: companyMember?.degree,
        personalEmail: companyMember?.personalEmail,
        profileStatus: companyMember?.profileStatus,
        createdAt: Date.now(),
        company: companyMember?.company?.dataValues,
      }
    }
    return recordData;
  }

  getUserDataForQdrant(recruiter: Recruiter, action = 'create') {
    const vectorStr = action === 'create' ? [
      recruiter?.id ? `RecruiterId:${recruiter.id}` : '',
      recruiter?.user?.firstname ? `FirstName:${recruiter.user.firstname}` : '',
      recruiter?.user?.lastname ? `LastName:${recruiter.user.lastname}` : '',
      recruiter?.user?.middlename ? `MiddleName:${recruiter.user.middlename}` : '',
      recruiter?.user?.avatar ? `ProfilePicture:${recruiter.user.avatar}` : '',
      recruiter?.user?.email ? `Email:${recruiter.user.email}` : '',
      recruiter?.user?.phone ? `Phone:${recruiter.user.phone}` : '',
      recruiter?.user?.roles?.length ? `Roles:${recruiter.user.roles.map(role => role.label.split('_')[0]).join(', ')}` : '',
      `- Is a Recruiter and team member of ${recruiter.company ? `${recruiter.company.name}` : ''}`,
      recruiter?.positionId ? `- has position ${recruiter.position?.label}` : '',
      recruiter?.locationId ? `Loaction: ${recruiter.location?.city}, ${recruiter.location?.state}, ${recruiter.location?.st}` : '',
      recruiter?.department ? `In Department: ${recruiter.department}` : '',
      recruiter?.degree ? `Has Degree: ${recruiter.degree}` : '',
      recruiter?.birthday ? `Birthday: ${recruiter.birthday}` : '',
      recruiter?.dateJoining ? `Joined on: ${recruiter.dateJoining}` : '',
      recruiter?.personalEmail ? `Personal Email: ${recruiter.personalEmail}` : '',
      recruiter?.profileStatus ? `Profile Status: ${recruiter.profileStatus}` : '',
      recruiter?.gender ? `Gender: ${recruiter.gender}` : '',
      recruiter?.officeEmail ? `Office Email: ${recruiter.officeEmail}` : '',
      recruiter?.officePhone ? `Office Phone: ${recruiter.officePhone}` : '',
    ].filter(Boolean).join(', ') : '';

    return {
      for: 'users',
      id: recruiter.user.id,
      action,
      ...(action === 'create' && {
        str: vectorStr,
        payload: {
          id: recruiter.user.id,
          user_type: 'recruiter',
          ...(recruiter.reportingId && { reportingId: recruiter.reportingId }),
          ... (recruiter.user.firstname && { firstname: recruiter.user.firstname }),
          ... (recruiter.user.lastname && { lastname: recruiter.user.lastname }),
          ... (recruiter.user.middlename && { middlename: recruiter.user.middlename }),
          ... (recruiter.user.avatar && { avatar: recruiter.user.avatar }),
          ... (recruiter.user.email && { email: recruiter.user.email }),
          ... (recruiter.user.phone && { phone: recruiter.user.phone }),
          ... (recruiter.user.roles && { role: recruiter.user.roles }),
          ... (recruiter.companyId && { companyId: recruiter.companyId }),
          ... (recruiter.company && { company: recruiter.company?.dataValues }),
          ... (recruiter.position && { position: recruiter.position?.dataValues }),
          ... (recruiter.location && { location: recruiter.location?.dataValues }),
          ... (recruiter.department && { department: recruiter.department }),
          ... (recruiter.degree && { degree: recruiter.degree }),
          ... (recruiter.birthday && { birthday: recruiter.birthday }),
          ... (recruiter.dateJoining && { dateJoining: recruiter.dateJoining }),
          ... (recruiter.personalEmail && { personalEmail: recruiter.personalEmail }),
          ... (recruiter.profileStatus && { profileStatus: recruiter.profileStatus }),
          ... (recruiter.gender && { gender: recruiter.gender }),
          ... (recruiter.officeEmail && { officeEmail: recruiter.officeEmail }),
          ... (recruiter.officePhone && { officePhone: recruiter.officePhone }),
          ... (recruiter.user.createdAt && { createdAt: recruiter.user.createdAt }),
        }
      })
    }
  }
}

const getLast7DaysDatePairs = () => {
  const today = new Date();
  const datePairs = [];

  for (let i = 0; i < 7; i++) {
    const dateFrom = new Date(today);
    dateFrom.setDate(today.getDate() - i);
    const dateTo = new Date(today);
    dateTo.setDate(today.getDate() - i - 1);
    datePairs.push({ dateFrom, dateTo });
  }

  return datePairs;
};