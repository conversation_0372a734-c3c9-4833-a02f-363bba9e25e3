{"root": "libs/editor-manager", "sourceRoot": "libs/editor-manager/src", "projectType": "library", "targets": {"lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/editor-manager/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/libs/editor-manager"], "options": {"jestConfig": "libs/editor-manager/jest.config.js", "passWithNoTests": true}}}, "tags": []}