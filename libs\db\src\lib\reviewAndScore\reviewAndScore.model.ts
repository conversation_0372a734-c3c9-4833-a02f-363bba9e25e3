import { Column, DataType, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { AssessmentCategory } from '../domain-questions/domain-assessment.model';

@Table({
  tableName: 'review-score',
})
export class ReviewAndScore extends Model<ReviewAndScore> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
    id: number;

  @ApiProperty({
    description: 'Review of the assessment',
  })
  @Column({ type: DataType.TEXT, allowNull: true })
    review: string;

  @ApiProperty({
    example: '1',
    description: 'Id of candidate',
  })
  @Column({ type: DataType.INTEGER, allowNull: false })
    userId: number;

  @ApiProperty({
    example: '23:56',
    description: 'Duration of assessment',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    duration: string;

  @ApiProperty({
    example: 'Dec 29, 2020 12:00 PM',
    description: 'Date of completion',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    completedOn: string;

  @ApiProperty({
    example: 'ACTIVE',
    description: 'Status of user flow',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    userFlowStatus: string;

  @ApiProperty({
    example: 'ACTIVE',
    description: 'Status of assessment',
  })
  @Column({ type: DataType.STRING, allowNull: false })
    assessmentStatus: string;

  @ApiProperty({
    example: '76',
    description: 'Score of assessment',
  })
  @Column({ type: DataType.STRING, allowNull: true })
    score: string;

  @ApiProperty({
    example: '1',
    description: 'Id of reviewed assessment',
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
    assessmentId: number;

  @ApiProperty({
    enum: AssessmentCategory,
    oneOf: [
      { type: AssessmentCategory.domainAssessment },
      { type: AssessmentCategory.codingAssessment },
      { type: AssessmentCategory.thirdPartyAssessment },
    ],
  })
  @Column({
    type: DataType.ENUM(
      AssessmentCategory.domainAssessment,
      AssessmentCategory.codingAssessment,
      AssessmentCategory.thirdPartyAssessment
    ),
    allowNull: false,
  })
    assessmentType: AssessmentCategory;
}
