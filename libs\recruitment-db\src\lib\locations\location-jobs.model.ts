import {
  BelongsTo,
  Column,
  DataType,
  Foreign<PERSON>ey,
  Model,
  Table,
} from "sequelize-typescript";
import { Jobs } from "../job/job.model";
import { Location } from "./location.model";

@Table({
  tableName: "location_jobs",
  createdAt: false,
  updatedAt: false,
})
export class LocationJobs extends Model<LocationJobs> {
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => Jobs)
  @Column({ type: DataType.INTEGER })
  jobId: number;

  @ForeignKey(() => Location)
  @Column({ type: DataType.INTEGER })
  locationId: number;

  @BelongsTo(() => Jobs)
  job: Jobs;

  @BelongsTo(() => Location)
  location: Location;
}
