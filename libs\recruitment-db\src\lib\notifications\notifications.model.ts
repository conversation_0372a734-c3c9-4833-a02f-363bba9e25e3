import { ApiProperty } from "@nestjs/swagger";
import {
  BelongsToMany,
  Column,
  DataType,
  Table,
  Model,
  BelongsTo,
  ForeignKey,
} from "sequelize-typescript";
import { User } from "../users/users.model";

@Table({
  tableName: "notifications",
  createdAt: true,
  updatedAt: true,
  timestamps: true,
})
export class NotificationsModel extends Model<NotificationsModel> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  userId: number;

  @Column({ type: DataType.STRING, allowNull: true })
  title: string;

  @Column({ type: DataType.STRING, allowNull: true })
  description: string;

  @Column({ type: DataType.STRING, allowNull: true })
  image: string;

  @Column({ type: DataType.BOOLEAN, allowNull: false })
  read: boolean;

  @Column({ type: DataType.STRING, allowNull: true })
  link: string;
  
  @BelongsTo(() => User)
  user: User;
}
