import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";

export class ChangeStatusDto {
  @ApiProperty({ example: 87, description: "job ID" })
  @IsNotEmpty()
  jobId: number;

  @ApiProperty({ example: 87, description: "user ID" })
  @IsNotEmpty()
  userId: number;

  @ApiProperty({ example: 87, description: "subscribe ID" })
  subscribeId: number;

  @ApiPropertyOptional({
    example: "Functional/Domain Assessment",
    description: "Rounds",
  })
  @IsOptional()
  roundName?: any;

  @ApiPropertyOptional({ example: "In Progress", description: "Rounds" })
  @IsOptional()
  status?: any;

  @ApiPropertyOptional({
    example: "2023-12-21 19:09:31.406 +0530",
    description: "Rounds",
  })
  @IsOptional()
  startDate?: any;
}
