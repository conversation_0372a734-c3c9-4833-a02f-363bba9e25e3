import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  UseGuards,
  Body,
  Req,
  Query,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { CompanySubscriptionDto, SubscriptionIdDto, Subscriptions, CreateSubscriptionDto, PaymentMethodIdDto, PaymentMethodDto, PaymentService } from '@microservices/recruitment-db';

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   candidateId: user["https://urecruits.com/candidateId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

const apiSecret = 'whsec_a3f601eb9309134f5fc378f8b78fcfd4395d26f36a136114a030ba4bb09d886b'
@ApiTags('Payment')
@Controller('payment')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  @ApiOperation({
    summary: 'Get company payment methods.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns an array of valid payment methods.',
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-payment-methods')
  @Permissions('payment-integration:view')
  getPaymentMethods(@AuthUser() user: any) {
    return this.paymentService.getPaymentMethods(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({
    summary:
      'Create a SetupIntent object. If Stripe Customer is not exists or is not valid for current company - create new one.',
  })
  @ApiResponse({
    status: 201,
    description: 'Returns a setupIntents.client_secret.',
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/create-setup-intent')
  @Permissions('payment-integration:edit')
  createSetupIntent(@AuthUser() user: any) {
    return this.paymentService.createSetupIntent(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({
    summary: 'Set default payment method for Stripe Customer.',
  })
  @ApiResponse({
    status: 200,
    description: 'Default payment method successfully set.',
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Put('/set-default-payment-method')
  @Permissions('payment-integration:edit')
  setDefaultPaymentMethod(
    @Body() dto: PaymentMethodIdDto,
    @AuthUser() user: any,
  ) {
    return this.paymentService.setDefaultPaymentMethod(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({
    summary: 'Edit payment method metadata.',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment method updated successfully.',
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Put('/edit-payment-method')
  @Permissions('payment-integration:edit')
  editPaymentMethod(@Body() dto: PaymentMethodDto, @AuthUser() user: any) {
    return this.paymentService.editPaymentMethod(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({
    summary: 'Delete payment method.',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment method deleted successfully.',
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete('/delete-payment-method')
  @Permissions('payment-integration:edit')
  deletePaymentMethod(@Body() dto: PaymentMethodIdDto, @AuthUser() user: any) {
    return this.paymentService.deletePaymentMethod(dto, user["https://urecruits.com/companyId"]);
  }

  // @ApiOperation({
  //   summary: 'Get subscription plans.',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Returns list of subscription plans.',
  // })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  // @Get('/get-plans')
  // @Permissions('subscription:view')
  // getPlans() {
  //   return this.paymentService.getPlans();
  // }

  // @ApiOperation({
  //   summary: 'Create subscription.',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Returns Subscription object.',
  //   type: Subscriptions,
  // })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  // @Post('/create-subscription')
  // @Permissions('subscription:edit')
  // createSubscription(
  //   @Body() dto: CreateSubscriptionDto,
  //   @AuthUser() user: any,
  // ) {
  //   return this.paymentService.createSubscription(dto, user["https://urecruits.com/companyId"]);
  // }

  // @ApiOperation({
  //   summary: 'Delete subscription.',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Returns true|false.',
  // })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  // @Delete('/delete-subscription')
  // @Permissions('subscription:edit')
  // deleteSubscription(@Body() dto: SubscriptionIdDto, @AuthUser() user: any) {
  //   return this.paymentService.deleteSubscription(dto.subId, true, user["https://urecruits.com/companyId"]);
  // }

  // @ApiOperation({
  //   summary: 'Get company subscription info.',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Returns company subscription object.',
  //   type: CompanySubscriptionDto,
  // })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  // @Get('/get-company-subscription')
  // @Permissions('subscription:view')
  // getCompanySubscription(@AuthUser() user: any) {
  //   return this.paymentService.getCompanySubscription(user["https://urecruits.com/companyId"]);
  // }

@ApiOperation({
    summary:
      'Get company upcoming invoioce details regarding subscription',
  })
  @ApiResponse({
    status: 200,
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/company/get-upcoming-invoice')
  getCompanyPaymentsDetails(@AuthUser() user: any) {
    return this.paymentService.getupcomingCompanyInvoice(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({
    summary:
      'Get company payments deatails regarding subscription',
  })
  @ApiResponse({
    status: 200,
  })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/company/get-invoices')
  @ApiBearerAuth('access-token')
  getCompanyInvoices(@Query() query:any,@AuthUser() user: any) {
    return this.paymentService.getCompanyInvoices(user["https://urecruits.com/companyId"],query);
  }

  @ApiOperation({ summary: 'Stripe Webhook' })
  @Post('/stripe/events')
  stripeStatus(@Body() body: any, @Req() req: any) {
    return this.paymentService.handleStripeEvents(body)
  }
  
}
