{"root": "apps/pre-recruitment-api", "sourceRoot": "apps/pre-recruitment-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/pre-recruitment-api", "main": "apps/pre-recruitment-api/src/main.ts", "tsConfig": "apps/pre-recruitment-api/tsconfig.app.json", "assets": ["apps/pre-recruitment-api/src/assets"], "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/pre-recruitment-api/src/environments/environment.ts", "with": "apps/pre-recruitment-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "pre-recruitment-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/pre-recruitment-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/pre-recruitment-api"], "options": {"jestConfig": "apps/pre-recruitment-api/jest.config.js", "passWithNoTests": true}}}, "tags": []}