import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class ProfileRecruiterDto {
  @ApiPropertyOptional({ example: "1", description: "Position ID" })
  @IsOptional()
  positionId?: number;

  @ApiPropertyOptional({ example: "<EMAIL>", description: "Office Email" })
  @IsOptional()
  officeEmail?: string;

  @ApiPropertyOptional({ example: "test", description: "Employee id" })
  @IsOptional()
  employeeId?: string;

  @ApiPropertyOptional({ example: "5455654645", description: "Office Phone Number" })
  @IsOptional()
  officePhone?: string;

  @ApiPropertyOptional({ example: "5455654645", description: "Office Phone Number Extn" })
  @IsOptional()
  officePhoneExtn?: string;

  @ApiPropertyOptional({ example: "Sales Department", description: "Department" })
  @IsOptional()
  department?: string;

  @ApiPropertyOptional({ example: "1", description: "Location id" })
  @IsOptional()
  locationId?: number;

  @ApiProperty({ example: "1", description: "Company ID" })
  companyId: number;

  @ApiPropertyOptional({ example: "1", description: "User ID" })
  @IsOptional()
  userId?: number;

  @ApiPropertyOptional({ example: "1", description: "Recruiter ID" })
  @IsOptional()
  reportingId?: number;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Birthday data" })
  @IsOptional()
  birthday?: any;

  @ApiPropertyOptional({ example: "01.01.1990", description: "Date of joining" })
  @IsOptional()
  dateJoining?: any;

  @ApiPropertyOptional({ example: "Male", description: "Gender" })
  @IsOptional()
  gender?: string;

  @ApiPropertyOptional({ example: "master", description: "Education degree" })
  @IsOptional()
  degree?: string;

  @ApiPropertyOptional({ example: "Single", description: "Marital Status" })
  @IsOptional()
  maritalStatus?: string;

  @ApiPropertyOptional({ example: "Rob Anderson", description: "Emergency Contact Person" })
  @IsOptional()
  emergencyPerson?: string;

  @ApiPropertyOptional({ example: "Positive", description: "Relation with Employee" })
  @IsOptional()
  relationEmployee?: string;

  @ApiPropertyOptional({ example: "213214124", description: "Emergency Contact Mobile" })
  @IsOptional()
  emergencyMobile?: string;

  @ApiPropertyOptional({ example: "<EMAIL>", description: "Personal Email" })
  @IsOptional()
  personalEmail?: string;

  @ApiPropertyOptional({ example: "[{\"value\":\"English\",\"label\":\"English\"}]", description: "Languages" })
  @IsOptional()
  languages?: string;

  @ApiPropertyOptional({ example: "https://www.linkedin.com/", description: "Linkedin" })
  @IsOptional()
  linkedin?: string;

  @ApiPropertyOptional({ example: "https://twitter.com/", description: "Twitter" })
  @IsOptional()
  twitter?: string;

  @ApiPropertyOptional({ example: "https://www.facebook.com/", description: "Facebook" })
  @IsOptional()
  facebook?: string;

  @ApiPropertyOptional({ example: "https://www.instagram.com/", description: "Instagram" })
  @IsOptional()
  instagram?: string;

  @ApiPropertyOptional({ example: "New Street", description: "Current Address: Street" })
  @IsOptional()
  currentStreet?: string;

  @ApiPropertyOptional({ example: "95", description: "Current Address: House name or number" })
  @IsOptional()
  currentHouseNumber?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: City" })
  @IsOptional()
  currentCity?: string;

  @ApiPropertyOptional({ example: "USA", description: "Current Address: Country" })
  @IsOptional()
  currentCountry?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: State" })
  @IsOptional()
  currentState?: string;

  @ApiPropertyOptional({ example: "10030", description: "Current Address: Zip/Postcode" })
  @IsOptional()
  currentZip?: string;

  @ApiPropertyOptional({ example: "New Street", description: "Current Address: Street" })
  @IsOptional()
  permanentStreet?: string;

  @ApiPropertyOptional({ example: "95", description: "Current Address: House name or number" })
  @IsOptional()
  permanentHouseNumber?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: City" })
  @IsOptional()
  permanentCity?: string;

  @ApiPropertyOptional({ example: "USA", description: "Current Address: Country" })
  @IsOptional()
  permanentCountry?: string;

  @ApiPropertyOptional({ example: "New York", description: "Current Address: State" })
  @IsOptional()
  permanentState?: string;

  @ApiPropertyOptional({ example: "10030", description: "Current Address: Zip/Postcode" })
  @IsOptional()
  permanentZip?: string;

  @ApiPropertyOptional({ example: "2131232", description: "Passport №" })
  @IsOptional()
  passport?: string;

  @ApiPropertyOptional({ example: "U-1", description: "Visa Type" })
  @IsOptional()
  visaType?: string;

  @ApiPropertyOptional({ example: "USA", description: "Issue Country" })
  @IsOptional()
  issueCountry?: string;

  @ApiPropertyOptional({ example: "January 11, 2022", description: "Issued On" })
  @IsOptional()
  issuedOn?: string;

  @ApiPropertyOptional({ example: "January 11, 2022", description: "Expiration" })
  @IsOptional()
  expiration?: string;

  @ApiPropertyOptional({ example: "American", description: "Nationality" })
  @IsOptional()
  nationality?: string;

  @ApiPropertyOptional({ example: "Open", description: "Status" })
  @IsOptional()
  visaStatus?: string;

  @ApiPropertyOptional({ example: "Open", description: "Status" })
  @IsOptional()
  profileStatus?: string;
}
