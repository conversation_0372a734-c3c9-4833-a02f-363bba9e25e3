import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";
import { FilterCompanyJobsDto } from "../job/dto/filter-job.dto";

class TypicalTableDto {
  @ApiPropertyOptional({ example: "Designer", description: "Search" })
  @IsOptional()
  search?: string;

  @ApiProperty({ example: "id", description: "Sort by" })
  @IsOptional()
  sortBy?: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  @IsOptional()
  sortType?: string;

  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;
}

export class FilterJobsInterviews extends TypicalTableDto {
  @ApiPropertyOptional({ example: [], description: "Job Location" })
  @IsOptional()
  locations: [string];

  @ApiPropertyOptional({ example: '', description: "Name of interview round" })
  @IsOptional()
  roundName: string;
}
