import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, HasMany,
  Model,
  Table,
} from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "../users/users.model";
import { Industry } from "../industries/industries.model";
import { CandidateIndustry } from "../industries/candidates-industries.model";
import { Education } from "../educations/educations.model";
import { Experience } from "../experiences/experiences.model";
import { Skill } from "../skills/skills.model";
import { Certificate } from "../certificates/certificates.model";
import { Award } from "../awards/awards.model";
import { Project } from "../projects/projects.model";
import { Location } from "../locations/location.model";
import { Position } from "../positions/positions.model";

interface CandidateAttrs {
  candidate_type?: string;
  positionId?: number;
  userId: number;
  experience?: number;
  locationId?: number;
  currentCtc?: number;
  currency?: string;
  headlines?: string;
  summary?: string;
  cv<PERSON><PERSON>?: string;
  cvN<PERSON>?: string;
  birthday?: Date;
  gender?: string;
  degree?: string;
  maritalStatus?: string;
  emergencyPerson?: string;
  relationEmployee?: string;
  emergencyMobile?: string;
  personalEmail?: string;
  languages?: string;
  linkedin?: string;
  twitter?: string;
  facebook?: string;
  instagram?: string;
  currentStreet?: string;
  currentHouseNumber?: string;
  currentCity?: string;
  currentCountry?: string;
  currentState?: string;
  currentZip?: string;
  permanentStreet?: string;
  permanentHouseNumber?: string;
  permanentCity?: string;
  permanentCountry?: string;
  permanentState?: string;
  permanentZip?: string;
  onlineProfile?: string;
  passport?: string;
  visaType?: string;
  issueCountry?: string;
  issuedOn?: string;
  expiration?: string;
  nationality?: string;
  visaStatus?: string;
  preferencesRole?: string;
  preferencesFunctionalArea?: string;
  preferencesJobType?: string;
  preferencesPreferableShift?: string;
  preferencesJobLocation?: string;
  preferencesExpectedCTC?: number;
  preferencesAbleJoin?: string;
  remoteLocation?: boolean;
  inviteLink: string;
  status: string;
}

@Table({ tableName: "candidates" })
export class Candidate extends Model<Candidate,
  CandidateAttrs> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({ example: "Fresher", description: "Candidate Type" })
  @Column({ type: DataType.STRING, allowNull: true })
  candidate_type: string;

  @ApiProperty({ example: "1", description: "user id" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER })
  userId: number;

  @ApiProperty({ example: "1", description: "Location id" })
  @ForeignKey(() => Location)
  @Column({ type: DataType.INTEGER, allowNull: true })
  locationId: number;

  @ApiProperty({ example: "1", description: "Job id" })
  @ForeignKey(() => Position)
  @Column({ type: DataType.INTEGER, allowNull: true })
  positionId: number;

  @ApiProperty({ example: "5", description: "Total Experience" })
  @Column({ type: DataType.FLOAT, allowNull: true, defaultValue: 0 })
  experience: number;

  @ApiProperty({ example: "8000", description: "Current CTC" })
  @Column({ type: DataType.FLOAT, allowNull: true, defaultValue: 0 })
  currentCtc: number;

  @ApiProperty({ example: "USD", description: "currency" })
  @Column({ type: DataType.STRING, allowNull: true })
  currency: string;

  @ApiProperty({ example: "Headlines", description: "Headlines" })
  @Column({ type: DataType.STRING, allowNull: true })
  headlines: string;

  @ApiProperty({ example: "Summary", description: "Summary" })
  @Column({ type: DataType.STRING, allowNull: true })
  summary: string;

  @ApiProperty({ example: "https://www.youtube.com/", description: "CV Url" })
  @Column({ type: DataType.STRING, allowNull: true })
  cvKey: string;

  @ApiProperty({ example: "youtube.pdf", description: "CV Name" })
  @Column({ type: DataType.STRING, allowNull: true })
  cvName: string;

  @ApiProperty({ example: "01.01.1990", description: "Birthday data" })
  @Column({ type: DataType.DATE, allowNull: true })
  birthday: Date;

  @ApiProperty({ example: "Male", description: "Gender" })
  @Column({ type: DataType.STRING, allowNull: true })
  gender: string;

  @ApiProperty({ example: "master", description: "Education degree" })
  @Column({ type: DataType.STRING, allowNull: true })
  degree: string;

  @ApiProperty({ example: "Single", description: "Marital Status" })
  @Column({ type: DataType.STRING, allowNull: true })
  maritalStatus: string;

  @ApiProperty({
    example: "Rob Anderson",
    description: "Emergency Contact Person",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  emergencyPerson: string;

  @ApiProperty({ example: "Positive", description: "Relation with Employee" })
  @Column({ type: DataType.STRING, allowNull: true })
  relationEmployee: string;

  @ApiProperty({
    example: "213214124",
    description: "Emergency Contact Mobile",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  emergencyMobile: string;

  @ApiProperty({
    example: "<EMAIL>",
    description: "Personal Email",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  personalEmail: string;

  @ApiProperty({
    example: "[{\"value\":\"en\",\"label\":\"English\"}]",
    description: "Languages",
  })
  @Column({ type: DataType.JSON, allowNull: true })
  languages: string;

  @ApiProperty({
    example: "https://www.linkedin.com/",
    description: "Linkedin",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  linkedin: string;

  @ApiProperty({ example: "https://twitter.com/", description: "Twitter" })
  @Column({ type: DataType.STRING, allowNull: true })
  twitter: string;

  @ApiProperty({
    example: "https://www.facebook.com/",
    description: "Facebook",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  facebook: string;

  @ApiProperty({
    example: "https://www.instagram.com/",
    description: "Instagram",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  instagram: string;

  @ApiProperty({
    example: "New Street",
    description: "Current Address: Street",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  currentStreet: string;

  @ApiProperty({
    example: "95",
    description: "Current Address: House name or number",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  currentHouseNumber: string;

  @ApiProperty({ example: "New York", description: "Current Address: City" })
  @Column({ type: DataType.STRING, allowNull: true })
  currentCity: string;

  @ApiProperty({ example: "USA", description: "Current Address: Country" })
  @Column({ type: DataType.STRING, allowNull: true })
  currentCountry: string;

  @ApiProperty({ example: "New York", description: "Current Address: State" })
  @Column({ type: DataType.STRING, allowNull: true })
  currentState: string;

  @ApiProperty({
    example: "10030",
    description: "Current Address: Zip/Postcode",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  currentZip: string;

  @ApiProperty({
    example: "New Street",
    description: "Current Address: Street",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentStreet: string;

  @ApiProperty({
    example: "95",
    description: "Current Address: House name or number",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentHouseNumber: string;

  @ApiProperty({ example: "New York", description: "Current Address: City" })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentCity: string;

  @ApiProperty({ example: "USA", description: "Current Address: Country" })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentCountry: string;

  @ApiProperty({ example: "New York", description: "Current Address: State" })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentState: string;

  @ApiProperty({
    example: "10030",
    description: "Current Address: Zip/Postcode",
  })
  @Column({ type: DataType.STRING, allowNull: true })
  permanentZip: string;

  @ApiProperty({
    example: "matthew-stark",
    description: "Unique url slug",
  })
  @Column({ type: DataType.STRING, allowNull: true, unique: true })
  onlineProfile: string;

  @ApiProperty({ example: "2131232", description: "Passport №" })
  @Column({ type: DataType.STRING, allowNull: true })
  passport: string;

  @ApiProperty({ example: "U-1", description: "Visa Type" })
  @Column({ type: DataType.STRING, allowNull: true })
  visaType: string;

  @ApiProperty({ example: "USA", description: "Issue Country" })
  @Column({ type: DataType.STRING, allowNull: true })
  issueCountry: string;

  @ApiProperty({ example: "January 11, 2022", description: "Issued On" })
  @Column({ type: DataType.STRING, allowNull: true })
  issuedOn: string;

  @ApiProperty({ example: "January 11, 2022", description: "Expiration" })
  @Column({ type: DataType.STRING, allowNull: true })
  expiration: string;

  @ApiProperty({ example: "American", description: "Nationality" })
  @Column({ type: DataType.STRING, allowNull: true })
  nationality: string;

  @ApiProperty({ example: "Open", description: "Status" })
  @Column({ type: DataType.STRING, allowNull: true })
  visaStatus: string;

  @ApiProperty({ example: "Developer", description: "Position" })
  @Column({ type: DataType.STRING, allowNull: true })
  preferencesRole: string;

  @ApiProperty({ example: "Administration", description: "Functional Area" })
  @Column({ type: DataType.STRING, allowNull: true })
  preferencesFunctionalArea: string;

  @ApiProperty({ example: "Administration", description: "Job Type" })
  @Column({ type: DataType.STRING, allowNull: true })
  preferencesJobType: string;

  @ApiProperty({ example: "Night", description: "Preferable Shift" })
  @Column({ type: DataType.STRING, allowNull: true })
  preferencesPreferableShift: string;

  @ApiProperty({ example: "New York", description: "Preferred Job Location" })
  @Column({ type: DataType.STRING, allowNull: true })
  preferencesJobLocation: string;

  @ApiProperty({ example: "6000", description: "Expected CTC" })
  @Column({ type: DataType.FLOAT, allowNull: true, defaultValue: 0 })
  preferencesExpectedCTC: number;

  @ApiProperty({ example: "Summary", description: "Able to Join" })
  @Column({ type: DataType.TEXT, allowNull: true })
  preferencesAbleJoin: string;

  @ApiProperty({ example: "false", description: "Remote Location" })
  @Column({ type: DataType.BOOLEAN, allowNull: true, defaultValue: false })
  remoteLocation: boolean;

  @ApiProperty({ example: "", description: "invitation link id" })
  @Column({ type: DataType.STRING, allowNull: true })
  inviteLink: string;

  @ApiProperty({ example: "", description: "Status of a candidate" })
  @Column({ type: DataType.STRING, allowNull: true })
  status: string;

  @BelongsTo(() => User)
  user: User;

  @BelongsTo(() => Location)
  location: Location;

  @BelongsTo(() => Position)
  position: Position;

  @BelongsToMany(() => Industry, () => CandidateIndustry)
  industries: Industry[];

  @HasMany(() => Education)
  educations: Education[];

  @HasMany(() => Experience)
  experiences: Experience[];

  @HasMany(() => Skill)
  skills: Skill[];

  @HasMany(() => Certificate)
  certificates: Certificate[];

  @HasMany(() => Award)
  awards: Award[];

  @HasMany(() => Project)
  projects: Project[];
}
