import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class IAMUsersDto {
  @ApiProperty({ example: "20", description: "Limit" })
  limit: number;

  @ApiProperty({ example: "0", description: "Offset" })
  offset: number;

  @ApiProperty({ example: "0", description: "Role ID" })
  roleId: number;

  @ApiProperty({ example: "id", description: "Sort by" })
  sortBy: string;

  @ApiProperty({ example: "desc", description: "Sort type" })
  sortType: string;

  @ApiPropertyOptional({ example: "Rob", description: "Search field" })
  search?: string;
}
