import { Client } from '@microsoft/microsoft-graph-client';
import { Injectable, Logger } from '@nestjs/common';
import fetch from 'cross-fetch';
import { IntegrationAuthService } from '../../integrationAuth/integrationAuth.service';


require('isomorphic-fetch');
@Injectable()
export class OutlookAuthService {
  constructor(private integrationAuthService:IntegrationAuthService){ }
  private clientId = process.env.OUTLOOK_CLIENT_ID;
  private clientSecret = process.env.OUTLOOK_CLIENT_SECRET;
  private authorizationEndpoint = 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize';
  private tokenEndpoint = 'https://login.microsoftonline.com/common/oauth2/v2.0/token';
  private scope = 'offline_access Mail.Read Mail.Send Mail.ReadWrite Mail.ReadBasic User.Read';
  private redirectUri =`${process.env.MICROSERVICES_PRA_URL}/api/integrations/oauth2-callback`;

  async checkConnectionStatus(refreshToken) {
    // maybe check connection status on the API side
    return 'connected';
  }

  async getConnectUrl(state) {
    const url = new URL(this.authorizationEndpoint);
    url.search = new URLSearchParams({
      client_id: this.clientId,
      response_type: 'code',
      redirect_uri: this.redirectUri,
      scope: this.scope,
      response_mode: 'query',
      state: state,
      code_challenge: 'da_ZTTAnsYyEy8MAB4STblXIZfw2vW2K0avFu5VZT2s',
      code_challenge_method: 'S256',
    }).toString();

    return url;
  }

  async oauth2Callback(code,userId) {
   
    const result = {
      data: '',
      error: '',
    };

    try {
      await fetch(this.tokenEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          "Origin":`${process.env.MICROSERVICES_PRA_URL}`
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          code: code,
          redirect_uri: this.redirectUri,
          grant_type: 'authorization_code',
          scope: this.scope,
          'code_verifier':"6166cece54740b83a5d08bf79c840286525ed626faf5d089b822e3d2"
        }),
      })
        .then(function (res) {
          return res.json();
        })
        .then(async (body: any) =>{
          if (!body.refresh_token) {
            throw body;
          }
          const newTokens = {
            userId: userId,
            provider:"outlook",
            accessToken:body.access_token,
            refreshToken:body.refresh_token ,
            expiryDate: new Date().getTime() + body.expires_in * 1000,
            scope: body.scope,
          };
          const isAvailable = await this.integrationAuthService.findByProvider(userId,"outlook");
          if (isAvailable) {
            await this.integrationAuthService.update(userId, newTokens,this.scope);
          } else {
            await this.integrationAuthService.create(newTokens);
          }
          result.data = body.refresh_token;
        });
    } catch (e) {
      result.error = e.error || 'callback_error';
      Logger.error(e);
    }

    return result;
  }

  async authRevoke(refreshToken) {
    // todo
    // https://learn.microsoft.com/en-us/answers/questions/1092246/can39t-revoke-signin-session.html
    /*
    const clientt = await this.getClient(refreshToken);
    const client = clientt.client;
    if (client) {
      // @ts-ignore
      await client.api('/me/revokeSignInSessions').post();
    }
   */
    return true;
  }

  async getTokens(refreshToken) {
    const tokens = {
      'accessToken': '',
      'refreshToken': '',
    };
    await fetch(this.tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        'client_id': this.clientId,
        'scope': this.scope,
        'refresh_token': refreshToken,
        'grant_type': 'refresh_token',
        'client_secret': this.clientSecret,
      }),
    })
      .then(function (res) {
        return res.json();
      })
      .then(function (body: any) {
        if (!body.access_token || !body.refresh_token) {
          throw body;
        }
        tokens.accessToken = body.access_token;
        tokens.refreshToken = body.refresh_token;
      });

    return tokens;
  }

  async getClient(refreshToken) {
    const tokens = await this.getTokens(refreshToken);

    return {
      client: tokens.accessToken
        ? await Client.init({
          authProvider: async (callback) => {
            callback(null, tokens.accessToken);
          },
        })
        : false,
      data: tokens.refreshToken,
    };
  }
}