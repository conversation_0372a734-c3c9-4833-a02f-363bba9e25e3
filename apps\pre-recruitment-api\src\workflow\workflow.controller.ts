import { Controller, Post, Body, UseGuards, Get, Query, Patch, Delete, Param, Headers } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  EditWorkflowDto,
  Workflow, WorkflowChatbotService, WorkflowDto, WorkflowFilterDto,
  WorkflowService,
} from "@microservices/recruitment-db";

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("Workflow")
@Controller("workflow")
export class WorkflowController {
  constructor (
    private readonly workflowService: WorkflowService,
    private readonly workflowChatbotService: WorkflowChatbotService
  ) {}

  @ApiOperation({ summary: 'Create Workflow' })
  @ApiResponse({ status: 200, type: Workflow })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post()
  @Permissions('OR','job-post:add','job-post:edit')
  create(@Body() dto: WorkflowDto, @AuthUser() user: any) {
    return this.workflowService.create(dto, user["https://urecruits.com/userId"], user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get company workflows' })
  @ApiResponse({ status: 200, type: [Workflow] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/company')
  @Permissions('job-post:view')
  getPublicCompanyWorkflow(@Query() query: WorkflowFilterDto, @AuthUser() user: any) {
    return this.workflowService.getAll(query, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Edit Workflow' })
  @ApiResponse({ status: 200, type: Workflow })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch()
  @Permissions('OR','job-post:add','job-post:edit')
  edit(@Body() dto: EditWorkflowDto, @AuthUser() user: any) {
    return this.workflowService.edit(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Delete Workflow' })
  @ApiResponse({ status: 200, type: Workflow })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete()
  @Permissions('job-post:delete')
  delete(@Body() dto: EditWorkflowDto, @AuthUser() user: any) {
    return this.workflowService.delete(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get company workflow' })
  @ApiResponse({ status: 200, type: Workflow })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/company/:id')
  @Permissions('job-post:view')
  getById (@Param("id") id: number, @AuthUser() user: any) {
    return this.workflowService.getOne(id, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get company workflow - jobs' })
  @ApiResponse({ status: 200, type: [Workflow] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/company-job')
  @Permissions('job-post:view')
  getCompanyWorkflowToJob(@AuthUser() user: any) {
    return this.workflowService.getCompanyWorkflowToJob(user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Conversation with the positon workflow chat agent' })
  @ApiBearerAuth('access-token')
  @ApiResponse({ status: 200,})
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/suggestions')
  @Permissions('job-post:add','job-post:edit','job-post:view')
  startConverstaion(@Body() Body,@AuthUser() user: any,@Headers('authorization') authHeader:any) {
    return this.workflowChatbotService.startConversation(Body,user["https://urecruits.com/companyId"],user["https://urecruits.com/userId"],authHeader);
  }
}
