import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class CreateEventDTO {
  @ApiProperty({ example: 'date', description: "Start Date and Time" })
  date: any;

  @ApiProperty({ example: "11:00 AM", description: "Slot Time" })
  slot: any;

  @ApiProperty({ example: "12:00 AM", description: "end Slot Time" })
  endSlot?: any;

  @ApiProperty({ example: 'title', description: "Title of the event" })
  eventTitle: any;

  @ApiProperty({ example: "description", description: "description" })
  description: any;

  @ApiProperty({ example: 'recruiterId', description: "recruiterId" })
  @IsOptional()
  recruiterId: Number;

  @ApiProperty({ example: "true", description: "remind me time" })
  remindMe: boolean;

  @ApiProperty({ example: "20 minutes", description: "time before" })
  @IsOptional()
  timeBefore: String;

  @ApiProperty({ example: "20", description: "Job Id" })
  @IsOptional()
  jobId: any;

  @ApiProperty({ example: 20, description: "Candidate Id" })
  @IsOptional()
  candidateId: Number;

  @ApiProperty({ example: "false", description: "Does request have rescheduling request?" })
  @IsOptional()
  isReschedule: any;

  @ApiProperty({ example: [], description: "List of Interviewers" })
  @IsOptional()
  interviewers: any;

  @ApiProperty({ example: "1", description: "Event Id Stored In DB" })
  @IsOptional()
  eventId: any;

  @ApiProperty({ example: "dmhoZWE4YnUxcDVtbDFyOW91NGV0NDF1dmsgaGFyc2hpbC53ZWJkZXZAbQ", description: "Google Calendar Event Id" })
  @IsOptional()
  calEventId: any;

  @ApiProperty({ example: 'Asia/Calcutta', description: "Clients timeZone" })
  @IsOptional()
  timeZone: any;

  @ApiProperty({ example: new Date(), description: "UTC date with start time" })
  @IsOptional()
  utcStartDate: string;

  @ApiProperty({ example: new Date(), description: "UTC date with end time" })
  @IsOptional()
  utcEndDate: string;

  @ApiProperty({ example: "1", description: "Room Id" })
  @IsOptional()
  roomId: any;
}