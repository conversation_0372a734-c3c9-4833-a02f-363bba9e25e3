import { Module } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { IntegrationAuthModule } from '../integrationAuth/integrationAuth.module';
import { FileModule } from 'apps/pre-recruitment-api/src/file/file.module';
import { OutlookService } from './outlook.service';

@Module({
  imports: [SequelizeModule.forFeature(),IntegrationAuthModule,FileModule],
  providers: [OutlookService],
  exports: [SequelizeModule, OutlookService],
})
export class OutlookModule {}
