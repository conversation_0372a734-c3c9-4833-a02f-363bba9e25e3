import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { InjectModel } from '@nestjs/sequelize'
import { Experience } from './experiences.model'
import { CreateExperienceDto } from './dto/create-experience.dto'
import { UpdateExperienceDto } from "./dto/update-experience.dto";

@Injectable()
export class ExperiencesService {
  constructor (
    @InjectModel(Experience) private experienceRepository: typeof Experience,
  ) {}

  async create (dto: CreateExperienceDto) {
    return await this.experienceRepository.create(dto)
  }

  async update (dto: UpdateExperienceDto) {
    const data = await this.get(dto);
    Object.assign(data, dto);
    return await data.save();
  }

  async delete (dto: UpdateExperienceDto) {
    const data = await this.get(dto);
    await data.destroy();
    return true;
  }

  async get (dto: UpdateExperienceDto) {
    const data = await this.experienceRepository.findOne({
      where: {
        id: dto.id,
        candidateId: dto.candidateId,
        recruiterId: dto.recruiterId
      },
    });
    if (!data) {
      throw new HttpException("Experience not found", HttpStatus.NOT_FOUND);
    }
    return data;
  }
}
