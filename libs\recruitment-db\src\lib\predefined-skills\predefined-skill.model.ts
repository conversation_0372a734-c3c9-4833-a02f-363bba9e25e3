import { Column, DataType, Model, Table } from "sequelize-typescript";
import { ApiProperty } from "@nestjs/swagger";

interface PredefinedSkillsAttrs {
  name?: string;
}

@Table({ tableName: "predefined-skills", createdAt: false, updatedAt: false })
export class PredefinedSkills extends Model<PredefinedSkills, PredefinedSkillsAttrs> {
  @ApiProperty({ example: "1", description: "Primary key" })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: "Figma",
    description: "Name",
  })
  @Column({ type: DataType.STRING, allowNull: false, unique: true })
  name: string;
}

