import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class CompanyDto {
  @ApiPropertyOptional({ example: 'uRecruits', description: 'Company Name' })
  name?: string;

  @ApiPropertyOptional({
    example: '<EMAIL>',
    description: 'Contact Person Email',
  })
  @IsOptional()
  email: string;

  @ApiPropertyOptional({ example: '8622365552', description: 'Contact Person Phone' })
  @IsOptional()
  phone?: string;

  @ApiPropertyOptional({ example: 'cus_xxx', description: 'Stripe customer ID' })
  @IsOptional()
  stripeId?: string;

  @ApiPropertyOptional({ example: 'Corporate', description: 'Company Type' })
  @IsOptional()
  company_type?: string;

  @ApiPropertyOptional({ example: 'Matthew', description: 'Contact Person First Name' })
  @IsOptional()
  firstname?: string;

  @ApiPropertyOptional({ example: '<PERSON>', description: 'Contact Person Middle Name' })
  @IsOptional()
  middlename?: string;

  @ApiPropertyOptional({ example: 'Stark', description: 'Contact Person Last Name' })
  @IsOptional()
  lastname?: string;

  @ApiProperty({ example: 0, description: 'Company specialists count' })
  @IsOptional()
  companySpecialists?: number;

  @ApiPropertyOptional({
    example: 'About the Company',
    description: 'About the Company',
  })
  @IsOptional()
  about?: string;

  @ApiPropertyOptional({ example: '1', description: 'Industry' })
  @IsOptional()
  industryId?: number;

  @ApiPropertyOptional({
    example: 'https://domain.com/avatar.jpg',
    description: 'Avatar',
  })
  @IsOptional()
  avatar?: string;

  @ApiPropertyOptional({
    example: 'https://domain.com',
    description: 'website',
  })
  @IsOptional()
  website?: string;

  @ApiPropertyOptional({
    example: [],
    description: 'Gallery',
  })
  @IsOptional()
  gallery?: any;

  @ApiPropertyOptional({
    example: [],
    description: 'Addresses',
  })
  @IsOptional()
  addresses?: any;
}
