import { Body, Controller, Get, Param, ParseArrayPipe, Patch, Post, Query, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from "@nestjs/swagger";
import {
  ChangeStatusDto,
  Round,
  RoundService,
  UpdateRoundDto,
  AddEventDto,
  AddScoreToRoundDto,
} from "@microservices/recruitment-db";
import { AuthGuard } from "@nestjs/passport";
import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("Round")
@Controller("round")
@ApiBearerAuth("access-token")
export class RoundController {
  constructor (private readonly roundService: RoundService) {}
  @ApiOperation({ summary: "Edit Round" })
  @ApiResponse({ status: 200, type: Round })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch()
  @Permissions("job-post:edit")
  edit (@Body() dto: UpdateRoundDto, @AuthUser() user: any) {
    return this.roundService.update(dto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get round' })
  @ApiResponse({ status: 200, type: Round })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-one/:userId/:jobId')
  @Permissions('recruiter','candidate','job-post:view','OR')
  getOne(@Param('userId') userId: number, @Param('jobId') jobId: number, @AuthUser() user: any) {
    return this.roundService.getOne(userId, jobId);
  }

  @ApiOperation({ summary: 'Get round' })
  @ApiResponse({ status: 200, type: Round })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/get-all/:jobId')
  @Permissions('job-post:view')
  getAllByJobId(
  @Query() dto:any,
  @Query('ids') ids: any,
  @Param('jobId') jobId: number, @AuthUser() user: any) {
    if (ids) {
      const arrayParam = new ParseArrayPipe({ items: String, separator: ',' });
      return this.roundService.getAllByJobId(jobId,dto,arrayParam );
    }else{
      return this.roundService.getAllByJobId(jobId,dto,[] );
    }
  }

  @ApiOperation({ summary: 'Get round' })
  @ApiResponse({ status: 200, type: Round })
  // @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch('changeStatus')
  // @Permissions('job-post:view')
  changeStatus(@Body() data:ChangeStatusDto) {
    return this.roundService.changeStatus(data);
  }

  @ApiOperation({ summary: 'Add eventId in round' })
  @ApiResponse({ status: 200, type: Round })
  @Patch('addEvent')
  addEventId(@Body() data:AddEventDto) {
    return this.roundService.addEventId(data);
  }

  @ApiOperation({ summary: "Get scoreboard data for a job" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("scoreboard/:jobId")
  @Permissions("candidate")
  getCandidateJobScoreboard(@Param('jobId') jobId: number, @AuthUser() user: any) {
    const userId = user["https://urecruits.com/userId"];
    return this.roundService.getCandidateJobScoreboard(userId, jobId)
  }

  @ApiOperation({ summary: "Get all rounds details for candidate for all job" })
  @ApiResponse({ status: 200 })
  @ApiBearerAuth('access-token')
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("candidate")
  @Permissions("candidate")
  getCandidateRounds(@AuthUser() user: any) {
    const userId = user["https://urecruits.com/userId"];
    return this.roundService.getCandidateRounds(userId)
  }

  @ApiOperation({ summary: 'Add eventId in round' })
  @ApiResponse({ status: 200, type: Round })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch('/add-score')
  @Permissions("recruiter","assessment:edit","assessment:add","OR")
  AddScoreToRoundDto(@Body() data:AddScoreToRoundDto) {
    return this.roundService.addScoreToRound(data);
  }
}
