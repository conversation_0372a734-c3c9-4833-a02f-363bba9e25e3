import { <PERSON>, Body, Patch, Get, Param, UseGuards, Post, Delete } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth} from "@nestjs/swagger";
import { NotifyPermissionsService , NotifyPermissionsDto } from "@microservices/recruitment-db";
import { AuthGuard } from "@nestjs/passport";
import { AuthUser } from "@microservices/auth";

//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags('Notify Permissions')
@ApiBearerAuth("access-token")
@Controller('notify-permissions')
export class NotifyPermissionsController {
  constructor (private readonly notifyPermissionsService: NotifyPermissionsService) {}

  @ApiOperation({ summary: 'Store Notify Permission Token' })
  @UseGuards(AuthGuard('jwt'))
  @Post()
  async storeNotifyPermissionToken (@Body() dto: NotifyPermissionsDto, @AuthUser() user: any) {
    return this.notifyPermissionsService.storeNotifyPermissionToken(dto, +user["https://urecruits.com/userId"])
  }

  @ApiOperation({ summary: 'Get Notify Permission Token' })
  @UseGuards(AuthGuard('jwt'))
  @Get()
  getNotifyPermissionToken (@AuthUser() user: any) {
    return this.notifyPermissionsService.getNotifyPermissionToken(+user["https://urecruits.com/userId"])
  }

  @ApiOperation({ summary: 'Update Notify Permission Token' })
  @UseGuards(AuthGuard('jwt'))
  @Patch()
  updateNotifyPermissionToken(@Body() dto: NotifyPermissionsDto, @AuthUser() user: any) {
    return this.notifyPermissionsService.updateNotifyPermissionToken(dto, +user["https://urecruits.com/userId"])
  }


  @ApiOperation({ summary: 'Delete All Device Notify Permission Token' })
  @UseGuards(AuthGuard('jwt'))
  @Delete()
  deleteNotifyPermissionToken(@AuthUser() user: any) {
    return this.notifyPermissionsService.deleteNotifyPermissionToken(+user["https://urecruits.com/userId"])
  }  
}
