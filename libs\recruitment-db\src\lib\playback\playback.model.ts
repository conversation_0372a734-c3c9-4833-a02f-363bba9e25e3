import { <PERSON><PERSON>s<PERSON><PERSON>, <PERSON>umn, DataType, ForeignKey, HasMany, HasOne, Model, Table } from 'sequelize-typescript';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '../users/users.model';
import { Jobs } from '../job/job.model';


@Table({
  tableName: 'playback',
  createdAt: true,
  updatedAt: true
})
export class Playback extends Model<Playback> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: '123456',
    description: 'Playback Id',
  })
  @Column({ type: DataType.STRING, allowNull: false })
  playbackId: string;

  @ApiProperty({
    example: '123456',
    description: 'Playback Id',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  assessmentId: string;

  @ApiProperty({ example: 'take-home', description: 'type of assessment' })
  @Column({ type: DataType.STRING, allowNull: false })
  assessmentType?: string;

  @ApiProperty({ example: "1", description: "User ID" })
  @ForeignKey(() => User)
  @Column({ type: DataType.INTEGER, allowNull: false })
  candidateId: number;

  @ApiProperty({ example: "1", description: "Job ID" })
  @ForeignKey(() => Jobs)
  @Column({ type: DataType.INTEGER, allowNull: false })
  jobId: number;

  @ApiProperty({
    example: '123456',
    description: 'Question ID',
  })
  @Column({ type: DataType.INTEGER, allowNull: true })
  questionId: number;

  @ApiProperty({
    example: '123456',
    description: 'Name of the question',
  })
  @Column({ type: DataType.STRING, allowNull: true })
  questionName: string;

  
  @BelongsTo(() => Jobs)
  Jobs: Jobs;

  @BelongsTo(() => User)
  User: User;
}
