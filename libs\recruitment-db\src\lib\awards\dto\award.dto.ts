import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class AwardDto {
  @ApiPropertyOptional({ example: "Leadership", description: "Award Name" })
  @IsOptional()
  readonly name?: string;

  @ApiPropertyOptional({ example: "Adobe", description: "Company Name" })
  @IsOptional()
  readonly companyName?: string;

  @ApiPropertyOptional({ example: "2021-12-28T22:00:00.000Z", description: "Award Date" })
  @IsOptional()
  readonly date?: string;

  @ApiPropertyOptional({ example: "Notes", description: "Notes" })
  @IsOptional()
  readonly notes?: string;

  @ApiPropertyOptional({ example: "file.pdf", description: "File name" })
  @IsOptional()
  readonly fileName?: string;

  @ApiPropertyOptional({ example: "213122 - file.pdf", description: "File key" })
  @IsOptional()
  readonly fileKey?: string;
}
