import { ApiPropertyOptional } from "@nestjs/swagger";

export class AddressDto {
  @ApiPropertyOptional({
    example: "19 W. 34th Street Suite",
    description: "Company Street",
  })
  street?: string;

  @ApiPropertyOptional({
    example: "New York",
    description: "Company Location",
  })
  location?: string;

  @ApiPropertyOptional({
    example: "USA",
    description: "Country",
  })
  country?: string;

  @ApiPropertyOptional({
    example: "<EMAIL>",
    description: "Company Email",
  })
  email?: string;

  @ApiPropertyOptional({
    example: "+380978760605",
    description: "Company Phone",
  })
  phone?: string;
}
