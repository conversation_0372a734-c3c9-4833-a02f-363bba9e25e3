Parameters:
  App:
    Type: String
    Description: Your application's name.
  Env:
    Type: String
    Description: The environment name your service, job, or workflow is being deployed to.
  Name:
    Type: String
    Description: The name of the service, job, or workflow being deployed.
  # Customize your Aurora Serverless cluster by setting the default value of the following parameters.
  assessmentclusterDBName:
    Type: String
    Description: The name of the initial database to be created in the DB cluster.
    Default: postgres
    # Cannot have special characters
    # Naming constraints: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/CHAP_Limits.html#RDS_Limits.Constraints
  assessmentclusterDBAutoPauseSeconds:
    Type: Number
    Description: The duration in seconds before the cluster pauses.
    Default: 1000
Mappings:
  assessmentclusterEnvScalingConfigurationMap:
    dev:
      "DBMinCapacity": 2 # AllowedValues: [2, 4, 8, 16, 32, 64, 192, 384]
      "DBMaxCapacity": 8 # AllowedValues: [2, 4, 8, 16, 32, 64, 192, 384]

    All:
      "DBMinCapacity": 2 # AllowedValues: [2, 4, 8, 16, 32, 64, 192, 384]
      "DBMaxCapacity": 8 # AllowedValues: [2, 4, 8, 16, 32, 64, 192, 384]


Resources:
  assessmentclusterDBSubnetGroup:
    Type: 'AWS::RDS::DBSubnetGroup'
    Properties:
      DBSubnetGroupDescription: Group of Copilot private subnets for Aurora cluster.
      SubnetIds:
        !Split [',', { 'Fn::ImportValue': !Sub '${App}-${Env}-PrivateSubnets' }]
  assessmentclusterSecurityGroup:
    Metadata:
      'aws:copilot:description': 'A security group for your workload to access the DB cluster assessmentcluster'
    Type: 'AWS::EC2::SecurityGroup'
    Properties:
      GroupDescription: !Sub 'The Security Group for ${Name} to access DB cluster assessmentcluster.'
      VpcId:
        Fn::ImportValue:
          !Sub '${App}-${Env}-VpcId'
      Tags:
        - Key: Name
          Value: !Sub 'copilot-${App}-${Env}-${Name}-Aurora'
  assessmentclusterDBClusterSecurityGroup:
    Metadata:
      'aws:copilot:description': 'A security group for your DB cluster assessmentcluster'
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: The Security Group for the database cluster.
      SecurityGroupIngress:
        - ToPort: 5432
          FromPort: 5432
          IpProtocol: tcp
          Description: !Sub 'From the Aurora Security Group of the workload ${Name}.'
          SourceSecurityGroupId: !Ref assessmentclusterSecurityGroup
      VpcId:
        Fn::ImportValue:
          !Sub '${App}-${Env}-VpcId'
  assessmentclusterAuroraSecret:
    Metadata:
      'aws:copilot:description': 'A Secrets Manager secret to store your DB credentials'
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub 'urecruits-${Env}-assessment-AuroraPostgres'
      Description: !Sub Aurora main user secret for ${AWS::StackName}
      GenerateSecretString:
        SecretStringTemplate: '{"username": "postgres"}'
        GenerateStringKey: "password"
        ExcludePunctuation: true
        IncludeSpace: false
        PasswordLength: 16
  assessmentclusterDBClusterParameterGroup:
    Metadata:
      'aws:copilot:description': 'A DB parameter group for engine configuration values'
    Type: 'AWS::RDS::DBClusterParameterGroup'
    Properties:
      Description: !Ref 'AWS::StackName'
      Family: 'aurora-postgresql10'
      Parameters:
        client_encoding: 'UTF8'
  assessmentclusterDBCluster:
    Metadata:
      'aws:copilot:description': 'The assessmentcluster Aurora Serverless database cluster'
    Type: 'AWS::RDS::DBCluster'
    Properties:
      MasterUsername:
        !Join [ "",  [ '{{resolve:secretsmanager:', !Ref assessmentclusterAuroraSecret, ":SecretString:username}}" ]]
      MasterUserPassword:
        !Join [ "",  [ '{{resolve:secretsmanager:', !Ref assessmentclusterAuroraSecret, ":SecretString:password}}" ]]
      DatabaseName: !Ref assessmentclusterDBName
      Engine: 'aurora-postgresql'
      EngineVersion: '10.12'
      EngineMode: serverless
      DBClusterParameterGroupName: !Ref assessmentclusterDBClusterParameterGroup
      DBSubnetGroupName: !Ref assessmentclusterDBSubnetGroup
      VpcSecurityGroupIds:
        - !Ref assessmentclusterDBClusterSecurityGroup
      ScalingConfiguration:
        AutoPause: true
        # Replace "All" below with "!Ref Env" to set different autoscaling limits per environment.
        MinCapacity: !FindInMap [assessmentclusterEnvScalingConfigurationMap, All, DBMinCapacity]
        MaxCapacity: !FindInMap [assessmentclusterEnvScalingConfigurationMap, All, DBMaxCapacity]
        SecondsUntilAutoPause: !Ref assessmentclusterDBAutoPauseSeconds
  assessmentclusterSecretAuroraClusterAttachment:
    Type: AWS::SecretsManager::SecretTargetAttachment
    Properties:
      SecretId: !Ref assessmentclusterAuroraSecret
      TargetId: !Ref assessmentclusterDBCluster
      TargetType: AWS::RDS::DBCluster
Outputs:
  assessmentclusterSecret: # injected as ASSESSMENTCLUSTER_SECRET environment variable by Copilot.
    Description: "The JSON secret that holds the database username and password. Fields are 'host', 'port', 'dbname', 'username', 'password', 'dbClusterIdentifier' and 'engine'"
    Value: !Ref assessmentclusterAuroraSecret
  assessmentclusterSecurityGroup:
    Description: "The security group to attach to the workload."
    Value: !Ref assessmentclusterSecurityGroup
