import { AuthUser, Permissions, PermissionsGuard } from '@microservices/auth';
import { TakeHomeDraftsService, TakeHomeTaskService } from '@microservices/db';
import { Controller, Get, Post, Body, Patch, Param, Delete, Query, UseGuards } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
// import { QdrantClient } from '@qdrant/qdrant-js';
// import { LangchainService } from '@microservices/integrations';

// const langchainService = new LangchainService();


@Controller('take-home-task')
@ApiBearerAuth("access-token")
export class TakeHomeTaskController {
  constructor(private takeHomeTaskService: TakeHomeTaskService, private takeHomeDraftsService: TakeHomeDraftsService) { }


  @ApiOperation({ summary: 'Get Packages' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get('/packages')
  findAllPackages() {
    return this.takeHomeTaskService.findAllPackages();
  }

  @ApiOperation({ summary: 'Create Take home task' })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @ApiResponse({ status: 201 })
  @Post()
  @Permissions('assessment:edit')
  async create(@Body() createCodingInstructionDto, @AuthUser() user) {
    return await this.takeHomeTaskService.create(createCodingInstructionDto, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Create take home draft' })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Post('/drafts')
  @Permissions('assessment:edit')
  async createDraft(@Body() draftDTO, @AuthUser() user) {
    return this.takeHomeDraftsService.create(draftDTO, user["https://urecruits.com/companyId"]);
  }

  @ApiOperation({ summary: 'Get all take home task active and drafts' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get()
  @Permissions('recruiter', 'assessment:view')
  async findAll(@Query() query, @AuthUser() user) {
    const takeHomeTask = await this.takeHomeTaskService.findAll(user["https://urecruits.com/companyId"]);
    const takeHomeDraft = await this.takeHomeDraftsService.findAll(user["https://urecruits.com/companyId"]);
    if (query.status === 'ACTIVE') {
      return [...takeHomeTask];
    } else if (query.status === 'DRAFT') {
      return [...takeHomeDraft];
    } else {
      return [...takeHomeTask, ...takeHomeDraft];
    }
  }

  @ApiOperation({ summary: 'Get take home by taskId ' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/task/:taskId')
  @Permissions('recruiter', 'assessment:view')
  async findDraftByTaskId(@Param('taskId') taskId: number, @AuthUser() user) {

    const draft = await this.takeHomeDraftsService.findByTaskId(taskId, user["https://urecruits.com/companyId"])
    if (draft) {
      return draft
    } else {
      return await this.takeHomeTaskService.findByTaskId(taskId, user["https://urecruits.com/companyId"])
    }
  }

  @ApiOperation({ summary: 'Get take home task by id' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get(':id')
  // @Permissions('assessment:view')
  @Permissions('OR', 'recruiter', 'candidate')
  findOne(@Param('id') id: string, @AuthUser() user) {
    return this.takeHomeTaskService.findOne(+id, user);
  }

  @ApiOperation({ summary: 'Update take home task' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch(':id')
  @Permissions('assessment:edit')
  async update(@Param('id') id: string, @Body() takeHomeTaskDto, @AuthUser() user) {
    // Process questions that need test cases regenerated
    if (takeHomeTaskDto.questions) {
      for (const question of takeHomeTaskDto.questions) {
        if (question.regenerateTestCases) {
          // Delete existing test cases first
          await this.takeHomeTaskService.removeTestCasesByQuestionId(
            question.id,
            user["https://urecruits.com/companyId"],
            user["https://urecruits.com/userId"]
          );

          // The frontend will call /generate-test-case endpoint separately
          delete question.regenerateTestCases;
        }
      }
    }

    return this.takeHomeTaskService.update(+id, takeHomeTaskDto, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({ summary: 'Update take home task' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch('/task/:taskId')
  @Permissions('assessment:edit')
  async updateByTaskId(@Param('taskId') taskId: string, @Body() takeHomeTaskDto, @AuthUser() user) {
    // Process questions that need test cases regenerated
    if (takeHomeTaskDto.questions) {
      for (const question of takeHomeTaskDto.questions) {
        if (!question.useAIGeneratedTestCases) {
          // Delete existing test cases first
          await this.takeHomeTaskService.removeTestCasesByQuestionId(
            question.id,
            user["https://urecruits.com/companyId"],
            user["https://urecruits.com/userId"]
          );
        }
      }
    }

    return this.takeHomeTaskService.updateByTaskId(taskId, takeHomeTaskDto, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({ summary: 'Update take home draft task' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Patch('/draft/task/:taskId')
  @Permissions('assessment:edit')
  async updateDraftByTaskId(@Param('taskId') taskId: string, @Body() takeHomeTaskDto, @AuthUser() user) {
    return this.takeHomeDraftsService.updateByTaskId(taskId, takeHomeTaskDto, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({ summary: 'Delete take home task' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete(':id')
  @Permissions('assessment:edit')
  remove(@Param('id') id: string, @AuthUser() user) {
    return this.takeHomeTaskService.remove(+id, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({ summary: "Delete take home task's questions" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete('/question/:id')
  @Permissions('assessment:delete', 'assessment:edit')
  removeQuestions(@Param('id') id: string, @AuthUser() user) {
    return this.takeHomeTaskService.removeQuestionById(+id, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }

  @ApiOperation({ summary: "Delete test cases for a specific questionId" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Delete('/question/:questionId/testcases')
  @Permissions('assessment:delete', 'assessment:edit')
  removeTestCases(@Param('questionId') questionId: string, @AuthUser() user) {
    return this.takeHomeTaskService.removeTestCasesByQuestionId(+questionId, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }


  @ApiOperation({ summary: "Get test cases for a specific questionId" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/question/:questionId/testcases')
  @Permissions('assessment:view')
  getTestCases(@Param('questionId') questionId: string, @AuthUser() user) {
    return this.takeHomeTaskService.getTestCasesByQuestionId(+questionId, user["https://urecruits.com/companyId"], user["https://urecruits.com/userId"]);
  }


  @ApiOperation({ summary: 'Search take home tasks' })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('search')
  @Permissions('assessment:view')
  async search(@Query() query, @AuthUser() user) {
    const takeHomeTask = await this.takeHomeTaskService.search(query, user["https://urecruits.com/companyId"]);
    const takeHomeDraft = await this.takeHomeDraftsService.search(query, user["https://urecruits.com/companyId"]);

    return [...takeHomeTask, ...takeHomeDraft];
  }

  @ApiOperation({ summary: 'Create dummy take home tasks' })
  @ApiResponse({ status: 200 })
  @Post('dummy')
  async createDummyTakeHomeTasks(@Body() dto) {
    return this.takeHomeTaskService.createDummyTakeHomeTask(dto.companyId);
  }

  // @ApiOperation({ summary: 'Feed take home tasks to qdrant  ' })
  // @ApiResponse({ status: 200 })
  // @Post('qdrant/feed')
  // async feedToQdrant() {
  //   console.log("Feeding to qdrant");
  //   const client = new QdrantClient({ url: process.env.QDRANT_DATABASE_URL, apiKey: process.env.QDRANT_API_KEY });

  // Fetch Take home tasks in batches
  // const batchSize = 10;
  // let offset = 0;
  // let hasMoreTakeHome = true;

  // while (hasMoreTakeHome) {
  //   console.log({offset,limit:batchSize});
  //   const takehomes = await this.takeHomeTaskService.getAllTakeHome({limit:batchSize,offset});
  //   console.log({takehomes});
  //   if (!takehomes?.length) {
  //     hasMoreTakeHome = false;
  //     break;
  //   }

  //   const points = [];
  //   for (let takehome of takehomes) {
  //     const data = this.takeHomeTaskService.getTakeHomeTaskDataForQdrant(takehome);
  //     if(!data){
  //       continue;
  //     }
  //     console.log({dataString:data.str});
  //     try {
  //       const takeHomeVector = await langchainService.getEmbeddings(data.str);
  //       console.log({takeHomeVector});
  //       points.push({
  //         id:data.id,
  //         vector:takeHomeVector,
  //         payload:data.payload
  //       })
  //     } catch (error) {
  //       console.error("Error fetching embeddings:", error);
  //     }

  //     // Add a delay to respect rate limits
  //     await new Promise(resolve => setTimeout(()=>{
  //       resolve(true)
  //     }, 3000));
  //   }

  //   console.log({ points });
  //   try {
  //     const response = await client.upsert("take-home", { wait: true, points });
  //     console.log("Upsert Response:", response);
  //   } catch (error) {
  //     console.error("Upsert failed:", error);
  //   }

  //   // Move to the next batch
  //   offset += batchSize;
  // }


  // // Feed drafts to qdrant
  //   let draftOffset = 0;
  //   let hasMoreDrafts = true;

  //   while (hasMoreDrafts) {
  //     const drafts = await this.takeHomeDraftsService.getAllTakeHomeDrafts({ limit: batchSize, offset:draftOffset });
  //     if (!drafts?.length) {
  //       hasMoreDrafts = false;
  //       break;
  //     }

  //     const points = [];
  //     for (let draft of drafts) {
  //       const data = this.takeHomeTaskService.getTakeHomeTaskDataForQdrant(draft);
  //       if(!data){
  //         continue;
  //       }
  //       try {
  //         const takeHomeVector = await langchainService.getEmbeddings(data.str);
  //         points.push({
  //           id:data.id,
  //           vector:takeHomeVector,
  //           payload:data.payload
  //         })
  //       } catch (error) {
  //         console.error("Error fetching embeddings:", error);
  //       }

  //       // Add a delay to respect rate limits
  //       await new Promise(resolve => setTimeout(()=>{
  //         resolve(true)
  //       }, 3000));
  //     }

  //     console.log({ points });
  //     try {
  //       const response = await client.upsert("take-home", { wait: true, points });
  //       console.log("Upsert Response:", response);
  //     } catch (error) {
  //       console.error("Upsert failed:", error);
  //     }

  //     // Move to the next batch
  //     draftOffset += batchSize;
  //   }


  //   return { message: "Feeding to qdrant completed" };
  // }
}