{"root": "apps/hr-analytics-api", "sourceRoot": "apps/hr-analytics-api/src", "projectType": "application", "targets": {"build": {"executor": "@nrwl/node:build", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/hr-analytics-api", "main": "apps/hr-analytics-api/src/main.ts", "tsConfig": "apps/hr-analytics-api/tsconfig.app.json", "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/hr-analytics-api/src/environments/environment.ts", "with": "apps/hr-analytics-api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:execute", "options": {"buildTarget": "hr-analytics-api:build"}}, "lint": {"executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/hr-analytics-api/**/*.ts"]}}, "test": {"executor": "@nrwl/jest:jest", "outputs": ["coverage/apps/hr-analytics-api"], "options": {"jestConfig": "apps/hr-analytics-api/jest.config.js", "passWithNoTests": true}}}, "tags": []}