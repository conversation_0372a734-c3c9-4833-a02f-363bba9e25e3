import { Controller, Get, Param, Query, UseGuards } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { PermissionsGuard } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  Department, DepartmentService, Industry,
} from "@microservices/recruitment-db";
import { DepartmentDto } from "./dto/department.dto";

@ApiTags("Department")
@Controller("department")
export class DepartmentsController {
  constructor (private readonly departmentService: DepartmentService) {}

  @ApiOperation({ summary: "Get departments" })
  @ApiResponse({ status: 200, type: [Department] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/label/:label?')
  @Get()
  getAll (@Param('label') label?: string,) {
    return this.departmentService.findByLabel(label);
  }


  @ApiOperation({ summary: "Get department" })
  @ApiResponse({ status: 200, type: Industry })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/id/:id')
  getOne (@Param('id') id: string) {
    return this.departmentService.getByPk(+id);
  }

  @ApiOperation({ summary: "Get departments by ids" })
  @ApiResponse({ status: 200, type: [Industry] })
  @UseGuards(AuthGuard('jwt'), PermissionsGuard)
  @Get('/array')
  getArray (@Query() query: DepartmentDto) {
    if (query.ids instanceof Array) {
      return this.departmentService.getByIdArrays(query.ids)
    } else {
      return this.departmentService.getByIdArrays([query.ids])
    }
  }
}
