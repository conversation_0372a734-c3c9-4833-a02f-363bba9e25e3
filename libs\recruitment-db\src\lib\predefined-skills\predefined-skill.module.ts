import { Module } from '@nestjs/common';
import { PredefinedSkillService } from './predefined-skill.service';
import { SequelizeModule } from '@nestjs/sequelize';
import { PredefinedSkills } from './predefined-skill.model';

@Module({
  providers: [PredefinedSkillService],
  imports: [SequelizeModule.forFeature([PredefinedSkills])],
  exports: [SequelizeModule, PredefinedSkillService],
})
export class PredefinedSkillModule {}
