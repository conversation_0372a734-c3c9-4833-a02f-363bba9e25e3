import { Controller, Post, Body, Get, Patch, Delete, UseGuards, Param } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse,ApiBearerAuth } from "@nestjs/swagger";
import { PermissionsGuard, Permissions, AuthUser } from "@microservices/auth";
import { AuthGuard } from "@nestjs/passport";
import {
  Education,
  Experience,
  UpdateEducationDto,
  UpdateExperienceDto,
  SkillsService,
  CertificatesService,
  AwardsService,
  ProjectsService,
  EducationsService,
  ExperiencesService,
  RecruitersService,
  Recruiter,
  UpdateProfileRecruiterDto,
  VerificationDto,
} from "@microservices/recruitment-db";
// import { QdrantClient } from "@qdrant/qdrant-js";
// import { LangchainService } from "@microservices/integrations";

// const langchainService = new LangchainService();
//   companyId: user["https://urecruits.com/companyId"],
//   tenantId: user["https://urecruits.com/tenantId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   recruiterId: user["https://urecruits.com/recruiterId"],
//   userId: user["https://urecruits.com/userId"],

@ApiTags("Recruiter")
@ApiBearerAuth("access-token")
@Controller("recruiter")
export class RecruiterController {
  constructor (
    private readonly recruitersService: RecruitersService,
    private skillsService: SkillsService,
    private certificatesService: CertificatesService,
    private awardsService: AwardsService,
    private projectsService: ProjectsService,
    private educationsService: EducationsService,
    private experiencesService: ExperiencesService,
  ) {}

  @ApiOperation({ summary: "Get recruiter profile" })
  @ApiResponse({ status: 200, type: Recruiter })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get()
  @Permissions("recruiter")
  getProfile (@AuthUser() user: any) {
    return this.recruitersService.getRecruiterById(user["https://urecruits.com/recruiterId"]);
  }

  @Get("/open-search/:id")
  addDataOnOS (@Param("id") id: number) {
    return this.recruitersService.addDataOnOpenSearch(id);
  }

  @ApiOperation({ summary: "Update recruiter" })
  @ApiResponse({ status: 201, type: Recruiter })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch()
  @Permissions("recruiter","profile-management:edit")
  update (@Body() dto: UpdateProfileRecruiterDto, @AuthUser() user: any) {
    return this.recruitersService.updateRecruiterProfile({...dto, id: user["https://urecruits.com/recruiterId"], userId: user["https://urecruits.com/userId"]});
  }

  @ApiOperation({ summary: "Delete education" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/education")
  @Permissions("recruiter","profile-management:edit")
  deleteEducation (@Body() dto: UpdateEducationDto, @AuthUser() user: any) {
    return this.educationsService.delete({ ...dto, recruiterId: user["https://urecruits.com/recruiterId"], candidateId: null });
  }

  @ApiOperation({ summary: "Update education" })
  @ApiResponse({ status: 201, type: Education })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/education")
  @Permissions("recruiter","profile-management:edit")
  updateEducation (@Body() dto: UpdateEducationDto, @AuthUser() user: any) {
    return this.educationsService.update({ ...dto, recruiterId: user["https://urecruits.com/recruiterId"], candidateId: null });
  }

  @ApiOperation({ summary: "Create education" })
  @ApiResponse({ status: 201, type: Education })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/education")
  @Permissions("recruiter", "profile-management:edit")
  createEducation (@AuthUser() user: any) {
    return this.educationsService.create({ recruiterId: user["https://urecruits.com/recruiterId"], candidateId: null });
  }

  @ApiOperation({ summary: "Delete experience" })
  @ApiResponse({ status: 201, type: "Boolean" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Delete("/experience")
  @Permissions("recruiter", "profile-management:edit")
  deleteExperience (@Body() dto: UpdateExperienceDto, @AuthUser() user: any) {
    return this.experiencesService.delete({ ...dto, recruiterId: user["https://urecruits.com/recruiterId"], candidateId: null });
  }

  @ApiOperation({ summary: "Update experience" })
  @ApiResponse({ status: 201, type: Experience })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/experience")
  @Permissions("recruiter","profile-management:edit")
  updateExperience (@Body() dto: UpdateExperienceDto, @AuthUser() user: any) {
    return this.experiencesService.update({ ...dto, recruiterId: user["https://urecruits.com/recruiterId"], candidateId: null });
  }

  @ApiOperation({ summary: "Create experience" })
  @ApiResponse({ status: 201, type: Experience })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/experience")
  @Permissions("recruiter","profile-management:edit")
  createExperience (@AuthUser() user: any) {
    return this.experiencesService.create({ recruiterId: user["https://urecruits.com/recruiterId"], candidateId: null });
  }

  @ApiOperation({ summary: "Verification" })
  @ApiResponse({ status: 200, type: Recruiter })
  @Patch("/verification")
  verification (@Body() dto: VerificationDto) {
    return this.recruitersService.verification(dto);
  }

  @ApiOperation({ summary: "Check verification" })
  @ApiResponse({ status: 200, type: Recruiter })
  @Get("/check-verification/:ticket")
  checkVerification (@Param("ticket") ticket: string) {
    return this.recruitersService.checkVerification(ticket);
  }

  // @ApiOperation({ summary: "Feed recruiter to Qdrant" })
  // @ApiResponse({ status: 200 })
  // @Post("/qdrant/seed")
  // async feedRecruiterToQdrant() {
  //   const client = new QdrantClient({ url: process.env.QDRANT_DATABASE_URL, apiKey: process.env.QDRANT_API_KEY });

  //   // Fetch jobs in batches
  //   const batchSize = 10; 
  //   let offset = 0;
  //   let hasMoreJobs = true;

  //   while (hasMoreJobs) {
  //     const recruiters = await this.recruitersService.getAllRecruiters({ limit: batchSize, offset });
  //     if (!recruiters.rows?.length) {
  //       hasMoreJobs = false;
  //       break;
  //     }

  //     const points = [];
  //     for (let recruiter of recruiters.rows) {
  //       const data =await this.recruitersService.getUserDataForQdrant(recruiter);
  //       console.log(data.id,'data ids');
  //       if(!data){
  //         continue;
  //       }
  //       try {
  //         const recruiterVector = await langchainService.getEmbeddings(data.str);
  //         points.push({
  //           id:data.id,
  //           vector:recruiterVector,
  //           payload:data.payload
  //         })
  //       } catch (error) {
  //         console.error("Error fetching embeddings:", error);
  //       }

  //       // Add a delay to avoid rate limits
  //       await new Promise(resolve => setTimeout(()=>{
  //         resolve(true)
  //       }, 3000));
  //     }

  //     console.log({ points });
  //     try {
  //       const response = await client.upsert("users", { wait: true, points });
  //       console.log("Upsert Response:", response);
  //     } catch (error) {
  //       console.error("Upsert failed:", error);
  //     }

  //     offset += batchSize;
  //   }
  // }
}
