export enum roundType{
    FUNCTIONAL_DOMAIN_ASSESSMENT="Functional/Domain Assessment",
    LIVE_TASK_CODING_ASSESSMENT  = 'Live Task/Coding Assessment',
    TAKE_HOME_CODING_ASSESSMENT = 'Take Home/Coding Assessment',
    TECHNICAL_VIDEO_AUDIO_INTERVIEW="Technical Audio Video Interview",
    HR_AUDIO_VIDEO_INTERVIEW="HR Audio Video Interview",
    SENIOR_HR_AUDIO_VIDEO_INTERVIEW = "Senior HR Audio Video Interview",
    CONDITIONAL_OFFFER_LETTER_GENERATION="Conditional Offer Letter Generation",
    BACKGROUND_SCREENING = "Background Screening",
    DRUG_SCREENING = "Drug Screening"
}

export enum roundStatus{
    IN_PROGRESS = "In Progress",
    COMPLETED = "Completed",
    REJECTED = "Rejected",
    TERMINATED="Terminated"
}