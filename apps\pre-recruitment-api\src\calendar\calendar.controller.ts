import { AuthUser, Permissions, PermissionsGuard } from "@microservices/auth";
import { GoogleAuthService } from "@microservices/calendars";
import { Company, TemporalWorkflowService } from "@microservices/recruitment-db";
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Headers,
  Logger,
  Patch,
  Post,
  Query,
  UseGuards,
} from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { InjectModel } from "@nestjs/sequelize";
import { CreateEventDTO } from "libs/calendars/src/lib/dto/create-event.dto";
import { GetFreeSlotsDTO } from "libs/calendars/src/lib/dto/get-free-slots.dto";
import { timeSlotProposingSignal } from "apps/temporal/src/app/workflow/temporal/workflows";
import { connectToTemporal } from "apps/temporal/src/app/workflow/workflow.provider";
import { User, Subscribe, Jobs } from "@microservices/recruitment-db";
import { EmailService } from "@microservices/email";
import { HttpService } from "@nestjs/axios";

@ApiTags("Calendar")
@Controller("calendar")
export class CalendareController {
  constructor(
    private gmailService: GoogleAuthService,
    private temporalWorkflowService: TemporalWorkflowService,
    private emailService: EmailService,
    // @InjectModel(Event) private eventModel: typeof Event,
    @InjectModel(User) private userRepository: typeof User,
    @InjectModel(Company) private companyRepository: typeof Company,
    private readonly httpService: HttpService
  ) { }

  @ApiOperation({ summary: "Book Apppoinment" })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/book-appointment")
  @Permissions("job-post:view")
  async bookAppointment(@Body() data: CreateEventDTO, @AuthUser() user) {
    const eventData = await this.gmailService.bookAppointment(
      user["https://urecruits.com/userId"],
      data
    );
    const eventId = eventData?.id
    data.eventId = eventId;
    data.roomId = eventData?.roomId;
    const obj = {
      jobId: data.jobId,
      userId: data.candidateId,
      eventId,
      roundName: data.description,
      interviewers: data.interviewers,
      eventTitle: data.eventTitle,
      roomId: eventData?.roomId,
      eventStartTime: eventData?.startGmt,
      eventEndTime: eventData?.endGmt
    }

    const url = `${process.env.RECRUITMENT_API_URI}/api/round/addEvent`;
    await this.httpService.patch(url, obj).toPromise();

    const response = await this.temporalWorkflowService.find(
      data?.candidateId,
      data.jobId
    );
    const client = await connectToTemporal();
    if (client && response?.workflowid) {
      const handle = client.getHandle(response?.workflowid);
      await handle.signal(timeSlotProposingSignal, {
        message: "Meeting time is proposed",
        data: data,
      });
    } else {
      Logger.log(
        "Getting issue to connect the client in bookAppointment of Gcal"
      );
    }
    return { message: "Appointment booked successfully" };
  }

  @ApiOperation({ summary: "Book Apppoinment" })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Post("/meet-up/book-appointment")
  @Permissions("job-post:view")
  async meetupBookAppointment(@Body() data: CreateEventDTO, @AuthUser() user) {
    const eventData = await this.gmailService.bookAppointment(
      user["https://urecruits.com/userId"],
      data
    );
    const jobId = eventData?.jobId;
    const userId = data?.candidateId;
    const recruitmentUrl = `${process.env.WEB_APP_URI}`;
    let url = `${recruitmentUrl}/live-coding/interview/${eventData?.roomId}`
    try {

      const userData = await this.userRepository.findOne({where:{id:Number(userId)},attributes:["id","email","firstname","lastname"]})
      const company = await this.companyRepository.findByPk(user["https://urecruits.com/companyId"],{attributes:["name","avatar"]})


      await this.emailService.sendRecruitmentActivityService(
        "jobDetail.html",
        {
          companyLogo: `${company?.avatar}`,
          position: `Meeting Scheduled`,
          body: `
          <p>Dear ${userData?.firstname} ${userData?.lastname},</p>
          <p>We are excited to inform you that your interview has been scheduled with ${company?.name} Company.</p>
          <p>Date: ${data?.date}</p>
          <p>Time: ${data?.slot}</p>
        <p>You can join the meeting via the <strong>Calendar</strong> section of your dashboard or directly through the <a href='${url}' style="color: #099C73;" >Link</a>.</p>
          <br/>
          <button style="
            font-size: 14px;
            font-weight: 900;
            line-height: 100%;
            color: white;
            padding: 16px 28px;
            border: none;
            cursor: pointer;
            background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
            border-radius: 4px;
          ">
            <a href="https://urecruits.com/" style="
                text-decoration: none;
                color: inherit;
                display: block;
                width: 100%;
                height: 100%;
            ">
                Click here to Login
            </a>
          </button>
          <br/>
        `,
          userId: userData.id,
        },
        userData?.email,
        `Meeting has been scheduled by ${company?.name} Company`,
        {
          notificationTitle: `Meeting Scheduled`,
          notificationMsg: `${company?.name} has scheduled a meeting with you `
        }
      );
      const recruiter = await this.userRepository.findByPk(user["https://urecruits.com/userId"],{attributes:["id","firstname","lastname","avatar","email"]})

      if(recruiter){
        this.emailService.sendRecruitmentActivityService(
          "jobDetail.html",
          {
            companyLogo: `${company?.avatar}`,
            position: `Meeting Scheduled`,
            body: `
        <p>Dear ${recruiter?.firstname} ${recruiter?.lastname}</p>
        <p>we are writing to inform you about the scheduled meeting with candidate ${userData?.firstname + " " + userData?.lastname} at ${company?.name}</p>
        <p>Date: ${data?.date}</p>
        <p>Time: ${data?.slot}</p>
        <p>You can join the meeting via the <strong>Calendar</strong> section of your dashboard or directly through the <a href='${url}' style="color: #099C73;" >Link</a>.</p>
        <br/>
        <button style="
        font-size: 14px;
        font-weight: 900;
        line-height: 100%;
        color: white;
        padding: 16px 28px;
        border: none;
        cursor: pointer;
        background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
        border-radius: 4px;
       ">
          <a href="https://urecruits.com/" style="
              text-decoration: none;
              color: inherit;
              display: block;
              width: 100%;
              height: 100%;
          ">
              Login
          </a>
        </button>
        <br/>
        `,
            userId: recruiter?.id,
          },
          recruiter?.email,
          `Meeting has been scheduled with candidate ${userData?.firstname + " " + userData?.lastname}.`,
          {
            notificationTitle: `Meeting Scheduled Successfully...`,
            notificationMsg: `You have scheduled a meeting with ${userData?.firstname} ${userData?.lastname}.`
          }
        ).catch(error=>console.log("recruiter email failed",error));
      }

      eventData?.interviewers?.map(interviewer => {
        this.emailService.sendRecruitmentActivityService(
          "jobDetail.html",
          {
            companyLogo: `${company?.avatar}`,
            position: `Meeting Scheduled`,
            body: `
        <p>Dear ${interviewer?.name}</p>
        <p>we are writing to inform you about the scheduled meeting with candidate ${userData?.firstname + " " + userData?.lastname} at ${company?.name}</p>
        <p>Date: ${data?.date}</p>
        <p>Time: ${data?.slot}</p>
        <p>You can join the meeting via the <strong>Calendar</strong> section of your dashboard or directly through the <a href='${url}' style="color: #099C73;" >Link</a>.</p>
        <br/>
        <button style="
        font-size: 14px;
        font-weight: 900;
        line-height: 100%;
        color: white;
        padding: 16px 28px;
        border: none;
        cursor: pointer;
        background: linear-gradient(125.2deg, #099C73 8.04%, #**********.26%);
        border-radius: 4px;
       ">
          <a href="https://urecruits.com/" style="
              text-decoration: none;
              color: inherit;
              display: block;
              width: 100%;
              height: 100%;
          ">
              Login
          </a>
        </button>
        <br/>
        `,
            userId: interviewer?.id,
          },
          interviewer?.email,
          `Meeting has been scheduled with candidate ${userData?.firstname + " " + userData?.lastname}.`,
          {
            notificationTitle: `Meeting Scheuled Successfully...`,
            notificationMsg: `You have scheduled a meeting with ${userData?.firstname} ${userData?.lastname}.`
          }
        ).catch(error=>console.log("interview email failed",error));
      })

      return { message: "Appointment booked successfully" };
    } catch (error) {
      Logger.log(error);
      throw new BadRequestException(error);
    }
  }

  @ApiOperation({ summary: "Reschedule Appoinment" })
  @ApiResponse({ status: 201 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Patch("/reschedule-appointment")
  @Permissions("OR", "candidate", "job-post:view")
  async rescheduleAppointment(@Headers('authorization') authHeader: any, @Body() data: CreateEventDTO, @AuthUser() user) {
    try {
      const url = `${process.env.ASSESSMENT_API_URI}/api/calendar/event/${data.eventId}`;
      const headers = {
        Authorization: authHeader
      }
      const eventData = (await this.httpService.get(url, { headers }).toPromise()).data;
      data.interviewers = eventData.interviewers;
      const newEventData = await this.gmailService.rescheduleAppointment(
        user["https://urecruits.com/userId"],
        data,
        eventData
      );
      data.roomId = eventData?.roomId;

      const response = await this.temporalWorkflowService.find(
        data?.candidateId,
        data.jobId
      );

      const obj = {
        jobId: data.jobId,
        userId: data.candidateId,
        eventId: eventData?.id,
        roundName: data.description,
        interviewers: data.interviewers,
        eventTitle: data.eventTitle,
        roomId: eventData?.roomId,
        eventStartTime: newEventData?.eventData?.startGmt||'',
        eventEndTime: newEventData?.eventData.endGmt||''
      }
      const roundUrl = `${process.env.RECRUITMENT_API_URI}/api/round/addEvent`;
      await this.httpService.patch(roundUrl, obj).toPromise();

      const client = await connectToTemporal();
      if (client && response?.workflowid) {
        const handle = client.getHandle(response?.workflowid);
        await handle.signal(timeSlotProposingSignal, {
          message: "Meeting time is proposed",
          data: { ...data, rescheduled: true },
        });
      } else {
        Logger.log(
          "Getting issue to connect the client in bookAppointment of Gcal"
        );
      }
      return { message: "Appointment booked successfully" };
    } catch (error) {
      console.log({ ErrorInRescheduling: error })
      throw new BadRequestException(error);
    }
  }

  @ApiOperation({ summary: "get calendar" })
  @ApiResponse({ status: 200 })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @Get("/get-calendar")
  async getEvents(@Query() query: any, @AuthUser() user) {
    return this.gmailService.getEvents(
      user["https://urecruits.com/userId"],
      query
    );
  }

  @ApiOperation({ summary: "get free slots" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get("/freeslots")
  async getFreeSlots(@Query() data: GetFreeSlotsDTO, @AuthUser() user) {
    return this.gmailService.getFreeBusySlots(
      user["https://urecruits.com/userId"],
      data
    );
  }

  @ApiOperation({ summary: "get appoinment" })
  @UseGuards(AuthGuard("jwt"), PermissionsGuard)
  @ApiResponse({ status: 200 })
  @Get("/appointment")
  async appointment(@Query() data: GetFreeSlotsDTO, @AuthUser() user) {
    return this.gmailService.appointment(
      user["https://urecruits.com/userId"],
      data
    );
  }
}
