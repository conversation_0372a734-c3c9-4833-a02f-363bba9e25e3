import { Injectable } from '@nestjs/common'
import { InjectModel } from '@nestjs/sequelize'
import { Industry } from './industries.model'
import { IndustryDto } from './dto/industry.dto'
import * as data from "./data.json";
import { Op } from "sequelize";

@Injectable()
export class IndustriesService {
  constructor (
    @InjectModel(Industry) private industryRepository: typeof Industry,
  ) {}

  async getIndustry (dto: IndustryDto) {
    const [industry] = await this.industryRepository.findOrCreate({
      where: {
        value: dto.value,
        label: dto.label,
      },
    })

    return industry
  }

  async seed() {
    await this.industryRepository.bulkCreate(data, {
      validate: true,
    }).then(async () => this.industryRepository.sequelize.query(`ALTER SEQUENCE "${this.industryRepository.tableName}_id_seq" RESTART WITH ${await this.industryRepository.count() + 1}`))
    return true;
  }

  async findByLabel(find?: string,limit:number=30,offset:number=0) {
    let where = {}
    if (find && find.length > 0) {
      where = {
        label: {
          [Op.iLike]: `%${find}%`,
        },
      }
    }
    return await this.industryRepository.findAll({
      where,
      limit,
      offset,
    });
  }

  async getByPk(id: number) {
    return await this.industryRepository.findByPk(id)
  }

  async getByIdArrays(ids: [string]) {
    return await this.industryRepository.findAll(
      {
        attributes: ["id", "value", "label"],
        where: {
          id: {
            [Op.in]: ids,
          }
        }
      }
    )
  }
}
