import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class UserDto {
  @ApiPropertyOptional({ example: "https://domain.com/avatar.jpg", description: "Avatar" })
  @IsOptional()
  readonly avatar?: string;

  @ApiProperty({ example: "<EMAIL>", description: "Email address" })
  readonly email: string;

  @ApiProperty({ example: "+*********", description: "Phone" })
  readonly phone: string;

  @ApiProperty({ example: "Rob", description: "First Name" })
  readonly firstname: string;

  @ApiPropertyOptional({ example: "<PERSON>", description: "First Name" })
  @IsOptional()
  readonly middlename?: string;

  @ApiProperty({ example: "Stark", description: "Last Name" })
  readonly lastname: string;

  @ApiPropertyOptional({ example: 1, description: "Company id" })
  @IsOptional()
  readonly companyId?: number;
}
