import { HttpException, HttpStatus, Injectable, Logger, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/sequelize";

import { DomainResults } from "./domain-result.model";
import { DomainAnswers } from "./domain-answer.model";
import { Op } from 'sequelize';
import { DomainAssessment } from "../domain-questions/domain-assessment.model";

@Injectable()
export class DomainResultService {
  constructor (
    @InjectModel(DomainResults)
    private domainResults: typeof DomainResults,
    @InjectModel(DomainAnswers)
    private domainAnswer: typeof DomainAnswers,
  ) {}

  async findAllAssessment(data,companyId) {
    const order = [];
    if (data.sortBy && data.sortType) {
      order.push([data.sortBy, data.sortType]);
    } else {
      order.push(["id", "DESC"]);
    }
  
    const where:any = {
      jobId: data?.jobId,
      assessmentId: data.assessmentId,
    };
  
    if (data.minScore !== undefined && data.minScore !== "0") {
      where.totalScore = { [Op.gte]: data.minScore };
    }
    if (data.maxScore !== undefined) {
      if (!where.totalScore) where.totalScore = {};
      where.totalScore[Op.lte] = data.maxScore;
    }
  
    const result = await this.domainResults.findAndCountAll({
      where,
      distinct: true,
      limit: data.limit || 10,
      offset: data.offset || 0,
      order,
      include:[{
        model: DomainAssessment,
        where: {companyId}
      }]
    });        
      if (result?.rows?.length > 0) {
        const totalScores = result.rows.map((row) => row.totalScore);
        const averageTotalScore = totalScores.reduce((sum, score) => sum + score, 0) / totalScores.length;
        return {result,averageTotalScore}
    } else {
      return {result: { rows: [], count: 0 }, averageTotalScore:0}
    }
  }

 async findById(id:number, companyId: number) {
    const result = await this.domainResults.findByPk(id,{
      include:[
        DomainAnswers,
        {
          model: DomainAssessment,
          where: {companyId}
        }
      ]})
    if(result){
      return result ;
    }else{
      throw new NotFoundException("Data not found")
    }
  }

  async create(dto): Promise<DomainResults> {
    try {
        const existingResult = await this.domainResults.findOne({
            where: {
                candidateId: dto.candidateId,
                jobId: dto.jobId,
                assessmentId: dto.assessmentId,
            },
        });

        if (existingResult) {
            throw new HttpException("You've already taken this test before.", HttpStatus.CONFLICT);
        }

        const newResult = await this.domainResults.create(dto, { include: [DomainAnswers] });

        return newResult;
    } catch (err) {
        Logger.log(err);
        if(err?.status === 409){
          throw new HttpException("You've already taken this test before.", HttpStatus.CONFLICT);
        }
        throw new HttpException("Failed to create test result.", HttpStatus.INTERNAL_SERVER_ERROR);
    }
}

  async update(id: number, dto, companyId) {
    const data = await this.domainAnswer.findOne({ where: { id }
    });
    if (!data) {
      throw new NotFoundException("Data not found"); 
    }
    const result =await data.update(dto);
    return {message:"Data updated successfully",status:200}
  }

  async updateScore (id: number, dto) {
    const data = await this.domainResults.findOne({ where: { id } });
    if (!data) {
      throw new NotFoundException("Data not found"); 
    }
    const result =await data.update(dto);
    return {message:"Data updated successfully",status:200}
  }

  async deleteFeedback (id: number) {
    const data = await this.domainAnswer.findOne({ where: { id } });
    if (!data) {
      throw new NotFoundException("Data not found"); 
    }
    const result =await data.update({feedback:null});
    return {message:"Data deleted successfully",status:200}
  }

  async findByAssessmentId(assessmentId, candidateId, jobId, companyId?) {
    const data = await this.domainResults.findOne({
      where: {
        assessmentId,
        candidateId,
        jobId
      },
      include: [
        DomainAnswers,
        {
          model: DomainAssessment,
          ...(companyId ? { where: {companyId} } : '')
        }
        
      ]
    })

    if(!data){
      throw new NotFoundException("Domain result data not found")
    }

    return data
  }
}
