import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsOptional } from "class-validator";

export class ProjectsDto {
  @ApiPropertyOptional({ example: "Pet project", description: "Project Name" })
  @IsOptional()
  readonly projectName?: string;

  @ApiPropertyOptional({ example: "2021-12-28T22:00:00.000Z", description: "Period Date start" })
  @IsOptional()
  readonly dateStart?: string;

  @ApiPropertyOptional({ example: "2021-12-28T22:00:00.000Z", description: "Period Date end" })
  @IsOptional()
  readonly dateEnd?: string;

  @ApiPropertyOptional({ example: "2021-12-28T22:00:00.000Z", description: "Award date" })
  @IsOptional()
  readonly awardDate?: string;

  @ApiPropertyOptional({ example: "Notes", description: "Notes" })
  @IsOptional()
  readonly notes?: string;
}
