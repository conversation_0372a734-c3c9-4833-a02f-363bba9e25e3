import { BelongsTo<PERSON>any, Column, DataType, HasOne, Model, Table } from "sequelize-typescript";
import { ApiProperty } from '@nestjs/swagger';
import { Candidate } from "../candidates/candidates.model";
import { Jobs } from "../job/job.model";
import { LocationJobs } from "./location-jobs.model";
import { Recruiter } from "../recruiters/recruiters.model";

interface LocationAttrs {
  city: string;
  state: string;
  st: string;
}

@Table({ tableName: 'locations', createdAt: false, updatedAt: false })
export class Location extends Model<Location, LocationAttrs> {
  @ApiProperty({ example: '1', description: 'Primary key' })
  @Column({
    type: DataType.INTEGER,
    unique: true,
    autoIncrement: true,
    primaryKey: true,
  })
  id: number;

  @ApiProperty({
    example: 'FPO',
    description: 'City',
  })
  @Column({ type: DataType.STRING, allowNull: false, unique: false })
  city: string;

  @ApiProperty({
    example: 'New York',
    description: 'State',
  })
  @Column({ type: DataType.STRING, allowNull: false })
  state: string;

  @ApiProperty({
    example: 'AA',
    description: 'St',
  })
  @Column({ type: DataType.STRING, allowNull: false })
  st: string;

  @HasOne(() => Candidate)
  candidate: Candidate;

  @HasOne(() => Recruiter)
  recruiter: Recruiter;

  @BelongsToMany(() => Jobs, () => LocationJobs)
  jobs: Jobs[];
}
